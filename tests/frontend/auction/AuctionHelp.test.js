// @ts-check
const { test, expect } = require('@playwright/test');

/**
 * Test suite for the AuctionHelp component
 * Tests the help page functionality and responsive design
 */
test.describe('AuctionHelp Component', () => {
  // Setup before each test
  test.beforeEach(async ({ page }) => {
    // Navigate to the auction help page
    await page.goto('/auctions/help');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("Auction Help Center")');
  });

  // Test section navigation
  test('should display different content based on selected section', async ({ page }) => {
    // Auction Basics section should be visible by default
    await expect(page.locator('h2:has-text("Auction Basics")')).toBeVisible();
    
    // Click on the Bidding Strategies section
    await page.click('button:has-text("Bidding Strategies")');
    
    // Bidding Strategies content should be visible
    await expect(page.locator('h2:has-text("Bidding Strategies")')).toBeVisible();
    await expect(page.locator('text=Tips and strategies for successful bidding in auctions')).toBeVisible();
    
    // Click on the Creating Auctions section
    await page.click('button:has-text("Creating Auctions")');
    
    // Creating Auctions content should be visible
    await expect(page.locator('h2:has-text("Creating Auctions")')).toBeVisible();
    await expect(page.locator('text=How to create and manage your own auctions')).toBeVisible();
    
    // Click on the Payment & Security section
    await page.click('button:has-text("Payment & Security")');
    
    // Payment & Security content should be visible
    await expect(page.locator('h2:has-text("Payment & Security")')).toBeVisible();
    await expect(page.locator('text=Understanding payment processes and security measures')).toBeVisible();
  });

  // Test FAQ expansion
  test('should expand and collapse FAQs when clicked', async ({ page }) => {
    // FAQs should be collapsed by default
    await expect(page.locator('text=What is an auction on RentUp?')).toBeVisible();
    await expect(page.locator('text=An auction on RentUp is a time-limited bidding process')).not.toBeVisible();
    
    // Click on a FAQ to expand it
    await page.click('text=What is an auction on RentUp?');
    
    // FAQ answer should now be visible
    await expect(page.locator('text=An auction on RentUp is a time-limited bidding process')).toBeVisible();
    
    // Click on the FAQ again to collapse it
    await page.click('text=What is an auction on RentUp?');
    
    // FAQ answer should no longer be visible
    await expect(page.locator('text=An auction on RentUp is a time-limited bidding process')).not.toBeVisible();
  });

  // Test responsive design
  test('should be responsive on different screen sizes', async ({ page }) => {
    // Test on mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500); // Wait for responsive layout to adjust
    
    // Navigation should be scrollable on mobile
    const navOverflow = await page.evaluate(() => {
      const nav = document.querySelector('.flex.overflow-x-auto');
      return nav ? window.getComputedStyle(nav).overflowX : null;
    });
    expect(navOverflow).toBe('auto');
    
    // Test on desktop
    await page.setViewportSize({ width: 1280, height: 800 });
    await page.waitForTimeout(500);
    
    // All navigation buttons should be visible without scrolling
    const navButtons = await page.locator('button:has-text("Auction Basics"), button:has-text("Bidding Strategies"), button:has-text("Creating Auctions"), button:has-text("Payment & Security")').count();
    expect(navButtons).toBe(4);
  });

  // Test additional resources links
  test('should have working links to related pages', async ({ page }) => {
    // Click on the "Browse Active Auctions" link
    const auctionsLinkPromise = page.waitForNavigation();
    await page.click('text=Browse Active Auctions');
    await auctionsLinkPromise;
    
    // Should navigate to the auctions page
    await expect(page).toHaveURL('/auctions');
    
    // Go back to the help page
    await page.goto('/auctions/help');
    await page.waitForSelector('h1:has-text("Auction Help Center")');
    
    // Click on the "Create Your First Auction" link
    const createLinkPromise = page.waitForNavigation();
    await page.click('text=Create Your First Auction');
    await createLinkPromise;
    
    // Should navigate to the create auction page
    await expect(page).toHaveURL('/auctions/create');
    
    // Go back to the help page
    await page.goto('/auctions/help');
    await page.waitForSelector('h1:has-text("Auction Help Center")');
    
    // Click on the "Contact Support" link
    const contactLinkPromise = page.waitForNavigation();
    await page.click('text=Contact Support');
    await contactLinkPromise;
    
    // Should navigate to the contact page
    await expect(page).toHaveURL('/contact');
  });

  // Test accessibility
  test('should be accessible', async ({ page }) => {
    // Check that all interactive elements have accessible names
    const buttons = await page.locator('button').count();
    const buttonsWithAccessibleName = await page.locator('button[aria-label], button:has-text("")').count();
    expect(buttonsWithAccessibleName).toBe(buttons);
    
    // Check that all images have alt text
    const images = await page.locator('img').count();
    const imagesWithAlt = await page.locator('img[alt]').count();
    expect(imagesWithAlt).toBe(images);
    
    // Check that the page has a proper heading structure
    await expect(page.locator('h1')).toHaveCount(1);
    await expect(page.locator('h2')).toHaveCount(1);
    await expect(page.locator('h3')).toHaveCount(2);
  });
});
