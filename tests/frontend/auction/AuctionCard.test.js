// @ts-check
const { test, expect } = require('@playwright/test');

/**
 * Test suite for the AuctionCard component
 * Tests the card display and responsive design
 */
test.describe('AuctionCard Component', () => {
  // Mock auction data
  const mockAuction = {
    id: '1',
    title: 'Test Auction Item',
    description: 'This is a test auction item description',
    current_highest_bid: 100.00,
    reserve_price: 150.00,
    end_time: new Date(Date.now() + 86400000).toISOString(), // 1 day from now
    status: 'active',
    bidCount: 5,
    imageUrl: '/placeholder-item.jpg',
    location: 'New York, NY',
    category: 'Electronics',
    subcategory: 'Cameras',
    auctionType: 'standard'
  };
  
  // Setup before each test
  test.beforeEach(async ({ page }) => {
    // Mock the API response for the auction
    await page.route('**/api/v1/auctions/1', route => {
      return route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(mockAuction)
      });
    });
    
    // Navigate to a page that displays the auction card
    await page.goto('/auctions');
    
    // Wait for the auction card to be visible
    await page.waitForSelector('.auction-card');
  });

  // Test the auction card display
  test('should display auction information correctly', async ({ page }) => {
    // Check that the title is displayed
    await expect(page.locator('.auction-card h3')).toContainText('Test Auction Item');
    
    // Check that the current bid is displayed
    await expect(page.locator('.auction-card')).toContainText('$100.00');
    
    // Check that the bid count is displayed
    await expect(page.locator('.auction-card')).toContainText('5');
    
    // Check that the status badge is displayed
    await expect(page.locator('.auction-card')).toContainText('Active');
    
    // Check that the reserve not met badge is displayed
    await expect(page.locator('.auction-card')).toContainText('Reserve not met');
    
    // Check that the auction type badge is displayed
    await expect(page.locator('.auction-card')).toContainText('Standard');
    
    // Check that the category tag is displayed
    await expect(page.locator('.auction-card')).toContainText('Electronics');
    
    // Check that the subcategory tag is displayed
    await expect(page.locator('.auction-card')).toContainText('Cameras');
  });

  // Test the auction card responsive design
  test('should be responsive on different screen sizes', async ({ page }) => {
    // Test on mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500); // Wait for responsive layout to adjust
    
    // Check that the card is displayed in a single column
    const mobileCardWidth = await page.evaluate(() => {
      const card = document.querySelector('.auction-card');
      return card ? window.getComputedStyle(card).width : null;
    });
    expect(parseInt(mobileCardWidth)).toBeLessThan(400); // Should be less than 400px on mobile
    
    // Test on desktop
    await page.setViewportSize({ width: 1280, height: 800 });
    await page.waitForTimeout(500);
    
    // Check that the cards are displayed in multiple columns
    const desktopCardWidth = await page.evaluate(() => {
      const card = document.querySelector('.auction-card');
      return card ? window.getComputedStyle(card).width : null;
    });
    expect(parseInt(desktopCardWidth)).toBeLessThan(400); // Should still be less than 400px on desktop (multiple columns)
  });

  // Test the auction card link
  test('should navigate to auction detail page when clicked', async ({ page }) => {
    // Click on the auction card
    const navigationPromise = page.waitForNavigation();
    await page.click('.auction-card a');
    await navigationPromise;
    
    // Check that we navigated to the auction detail page
    await expect(page).toHaveURL('/auctions/1');
  });

  // Test the auction card with compact mode
  test('should display in compact mode when specified', async ({ page }) => {
    // Navigate to a page that displays the auction card in compact mode
    await page.goto('/auctions/dashboard');
    
    // Wait for the auction card to be visible
    await page.waitForSelector('.auction-card.compact');
    
    // Check that the card has the compact class
    await expect(page.locator('.auction-card.compact')).toBeVisible();
    
    // Check that the image height is smaller in compact mode
    const imageHeight = await page.evaluate(() => {
      const image = document.querySelector('.auction-card.compact img');
      return image ? window.getComputedStyle(image).height : null;
    });
    expect(parseInt(imageHeight)).toBeLessThan(150); // Should be less than 150px in compact mode
  });

  // Test the auction card with different statuses
  test('should display different badges based on auction status', async ({ page }) => {
    // Mock the API response for an ended auction
    await page.route('**/api/v1/auctions/ended', route => {
      return route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          ...mockAuction,
          id: 'ended',
          status: 'ended'
        })
      });
    });
    
    // Navigate to the ended auction
    await page.goto('/auctions/ended');
    
    // Check that the ended status badge is displayed
    await expect(page.locator('.auction-card')).toContainText('Ended');
    
    // Check that the "View Details" button is displayed instead of "Place Bid"
    await expect(page.locator('.auction-card a')).toContainText('View Details');
  });
});
