"""
Comprehensive tests for the database optimization modules.

This test suite verifies all the optimization functionality including:
- Query optimization and performance tracking
- JOIN optimization strategies
- Query caching mechanisms
- Database configuration management
- Optimization service integration
"""

import pytest
import time
from unittest.mock import Mock, patch
from typing import List, Dict, Any

# Test imports with fallback
try:
    from app.core.query_optimization import (
        QueryPerformanceTracker, 
        QueryStats, 
        QueryAnalysis,
        track_query_performance,
        query_tracker,
        optimize_query,
        paginate_query,
        OptimizedQuery
    )
    from app.core.join_optimization import (
        JoinStrategy, 
        JoinType, 
        Join<PERSON>attern,
        JoinAnalysis,
        optimize_join_query,
        analyze_join_performance
    )
    from app.core.query_cache import (
        CacheStrategy, 
        QueryCache, 
        CacheEntry,
        CacheStats,
        cached_query,
        query_cache
    )
    from app.core.db_config import (
        DatabaseConfig,
        ConnectionPoolConfig,
        QueryOptimizationConfig,
        CachingConfig,
        MonitoringConfig,
        db_config
    )
    from app.services.db_optimization_service import (
        DatabaseOptimizationService,
        optimized_query,
        batch_get,
        get_optimization_service
    )
    OPTIMIZATION_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Optimization modules not available: {e}")
    OPTIMIZATION_MODULES_AVAILABLE = False
    pytest.skip("Optimization modules not available", allow_module_level=True)


class TestQueryPerformanceTracker:
    """Test query performance tracking functionality."""
    
    def test_tracker_initialization(self):
        """Test tracker initialization."""
        tracker = QueryPerformanceTracker(slow_query_threshold=2.0)
        assert tracker.slow_query_threshold == 2.0
        assert len(tracker.query_stats) == 0
    
    def test_record_query(self):
        """Test recording query execution."""
        tracker = QueryPerformanceTracker()
        tracker.record_query("test_query", 1.5, 100)
        
        assert "test_query" in tracker.query_stats
        stats = tracker.query_stats["test_query"]
        assert stats.count == 1
        assert stats.total_time == 1.5
        assert stats.total_rows == 100
        assert stats.avg_time == 1.5
    
    def test_slow_query_detection(self):
        """Test slow query detection."""
        tracker = QueryPerformanceTracker(slow_query_threshold=1.0)
        
        # Record a slow query
        tracker.record_query("slow_query", 2.0, 50)
        slow_queries = tracker.get_slow_queries()
        
        assert len(slow_queries) == 1
        assert slow_queries[0].query_name == "slow_query"
    
    def test_statistics_collection(self):
        """Test statistics collection."""
        tracker = QueryPerformanceTracker()
        tracker.record_query("query1", 0.5, 10)
        tracker.record_query("query2", 1.5, 20)
        tracker.record_query("query1", 0.8, 15)
        
        stats = tracker.get_statistics()
        assert stats["total_queries"] == 2
        assert stats["total_executions"] == 3
        assert stats["total_time"] == 2.8
    
    def test_performance_decorator(self):
        """Test query performance tracking decorator."""
        @track_query_performance("decorated_query")
        def mock_query():
            time.sleep(0.1)  # Simulate query execution
            return [1, 2, 3]
        
        result = mock_query()
        assert result == [1, 2, 3]
        
        # Check if query was tracked
        assert "decorated_query" in query_tracker.query_stats


class TestJoinOptimization:
    """Test JOIN optimization functionality."""
    
    def test_join_strategy_enum(self):
        """Test JOIN strategy enumeration."""
        assert JoinStrategy.LAZY.value == "lazy"
        assert JoinStrategy.EAGER.value == "eager"
        assert JoinStrategy.SELECTIN.value == "selectin"
        assert JoinStrategy.SUBQUERY.value == "subquery"
        assert JoinStrategy.JOINED.value == "joined"
    
    def test_join_type_enum(self):
        """Test JOIN type enumeration."""
        assert JoinType.INNER.value == "inner"
        assert JoinType.LEFT.value == "left"
        assert JoinType.RIGHT.value == "right"
        assert JoinType.OUTER.value == "outer"
    
    def test_join_pattern_creation(self):
        """Test JOIN pattern creation."""
        pattern = JoinPattern(
            table_name="users",
            join_type=JoinType.INNER,
            join_condition="users.id = orders.user_id",
            estimated_rows=1000,
            selectivity=0.5,
            cost_estimate=100.0
        )
        
        assert pattern.table_name == "users"
        assert pattern.join_type == JoinType.INNER
        assert pattern.estimated_rows == 1000
        assert pattern.selectivity == 0.5
    
    @patch('app.core.join_optimization.Session')
    def test_optimize_join_query(self, mock_session):
        """Test JOIN query optimization."""
        mock_query = Mock()
        mock_session_instance = Mock()
        
        # Test that optimization doesn't break the query
        result = optimize_join_query(mock_query, mock_session_instance)
        assert result is not None


class TestQueryCache:
    """Test query caching functionality."""
    
    def test_cache_initialization(self):
        """Test cache initialization."""
        cache = QueryCache(max_size=100, default_ttl=600)
        assert cache.max_size == 100
        assert cache.default_ttl == 600
        assert len(cache._cache) == 0
    
    def test_cache_set_get(self):
        """Test cache set and get operations."""
        cache = QueryCache()
        cache.set("test_key", "test_value", ttl=300)
        
        result = cache.get("test_key")
        assert result == "test_value"
    
    def test_cache_expiration(self):
        """Test cache entry expiration."""
        cache = QueryCache()
        cache.set("expire_key", "expire_value", ttl=1)
        
        # Should be available immediately
        assert cache.get("expire_key") == "expire_value"
        
        # Wait for expiration
        time.sleep(1.1)
        assert cache.get("expire_key") is None
    
    def test_cache_invalidation(self):
        """Test cache invalidation."""
        cache = QueryCache()
        cache.set("key1", "value1")
        cache.set("key2", "value2")
        cache.set("test_key", "test_value")
        
        # Invalidate keys containing "key"
        invalidated = cache.invalidate("key")
        assert invalidated == 3
        
        # All keys should be gone
        assert cache.get("key1") is None
        assert cache.get("key2") is None
        assert cache.get("test_key") is None
    
    def test_cache_stats(self):
        """Test cache statistics."""
        cache = QueryCache()
        
        # Generate some cache activity
        cache.set("key1", "value1")
        cache.get("key1")  # Hit
        cache.get("nonexistent")  # Miss
        
        stats = cache.get_stats()
        assert stats.hits >= 1
        assert stats.misses >= 1
        assert stats.total_requests >= 2
    
    def test_cached_query_decorator(self):
        """Test cached query decorator."""
        cache = QueryCache()
        
        @cache.cached_query(ttl=300)
        def expensive_operation(x, y):
            time.sleep(0.1)  # Simulate expensive operation
            return x + y
        
        # First call should execute the function
        start_time = time.time()
        result1 = expensive_operation(1, 2)
        first_call_time = time.time() - start_time
        
        # Second call should be cached (much faster)
        start_time = time.time()
        result2 = expensive_operation(1, 2)
        second_call_time = time.time() - start_time
        
        assert result1 == result2 == 3
        assert second_call_time < first_call_time


class TestDatabaseConfig:
    """Test database configuration functionality."""
    
    def test_config_initialization(self):
        """Test configuration initialization."""
        config = DatabaseConfig()
        assert config.engine is not None
        assert config.connection_pool is not None
        assert config.query_optimization is not None
        assert config.caching is not None
        assert config.monitoring is not None
    
    def test_connection_pool_config(self):
        """Test connection pool configuration."""
        pool_config = ConnectionPoolConfig(
            pool_size=20,
            max_overflow=30,
            pool_timeout=60
        )
        
        assert pool_config.pool_size == 20
        assert pool_config.max_overflow == 30
        assert pool_config.pool_timeout == 60
    
    def test_sqlalchemy_config_generation(self):
        """Test SQLAlchemy configuration generation."""
        config = DatabaseConfig()
        sqlalchemy_config = config.get_sqlalchemy_config()
        
        assert "url" in sqlalchemy_config
        assert "pool_size" in sqlalchemy_config
        assert "max_overflow" in sqlalchemy_config
        assert "pool_timeout" in sqlalchemy_config
    
    def test_database_engine_detection(self):
        """Test database engine detection."""
        config = DatabaseConfig()
        config.url = "postgresql://user:pass@localhost/db"
        assert config.is_postgresql()
        assert not config.is_sqlite()
        
        config.url = "sqlite:///test.db"
        assert config.is_sqlite()
        assert not config.is_postgresql()


class TestOptimizationService:
    """Test the unified optimization service."""
    
    def test_service_initialization(self):
        """Test optimization service initialization."""
        service = DatabaseOptimizationService()
        assert service.config is not None
    
    @patch('app.services.db_optimization_service.Session')
    def test_optimized_query_creation(self, mock_session):
        """Test optimized query wrapper creation."""
        service = DatabaseOptimizationService()
        mock_query = Mock()
        mock_session_instance = Mock()
        
        optimized = service.optimized_query(mock_query, mock_session_instance)
        assert optimized is not None
        assert hasattr(optimized, 'optimize')
        assert hasattr(optimized, 'cache')
        assert hasattr(optimized, 'paginate')
    
    def test_performance_stats(self):
        """Test performance statistics collection."""
        service = DatabaseOptimizationService()
        stats = service.get_performance_stats()
        
        assert "optimization_enabled" in stats
        assert "caching_enabled" in stats
        assert "monitoring_enabled" in stats
    
    def test_cache_clearing(self):
        """Test cache clearing functionality."""
        service = DatabaseOptimizationService()
        cleared = service.clear_caches()
        
        assert "query_cache" in cleared
        assert "query_stats" in cleared


class TestIntegration:
    """Integration tests for optimization modules."""
    
    def test_module_compatibility(self):
        """Test that all modules work together."""
        # Test that we can import and use all modules together
        tracker = QueryPerformanceTracker()
        cache = QueryCache()
        config = DatabaseConfig()
        service = DatabaseOptimizationService()
        
        # Basic functionality test
        tracker.record_query("integration_test", 0.5, 10)
        cache.set("integration_key", "integration_value")
        
        assert tracker.get_statistics()["total_queries"] >= 1
        assert cache.get("integration_key") == "integration_value"
        assert service.get_performance_stats() is not None
    
    def test_fallback_compatibility(self):
        """Test fallback import patterns."""
        # This test ensures that the new modules can be imported
        # and used as drop-in replacements for old functionality
        try:
            from app.core.query_optimization import optimize_query
            from app.core.join_optimization import optimize_joins
            from app.core.query_cache import cached_query
            
            # These should be callable
            assert callable(optimize_query)
            assert callable(optimize_joins)
            assert callable(cached_query)
            
        except ImportError:
            pytest.skip("Optimization modules not available for fallback test")


if __name__ == "__main__":
    # Run basic tests if executed directly
    print("Running basic optimization module tests...")
    
    if OPTIMIZATION_MODULES_AVAILABLE:
        # Test basic functionality
        tracker = QueryPerformanceTracker()
        tracker.record_query("test", 1.0, 100)
        print(f"✅ Query tracker: {tracker.get_statistics()}")
        
        cache = QueryCache()
        cache.set("test", "value")
        print(f"✅ Cache: {cache.get('test')}")
        
        config = DatabaseConfig()
        print(f"✅ Config: {config.url}")
        
        service = DatabaseOptimizationService()
        print(f"✅ Service: {service.get_performance_stats()}")
        
        print("✅ All optimization modules working correctly!")
    else:
        print("❌ Optimization modules not available")
