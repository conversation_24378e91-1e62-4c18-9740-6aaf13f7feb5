"""
Test module for JOIN optimization.

This module tests the JOIN optimization functionality in the RentUp backend.
"""

import pytest
import time
from sqlalchemy import create_engine, Column, String, Integer, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Query

from app.core.join_optimization import (
    analyze_join_patterns,
    optimize_join_query,
    get_join_loading_strategy,
    optimize_relationship_loading,
    create_join_indexes,
    analyze_join_performance,
    JoinStrategy
)

# Create test database
Base = declarative_base()

class TestUser(Base):
    __tablename__ = "test_users"
    
    id = Column(String, primary_key=True)
    name = Column(String)
    
    items = relationship("TestItem", back_populates="owner")

class TestItem(Base):
    __tablename__ = "test_items"
    
    id = Column(String, primary_key=True)
    name = Column(String)
    owner_id = Column(String, ForeignKey("test_users.id"))
    
    owner = relationship("TestUser", back_populates="items")
    bookings = relationship("TestBooking", back_populates="item")

class TestBooking(Base):
    __tablename__ = "test_bookings"
    
    id = Column(String, primary_key=True)
    item_id = Column(String, ForeignKey("test_items.id"))
    user_id = Column(String, ForeignKey("test_users.id"))
    
    item = relationship("TestItem", back_populates="bookings")
    user = relationship("TestUser")

@pytest.fixture
def db_session():
    """Create a test database session."""
    # Create in-memory SQLite database
    engine = create_engine("sqlite:///:memory:")
    
    # Create tables
    Base.metadata.create_all(engine)
    
    # Create session
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # Add test data
    users = []
    for i in range(10):
        user = TestUser(id=f"user{i}", name=f"User {i}")
        users.append(user)
        session.add(user)
    
    items = []
    for i in range(20):
        item = TestItem(id=f"item{i}", name=f"Item {i}", owner_id=users[i % 10].id)
        items.append(item)
        session.add(item)
    
    for i in range(30):
        booking = TestBooking(
            id=f"booking{i}",
            item_id=items[i % 20].id,
            user_id=users[i % 10].id
        )
        session.add(booking)
    
    session.commit()
    
    yield session
    
    # Clean up
    session.close()

def test_analyze_join_patterns(db_session):
    """Test analyzing JOIN patterns."""
    # Create a query with JOINs
    query = db_session.query(TestBooking).join(TestItem).join(TestUser)
    
    # Analyze patterns
    patterns = analyze_join_patterns(db_session, query)
    
    # Check results
    assert len(patterns) > 0
    assert patterns[0].left_table == "test_bookings"
    assert patterns[0].right_table == "test_items"
    assert "test_bookings.item_id" in patterns[0].join_condition

def test_optimize_join_query(db_session):
    """Test optimizing a JOIN query."""
    # Create a query with JOINs
    query = db_session.query(TestBooking).join(TestItem).join(TestUser)
    
    # Optimize query
    optimized_query = optimize_join_query(query, db_session)
    
    # Check that optimization doesn't break the query
    results = list(optimized_query)
    assert len(results) > 0

def test_optimize_relationship_loading(db_session):
    """Test optimizing relationship loading."""
    # Create a query
    query = db_session.query(TestUser)
    
    # Optimize relationship loading
    optimized_query = optimize_relationship_loading(
        query, TestUser, ["items"], JoinStrategy.SELECTIN
    )
    
    # Check that optimization works
    results = optimized_query.all()
    assert len(results) > 0
    
    # Check that items are loaded
    assert len(results[0].items) >= 0  # Should not trigger a new query

def test_create_join_indexes(db_session):
    """Test creating indexes for JOIN columns."""
    # Create a query with JOINs
    query = db_session.query(TestBooking).join(TestItem).join(TestUser)
    
    # Analyze patterns
    patterns = analyze_join_patterns(db_session, query)
    
    # Create indexes
    created_indexes = create_join_indexes(db_session, patterns)
    
    # Check results (SQLite in-memory doesn't support CREATE INDEX, so this will be empty)
    assert isinstance(created_indexes, list)

def test_analyze_join_performance(db_session):
    """Test analyzing JOIN performance."""
    # Create a query with JOINs
    query = db_session.query(TestBooking).join(TestItem).join(TestUser)
    
    # Analyze performance
    stats = analyze_join_performance(db_session, query)
    
    # Check results
    assert "original_time" in stats
    assert "optimized_time" in stats
    assert "improvement_percent" in stats
    assert "pattern_count" in stats
