# RentUp Kubernetes Deployment

This directory contains Kubernetes manifests for deploying the RentUp application in a production environment.

## Prerequisites

- Kubernetes cluster (v1.25+)
- kubectl CLI tool
- Docker registry for storing container images
- Helm (for optional components)

## Directory Structure

```
kubernetes/
├── backend/             # Backend service manifests
│   ├── configmap.yaml   # Configuration for the backend
│   ├── deployment.yaml  # Backend deployment
│   ├── hpa.yaml         # Horizontal Pod Autoscaler
│   └── service.yaml     # Backend service
├── frontend/            # Frontend service manifests
├── database/            # Database manifests
├── redis/               # Redis manifests
├── qdrant/              # Qdrant vector database manifests
├── monitoring/          # Prometheus and Grafana manifests
├── namespace.yaml       # RentUp namespace
└── secrets-template.yaml # Template for secrets (do not commit actual secrets)
```

## Deployment Steps

### 1. Create Namespace

```bash
kubectl apply -f namespace.yaml
```

### 2. Create Secrets

Copy the secrets template and fill in the actual values:

```bash
cp secrets-template.yaml secrets.yaml
# Edit secrets.yaml with your actual secret values
kubectl apply -f secrets.yaml
```

### 3. Deploy Backend

```bash
kubectl apply -f backend/
```

### 4. Deploy Frontend

```bash
kubectl apply -f frontend/
```

### 5. Deploy Database

```bash
kubectl apply -f database/
```

### 6. Deploy Redis

```bash
kubectl apply -f redis/
```

### 7. Deploy Qdrant

```bash
kubectl apply -f qdrant/
```

### 8. Deploy Monitoring

```bash
kubectl apply -f monitoring/
```

## Environment Variables

The following environment variables need to be set before deploying:

- `DOCKER_REGISTRY`: The Docker registry where your images are stored
- `IMAGE_TAG`: The tag of the images to deploy

Example:

```bash
export DOCKER_REGISTRY=your-registry.com
export IMAGE_TAG=v1.0.0
```

## Scaling

The backend deployment includes a Horizontal Pod Autoscaler (HPA) that will automatically scale the number of pods based on CPU and memory usage.

## Monitoring

The deployment includes Prometheus and Grafana for monitoring. You can access Grafana at:

```
http://<cluster-ip>/grafana
```

Default credentials:
- Username: admin
- Password: See the `grafana-admin-password` in the secrets

## Troubleshooting

### Check Pod Status

```bash
kubectl get pods -n rentup
```

### View Pod Logs

```bash
kubectl logs -f <pod-name> -n rentup
```

### Check Service Status

```bash
kubectl get svc -n rentup
```

### Check Deployment Status

```bash
kubectl get deployments -n rentup
```

### Check HPA Status

```bash
kubectl get hpa -n rentup
```

## Maintenance

### Updating Deployments

To update a deployment with a new image:

```bash
export IMAGE_TAG=v1.0.1
kubectl set image deployment/rentup-backend backend=${DOCKER_REGISTRY}/rentup-backend:${IMAGE_TAG} -n rentup
```

### Rolling Back

If an update causes issues, you can roll back to the previous version:

```bash
kubectl rollout undo deployment/rentup-backend -n rentup
```

### Scaling Manually

You can manually scale the number of pods:

```bash
kubectl scale deployment/rentup-backend --replicas=6 -n rentup
```
