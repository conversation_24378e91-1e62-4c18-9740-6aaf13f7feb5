apiVersion: v1
kind: Secret
metadata:
  name: rentup-secrets
  namespace: rentup
  labels:
    app: rentup
type: Opaque
stringData:
  # Database
  database-url: "*******************************************/rentup"
  
  # Redis
  redis-url: "redis://redis-host:6379/0"
  
  # Qdrant
  qdrant-url: "http://qdrant-host:6333"
  
  # Security
  secret-key: "your-secret-key-here"
  
  # API Keys
  anthropic-api-key: "your-anthropic-api-key-here"
  openai-api-key: "your-openai-api-key-here"
  cohere-api-key: "your-cohere-api-key-here"
  
  # Monitoring
  sentry-dsn: "your-sentry-dsn-here"
  
  # SMTP
  smtp-host: "smtp.example.com"
  smtp-port: "587"
  smtp-username: "your-smtp-username"
  smtp-password: "your-smtp-password"
  smtp-from-email: "<EMAIL>"
  
  # Storage
  aws-access-key-id: "your-aws-access-key-id"
  aws-secret-access-key: "your-aws-secret-access-key"
  aws-region: "us-west-2"
  aws-s3-bucket: "rentup-uploads"
