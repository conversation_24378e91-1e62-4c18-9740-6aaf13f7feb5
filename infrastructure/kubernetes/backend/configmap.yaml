apiVersion: v1
kind: ConfigMap
metadata:
  name: rentup-backend-config
  namespace: rentup
  labels:
    app: rentup
    component: backend
data:
  logging.yaml: |
    version: 1
    disable_existing_loggers: false
    formatters:
      standard:
        format: '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
      json:
        format: '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "name": "%(name)s", "message": "%(message)s"}'
        class: pythonjsonlogger.jsonlogger.JsonFormatter
    handlers:
      console:
        class: logging.StreamHandler
        level: INFO
        formatter: json
        stream: ext://sys.stdout
      file:
        class: logging.handlers.RotatingFileHandler
        level: INFO
        formatter: json
        filename: /tmp/rentup-backend.log
        maxBytes: 10485760  # 10MB
        backupCount: 5
    loggers:
      uvicorn:
        level: INFO
        handlers: [console, file]
        propagate: no
      app:
        level: INFO
        handlers: [console, file]
        propagate: no
    root:
      level: INFO
      handlers: [console, file]
      propagate: no
  
  cors.yaml: |
    allow_origins:
      - https://rentup.com
      - https://www.rentup.com
      - https://app.rentup.com
    allow_methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
      - PATCH
    allow_headers:
      - Content-Type
      - Authorization
      - X-Requested-With
      - Accept
      - Origin
      - Access-Control-Request-Method
      - Access-Control-Request-Headers
    allow_credentials: true
    max_age: 600
  
  rate_limiting.yaml: |
    enabled: true
    rate_limit: 100
    time_window: 60
    by_ip: true
    by_user: true
    excluded_paths:
      - /api/v1/health
      - /api/v1/metrics
      - /docs
      - /redoc
      - /openapi.json
    
  caching.yaml: |
    enabled: true
    ttl: 300
    max_size: 1000
    excluded_paths:
      - /api/v1/health
      - /api/v1/metrics
      - /api/v1/users
      - /api/v1/auth
