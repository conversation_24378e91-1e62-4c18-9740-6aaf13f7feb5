apiVersion: apps/v1
kind: Deployment
metadata:
  name: rentup-backend
  namespace: rentup
  labels:
    app: rentup
    component: backend
spec:
  replicas: 4
  selector:
    matchLabels:
      app: rentup
      component: backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: rentup
        component: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
        prometheus.io/path: "/metrics"
    spec:
      containers:
      - name: backend
        image: ${DOCKER_REGISTRY}/rentup-backend:${IMAGE_TAG}
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: rentup-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: rentup-secrets
              key: redis-url
        - name: QDRANT_URL
          valueFrom:
            secretKeyRef:
              name: rentup-secrets
              key: qdrant-url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: rentup-secrets
              key: secret-key
        - name: ANTHROPIC_API_KEY
          valueFrom:
            secretKeyRef:
              name: rentup-secrets
              key: anthropic-api-key
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: rentup-secrets
              key: openai-api-key
        - name: COHERE_API_KEY
          valueFrom:
            secretKeyRef:
              name: rentup-secrets
              key: cohere-api-key
        - name: SENTRY_DSN
          valueFrom:
            secretKeyRef:
              name: rentup-secrets
              key: sentry-dsn
        - name: LOG_LEVEL
          value: "INFO"
        resources:
          requests:
            cpu: "500m"
            memory: "512Mi"
          limits:
            cpu: "2"
            memory: "2Gi"
        livenessProbe:
          httpGet:
            path: /api/v1/health/liveness
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/v1/health/readiness
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 10
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/v1/health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: tmp-volume
          mountPath: /tmp
      volumes:
      - name: config-volume
        configMap:
          name: rentup-backend-config
      - name: tmp-volume
        emptyDir: {}
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - rentup
                - key: component
                  operator: In
                  values:
                  - backend
              topologyKey: "kubernetes.io/hostname"
      terminationGracePeriodSeconds: 60
