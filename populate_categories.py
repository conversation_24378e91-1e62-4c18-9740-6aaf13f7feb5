"""
<PERSON><PERSON>t to populate the categories table with data from the frontend configuration.
This script should be run after the database is initialized.

Usage:
    python -m app.scripts.populate_categories
"""

import sys
import os
import json
from sqlalchemy.orm import Session

# Add the parent directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.core.database import SessionLocal, engine
from app.models.category import Category, Base

# Frontend categories data (copied from frontend/src/config/categories.ts)
MAIN_CATEGORIES = [
    {
        "id": "medical-equipment",
        "name": "Medical & Mobility Equipment",
        "description": "Medical devices, mobility aids, and healthcare equipment",
        "icon": "medical",
        "subCategories": [
            {"id": "mobility-aids", "name": "Mobility Aids", "description": "Wheelchairs, walkers, canes, and mobility assistance devices"},
            {"id": "medical-devices", "name": "Medical Devices", "description": "Medical monitoring equipment, therapy devices, and healthcare tools"},
            {"id": "home-care", "name": "Home Care Equipment", "description": "Hospital beds, lift chairs, and home healthcare equipment"},
            {"id": "accessibility", "name": "Accessibility Equipment", "description": "Ramps, lifts, and accessibility modifications"},
            {"id": "rehabilitation", "name": "Rehabilitation Equipment", "description": "Physical therapy equipment and rehabilitation tools"},
            {"id": "daily-living", "name": "Daily Living Aids", "description": "Bathroom aids, dressing aids, and daily living assistance devices"},
            {"id": "sensory", "name": "Sensory & Communication Aids", "description": "Hearing aids, visual aids, and communication devices"},
            {"id": "specialty-medical", "name": "Specialty Medical Equipment", "description": "Specialized medical equipment for specific conditions"}
        ]
    },
    # Add more categories here...
]

PROHIBITED_CATEGORIES = [
    {
        "id": "illegal-items",
        "name": "Illegal Items & Services",
        "description": "Items and services that are illegal or violate laws",
        "isProhibited": True,
        "subCategories": [
            {"id": "weapons", "name": "Weapons & Ammunition", "description": "Firearms, ammunition, explosives, and weapons", "isProhibited": True},
            {"id": "controlled-substances", "name": "Controlled Substances", "description": "Illegal drugs, narcotics, and controlled substances", "isProhibited": True},
            {"id": "counterfeit", "name": "Counterfeit Goods", "description": "Fake products, replicas, and counterfeit items", "isProhibited": True},
            {"id": "stolen-property", "name": "Stolen Property", "description": "Items that are stolen or obtained illegally", "isProhibited": True}
        ]
    },
    # Add more prohibited categories here...
]

def create_category(db: Session, category_data: dict, parent_id: str = None, is_prohibited: bool = False):
    """Create a category and its subcategories recursively."""
    # Extract category data
    category_id = category_data["id"]
    name = category_data["name"]
    description = category_data.get("description", "")
    icon = category_data.get("icon", "")
    is_prohibited = category_data.get("isProhibited", is_prohibited)

    # Check if category already exists
    existing_category = db.query(Category).filter(Category.id == category_id).first()
    if existing_category:
        print(f"Category {category_id} already exists, skipping...")
        return existing_category

    # Create the category
    category = Category(
        id=category_id,
        name=name,
        description=description,
        icon=icon,
        parent_id=parent_id,
        is_prohibited=is_prohibited
    )
    db.add(category)
    db.commit()
    db.refresh(category)

    print(f"Created category: {category.name} (ID: {category.id})")

    # Create subcategories if any
    subcategories = category_data.get("subCategories", [])
    for subcategory_data in subcategories:
        create_category(db, subcategory_data, category.id, is_prohibited)

    return category

def populate_categories():
    """Populate the categories table with data from the frontend configuration."""
    db = SessionLocal()
    try:
        # Create main categories
        for category_data in MAIN_CATEGORIES:
            create_category(db, category_data)

        # Create prohibited categories
        for category_data in PROHIBITED_CATEGORIES:
            create_category(db, category_data, is_prohibited=True)

        print("Categories populated successfully!")
    finally:
        db.close()

if __name__ == "__main__":
    # Create tables if they don't exist
    Base.metadata.create_all(bind=engine)

    # Populate categories
    populate_categories()
