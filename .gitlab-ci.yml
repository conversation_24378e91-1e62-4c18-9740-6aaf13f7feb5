stages:
  - lint
  - test
  - security
  - build
  - deploy

variables:
  POSTGRES_USER: rentup
  POSTGRES_PASSWORD: rentup_secure_password
  POSTGRES_DB: rentup_test
  POSTGRES_HOST: postgres
  POSTGRES_PORT: 5432
  SECRET_KEY: test_secret_key
  ENVIRONMENT: test
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_IMAGE: $CI_REGISTRY_IMAGE/backend:$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHORT_SHA

# Cache pip dependencies between jobs
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - .pip-cache/

# Lint stage
lint:
  stage: lint
  image: python:3.10-slim
  script:
    - pip install --cache-dir=.pip-cache flake8 black isort mypy
    - pip install --cache-dir=.pip-cache -r backend/requirements.txt
    - flake8 backend --count --select=E9,F63,F7,F82 --show-source --statistics
    - flake8 backend --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    - black --check backend
    - isort --check-only --profile black backend
    - mypy backend
  rules:
    - changes:
        - backend/**/*
        - .gitlab-ci.yml

# Test stage
test:
  stage: test
  image: python:3.10-slim
  services:
    - name: postgres:14
      alias: postgres
  script:
    - apt-get update && apt-get install -y --no-install-recommends gcc libpq-dev
    - pip install --cache-dir=.pip-cache pytest pytest-cov
    - pip install --cache-dir=.pip-cache -r backend/requirements.txt
    - cd backend
    - pytest --cov=app --cov-report=xml
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: backend/coverage.xml
  rules:
    - changes:
        - backend/**/*
        - .gitlab-ci.yml

# Security scan stage
security:
  stage: security
  image: python:3.10-slim
  script:
    - pip install --cache-dir=.pip-cache bandit safety
    - pip install --cache-dir=.pip-cache -r backend/requirements.txt
    - bandit -r backend/app -f json -o bandit-results.json || true
    - safety check -r backend/requirements.txt --json > safety-results.json || true
    - cd backend
    - python scripts/security_audit.py --all --output security_audit_report.json || true
  artifacts:
    paths:
      - bandit-results.json
      - safety-results.json
      - backend/security_audit_report.json
    expire_in: 1 week
  rules:
    - changes:
        - backend/**/*
        - .gitlab-ci.yml

# Build Docker image
build:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $DOCKER_IMAGE -f backend/Dockerfile.prod ./backend
    - docker push $DOCKER_IMAGE
  rules:
    - if: $CI_COMMIT_BRANCH == "main" || $CI_COMMIT_BRANCH == "develop"
      changes:
        - backend/**/*
        - .gitlab-ci.yml

# Deploy to staging
deploy_staging:
  stage: deploy
  image: 
    name: bitnami/kubectl:latest
    entrypoint: [""]
  before_script:
    - echo "$KUBE_CONFIG" > kubeconfig
    - export KUBECONFIG=kubeconfig
  script:
    # Update image tag in deployment
    - sed -i "s|image: .*|image: $DOCKER_IMAGE|g" kubernetes/rentup-deployment.yaml
    
    # Apply Kubernetes manifests
    - kubectl apply -f kubernetes/rentup-deployment.yaml
    
    # Wait for deployment to complete
    - kubectl rollout status deployment/rentup-backend -n rentup --timeout=300s
    
    # Run database migrations
    - |
      cat <<EOF | kubectl apply -f -
      apiVersion: batch/v1
      kind: Job
      metadata:
        name: rentup-migrations-$CI_COMMIT_SHORT_SHA
        namespace: rentup
      spec:
        ttlSecondsAfterFinished: 100
        template:
          spec:
            containers:
            - name: migrations
              image: $DOCKER_IMAGE
              command: ["alembic", "upgrade", "head"]
              env:
              - name: POSTGRES_SERVER
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_host
              - name: POSTGRES_USER
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_user
              - name: POSTGRES_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_password
              - name: POSTGRES_DB
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_name
            restartPolicy: Never
        backoffLimit: 4
      EOF
    
    # Wait for migration job to complete
    - kubectl wait --for=condition=complete job/rentup-migrations-$CI_COMMIT_SHORT_SHA -n rentup --timeout=300s
    
    # Run security tests
    - |
      cat <<EOF | kubectl apply -f -
      apiVersion: batch/v1
      kind: Job
      metadata:
        name: rentup-security-tests-$CI_COMMIT_SHORT_SHA
        namespace: rentup
      spec:
        ttlSecondsAfterFinished: 100
        template:
          spec:
            containers:
            - name: security-tests
              image: $DOCKER_IMAGE
              command: ["python", "scripts/test_security.py", "--all", "--api-url", "http://rentup-backend.rentup.svc.cluster.local"]
              env:
              - name: POSTGRES_SERVER
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_host
              - name: POSTGRES_USER
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_user
              - name: POSTGRES_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_password
              - name: POSTGRES_DB
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_name
            restartPolicy: Never
        backoffLimit: 4
      EOF
    
    # Wait for security tests job to complete
    - kubectl wait --for=condition=complete job/rentup-security-tests-$CI_COMMIT_SHORT_SHA -n rentup --timeout=300s
    
    # Notify deployment
    - 'curl -X POST -H "Content-Type: application/json" -d "{\"text\":\"RentUp backend deployed to staging: $CI_COMMIT_SHORT_SHA\"}" $SLACK_WEBHOOK_URL'
  environment:
    name: staging
    url: https://staging-api.rentup.example.com
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
      changes:
        - backend/**/*
        - .gitlab-ci.yml

# Deploy to production
deploy_production:
  stage: deploy
  image: 
    name: bitnami/kubectl:latest
    entrypoint: [""]
  before_script:
    - echo "$KUBE_CONFIG" > kubeconfig
    - export KUBECONFIG=kubeconfig
  script:
    # Update image tag in deployment
    - sed -i "s|image: .*|image: $DOCKER_IMAGE|g" kubernetes/rentup-deployment.yaml
    
    # Apply Kubernetes manifests
    - kubectl apply -f kubernetes/rentup-deployment.yaml
    
    # Wait for deployment to complete
    - kubectl rollout status deployment/rentup-backend -n rentup --timeout=300s
    
    # Run database migrations
    - |
      cat <<EOF | kubectl apply -f -
      apiVersion: batch/v1
      kind: Job
      metadata:
        name: rentup-migrations-$CI_COMMIT_SHORT_SHA
        namespace: rentup
      spec:
        ttlSecondsAfterFinished: 100
        template:
          spec:
            containers:
            - name: migrations
              image: $DOCKER_IMAGE
              command: ["alembic", "upgrade", "head"]
              env:
              - name: POSTGRES_SERVER
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_host
              - name: POSTGRES_USER
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_user
              - name: POSTGRES_PASSWORD
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_password
              - name: POSTGRES_DB
                valueFrom:
                  secretKeyRef:
                    name: rentup-db-secrets
                    key: db_name
            restartPolicy: Never
        backoffLimit: 4
      EOF
    
    # Wait for migration job to complete
    - kubectl wait --for=condition=complete job/rentup-migrations-$CI_COMMIT_SHORT_SHA -n rentup --timeout=300s
    
    # Notify deployment
    - 'curl -X POST -H "Content-Type: application/json" -d "{\"text\":\"RentUp backend deployed to production: $CI_COMMIT_SHORT_SHA\"}" $SLACK_WEBHOOK_URL'
  environment:
    name: production
    url: https://api.rentup.example.com
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      changes:
        - backend/**/*
        - .gitlab-ci.yml
  when: manual
