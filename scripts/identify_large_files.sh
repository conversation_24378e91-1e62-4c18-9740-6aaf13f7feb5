#!/bin/bash

# Script to identify large JavaScript/TypeScript files that need refactoring
# according to the May 2025 code optimization guidelines
#
# Usage: ./identify_large_files.sh [directory] [threshold]
#   directory: The directory to search (default: frontend/src)
#   threshold: The line count threshold (default: 600)
#
# Example: ./identify_large_files.sh frontend/src 500

# Set default values
DIRECTORY=${1:-frontend/src}
THRESHOLD=${2:-600}

# Print header
echo "Identifying JavaScript/TypeScript files with more than $THRESHOLD lines in $DIRECTORY"
echo "According to May 2025 code optimization guidelines:"
echo "- Target: 300-500 lines per file"
echo "- Maximum threshold: 600 lines (files exceeding this must be refactored)"
echo "- Absolute limit: 800 lines (files exceeding this must be refactored immediately)"
echo ""
echo "Results:"
echo "--------"

# Find all JavaScript/TypeScript files and count their lines
find "$DIRECTORY" -type f \( -name "*.js" -o -name "*.jsx" -o -name "*.ts" -o -name "*.tsx" \) -not -path "*/node_modules/*" | while read -r file; do
  # Count lines in the file
  line_count=$(wc -l < "$file")
  
  # Check if the file exceeds the threshold
  if [ "$line_count" -gt "$THRESHOLD" ]; then
    # Determine the priority based on line count
    if [ "$line_count" -gt 800 ]; then
      priority="HIGH (exceeds absolute limit)"
    elif [ "$line_count" -gt 600 ]; then
      priority="MEDIUM (exceeds maximum threshold)"
    else
      priority="LOW (exceeds target range)"
    fi
    
    # Print the file path, line count, and priority
    printf "%-80s %5d lines   Priority: %s\n" "$file" "$line_count" "$priority"
  fi
done | sort -k2 -nr  # Sort by line count in descending order

echo ""
echo "Refactoring Recommendation:"
echo "-------------------------"
echo "1. Start with HIGH priority files (>800 lines)"
echo "2. Then address MEDIUM priority files (601-800 lines)"
echo "3. Finally, consider refactoring LOW priority files (501-600 lines)"
echo ""
echo "Use the following directory structure pattern for refactoring:"
echo ""
echo "frontend/src/path/to/ComponentName/"
echo "├── index.tsx                      # Main export file (150-200 lines)"
echo "├── components/                    # Extracted components"
echo "│   ├── SubComponentA.tsx          # Extracted component (100-200 lines)"
echo "│   ├── SubComponentB.tsx          # Extracted component (100-200 lines)"
echo "│   └── SubComponentC.tsx          # Extracted component (100-200 lines)"
echo "├── hooks/                         # Feature-specific hooks"
echo "│   ├── useFeatureSpecificHookA.ts # Custom hook (50-100 lines)"
echo "│   └── useFeatureSpecificHookB.ts # Custom hook (50-100 lines)"
echo "└── utils/                         # Feature-specific utilities"
echo "    ├── helperFunctions.ts         # Helper functions (50-100 lines)"
echo "    └── constants.ts               # Constants and types (50-100 lines)"
