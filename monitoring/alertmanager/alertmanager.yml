global:
  resolve_timeout: 5m
  slack_api_url: '*****************************************************************************'
  smtp_smarthost: 'smtp.example.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'
  smtp_require_tls: true

templates:
  - '/etc/alertmanager/template/*.tmpl'

route:
  group_by: ['alertname', 'job', 'severity']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 4h
  receiver: 'slack-notifications'
  routes:
    - match:
        severity: critical
      receiver: 'pagerduty-critical'
      continue: true
    - match:
        severity: warning
      receiver: 'slack-notifications'
      continue: true
    - match_re:
        service: ^(rentup-backend|postgres)$
      receiver: 'team-backend'
    - match_re:
        service: ^(rentup-frontend)$
      receiver: 'team-frontend'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']

receivers:
  - name: 'slack-notifications'
    slack_configs:
      - channel: '#alerts'
        send_resolved: true
        title: '{{ template "slack.default.title" . }}'
        text: '{{ template "slack.default.text" . }}'
        title_link: '{{ template "slack.default.titlelink" . }}'
        footer: '{{ template "slack.default.footer" . }}'
        actions:
          - type: button
            text: 'Runbook'
            url: '{{ (index .Alerts 0).Annotations.runbook }}'
          - type: button
            text: 'Dashboard'
            url: '{{ (index .Alerts 0).Annotations.dashboard }}'
          - type: button
            text: 'Silence'
            url: '{{ template "slack.default.silencelink" . }}'

  - name: 'team-backend'
    slack_configs:
      - channel: '#team-backend'
        send_resolved: true
        title: '{{ template "slack.default.title" . }}'
        text: '{{ template "slack.default.text" . }}'
        title_link: '{{ template "slack.default.titlelink" . }}'
        footer: '{{ template "slack.default.footer" . }}'
        actions:
          - type: button
            text: 'Runbook'
            url: '{{ (index .Alerts 0).Annotations.runbook }}'
          - type: button
            text: 'Dashboard'
            url: '{{ (index .Alerts 0).Annotations.dashboard }}'
          - type: button
            text: 'Silence'
            url: '{{ template "slack.default.silencelink" . }}'

  - name: 'team-frontend'
    slack_configs:
      - channel: '#team-frontend'
        send_resolved: true
        title: '{{ template "slack.default.title" . }}'
        text: '{{ template "slack.default.text" . }}'
        title_link: '{{ template "slack.default.titlelink" . }}'
        footer: '{{ template "slack.default.footer" . }}'
        actions:
          - type: button
            text: 'Runbook'
            url: '{{ (index .Alerts 0).Annotations.runbook }}'
          - type: button
            text: 'Dashboard'
            url: '{{ (index .Alerts 0).Annotations.dashboard }}'
          - type: button
            text: 'Silence'
            url: '{{ template "slack.default.silencelink" . }}'

  - name: 'pagerduty-critical'
    pagerduty_configs:
      - service_key: 'your_pagerduty_service_key'
        send_resolved: true
        description: '{{ template "pagerduty.default.description" . }}'
        client: 'Alertmanager'
        client_url: '{{ template "pagerduty.default.clientURL" . }}'
        details:
          firing: '{{ template "pagerduty.default.instances" .Alerts.Firing }}'
          resolved: '{{ template "pagerduty.default.instances" .Alerts.Resolved }}'
          num_firing: '{{ .Alerts.Firing | len }}'
