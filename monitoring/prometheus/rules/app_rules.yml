groups:
  - name: rentup_backend_alerts
    rules:
      - alert: HighRequestLatency
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job="rentup-backend"}[5m])) by (le, route)) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High request latency on {{ $labels.route }}"
          description: "95th percentile of request latency on {{ $labels.route }} is above 1s (current value: {{ $value }}s)"

      - alert: HighErrorRate
        expr: sum(rate(http_requests_total{job="rentup-backend", status=~"5.."}[5m])) by (route) / sum(rate(http_requests_total{job="rentup-backend"}[5m])) by (route) > 0.05
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate on {{ $labels.route }}"
          description: "Error rate on {{ $labels.route }} is above 5% (current value: {{ $value | humanizePercentage }})"

      - alert: HighCPUUsage
        expr: process_cpu_seconds_total{job="rentup-backend"} / on(instance) group_left() machine_cpu_cores > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on {{ $labels.instance }}"
          description: "CPU usage on {{ $labels.instance }} is above 80% (current value: {{ $value | humanizePercentage }})"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes{job="rentup-backend"} / on(instance) group_left() node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage on {{ $labels.instance }} is above 80% (current value: {{ $value | humanizePercentage }})"

      - alert: HighConnectionPoolUsage
        expr: rentup_db_pool_used{job="rentup-backend"} / rentup_db_pool_size{job="rentup-backend"} > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High connection pool usage"
          description: "Database connection pool usage is above 80% (current value: {{ $value | humanizePercentage }})"

      - alert: HighCacheHitRatio
        expr: rentup_cache_hits{job="rentup-backend"} / (rentup_cache_hits{job="rentup-backend"} + rentup_cache_misses{job="rentup-backend"}) < 0.5
        for: 15m
        labels:
          severity: warning
        annotations:
          summary: "Low cache hit ratio"
          description: "Cache hit ratio is below 50% (current value: {{ $value | humanizePercentage }})"

      - alert: HighReplicaLag
        expr: rentup_replica_lag_seconds{job="rentup-backend"} > 30
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High replica lag on {{ $labels.replica }}"
          description: "Replica lag on {{ $labels.replica }} is above 30s (current value: {{ $value }}s)"

      - alert: InstanceDown
        expr: up{job="rentup-backend"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Instance {{ $labels.instance }} down"
          description: "{{ $labels.instance }} of job {{ $labels.job }} has been down for more than 1 minute."

      - alert: EndpointDown
        expr: probe_success{job="blackbox"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Endpoint {{ $labels.instance }} down"
          description: "Endpoint {{ $labels.instance }} is down for more than 1 minute."

      - alert: SlowEndpoint
        expr: probe_duration_seconds{job="blackbox"} > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Slow endpoint {{ $labels.instance }}"
          description: "Endpoint {{ $labels.instance }} response time is above 1s (current value: {{ $value }}s)"
