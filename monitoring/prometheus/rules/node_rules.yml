groups:
  - name: rentup_node_alerts
    rules:
      - alert: HighCPULoad
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU load on {{ $labels.instance }}"
          description: "CPU load on {{ $labels.instance }} is above 80% (current value: {{ $value }}%)"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on {{ $labels.instance }}"
          description: "Memory usage on {{ $labels.instance }} is above 80% (current value: {{ $value }}%)"

      - alert: HighDiskUsage
        expr: (node_filesystem_size_bytes{fstype=~"ext4|xfs"} - node_filesystem_free_bytes{fstype=~"ext4|xfs"}) / node_filesystem_size_bytes{fstype=~"ext4|xfs"} * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High disk usage on {{ $labels.instance }} ({{ $labels.mountpoint }})"
          description: "Disk usage on {{ $labels.instance }} ({{ $labels.mountpoint }}) is above 80% (current value: {{ $value }}%)"

      - alert: HighInodeUsage
        expr: node_filesystem_files_free{fstype=~"ext4|xfs"} / node_filesystem_files{fstype=~"ext4|xfs"} * 100 < 20
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High inode usage on {{ $labels.instance }} ({{ $labels.mountpoint }})"
          description: "Inode usage on {{ $labels.instance }} ({{ $labels.mountpoint }}) is above 80% (current value: {{ $value }}%)"

      - alert: HighNetworkTraffic
        expr: sum by (instance) (rate(node_network_receive_bytes_total[5m]) + rate(node_network_transmit_bytes_total[5m])) / 1024 / 1024 > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High network traffic on {{ $labels.instance }}"
          description: "Network traffic on {{ $labels.instance }} is above 100 MB/s (current value: {{ $value }} MB/s)"

      - alert: HighOpenFileDescriptors
        expr: node_filefd_allocated / node_filefd_maximum * 100 > 70
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High open file descriptors on {{ $labels.instance }}"
          description: "Open file descriptors on {{ $labels.instance }} is above 70% (current value: {{ $value }}%)"

      - alert: SystemdServiceFailed
        expr: node_systemd_unit_state{state="failed"} == 1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Systemd service failed on {{ $labels.instance }}"
          description: "Systemd service {{ $labels.name }} failed on {{ $labels.instance }}"

      - alert: NodeDown
        expr: up{job="node-exporter"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Node {{ $labels.instance }} down"
          description: "Node {{ $labels.instance }} has been down for more than 1 minute."

      - alert: NodeHighLoad
        expr: node_load1 / on(instance) count(node_cpu_seconds_total{mode="idle"}) by (instance) > 1.5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Node high load on {{ $labels.instance }}"
          description: "Node {{ $labels.instance }} has a high load average (current value: {{ $value }})"

      - alert: NodeOutOfMemory
        expr: node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Node out of memory on {{ $labels.instance }}"
          description: "Node {{ $labels.instance }} has less than 10% memory available (current value: {{ $value }}%)"

      - alert: NodeDiskWillFillIn24Hours
        expr: predict_linear(node_filesystem_free_bytes{fstype=~"ext4|xfs"}[1h], 24 * 3600) < 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Node disk will fill in 24 hours on {{ $labels.instance }} ({{ $labels.mountpoint }})"
          description: "Node {{ $labels.instance }} disk {{ $labels.mountpoint }} will fill in 24 hours at current write rate"
