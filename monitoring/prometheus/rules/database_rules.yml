groups:
  - name: rentup_database_alerts
    rules:
      - alert: PostgresqlDown
        expr: pg_up{job="postgres-exporter"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL instance {{ $labels.instance }} down"
          description: "PostgreSQL instance {{ $labels.instance }} is down for more than 1 minute."

      - alert: PostgresqlHighCPUUsage
        expr: rate(process_cpu_seconds_total{job="postgres-exporter"}[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL high CPU usage on {{ $labels.instance }}"
          description: "PostgreSQL CPU usage on {{ $labels.instance }} is above 80% (current value: {{ $value | humanizePercentage }})"

      - alert: PostgresqlHighMemoryUsage
        expr: (sum(pg_memory_bytes{job="postgres-exporter"}) by (instance) / on(instance) group_left() node_memory_MemTotal_bytes) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL high memory usage on {{ $labels.instance }}"
          description: "PostgreSQL memory usage on {{ $labels.instance }} is above 80% (current value: {{ $value | humanizePercentage }})"

      - alert: PostgresqlHighConnectionCount
        expr: sum(pg_stat_activity_count{job="postgres-exporter"}) by (instance) > pg_settings_max_connections{job="postgres-exporter"} * 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL high connection count on {{ $labels.instance }}"
          description: "PostgreSQL connection count on {{ $labels.instance }} is above 80% of max connections (current value: {{ $value }})"

      - alert: PostgresqlReplicationLag
        expr: pg_stat_replication_lag_bytes{job="postgres-exporter"} / 1024 / 1024 > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL replication lag on {{ $labels.instance }}"
          description: "PostgreSQL replication lag on {{ $labels.instance }} is above 100MB (current value: {{ $value }}MB)"

      - alert: PostgresqlSlowQueries
        expr: rate(pg_stat_activity_max_tx_duration{job="postgres-exporter"}[5m]) > 30
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL slow queries on {{ $labels.instance }}"
          description: "PostgreSQL has queries running for more than 30s (current max: {{ $value }}s)"

      - alert: PostgresqlHighTransactionIDUtilization
        expr: (pg_stat_database_xid_age{job="postgres-exporter"} / pg_settings_max_transaction_id_utilization{job="postgres-exporter"}) > 0.7
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL high transaction ID utilization on {{ $labels.instance }}"
          description: "PostgreSQL transaction ID utilization on {{ $labels.instance }} is above 70% (current value: {{ $value | humanizePercentage }})"

      - alert: PostgresqlDeadlocks
        expr: increase(pg_stat_database_deadlocks{job="postgres-exporter"}[5m]) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL deadlocks on {{ $labels.instance }}"
          description: "PostgreSQL has deadlocks ({{ $value }} in the last 5 minutes)"

      - alert: PostgresqlHighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{mountpoint=~"/var/lib/postgresql.*",job="node-exporter"} / node_filesystem_size_bytes{mountpoint=~"/var/lib/postgresql.*",job="node-exporter"})) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL high disk usage on {{ $labels.instance }}"
          description: "PostgreSQL disk usage on {{ $labels.instance }} is above 80% (current value: {{ $value | humanizePercentage }})"

      - alert: PostgresqlLowDiskSpace
        expr: node_filesystem_avail_bytes{mountpoint=~"/var/lib/postgresql.*",job="node-exporter"} / 1024 / 1024 / 1024 < 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL low disk space on {{ $labels.instance }}"
          description: "PostgreSQL has less than 10GB of disk space available on {{ $labels.instance }} (current value: {{ $value }}GB)"

      - alert: PostgresqlTooManyConnections
        expr: sum(pg_stat_activity_count{job="postgres-exporter"}) by (instance) > pg_settings_max_connections{job="postgres-exporter"} * 0.9
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL too many connections on {{ $labels.instance }}"
          description: "PostgreSQL connections on {{ $labels.instance }} is above 90% of max connections (current value: {{ $value }})"
