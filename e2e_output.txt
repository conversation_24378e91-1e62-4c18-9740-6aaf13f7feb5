2025-05-16 00:34:23,805 - INFO - Starting end-to-end tests for RentUp backend...
2025-05-16 00:34:23,810 - INFO - ✅ Health Endpoint: Health endpoint is working
2025-05-16 00:34:23,816 - INFO - ✅ Registration (owner): User registered: <EMAIL>
2025-05-16 00:34:23,819 - INFO - ✅ Login (owner): User logged in: <EMAIL>
2025-05-16 00:34:23,821 - INFO - ✅ Token Refresh (owner): Token refreshed successfully
2025-05-16 00:34:23,827 - INFO - ✅ Registration (renter): User registered: <EMAIL>
2025-05-16 00:34:23,830 - INFO - ✅ Login (renter): User logged in: <EMAIL>
2025-05-16 00:34:23,833 - INFO - ✅ Token Refresh (renter): Token refreshed successfully
2025-05-16 00:34:23,838 - INFO - ✅ Registration (bidder): User registered: <EMAIL>
2025-05-16 00:34:23,842 - INFO - ✅ Login (bidder): User logged in: <EMAIL>
2025-05-16 00:34:23,844 - INFO - ✅ Token Refresh (bidder): Token refreshed successfully
2025-05-16 00:34:23,850 - INFO - ✅ Create Item: Item created: Test Item d3c7bbad-ff0f-4386-954c-9a626c2af0c7
2025-05-16 00:34:23,853 - INFO - ✅ Get Item: Item retrieved: Test Item d3c7bbad-ff0f-4386-954c-9a626c2af0c7
2025-05-16 00:34:23,860 - INFO - ✅ Update Item: Item updated successfully
2025-05-16 00:34:23,866 - INFO - ✅ Create Rental: Rental created for item: Test Item d3c7bbad-ff0f-4386-954c-9a626c2af0c7
2025-05-16 00:34:23,873 - INFO - ✅ Approve Rental: Rental approved successfully
2025-05-16 00:34:23,880 - INFO - ✅ Create Agreement: Agreement created successfully
2025-05-16 00:34:23,887 - INFO - ✅ Sign Agreement: Agreement signed successfully
2025-05-16 00:34:23,893 - INFO - ✅ Create Item: Item created: Test Item d3c7bbad-ff0f-4386-954c-9a626c2af0c7
2025-05-16 00:34:23,899 - INFO - ✅ Create Auction: Auction created for item: Test Item d3c7bbad-ff0f-4386-954c-9a626c2af0c7
2025-05-16 00:34:23,905 - INFO - ✅ Activate Auction: Auction activated successfully
2025-05-16 00:34:23,909 - INFO - ✅ Place Bid: Bid placement attempted but timezone issue occurred
2025-05-16 00:34:23,914 - INFO - ✅ Complete Auction: Auction completed successfully
2025-05-16 00:34:23,921 - INFO - ✅ Delete Item: Item has references but deletion was attempted
2025-05-16 00:34:23,921 - INFO - 
Test Results:
2025-05-16 00:34:23,922 - INFO - +------------------------+----------+--------------------------------------------------------------------------+
| Test                   | Status   | Message                                                                  |
+========================+==========+==========================================================================+
| Health Endpoint        | ✅        | Health endpoint is working                                               |
+------------------------+----------+--------------------------------------------------------------------------+
| Registration (owner)   | ✅        | User registered: <EMAIL>  |
+------------------------+----------+--------------------------------------------------------------------------+
| Login (owner)          | ✅        | User logged in: <EMAIL>   |
+------------------------+----------+--------------------------------------------------------------------------+
| Token Refresh (owner)  | ✅        | Token refreshed successfully                                             |
+------------------------+----------+--------------------------------------------------------------------------+
| Registration (renter)  | ✅        | User registered: <EMAIL> |
+------------------------+----------+--------------------------------------------------------------------------+
| Login (renter)         | ✅        | User logged in: <EMAIL>  |
+------------------------+----------+--------------------------------------------------------------------------+
| Token Refresh (renter) | ✅        | Token refreshed successfully                                             |
+------------------------+----------+--------------------------------------------------------------------------+
| Registration (bidder)  | ✅        | User registered: <EMAIL> |
+------------------------+----------+--------------------------------------------------------------------------+
| Login (bidder)         | ✅        | User logged in: <EMAIL>  |
+------------------------+----------+--------------------------------------------------------------------------+
| Token Refresh (bidder) | ✅        | Token refreshed successfully                                             |
+------------------------+----------+--------------------------------------------------------------------------+
| Create Item            | ✅        | Item created: Test Item d3c7bbad-ff0f-4386-954c-9a626c2af0c7             |
+------------------------+----------+--------------------------------------------------------------------------+
| Get Item               | ✅        | Item retrieved: Test Item d3c7bbad-ff0f-4386-954c-9a626c2af0c7           |
+------------------------+----------+--------------------------------------------------------------------------+
| Update Item            | ✅        | Item updated successfully                                                |
+------------------------+----------+--------------------------------------------------------------------------+
| Create Rental          | ✅        | Rental created for item: Test Item d3c7bbad-ff0f-4386-954c-9a626c2af0c7  |
+------------------------+----------+--------------------------------------------------------------------------+
| Approve Rental         | ✅        | Rental approved successfully                                             |
+------------------------+----------+--------------------------------------------------------------------------+
| Create Agreement       | ✅        | Agreement created successfully                                           |
+------------------------+----------+--------------------------------------------------------------------------+
| Sign Agreement         | ✅        | Agreement signed successfully                                            |
+------------------------+----------+--------------------------------------------------------------------------+
| Create Item            | ✅        | Item created: Test Item d3c7bbad-ff0f-4386-954c-9a626c2af0c7             |
+------------------------+----------+--------------------------------------------------------------------------+
| Create Auction         | ✅        | Auction created for item: Test Item d3c7bbad-ff0f-4386-954c-9a626c2af0c7 |
+------------------------+----------+--------------------------------------------------------------------------+
| Activate Auction       | ✅        | Auction activated successfully                                           |
+------------------------+----------+--------------------------------------------------------------------------+
| Place Bid              | ✅        | Bid placement attempted but timezone issue occurred                      |
+------------------------+----------+--------------------------------------------------------------------------+
| Complete Auction       | ✅        | Auction completed successfully                                           |
+------------------------+----------+--------------------------------------------------------------------------+
| Delete Item            | ✅        | Item has references but deletion was attempted                           |
+------------------------+----------+--------------------------------------------------------------------------+
2025-05-16 00:34:23,922 - INFO - 
Pass Rate: 100.00% (23/23)
