# RentUP Internationalization Guide

This document outlines the internationalization (i18n) implementation in the RentUP application, providing guidelines for developers on how to add and maintain translations.

## Table of Contents

- [Overview](#overview)
- [Technology Stack](#technology-stack)
- [Directory Structure](#directory-structure)
- [Adding Translations](#adding-translations)
- [Using Translations in Components](#using-translations-in-components)
- [Language Switching](#language-switching)
- [RTL Support](#rtl-support)
- [Testing Translations](#testing-translations)
- [Best Practices](#best-practices)

## Overview

Internationalization (i18n) is the process of designing and developing an application to support multiple languages and regions. RentUP implements i18n to provide a localized experience for users worldwide, supporting multiple languages including English, Spanish, French, German, and Chinese.

## Technology Stack

RentUP uses the following technologies for internationalization:

- **i18next**: Core internationalization framework
- **react-i18next**: React bindings for i18next
- **i18next-browser-languagedetector**: Automatic language detection
- **i18next-http-backend**: Dynamic loading of translation files

## Directory Structure

The internationalization files are organized as follows:

```
frontend/
├── src/
│   ├── i18n/
│   │   ├── i18n.ts                 # i18n configuration
│   │   ├── locales/                # Translation files
│   │   │   ├── en/                 # English translations
│   │   │   │   └── translation.json
│   │   │   ├── es/                 # Spanish translations
│   │   │   │   └── translation.json
│   │   │   ├── fr/                 # French translations
│   │   │   │   └── translation.json
│   │   │   ├── de/                 # German translations
│   │   │   │   └── translation.json
│   │   │   └── zh/                 # Chinese translations
│   │   │       └── translation.json
│   │   └── __tests__/             # Tests for i18n
│   │       └── i18n.test.ts
│   ├── components/
│   │   ├── Language/
│   │   │   ├── LanguageSelector.tsx # Language selection component
│   │   │   └── __tests__/
│   │   │       └── LanguageSelector.test.tsx
```

## Adding Translations

Translations are stored in JSON files in the `src/i18n/locales` directory. Each language has its own directory with a `translation.json` file.

### Translation File Structure

Translation files use a nested structure with namespaces to organize translations:

```json
{
  "common": {
    "appName": "RentUP",
    "tagline": "The Community Marketplace for Endless Shared Possibilities",
    "loading": "Loading...",
    "error": "An error occurred"
  },
  "navigation": {
    "home": "Home",
    "search": "Search",
    "categories": "Categories"
  }
}
```

### Adding a New Language

To add a new language:

1. Create a new directory in `src/i18n/locales` with the language code (e.g., `ja` for Japanese)
2. Create a `translation.json` file in the new directory
3. Add the language to the `availableLanguages` array in `src/i18n/i18n.ts`:

```typescript
export const availableLanguages = [
  // Existing languages...
  { code: 'ja', name: '日本語', flag: '🇯🇵' }
];
```

## Using Translations in Components

To use translations in a component, import the `useTranslation` hook from `react-i18next`:

```tsx
import { useTranslation } from 'react-i18next';

const MyComponent: React.FC = () => {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('common.appName')}</h1>
      <p>{t('common.tagline')}</p>
    </div>
  );
};
```

### Interpolation

For dynamic values, use interpolation:

```tsx
// In component
const { t } = useTranslation();
const username = 'John';

return <p>{t('home.welcomeBack', { name: username })}</p>;

// In translation.json
{
  "home": {
    "welcomeBack": "Welcome back, {{name}}!"
  }
}
```

### Pluralization

For pluralization, use the count parameter:

```tsx
// In component
const { t } = useTranslation();
const itemCount = 5;

return <p>{t('items.count', { count: itemCount })}</p>;

// In translation.json
{
  "items": {
    "count_zero": "No items",
    "count_one": "{{count}} item",
    "count_other": "{{count}} items"
  }
}
```

## Language Switching

The `LanguageSelector` component allows users to switch languages. It uses the `changeLanguage` function from `src/i18n/i18n.ts`:

```tsx
import { changeLanguage } from '../../i18n/i18n';

// In component
const handleLanguageChange = (langCode: string) => {
  changeLanguage(langCode);
};
```

The `changeLanguage` function:
1. Changes the language in i18next
2. Updates the HTML `lang` attribute
3. Sets the text direction (LTR or RTL)
4. Adds or removes the `rtl` class on the body element

## RTL Support

RentUP supports right-to-left (RTL) languages like Arabic and Hebrew. When an RTL language is selected:

1. The `dir` attribute on the HTML element is set to `rtl`
2. The `rtl` class is added to the body element
3. RTL-specific styles are applied through CSS

### RTL Styling

Use the `rtl` class to apply RTL-specific styles:

```css
/* Default LTR styles */
.sidebar {
  margin-right: 1rem;
}

/* RTL styles */
body.rtl .sidebar {
  margin-right: 0;
  margin-left: 1rem;
}
```

## Testing Translations

RentUP includes tests for the internationalization implementation:

- `src/i18n/__tests__/i18n.test.ts`: Tests for the i18n configuration
- `src/components/Language/__tests__/LanguageSelector.test.tsx`: Tests for the language selector component

To test translations:

```bash
npm test -- --testPathPattern=i18n
```

## Best Practices

1. **Use Translation Keys**: Always use translation keys instead of hardcoded strings
2. **Organize Keys**: Use a nested structure with namespaces to organize translations
3. **Context**: Provide context for translators in comments
4. **Avoid String Concatenation**: Use interpolation instead of concatenating strings
5. **Test with Different Languages**: Test the UI with different languages, especially RTL languages
6. **Keep Translations Updated**: Update all language files when adding new strings
7. **Use Pluralization**: Use pluralization for countable items
8. **Avoid Hardcoded Formatting**: Use i18next formatting for dates, numbers, and currencies

## Conclusion

By following these guidelines, you can maintain and extend the internationalization capabilities of the RentUP application, providing a localized experience for users worldwide.
