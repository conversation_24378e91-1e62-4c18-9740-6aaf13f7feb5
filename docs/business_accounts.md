# RentUP Business Accounts

This document outlines the business accounts feature in the RentUP application, providing guidelines for developers on how to implement and maintain business account functionality.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Business Account Types](#business-account-types)
- [User Roles and Permissions](#user-roles-and-permissions)
- [Implementation Details](#implementation-details)
- [API Endpoints](#api-endpoints)
- [Frontend Components](#frontend-components)
- [Testing](#testing)
- [Best Practices](#best-practices)

## Overview

Business accounts allow organizations to use RentUP for their rental needs with enhanced features and team collaboration. Business accounts provide:

- Multiple user access with different permission levels
- Enhanced analytics and reporting
- Bulk listing management
- Dedicated support
- Custom branding options
- API access (for enterprise tier)
- Specialized insurance and fee discounts

## Architecture

The business accounts feature is built on a multi-tenant architecture where:

1. Each business account is a separate tenant
2. Users can belong to multiple business accounts with different roles
3. Users can switch between personal and business accounts
4. Business accounts have their own settings, members, and billing information

### Data Model

The core entities in the business accounts feature are:

- **BusinessAccount**: Represents a business organization
- **BusinessMember**: Represents a user's membership in a business account
- **BusinessMemberInvitation**: Represents an invitation to join a business account
- **BusinessPermissions**: Represents the permissions a member has in a business account

## Business Account Types

RentUP offers three tiers of business accounts:

### Starter Tier

- Up to 5 team members
- Up to 50 listings
- Basic analytics
- Standard support
- No transaction fee discounts

### Professional Tier

- Up to 20 team members
- Up to 200 listings
- Advanced analytics
- Priority support
- Bulk upload capabilities
- 5% insurance discount
- 10% transaction fee discount

### Enterprise Tier

- Up to 100 team members
- Up to 1000 listings
- Comprehensive analytics
- Priority support with dedicated account manager
- Bulk upload capabilities
- Custom branding
- API access
- 15% insurance discount
- 20% transaction fee discount

## User Roles and Permissions

Business accounts support the following roles:

### Owner

- Full access to all business account features
- Can manage billing and subscription
- Can add/remove members
- Can assign roles to members
- Cannot be removed from the business account

### Admin

- Can manage business account settings
- Can add/remove members
- Can assign roles to members (except Owner)
- Can manage listings and rentals
- Can view analytics

### Member

- Can create and manage listings
- Can manage rentals
- Can view analytics
- Cannot manage business account settings or members

### Viewer

- Can view listings and rentals
- Can view analytics
- Cannot create or manage listings or rentals
- Cannot manage business account settings or members

## Implementation Details

### Authentication Context

The authentication context is extended to support business accounts:

- `currentBusiness`: The currently selected business account
- `isBusinessAccount`: Whether the user is currently using a business account
- `switchToBusiness`: Function to switch to a business account
- `switchToPersonal`: Function to switch to the personal account
- `hasBusinessRole`: Function to check if the user has a specific role in the current business account

### Business Account Switching

Users can switch between their personal account and any business accounts they belong to using the `BusinessAccountSwitcher` component. When a user switches accounts:

1. The `currentBusinessId` is updated in the user object
2. The `currentBusiness` is set in the auth context
3. The UI updates to reflect the current account
4. API requests include the business account ID when applicable

### Business Account Creation

To create a new business account:

1. User navigates to the business account creation page
2. User fills out the business account details
3. User submits the form
4. A new business account is created
5. The user is automatically added as the owner
6. The user is switched to the new business account

### Member Management

Business account owners and admins can:

1. Invite new members by email
2. Assign roles and permissions to members
3. Remove members from the business account
4. View member activity

## API Endpoints

The business accounts feature uses the following API endpoints:

### Business Account Management

- `GET /api/v1/business-accounts/me`: Get all business accounts for the current user
- `GET /api/v1/business-accounts/:id`: Get a specific business account
- `POST /api/v1/business-accounts`: Create a new business account
- `PUT /api/v1/business-accounts/:id`: Update a business account
- `DELETE /api/v1/business-accounts/:id`: Delete a business account

### Member Management

- `GET /api/v1/business-accounts/:id/members`: Get all members of a business account
- `POST /api/v1/business-accounts/:id/invitations`: Invite a new member
- `GET /api/v1/business-accounts/:id/invitations`: Get all invitations for a business account
- `DELETE /api/v1/business-accounts/:id/invitations/:invitationId`: Cancel an invitation
- `POST /api/v1/business-invitations/:id/accept`: Accept an invitation
- `POST /api/v1/business-invitations/:id/decline`: Decline an invitation
- `DELETE /api/v1/business-accounts/:id/members/:memberId`: Remove a member
- `PUT /api/v1/business-accounts/:id/members/:memberId`: Update a member's role and permissions

### Subscription Management

- `POST /api/v1/business-accounts/:id/upgrade`: Upgrade business account tier
- `POST /api/v1/business-accounts/:id/cancel-subscription`: Cancel business account subscription
- `POST /api/v1/business-accounts/:id/reactivate-subscription`: Reactivate business account subscription

## Frontend Components

The business accounts feature includes the following frontend components:

### Core Components

- `BusinessAccountSwitcher`: Dropdown to switch between personal and business accounts
- `BusinessDashboard`: Dashboard for business accounts
- `BusinessCreate`: Form to create a new business account
- `BusinessSelect`: Page to select which business account to use

### Member Management Components

- `BusinessMembers`: Page to manage business account members
- `BusinessInvite`: Form to invite new members
- `BusinessMemberList`: List of business account members
- `BusinessMemberItem`: Individual member item with role and permissions

### Settings Components

- `BusinessSettings`: Page to manage business account settings
- `BusinessProfile`: Form to update business account profile
- `BusinessBilling`: Page to manage business account billing
- `BusinessSubscription`: Component to manage subscription tier

## Testing

The business accounts feature includes the following tests:

### Unit Tests

- Tests for business account services
- Tests for business account components
- Tests for authentication context with business accounts

### Integration Tests

- Tests for business account creation flow
- Tests for member invitation flow
- Tests for account switching flow

### End-to-End Tests

- Tests for complete business account lifecycle
- Tests for member management
- Tests for subscription management

## Best Practices

1. **Role-Based Access Control**: Always check user roles and permissions before allowing access to business account features
2. **Clear Account Context**: Make it clear to users which account they are currently using
3. **Consistent API Patterns**: Use consistent patterns for business account API endpoints
4. **Comprehensive Testing**: Test all business account features thoroughly
5. **Clear Error Handling**: Provide clear error messages for business account operations
6. **Performance Considerations**: Optimize business account operations for performance
7. **Security**: Implement proper security measures for business account data
8. **Audit Logging**: Log all important business account operations for audit purposes

## Conclusion

The business accounts feature provides a powerful way for organizations to use RentUP for their rental needs. By following the guidelines in this document, developers can implement and maintain business account functionality in a consistent and reliable way.
