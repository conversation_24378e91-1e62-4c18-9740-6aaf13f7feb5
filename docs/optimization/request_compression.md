# Request Compression

This document describes the request compression system implemented in the RentUp platform to reduce bandwidth usage and improve performance.

## Table of Contents

- [Overview](#overview)
- [Compression Algorithms](#compression-algorithms)
- [Configuration](#configuration)
- [Usage](#usage)
- [Performance Considerations](#performance-considerations)
- [Best Practices](#best-practices)

## Overview

The request compression system is designed to reduce bandwidth usage and improve performance by compressing HTTP responses using GZip and Brotli compression algorithms. This is particularly useful for mobile clients and large responses.

The system consists of a middleware that intercepts HTTP responses, compresses them if appropriate, and adds the necessary headers to indicate the compression method used.

## Compression Algorithms

The system supports two compression algorithms:

1. **GZip**: A widely supported compression algorithm that provides good compression ratios and performance. It is supported by all modern browsers and HTTP clients.

2. **Brotli**: A newer compression algorithm developed by Google that provides better compression ratios than GZip. It is supported by most modern browsers and HTTP clients, but may not be available in older clients.

The system automatically selects the best compression algorithm based on client capabilities and efficiency:

- If the client supports Brotli, it is used for better compression ratios
- If the client only supports GZip, it is used as a fallback
- If the client doesn't support any compression, no compression is applied

## Configuration

The compression middleware can be configured with the following parameters:

- `minimum_size`: Minimum response size in bytes to apply compression (default: 500)
- `gzip_level`: GZip compression level (1-9, where 9 is highest compression) (default: 6)
- `brotli_quality`: Brotli compression quality (0-11, where 11 is highest quality) (default: 4)
- `exclude_paths`: List of path prefixes to exclude from compression
- `exclude_media_types`: List of media types to exclude from compression
- `include_media_types`: List of media types to include in compression
- `debug`: Enable debug logging

These parameters can be configured in the `config.py` file:

```python
# Compression Settings
COMPRESSION_MINIMUM_SIZE: int = 500  # Minimum size in bytes to apply compression
COMPRESSION_GZIP_LEVEL: int = 6  # GZip compression level (1-9)
COMPRESSION_BROTLI_QUALITY: int = 4  # Brotli compression quality (0-11)
COMPRESSION_EXCLUDE_PATHS: List[str] = [
    "/health",
    "/metrics",
    "/api/docs",
    "/api/redoc",
    "/api/openapi.json"
]
COMPRESSION_EXCLUDE_MEDIA_TYPES: List[str] = [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "audio/",
    "video/"
]
COMPRESSION_DEBUG: bool = False
```

## Usage

The compression middleware is automatically applied to all HTTP responses that meet the following criteria:

1. The client supports compression (indicated by the `Accept-Encoding` header)
2. The response is not already compressed (no `Content-Encoding` header)
3. The response size is above the minimum threshold
4. The response media type is not excluded
5. The request path is not excluded

The middleware is added to the FastAPI application in the `main.py` file:

```python
# Add compression middleware
from app.core.compression_middleware import add_compression_middleware
add_compression_middleware(
    app,
    minimum_size=settings.COMPRESSION_MINIMUM_SIZE,
    gzip_level=settings.COMPRESSION_GZIP_LEVEL,
    brotli_quality=settings.COMPRESSION_BROTLI_QUALITY,
    exclude_paths=settings.COMPRESSION_EXCLUDE_PATHS,
    exclude_media_types=settings.COMPRESSION_EXCLUDE_MEDIA_TYPES,
    debug=settings.COMPRESSION_DEBUG
)
```

## Performance Considerations

The compression middleware adds some CPU overhead to compress responses, but this is usually outweighed by the bandwidth savings and improved client-side performance. Here are some performance considerations:

- **CPU Usage**: Compression requires CPU resources on the server. Higher compression levels require more CPU.
- **Memory Usage**: Compression requires memory to store the original and compressed response.
- **Bandwidth Savings**: Compression can significantly reduce the amount of data transferred, especially for text-based responses.
- **Client-side Performance**: Smaller responses mean faster downloads and less data usage for clients.

The middleware is designed to only compress responses when it makes sense:

- Only responses above a minimum size are compressed
- Only responses that are not already compressed are processed
- Only responses with compressible media types are compressed
- Compression is only applied if it actually reduces the response size

## Best Practices

Here are some best practices for using the compression middleware:

1. **Adjust Compression Levels**: Higher compression levels provide better compression ratios but require more CPU. Adjust the compression levels based on your server's CPU capacity and the types of responses you serve.

2. **Exclude Non-Compressible Content**: Exclude media types that are already compressed (images, audio, video) to avoid wasting CPU resources.

3. **Exclude Small Responses**: Set an appropriate minimum size threshold to avoid compressing small responses where the overhead of compression outweighs the benefits.

4. **Monitor Performance**: Monitor the CPU usage and response times to ensure that compression is not negatively impacting server performance.

5. **Use Brotli When Available**: Brotli provides better compression ratios than GZip, but it requires more CPU. Use it when available, but fall back to GZip for older clients.

6. **Cache Compressed Responses**: Consider caching compressed responses to avoid recompressing the same content for different clients.

7. **Combine with Other Optimizations**: Compression works well with other optimizations like caching, minification, and bundling.

By following these best practices, you can ensure that the compression middleware provides the best possible performance for your application.
