# Redis Caching Implementation

This document describes the Redis caching implementation for the RentUp platform.

## Overview

Redis caching is a critical component for improving application performance by reducing database load and API response times. The RentUp platform implements a comprehensive Redis caching solution with the following features:

1. **Centralized Cache Management**: A singleton Redis connection with consistent interface
2. **Tiered Caching Strategy**: Different cache types with appropriate TTLs
3. **Automatic Serialization**: Transparent serialization/deserialization of complex objects
4. **Decorator-based Caching**: Easy-to-use decorators for function and method caching
5. **Cache Invalidation**: Mechanisms for invalidating specific cache entries
6. **Cache Monitoring**: Statistics and metrics for cache performance
7. **Middleware Integration**: Automatic caching of API responses

## Components

### 1. Core Cache Module

The `app.core.cache` module provides the foundation for Redis caching:

- `RedisCache`: Singleton class for Redis operations
- `CacheType`: Enum for different cache types with predefined TTLs
- `cached`: Decorator for caching function results
- `async_cached`: Decorator for caching async function results

### 2. Cache Service

The `app.services.cache_service` module provides high-level caching functions for frequently accessed data:

- User data caching
- Item data caching
- Category data caching
- Platform statistics caching
- Cache invalidation functions
- Cache statistics functions

### 3. Cache Middleware

The `app.core.cache_middleware` module provides automatic caching of API responses:

- Response caching based on request path and query parameters
- Configurable exclusion of paths and methods
- Automatic cache invalidation for write operations

### 4. Cache API

The `app.api.v1.endpoints.cache` module provides API endpoints for cache management:

- Cache statistics endpoint
- Cache clearing endpoint
- Cache invalidation endpoints
- Cache key listing endpoint

## Cache Types

The caching system defines several cache types with different TTLs:

| Cache Type | TTL | Use Case |
|------------|-----|----------|
| VOLATILE | 5 minutes | Frequently changing data (stats, counts) |
| NORMAL | 1 hour | Standard data (items, users) |
| PERSISTENT | 1 day | Slowly changing data (categories) |
| STATIC | 1 week | Rarely changing data (configurations) |
| PERMANENT | No expiration | Constant data (reference data) |

## Usage Examples

### Basic Caching

```python
from app.core.cache import RedisCache

# Get cache instance
cache = RedisCache()

# Set a value with default expiration
cache.set("my_key", "my_value")

# Set a value with custom expiration
cache.set("my_key", "my_value", expiration=3600)  # 1 hour

# Get a value
value = cache.get("my_key")

# Delete a value
cache.delete("my_key")
```

### Function Caching

```python
from app.core.cache import cached, CacheType

# Cache with default expiration
@cached(prefix="user")
def get_user(user_id):
    # Expensive database query
    return db.query(User).filter(User.id == user_id).first()

# Cache with custom expiration
@cached(prefix="stats", expiration=300)  # 5 minutes
def get_statistics():
    # Expensive calculation
    return calculate_statistics()

# Cache with predefined cache type
@cached(prefix="categories", cache_type=CacheType.PERSISTENT)
def get_categories():
    # Rarely changing data
    return db.query(Category).all()
```

### Async Function Caching

```python
from app.core.cache import async_cached

@async_cached(prefix="async_data")
async def get_async_data():
    # Expensive async operation
    return await fetch_data()
```

### Cache Invalidation

```python
# Invalidate a specific cache entry
get_user.invalidate_cache(user_id)

# Invalidate all entries with a prefix
cache = RedisCache()
cache.clear_pattern("user:*")

# Clear all cache
cache.clear_all()
```

### Cache Statistics

```python
# Get cache statistics
from app.services.cache_service import get_cache_stats

stats = get_cache_stats()
print(f"Hit ratio: {stats['hit_ratio']:.2%}")
print(f"Total requests: {stats['total_requests']}")
```

## Best Practices

1. **Use Appropriate Cache Types**: Choose the right cache type based on data volatility
2. **Consistent Key Prefixes**: Use consistent prefixes for related data
3. **Cache Invalidation**: Invalidate cache when data changes
4. **Monitor Cache Performance**: Regularly check cache hit ratio and memory usage
5. **Avoid Caching Large Objects**: Be mindful of memory usage when caching large objects
6. **Use Skip Cache Option**: Use `skip_cache=True` for force-refreshing data

## Cache Monitoring

The cache implementation includes comprehensive monitoring:

- **Hit/Miss Ratio**: Track cache effectiveness
- **Operation Counts**: Track cache operations (gets, sets, deletes)
- **Error Tracking**: Track cache errors
- **Memory Usage**: Monitor Redis memory consumption
- **Cache Size**: Monitor number of cached items

## API Endpoints

The following API endpoints are available for cache management:

- `GET /api/v1/cache/stats`: Get cache statistics
- `POST /api/v1/cache/clear`: Clear all cache
- `POST /api/v1/cache/invalidate/user/{user_id}`: Invalidate user cache
- `POST /api/v1/cache/invalidate/item/{item_id}`: Invalidate item cache
- `POST /api/v1/cache/invalidate/categories`: Invalidate category cache
- `POST /api/v1/cache/invalidate/stats`: Invalidate platform statistics cache
- `GET /api/v1/cache/keys`: Get cache keys matching a pattern

## Next Steps

After implementing Redis caching, consider these additional performance improvements:

1. Add rate limiting for API endpoints
2. Optimize vector search performance
3. Implement query batching
4. Add request compression
5. Optimize authentication flow
6. Implement background processing for heavy tasks
7. Add API response caching
