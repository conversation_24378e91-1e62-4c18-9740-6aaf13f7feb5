# Database Optimizations for RentUp

This document provides a comprehensive guide to the database optimizations implemented in the RentUp backend.

## Table of Contents

1. [Introduction](#introduction)
2. [Database Connection Pooling](#database-connection-pooling)
3. [JOIN Operation Optimization](#join-operation-optimization)
4. [Query Caching](#query-caching)
5. [Batch Operations](#batch-operations)
6. [Pagination](#pagination)
7. [Performance Monitoring](#performance-monitoring)
8. [Backward Compatibility](#backward-compatibility)
9. [Usage Examples](#usage-examples)

## Introduction

The RentUp backend has been optimized to improve database performance, reduce latency, and increase throughput. The optimizations include:

- Database connection pooling
- JOIN operation optimization
- Query caching
- Batch operations
- Pagination
- Performance monitoring

These optimizations are designed to be backward compatible with the existing codebase, allowing for incremental adoption.

## Database Connection Pooling

Connection pooling improves performance by reusing database connections instead of creating new ones for each request.

### Configuration

Connection pooling is configured in `app/core/db_config.py`:

```python
# Database connection pool settings
DB_POOL_SIZE: int = 20
DB_MAX_OVERFLOW: int = 10
DB_POOL_TIMEOUT: int = 30
DB_POOL_RECYCLE: int = 1800  # Recycle connections after 30 minutes
```

### Implementation

The connection pool is implemented in `app/core/database_connection.py`:

```python
# Create engine with connection pooling
engine = create_engine(
    db_config.database_url,
    poolclass=QueuePool,
    pool_pre_ping=True,  # Enable connection health checks
    pool_size=db_config.pool_size,
    max_overflow=db_config.max_overflow,
    pool_timeout=db_config.pool_timeout,
    pool_recycle=db_config.pool_recycle,
    echo=db_config.echo,
    echo_pool=db_config.echo_pool
)
```

## JOIN Operation Optimization

JOIN operations are optimized to reduce the number of database queries and improve performance.

### Strategies

The following strategies are used to optimize JOIN operations:

1. **Relationship Loading Strategies**:
   - `selectinload`: Loads related objects using a separate SELECT statement (default)
   - `joinedload`: Loads related objects using a JOIN statement
   - `contains_eager`: Loads related objects from a query that already contains the JOIN

2. **Index Optimization**:
   - Indexes are created for foreign keys used in JOIN operations
   - Composite indexes are created for frequently used combinations of columns

3. **Query Hints**:
   - PostgreSQL-specific query hints are added to guide the query planner

### Implementation

JOIN optimization is implemented in `app/core/join_optimization.py`:

```python
def optimize_join_query(query: Query, db: Session) -> Query:
    """Optimize a query with JOIN operations."""
    # Analyze the query
    patterns = analyze_join_patterns(db, query)
    
    # Apply optimizations based on patterns
    optimized_query = query
    
    # Add query hints for the PostgreSQL query planner
    if patterns:
        # Convert to string and add hints
        query_str = str(query.statement.compile(
            dialect=db.bind.dialect,
            compile_kwargs={"literal_binds": True}
        ))
        
        # Add hints for each pattern
        hints = []
        for pattern in patterns:
            # Add hash join hint for large tables
            hints.append(f"HashJoin({pattern.left_table} {pattern.right_table})")
        
        # Add hints to query
        if hints:
            hint_str = "/*+ " + " ".join(hints) + " */"
            query_str = f"SELECT {hint_str} " + query_str[7:]
            
            # Execute with hints
            optimized_query = db.execute(text(query_str))
    
    return optimized_query
```

## Query Caching

Query caching improves performance by storing the results of expensive queries in memory or Redis.

### Caching Strategies

The following caching strategies are available:

1. **Simple Caching**: Cache query results based on the query string and parameters
2. **Smart Caching**: Cache query results with dependency tracking for automatic invalidation
3. **Adaptive Caching**: Adjust cache TTL based on query patterns and data volatility

### Implementation

Query caching is implemented in `app/core/query_cache.py`:

```python
@cached_query(ttl=3600, strategy=CacheStrategy.SMART)
def get_users(session):
    return session.query(User).all()
```

## Batch Operations

Batch operations improve performance by reducing the number of database round-trips.

### Implementation

Batch operations are implemented in `app/core/query_batching.py`:

```python
# Get multiple items by their IDs
item_ids = ["item1", "item2", "item3"]
items_map = batch_get_by_ids(db, Item, item_ids)

# Get items for multiple users
user_ids = ["user1", "user2", "user3"]
user_items = batch_get_related(db, Item, user_ids, "owner_id")
```

## Pagination

Pagination improves performance by limiting the number of rows returned by a query.

### Implementation

Pagination is implemented in `app/core/query_optimization.py`:

```python
def paginate_query(query: Query, page: int = 1, page_size: int = 20) -> Tuple[Query, int]:
    """Paginate a query."""
    # Get total count
    count_query = query.with_entities(func.count())
    total_count = count_query.scalar()

    # Apply pagination
    offset = (page - 1) * page_size
    paginated_query = query.offset(offset).limit(page_size)

    return paginated_query, total_count
```

## Performance Monitoring

Performance monitoring helps identify slow queries and optimization opportunities.

### Implementation

Performance monitoring is implemented in `app/core/query_optimization.py`:

```python
@track_query_performance(query_name="get_users")
def get_users(db: Session):
    return db.query(User).all()
```

## Backward Compatibility

The optimizations are designed to be backward compatible with the existing codebase, allowing for incremental adoption.

### Unified API

The `app/services/db_optimization_service.py` module provides a unified API for database optimizations:

```python
def optimized_query(query: Query, db: Session, model_class: Optional[M] = None) -> OptimizedQuery[M]:
    """Create an optimized query."""
    return OptimizedQuery(query, db, model_class)
```

## Usage Examples

### Basic Query Optimization

```python
from app.services.db_optimization_service import optimized_query

# Get all users with optimization
users = optimized_query(db.query(User), db, User).optimize().all()
```

### Query with Relationship Loading

```python
# Get users with their owned items
users = optimized_query(db.query(User), db, User).optimize().with_relationships(["owned_items"]).all()
```

### Query with Caching

```python
# Get users with caching
users = optimized_query(db.query(User), db, User).optimize().cache(ttl=3600).all()
```

### Query with Pagination

```python
# Get paginated users
users, total_count = optimized_query(db.query(User), db, User).optimize().paginate(page=1, page_size=20).get_page()
```

### Batch Operations

```python
from app.services.db_optimization_service import batch_get, batch_get_related

# Get multiple users by their IDs
user_ids = ["user1", "user2", "user3"]
users_map = batch_get(db, User, user_ids)

# Get items for multiple users
user_items = batch_get_related(db, Item, user_ids, "owner_id")
```
