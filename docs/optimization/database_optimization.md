# Database Query Optimization

This document describes the database query optimization implementation for the RentUp platform.

## Overview

Database query optimization is a critical aspect of application performance, especially as the database grows in size and complexity. The RentUp platform implements several optimization techniques to ensure efficient database operations:

1. **Query Performance Tracking**: Monitoring and logging query performance to identify slow queries
2. **Index Optimization**: Creating and managing indexes for common query patterns
3. **Query Optimization**: Applying optimization techniques to complex queries
4. **Pagination**: Implementing efficient pagination for large result sets
5. **Query Decorators**: Providing easy-to-use decorators for optimizing API endpoints

## Components

### 1. Query Performance Tracking

The `QueryPerformanceTracker` class in `backend/app/core/query_optimization.py` provides functionality to:

- Record query execution times and result sizes
- Calculate statistics (min, max, average) for query performance
- Identify slow queries based on configurable thresholds
- Generate performance reports

Example usage:

```python
from app.core.query_optimization import query_tracker

# Record a query
query_tracker.record_query("get_items", 0.15, 100)

# Get slow queries
slow_queries = query_tracker.get_slow_queries(threshold_seconds=0.1)

# Log statistics
query_tracker.log_statistics()
```

### 2. Index Optimization

The `index_optimization.py` module provides tools for:

- Defining and creating database indexes
- Analyzing index usage
- Identifying unused indexes
- Recommending new indexes based on query patterns

Example usage:

```python
from app.core.index_optimization import IndexDefinition, create_index

# Define an index
index_def = IndexDefinition(
    table_name="items",
    column_names=["category", "is_available"],
    index_name="idx_items_category_available"
)

# Create the index
create_index(db, index_def)
```

### 3. Query Optimization

The `optimize_query` function applies various optimization techniques to SQLAlchemy queries:

- Analyzing query execution plans
- Optimizing JOIN operations
- Applying query hints
- Restructuring complex queries

Example usage:

```python
from app.core.query_optimization import optimize_query

# Get a query
query = db.query(Item).filter(Item.category == "electronics")

# Optimize the query
optimized = optimize_query(query, db)

# Execute the optimized query
results = optimized.all()
```

### 4. Pagination

The `paginate_query` function provides efficient pagination for large result sets:

- Calculating total count efficiently
- Applying offset and limit
- Returning pagination metadata

Example usage:

```python
from app.core.query_optimization import paginate_query

# Get a query
query = db.query(Item)

# Apply pagination
paginated_query, total_count = paginate_query(query, page=2, page_size=20)

# Execute the paginated query
results = paginated_query.all()
```

### 5. Query Decorators

The `query_decorators.py` module provides decorators for easily applying optimization techniques to functions and API endpoints:

- `@optimized_query`: Tracks performance and applies optimization
- `@paginated_query`: Adds pagination to query functions
- `@optimized_endpoint`: Optimizes FastAPI endpoints with database queries

Example usage:

```python
from app.core.query_decorators import optimized_query, paginated_query

@optimized_query("get_items_by_category")
def get_items_by_category(db, category):
    return db.query(Item).filter(Item.category == category)

@paginated_query(default_page_size=20)
def get_all_items(db):
    return db.query(Item)
```

## Database Optimization Script

The `apply_db_optimizations.py` script provides a command-line interface for:

- Analyzing database performance
- Creating recommended indexes
- Vacuuming tables with dead tuples
- Updating table statistics
- Generating optimization reports

Usage:

```bash
# Analyze database and suggest optimizations
python -m backend.scripts.apply_db_optimizations --analyze

# Apply recommended optimizations
python -m backend.scripts.apply_db_optimizations --apply

# Generate optimization report
python -m backend.scripts.apply_db_optimizations --report

# Perform all optimization tasks
python -m backend.scripts.apply_db_optimizations --all
```

## Recommended Indexes

Based on analysis of the RentUp codebase, the following indexes are recommended for optimal performance:

### Users Table
- `email` (unique)
- `verification_level, is_active`

### Items Table
- `owner_id`
- `category, is_available`
- `location, is_available`
- `daily_price`
- `created_at`
- `name, description` (GIN index for text search)

### Rentals Table
- `item_id`
- `owner_id`
- `renter_id`
- `status, start_date, end_date`

### Auctions Table
- `item_id`
- `owner_id`
- `status, end_time`

### Bids Table
- `auction_id`
- `bidder_id`

### Agreements Table
- `rental_id`
- `owner_id`
- `renter_id`
- `status`

## Best Practices

1. **Use the decorators**: Apply the provided decorators to API endpoints and query functions
2. **Run the optimization script regularly**: Schedule regular database optimization
3. **Monitor query performance**: Check the logs for slow queries
4. **Update indexes as needed**: Add new indexes as query patterns change
5. **Test optimizations**: Verify performance improvements with benchmarks

## Next Steps

After implementing database query optimization, consider these additional performance improvements:

1. Implement Redis caching for frequently accessed data
2. Add rate limiting for API endpoints
3. Optimize vector search performance
4. Implement query batching
5. Add request compression
6. Optimize authentication flow
7. Implement background processing for heavy tasks
8. Add API response caching
