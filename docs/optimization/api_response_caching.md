# API Response Caching

This document describes the API response caching system implemented in the RentUp platform to improve performance and reduce database load.

## Table of Contents

- [Overview](#overview)
- [Implementation](#implementation)
- [Configuration](#configuration)
- [Usage](#usage)
- [Cache Invalidation](#cache-invalidation)
- [Cache Management](#cache-management)
- [Performance Considerations](#performance-considerations)
- [Best Practices](#best-practices)
- [Testing](#testing)

## Overview

The API response caching system is designed to improve performance and reduce database load by caching API responses. This is particularly useful for frequently accessed endpoints that return relatively static data, such as item listings, categories, and recommendations.

The system consists of two main components:

1. **Middleware**: A FastAPI middleware that intercepts API requests and responses, caching successful responses and serving cached responses for subsequent requests.

2. **Decorator**: A decorator that can be applied to individual API endpoints to cache their responses with fine-grained control.

Both components support Redis as the primary cache backend, with an in-memory fallback for development and testing.

## Implementation

### Middleware

The middleware is implemented in `app/core/api_cache.py` and provides the following features:

- Caching of API responses based on request path and query parameters
- Configurable TTL (Time-To-Live) for cached responses
- Support for Redis and in-memory cache backends
- Exclusion of specific paths, methods, and query parameters
- Cache bypass for authenticated requests (optional)
- Cache statistics and monitoring

### Decorator

The decorator is also implemented in `app/core/api_cache.py` and provides similar features to the middleware, but with more fine-grained control:

- Caching of individual API endpoint responses
- Configurable TTL per endpoint
- Support for varying cache keys based on specific query parameters
- Cache bypass for authenticated requests (optional)

## Configuration

The caching system can be configured in `app/core/config.py` with the following settings:

```python
# API Cache Settings
API_CACHE_ENABLED: bool = True
API_CACHE_TTL: int = 60  # Default TTL in seconds
API_CACHE_EXCLUDE_PATHS: List[str] = [
    "/health",
    "/metrics",
    "/api/docs",
    "/api/redoc",
    "/api/openapi.json",
    "/api/v1/cache",  # Exclude cache management endpoints
    "/api/v1/auth",   # Exclude auth endpoints
    "/api/v1/users/me"  # Exclude user profile endpoint
]
API_CACHE_EXCLUDE_METHODS: List[str] = ["POST", "PUT", "DELETE", "PATCH"]
API_CACHE_EXCLUDE_QUERY_PARAMS: List[str] = ["_t", "nocache", "token"]
API_CACHE_BYPASS_FOR_AUTHENTICATED: bool = True
API_CACHE_DEBUG: bool = False
```

The middleware is added to the FastAPI application in `app/main.py`:

```python
# Add API response caching middleware
from app.core.api_cache import add_api_cache_middleware
from app.core.cache import get_redis_client
add_api_cache_middleware(
    app,
    redis_client=get_redis_client(),
    ttl=settings.API_CACHE_TTL,
    cache_prefix="api_cache:",
    exclude_paths=settings.API_CACHE_EXCLUDE_PATHS,
    exclude_methods=settings.API_CACHE_EXCLUDE_METHODS,
    exclude_query_params=settings.API_CACHE_EXCLUDE_QUERY_PARAMS,
    bypass_for_authenticated=settings.API_CACHE_BYPASS_FOR_AUTHENTICATED,
    debug=settings.API_CACHE_DEBUG
)
```

## Usage

### Using the Middleware

The middleware is automatically applied to all API requests that meet the configured criteria. No additional code is required to use it.

### Using the Decorator

The decorator can be applied to individual API endpoints to cache their responses:

```python
from app.core.api_cache import cache_response

@router.get("/items", response_model=List[ItemInDB])
@cache_response(ttl=60)  # Cache for 60 seconds
async def get_items(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get all items."""
    items = db.query(Item).offset(skip).limit(limit).all()
    return items
```

The decorator supports the following parameters:

- `ttl`: TTL for cached responses in seconds
- `cache_key_prefix`: Prefix for cache keys
- `vary_by_params`: List of query parameters to include in cache key generation
- `bypass_for_authenticated`: Whether to bypass cache for authenticated requests

### Client-Side Cache Control

Clients can control caching behavior by including the `nocache` query parameter:

```
GET /api/v1/items?nocache=true
```

This will bypass the cache and always fetch fresh data from the database.

## Cache Invalidation

The caching system provides several methods for cache invalidation:

### Automatic Invalidation

The cache is automatically invalidated when:

- The TTL expires
- The server restarts

### Manual Invalidation

The cache can be manually invalidated through the cache management API:

```
POST /api/v1/cache/invalidate
{
    "pattern": "items"
}
```

This will invalidate all cache entries with keys matching the specified pattern.

### Programmatic Invalidation

The cache can also be invalidated programmatically:

```python
from app.core.api_cache import invalidate_cache

# Invalidate cache for a specific pattern
await invalidate_cache("items")
```

## Cache Management

The caching system provides a management API for monitoring and controlling the cache:

### Cache Statistics

```
GET /api/v1/cache/stats
```

Returns statistics about the cache, including hits, misses, and size.

### Cache Keys

```
GET /api/v1/cache/keys?pattern=items
```

Returns a list of cache keys matching the specified pattern.

### Cache Configuration

```
GET /api/v1/cache/config
```

Returns the current cache configuration.

### Clear Cache

```
POST /api/v1/cache/clear
```

Clears all cache entries.

## Performance Considerations

The caching system is designed to improve performance by reducing database load and response times. However, there are some considerations to keep in mind:

- **Memory Usage**: Caching requires memory, especially when using the in-memory cache backend. Monitor memory usage and adjust TTL and cache size accordingly.
- **Cache Invalidation**: Stale cache entries can lead to outdated data. Implement appropriate cache invalidation strategies.
- **Cache Hit Ratio**: Monitor the cache hit ratio to ensure the cache is effective. A low hit ratio may indicate that the cache is not being used effectively.

## Best Practices

Here are some best practices for using the API response caching system:

1. **Cache Appropriate Endpoints**: Cache endpoints that return relatively static data and are frequently accessed.
2. **Set Appropriate TTL**: Set TTL based on how frequently the data changes. Shorter TTL for frequently changing data, longer TTL for static data.
3. **Use Cache Invalidation**: Implement cache invalidation when data changes to ensure fresh data.
4. **Monitor Cache Performance**: Monitor cache hit ratio, memory usage, and response times to ensure the cache is effective.
5. **Test Caching Behavior**: Test caching behavior to ensure it works as expected.

## Testing

The caching system includes a test script for verifying caching behavior:

```bash
python backend/scripts/test_api_cache.py
```

This script tests various endpoints to ensure they are cached correctly and that cache invalidation works as expected.

You can also manually test caching behavior by making requests to the API and observing the response times and headers.
