# Query Batching System

This document describes the query batching system implemented in the RentUp platform to optimize database operations and API requests.

## Table of Contents

- [Overview](#overview)
- [Query Batching](#query-batching)
- [Batch API Requests](#batch-api-requests)
- [Usage Examples](#usage-examples)
- [Performance Considerations](#performance-considerations)
- [Best Practices](#best-practices)

## Overview

The query batching system is designed to reduce the number of database round-trips and improve performance by combining multiple queries into fewer transactions. This is particularly useful for scenarios where multiple related entities need to be fetched, such as items and their owners, or when multiple independent queries need to be executed.

The system consists of two main components:

1. **Database Query Batching**: Combines multiple database queries into fewer transactions
2. **API Request Batching**: Allows clients to send multiple API requests in a single HTTP request

## Query Batching

### BatchQuery

The `BatchQuery` class provides a fluent interface for building and executing database queries with performance optimizations:

```python
from app.core.query_batching import BatchQuery

# Create a batch query
query = BatchQuery(Item, db)

# Configure the query
query.filter(Item.category == "electronics")
     .filter(Item.is_available == True)
     .order_by(Item.created_at.desc())
     .limit(10)

# Execute the query
items = query.all()
```

### QueryBatcher

The `QueryBatcher` class allows executing multiple queries in a single database transaction:

```python
from app.core.query_batching import QueryBatcher

# Create a query batcher
batcher = QueryBatcher(db)

# Add queries to the batch
query1 = BatchQuery(Item, db).filter(Item.category == "electronics")
query2 = BatchQuery(User, db).filter(User.is_active == True)

batcher.add_query(query1)
batcher.add_query(query2)

# Execute all queries in a single transaction
results = batcher.execute()

# Access results
items = results[0]
users = results[1]
```

### Batch Utility Functions

The system also provides utility functions for common batching operations:

#### batch_get_by_ids

Efficiently fetches multiple entities by their IDs:

```python
from app.core.query_batching import batch_get_by_ids

# Get multiple items by their IDs
item_ids = ["item1", "item2", "item3"]
items_map = batch_get_by_ids(db, Item, item_ids)

# Access items by ID
item1 = items_map["item1"]
item2 = items_map["item2"]
```

#### batch_get_related

Efficiently fetches related entities for multiple parent IDs:

```python
from app.core.query_batching import batch_get_related

# Get items for multiple users
user_ids = ["user1", "user2", "user3"]
user_items = batch_get_related(db, Item, user_ids, "owner_id")

# Access items by user ID
user1_items = user_items["user1"]
user2_items = user_items["user2"]
```

## Batch API Requests

The batch API endpoint allows clients to send multiple API requests in a single HTTP request, reducing network overhead and improving performance.

### Endpoint

```
POST /api/v1/batch
```

### Request Format

```json
{
  "requests": [
    {
      "id": "req1",
      "path": "items",
      "method": "GET",
      "params": {"limit": 5}
    },
    {
      "id": "req2",
      "path": "users/me",
      "method": "GET"
    },
    {
      "id": "req3",
      "path": "items",
      "method": "POST",
      "body": {"name": "New Item", "price": 10.0}
    }
  ]
}
```

### Response Format

```json
{
  "responses": [
    {
      "id": "req1",
      "status": 200,
      "body": [...]
    },
    {
      "id": "req2",
      "status": 200,
      "body": {...}
    },
    {
      "id": "req3",
      "status": 201,
      "body": {...}
    }
  ],
  "execution_time": 0.123
}
```

## Usage Examples

### Fetching Items with Owners

```python
from app.core.query_batching import batch_get_by_ids, batch_get_related

# Get items
items = db.query(Item).filter(Item.category == "electronics").all()

# Get owner IDs
owner_ids = [item.owner_id for item in items]

# Batch fetch owners
owners = batch_get_by_ids(db, User, owner_ids)

# Combine data
for item in items:
    item.owner = owners.get(item.owner_id)
```

### Batch API Request from Frontend

```javascript
// Fetch multiple resources in a single request
async function fetchDashboardData() {
  const response = await fetch('/api/v1/batch', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      requests: [
        {
          id: 'profile',
          path: 'users/me',
          method: 'GET'
        },
        {
          id: 'items',
          path: 'items',
          method: 'GET',
          params: { limit: 5, sort_by: 'created_at', sort_order: 'desc' }
        },
        {
          id: 'notifications',
          path: 'users/me/notifications',
          method: 'GET',
          params: { unread_only: true }
        }
      ]
    })
  });

  const data = await response.json();
  
  // Access individual responses
  const profile = data.responses.find(r => r.id === 'profile').body;
  const items = data.responses.find(r => r.id === 'items').body;
  const notifications = data.responses.find(r => r.id === 'notifications').body;
  
  return { profile, items, notifications };
}
```

## Performance Considerations

- **Transaction Management**: Batch operations use a single database transaction, reducing overhead
- **Connection Pooling**: Fewer connections are used, improving connection pool efficiency
- **Network Latency**: Reduced number of round-trips minimizes network latency impact
- **Memory Usage**: Batching can increase memory usage, so batch sizes should be reasonable
- **Error Handling**: If one operation in a batch fails, the entire batch may be rolled back

## Best Practices

1. **Batch Size**: Keep batch sizes reasonable (typically 50-100 items)
2. **Related Data**: Use batching for fetching related data rather than eager loading
3. **Independent Operations**: Batch independent operations that don't depend on each other
4. **Error Handling**: Implement proper error handling for batch operations
5. **Monitoring**: Monitor batch performance to identify optimization opportunities
6. **Pagination**: Use pagination with batching for large result sets
7. **Caching**: Combine batching with caching for frequently accessed data
8. **Testing**: Test batch operations with various batch sizes to find optimal performance

By following these guidelines, you can effectively use the query batching system to improve the performance of your application.
