# Rate Limiting Implementation

This document describes the rate limiting implementation for the RentUp platform.

## Overview

Rate limiting is a critical component for protecting API endpoints from abuse, ensuring fair usage, and maintaining system stability. The RentUp platform implements a comprehensive rate limiting solution with the following features:

1. **Redis-based Sliding Window**: Efficient and accurate rate limiting using Redis sorted sets
2. **Tiered Rate Limits**: Different rate limits for different user types and endpoints
3. **Middleware Integration**: Automatic rate limiting for all API endpoints
4. **Programmatic Access**: Service for custom rate limiting in business logic
5. **Action-specific Limits**: Different limits for different actions
6. **Detailed Headers**: Standard rate limit headers in responses
7. **Configurable Settings**: Easily adjustable rate limit settings

## Components

### 1. Core Rate Limiter

The `app.core.rate_limiter` module provides the foundation for rate limiting:

- `RateLimiter`: Class for checking and enforcing rate limits
- `RateLimitExceeded`: Exception for rate limit violations
- Helper functions for identifying clients and endpoints

### 2. Rate Limit Middleware

The `app.core.rate_limit_middleware` module provides automatic rate limiting for API endpoints:

- `RateLimitMiddleware`: Middleware for applying rate limits to requests
- `add_rate_limit_middleware`: Function for adding the middleware to a FastAPI app

### 3. Rate Limit Service

The `app.services.rate_limit_service` module provides programmatic access to rate limiting:

- Functions for checking and enforcing rate limits for specific actions
- User-specific rate limit calculations
- Action-specific time windows

## Rate Limit Types

The rate limiting system defines several rate limit types:

| Type | Limit | Use Case |
|------|-------|----------|
| Default | 100 requests/minute | Standard API endpoints |
| Enhanced | 300 requests/minute | Partner API endpoints |
| Action-specific | Varies | Specific actions like item creation, messaging |
| User-specific | Varies | Based on user type (regular, premium, partner, admin) |

## Configuration

Rate limiting is configured in `app.core.config.py`:

```python
# Rate limiting settings
RATE_LIMIT_DEFAULT: int = 100  # Default requests per minute
RATE_LIMIT_WINDOW: int = 60  # Default window in seconds
RATE_LIMIT_ENHANCED: int = 300  # Enhanced requests per minute for partners
RATE_LIMIT_EXCLUDE_PATHS: List[str] = [
    "/health", 
    "/metrics", 
    "/docs", 
    "/redoc", 
    "/openapi.json"
]
RATE_LIMIT_ENHANCED_PATHS: List[str] = [
    "/api/v1/partner"
]
RATE_LIMIT_DEBUG: bool = False
```

## Usage Examples

### Middleware Rate Limiting

The rate limiting middleware is automatically applied to all API endpoints:

```python
# Add rate limiting middleware
from app.core.rate_limit_middleware import add_rate_limit_middleware
add_rate_limit_middleware(
    app,
    default_limit=settings.RATE_LIMIT_DEFAULT,
    default_window=settings.RATE_LIMIT_WINDOW,
    exclude_paths=settings.RATE_LIMIT_EXCLUDE_PATHS,
    enhanced_limit_paths=settings.RATE_LIMIT_ENHANCED_PATHS,
    enhanced_limit=settings.RATE_LIMIT_ENHANCED,
    debug=settings.RATE_LIMIT_DEBUG
)
```

### Programmatic Rate Limiting

For custom rate limiting in business logic:

```python
from app.services.rate_limit_service import enforce_rate_limit_for_action

# In an API endpoint
@router.post("/items/")
def create_item(item: ItemCreate, db: Session = Depends(get_db), current_user = Depends(get_current_user)):
    # Enforce rate limit for item creation
    enforce_rate_limit_for_action(current_user.id, "item_create", db)
    
    # Create the item
    return create_item_in_db(db, item, current_user.id)
```

### Checking Rate Limits

To check if a rate limit has been exceeded without enforcing it:

```python
from app.services.rate_limit_service import check_rate_limit_for_action

# Check if the rate limit has been exceeded
allowed, headers = check_rate_limit_for_action(user_id, "message_send")

if allowed:
    # Send the message
    send_message(message)
else:
    # Handle rate limit exceeded
    return {"error": "Rate limit exceeded", "retry_after": headers["X-RateLimit-Reset"]}
```

### Resetting Rate Limits

To reset a rate limit for a user and action:

```python
from app.services.rate_limit_service import reset_rate_limit_for_action

# Reset the rate limit for a user and action
reset_rate_limit_for_action(user_id, "item_create")
```

## Rate Limit Headers

The rate limiting system adds the following headers to all responses:

- `X-RateLimit-Limit`: Maximum requests per time window
- `X-RateLimit-Remaining`: Remaining requests in the current window
- `X-RateLimit-Reset`: Unix timestamp when the rate limit resets

## Rate Limit Exceeded Response

When a rate limit is exceeded, the API returns a 429 Too Many Requests response:

```json
{
  "error": {
    "code": "rate_limit_exceeded",
    "message": "Rate limit exceeded. Limit: 100 requests per 60 seconds.",
    "details": {
      "limit": 100,
      "window": 60,
      "reset": 1621234567,
      "retry_after": 30
    }
  }
}
```

The response also includes a `Retry-After` header indicating the number of seconds to wait before retrying.

## Action-specific Rate Limits

The following action-specific rate limits are defined:

| Action | Limit | Window |
|--------|-------|--------|
| item_create | 20 | 1 hour |
| rental_request | 50 | 1 hour |
| message_send | 100 | 1 hour |
| search | 200 | 1 hour |
| review_submit | 10 | 1 hour |
| bid_submit | 30 | 1 hour |
| report_submit | 5 | 1 hour |

## User-specific Rate Limits

Rate limits are adjusted based on user type:

- Regular users: Standard limits
- Premium users: 2x standard limits
- Partners: 3x standard limits
- Admins: Unlimited

## Best Practices

1. **Use Appropriate Rate Limits**: Choose the right rate limits based on endpoint sensitivity and resource usage
2. **Include Retry-After Headers**: Help clients know when to retry
3. **Monitor Rate Limit Violations**: Track and analyze rate limit violations to identify abuse
4. **Adjust Limits as Needed**: Regularly review and adjust rate limits based on usage patterns
5. **Use Action-specific Limits**: Apply different limits to different actions based on their impact

## Next Steps

After implementing rate limiting, consider these additional performance improvements:

1. Optimize vector search performance
2. Implement query batching
3. Add request compression
4. Optimize authentication flow
5. Implement background processing for heavy tasks
6. Add API response caching
