# Production Deployment Guide for RentUp Backend

This guide provides comprehensive instructions for deploying the optimized RentUp backend to a production environment.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Architecture Overview](#architecture-overview)
3. [Infrastructure Setup](#infrastructure-setup)
4. [Database Setup](#database-setup)
5. [Application Deployment](#application-deployment)
6. [Configuration](#configuration)
7. [Monitoring Setup](#monitoring-setup)
8. [Security Considerations](#security-considerations)
9. [Scaling Strategies](#scaling-strategies)
10. [Backup and Recovery](#backup-and-recovery)
11. [Troubleshooting](#troubleshooting)

## Prerequisites

Before deploying the RentUp backend to production, ensure you have the following:

- Access to a cloud provider (AWS, GCP, Azure) or on-premises infrastructure
- Docker and Docker Compose installed
- Kubernetes cluster (optional, for advanced deployments)
- PostgreSQL 14+ database server(s)
- Domain name and SSL certificates
- CI/CD pipeline (optional, but recommended)
- Monitoring tools (Prometheus, Grafana, etc.)

## Architecture Overview

The optimized RentUp backend uses a scalable architecture with the following components:

1. **Application Servers**: FastAPI application servers running in Docker containers
2. **Database Layer**:
   - Primary PostgreSQL database for write operations
   - Read replicas for read operations
   - Database shards for horizontal scaling
3. **Caching Layer**:
   - Query result cache
   - Query plan cache
4. **Monitoring Layer**:
   - Performance monitoring
   - Automated fine-tuning

## Infrastructure Setup

### Cloud Provider Setup (AWS Example)

1. **VPC and Networking**:
   ```bash
   # Create VPC with public and private subnets
   aws ec2 create-vpc --cidr-block 10.0.0.0/16
   
   # Create subnets
   aws ec2 create-subnet --vpc-id <vpc-id> --cidr-block ********/24 --availability-zone us-east-1a
   aws ec2 create-subnet --vpc-id <vpc-id> --cidr-block ********/24 --availability-zone us-east-1b
   ```

2. **Security Groups**:
   ```bash
   # Create security group for application servers
   aws ec2 create-security-group --group-name rentup-app --description "RentUp Application Servers"
   
   # Create security group for database servers
   aws ec2 create-security-group --group-name rentup-db --description "RentUp Database Servers"
   ```

3. **Load Balancer**:
   ```bash
   # Create application load balancer
   aws elbv2 create-load-balancer --name rentup-lb --subnets <subnet-id-1> <subnet-id-2>
   ```

### Docker Setup

1. Create a production Docker Compose file:

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    restart: always
    environment:
      - POSTGRES_SERVER=${POSTGRES_SERVER}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - READ_REPLICAS_ENABLED=true
      - READ_REPLICA_COUNT=${READ_REPLICA_COUNT}
      - SHARDING_ENABLED=${SHARDING_ENABLED}
      - SHARD_COUNT=${SHARD_COUNT}
    ports:
      - "8000:8000"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

2. Create a production Dockerfile:

```dockerfile
# Dockerfile.prod
FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Run with Gunicorn
CMD ["gunicorn", "app.main:app", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
```

## Database Setup

### Primary Database Setup

1. **Create Primary PostgreSQL Instance**:
   ```bash
   # Using AWS RDS (example)
   aws rds create-db-instance \
     --db-instance-identifier rentup-primary \
     --db-instance-class db.m5.large \
     --engine postgres \
     --engine-version 14.5 \
     --master-username rentup \
     --master-user-password <secure-password> \
     --allocated-storage 100 \
     --backup-retention-period 7 \
     --multi-az
   ```

2. **Configure for Replication**:
   ```bash
   # Run our setup script
   python backend/scripts/setup_postgres.py --host <primary-db-host> --port 5432 --user rentup --password <secure-password> --db rentup
   ```

### Read Replicas Setup

1. **Create Read Replicas**:
   ```bash
   # Using AWS RDS (example)
   aws rds create-db-instance-read-replica \
     --db-instance-identifier rentup-replica-1 \
     --source-db-instance-identifier rentup-primary
   
   aws rds create-db-instance-read-replica \
     --db-instance-identifier rentup-replica-2 \
     --source-db-instance-identifier rentup-primary
   ```

2. **Configure Read Replicas**:
   ```bash
   # Run our setup script
   python backend/scripts/setup_read_replicas.py --primary-host <primary-db-host> --replica-count 2 --update-config
   ```

### Database Sharding Setup

1. **Create Database Shards**:
   ```bash
   # Using AWS RDS (example)
   for i in {0..3}; do
     aws rds create-db-instance \
       --db-instance-identifier rentup-shard-$i \
       --db-instance-class db.m5.large \
       --engine postgres \
       --engine-version 14.5 \
       --master-username rentup \
       --master-user-password <secure-password> \
       --allocated-storage 100
   done
   ```

2. **Configure Sharding**:
   ```bash
   # Run our setup script
   python backend/scripts/setup_database_sharding.py --shard-count 4 --update-config
   ```

## Application Deployment

### Using Docker Compose

1. **Deploy with Docker Compose**:
   ```bash
   # Set environment variables
   export POSTGRES_SERVER=<primary-db-host>
   export POSTGRES_USER=rentup
   export POSTGRES_PASSWORD=<secure-password>
   export POSTGRES_DB=rentup
   export READ_REPLICA_COUNT=2
   export SHARDING_ENABLED=true
   export SHARD_COUNT=4
   
   # Deploy
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Using Kubernetes

1. **Create Kubernetes Deployment**:
   ```yaml
   # rentup-deployment.yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: rentup-backend
   spec:
     replicas: 3
     selector:
       matchLabels:
         app: rentup-backend
     template:
       metadata:
         labels:
           app: rentup-backend
       spec:
         containers:
         - name: rentup-backend
           image: rentup/backend:latest
           ports:
           - containerPort: 8000
           env:
           - name: POSTGRES_SERVER
             valueFrom:
               secretKeyRef:
                 name: rentup-db-secrets
                 key: db_host
           - name: POSTGRES_USER
             valueFrom:
               secretKeyRef:
                 name: rentup-db-secrets
                 key: db_user
           - name: POSTGRES_PASSWORD
             valueFrom:
               secretKeyRef:
                 name: rentup-db-secrets
                 key: db_password
           - name: POSTGRES_DB
             valueFrom:
               secretKeyRef:
                 name: rentup-db-secrets
                 key: db_name
           - name: READ_REPLICAS_ENABLED
             value: "true"
           - name: READ_REPLICA_COUNT
             value: "2"
           - name: SHARDING_ENABLED
             value: "true"
           - name: SHARD_COUNT
             value: "4"
           livenessProbe:
             httpGet:
               path: /health
               port: 8000
             initialDelaySeconds: 30
             periodSeconds: 10
           readinessProbe:
             httpGet:
               path: /health
               port: 8000
             initialDelaySeconds: 5
             periodSeconds: 5
   ```

2. **Create Kubernetes Service**:
   ```yaml
   # rentup-service.yaml
   apiVersion: v1
   kind: Service
   metadata:
     name: rentup-backend
   spec:
     selector:
       app: rentup-backend
     ports:
     - port: 80
       targetPort: 8000
     type: LoadBalancer
   ```

3. **Deploy to Kubernetes**:
   ```bash
   kubectl apply -f rentup-deployment.yaml
   kubectl apply -f rentup-service.yaml
   ```

## Configuration

### Environment Variables

Set the following environment variables for production:

```
# Database Configuration
POSTGRES_SERVER=<primary-db-host>
POSTGRES_USER=rentup
POSTGRES_PASSWORD=<secure-password>
POSTGRES_DB=rentup
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800

# Read Replica Configuration
READ_REPLICAS_ENABLED=true
READ_REPLICA_COUNT=2
READ_REPLICA_LOAD_BALANCING=round_robin
READ_PREFERENCE=replica
FALLBACK_TO_PRIMARY=true
MAX_REPLICA_LAG=30.0
HEALTH_CHECK_INTERVAL=60.0

# Sharding Configuration
SHARDING_ENABLED=true
SHARD_COUNT=4

# Caching Configuration
QUERY_CACHE_ENABLED=true
QUERY_CACHE_TTL=3600
PLAN_CACHE_ENABLED=true
PLAN_CACHE_TTL=3600
```

## Monitoring Setup

### Set Up Monitoring

1. **Deploy Monitoring Stack**:
   ```bash
   # Create monitoring directory
   mkdir -p monitoring
   
   # Start monitoring
   python backend/scripts/monitor_database_performance.py --interval 60 --output-dir monitoring
   ```

2. **Set Up Prometheus and Grafana** (optional):
   ```bash
   # Deploy Prometheus and Grafana using Docker Compose
   docker-compose -f monitoring/docker-compose.yml up -d
   ```

### Automated Fine-Tuning

1. **Schedule Fine-Tuning**:
   ```bash
   # Add to crontab
   echo "0 0 * * 0 python /path/to/backend/scripts/fine_tune_optimizations.py --monitoring-dir /path/to/monitoring --apply >> /var/log/rentup/fine_tune.log 2>&1" | crontab -
   ```

## Security Considerations

1. **Database Security**:
   - Use strong, unique passwords for database users
   - Restrict database access to application servers only
   - Enable SSL for database connections
   - Regularly audit database access

2. **Application Security**:
   - Keep dependencies up to date
   - Implement proper authentication and authorization
   - Use HTTPS for all communications
   - Implement rate limiting and request validation

3. **Infrastructure Security**:
   - Use private subnets for database servers
   - Implement network security groups
   - Enable VPC flow logs
   - Use IAM roles with least privilege

## Scaling Strategies

### Horizontal Scaling

1. **Application Scaling**:
   - Increase the number of application servers
   - Use auto-scaling based on CPU/memory usage

2. **Database Scaling**:
   - Add more read replicas for read-heavy workloads
   - Add more shards for write-heavy workloads

### Vertical Scaling

1. **Application Scaling**:
   - Increase CPU/memory for application servers

2. **Database Scaling**:
   - Upgrade database instance types
   - Increase allocated storage

## Backup and Recovery

### Database Backups

1. **Automated Backups**:
   - Enable automated backups for all database instances
   - Set appropriate retention period (e.g., 7 days)

2. **Manual Backups**:
   ```bash
   # Create manual backup
   pg_dump -h <db-host> -U rentup -d rentup -F c -f rentup_backup_$(date +%Y%m%d).dump
   ```

### Recovery Procedures

1. **Point-in-Time Recovery**:
   ```bash
   # Restore from backup
   pg_restore -h <db-host> -U rentup -d rentup -c rentup_backup_20230101.dump
   ```

2. **Disaster Recovery**:
   - Maintain database replicas in different availability zones
   - Document step-by-step recovery procedures

## Troubleshooting

### Common Issues

1. **Connection Pool Exhaustion**:
   - Increase `DB_POOL_SIZE` and `DB_MAX_OVERFLOW`
   - Check for connection leaks in the application

2. **Slow Queries**:
   - Run `python backend/scripts/optimize_joins.py --analyze`
   - Check for missing indexes

3. **Replica Lag**:
   - Check network bandwidth between primary and replicas
   - Reduce write load on primary database

### Diagnostic Tools

1. **Database Diagnostics**:
   ```bash
   # Check database status
   python backend/scripts/test_db_connection.py --verbose
   ```

2. **Performance Testing**:
   ```bash
   # Run comprehensive tests
   python backend/scripts/run_comprehensive_tests.py --all
   ```

3. **Monitoring Checks**:
   ```bash
   # Check latest monitoring data
   cat monitoring/latest.json
   ```
