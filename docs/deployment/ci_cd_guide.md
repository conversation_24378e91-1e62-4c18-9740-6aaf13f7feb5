# CI/CD Guide for RentUp Backend

This document provides comprehensive information about the Continuous Integration and Continuous Deployment (CI/CD) pipeline for the RentUp backend.

## Table of Contents

1. [Introduction](#introduction)
2. [CI/CD Pipeline Overview](#cicd-pipeline-overview)
3. [Supported CI/CD Platforms](#supported-cicd-platforms)
4. [Pipeline Stages](#pipeline-stages)
5. [Environment Configuration](#environment-configuration)
6. [Deployment Strategy](#deployment-strategy)
7. [Security in CI/CD](#security-in-cicd)
8. [Monitoring and Notifications](#monitoring-and-notifications)
9. [Troubleshooting](#troubleshooting)

## Introduction

The RentUp backend uses a comprehensive CI/CD pipeline to automate the testing, building, and deployment processes. This ensures that code changes are thoroughly tested and securely deployed to production.

## CI/CD Pipeline Overview

The CI/CD pipeline for the RentUp backend consists of the following stages:

1. **Lint**: Check code quality and style
2. **Test**: Run unit and integration tests
3. **Security**: Perform security scans and audits
4. **Build**: Build Docker image
5. **Deploy to Staging**: Deploy to staging environment
6. **Deploy to Production**: Deploy to production environment

The pipeline is triggered automatically when changes are pushed to the repository, with different behaviors based on the branch:

- **Feature branches**: Run lint, test, and security stages
- **Develop branch**: Run all stages and deploy to staging
- **Main branch**: Run all stages and deploy to production (with manual approval)

## Supported CI/CD Platforms

The RentUp backend supports multiple CI/CD platforms:

### GitHub Actions

Configuration file: `.github/workflows/ci.yml`

To use GitHub Actions:

1. Ensure the repository is hosted on GitHub
2. Configure the required secrets in the repository settings:
   - `KUBE_CONFIG`: Kubernetes configuration
   - `SLACK_WEBHOOK_URL`: Slack webhook URL for notifications

### GitLab CI/CD

Configuration file: `.gitlab-ci.yml`

To use GitLab CI/CD:

1. Ensure the repository is hosted on GitLab
2. Configure the required variables in the CI/CD settings:
   - `KUBE_CONFIG`: Kubernetes configuration
   - `SLACK_WEBHOOK_URL`: Slack webhook URL for notifications

### Jenkins

Configuration file: `Jenkinsfile`

To use Jenkins:

1. Set up a Jenkins server with the Kubernetes plugin
2. Create a pipeline job pointing to the repository
3. Configure the required credentials in Jenkins:
   - `docker-registry-credentials`: Docker registry credentials
   - `kube-config`: Kubernetes configuration

## Pipeline Stages

### Lint

The lint stage checks code quality and style using the following tools:

- **flake8**: Check for syntax errors and code style issues
- **black**: Check code formatting
- **isort**: Check import order
- **mypy**: Check type annotations

Example command:
```bash
flake8 backend --count --select=E9,F63,F7,F82 --show-source --statistics
black --check backend
isort --check-only --profile black backend
mypy backend
```

### Test

The test stage runs unit and integration tests using pytest:

Example command:
```bash
cd backend
pytest --cov=app --cov-report=xml
```

The test stage requires a PostgreSQL database, which is provided as a service in the CI/CD environment.

### Security

The security stage performs security scans and audits using the following tools:

- **bandit**: Check for common security issues in Python code
- **safety**: Check for known vulnerabilities in dependencies
- **security_audit.py**: Custom security audit script

Example command:
```bash
bandit -r backend/app -f json -o bandit-results.json
safety check -r backend/requirements.txt --json > safety-results.json
python scripts/security_audit.py --all --output security_audit_report.json
```

### Build

The build stage builds a Docker image for the backend:

Example command:
```bash
docker build -t $DOCKER_IMAGE -f backend/Dockerfile.prod ./backend
docker push $DOCKER_IMAGE
```

### Deploy to Staging

The deploy to staging stage deploys the backend to the staging environment:

1. Update the image tag in the Kubernetes deployment
2. Apply the Kubernetes manifests
3. Wait for the deployment to complete
4. Run database migrations
5. Run security tests
6. Notify the deployment

### Deploy to Production

The deploy to production stage deploys the backend to the production environment:

1. Require manual approval (except in GitHub Actions)
2. Update the image tag in the Kubernetes deployment
3. Apply the Kubernetes manifests
4. Wait for the deployment to complete
5. Run database migrations
6. Notify the deployment

## Environment Configuration

The CI/CD pipeline uses the following environment variables:

- **POSTGRES_USER**: PostgreSQL user for tests
- **POSTGRES_PASSWORD**: PostgreSQL password for tests
- **POSTGRES_DB**: PostgreSQL database name for tests
- **POSTGRES_HOST**: PostgreSQL host for tests
- **POSTGRES_PORT**: PostgreSQL port for tests
- **SECRET_KEY**: Secret key for tests
- **ENVIRONMENT**: Environment name (test, staging, production)

For deployment, the following secrets are required:

- **KUBE_CONFIG**: Kubernetes configuration
- **SLACK_WEBHOOK_URL**: Slack webhook URL for notifications
- **DOCKER_REGISTRY_CREDENTIALS**: Docker registry credentials (Jenkins only)

## Deployment Strategy

The RentUp backend uses a blue-green deployment strategy to ensure zero-downtime deployments:

1. Deploy the new version alongside the existing version
2. Run database migrations
3. Switch traffic to the new version
4. Monitor the new version
5. Remove the old version if the new version is stable

This is implemented using Kubernetes rolling updates, which ensure that the new version is fully operational before removing the old version.

## Security in CI/CD

The CI/CD pipeline includes several security measures:

1. **Security Scanning**: Automated security scanning using bandit and safety
2. **Security Testing**: Automated security testing using the custom security testing script
3. **Secrets Management**: Secrets are stored securely in the CI/CD platform
4. **Image Scanning**: Docker images are scanned for vulnerabilities
5. **Manual Approval**: Production deployments require manual approval (except in GitHub Actions)

## Monitoring and Notifications

The CI/CD pipeline includes monitoring and notifications:

1. **Build Status**: Build status is reported to the CI/CD platform
2. **Deployment Notifications**: Deployment notifications are sent to Slack
3. **Test Coverage**: Test coverage is reported to the CI/CD platform
4. **Security Reports**: Security reports are stored as artifacts

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check the build logs for errors
   - Ensure all dependencies are installed
   - Verify that the code passes linting and tests locally

2. **Deployment Failures**:
   - Check the Kubernetes logs for errors
   - Verify that the Kubernetes configuration is correct
   - Ensure that the database migrations run successfully

3. **Security Scan Failures**:
   - Review the security reports for issues
   - Fix any identified security vulnerabilities
   - Update dependencies to secure versions

### Getting Help

If you encounter issues with the CI/CD pipeline, contact the DevOps <NAME_EMAIL>.
