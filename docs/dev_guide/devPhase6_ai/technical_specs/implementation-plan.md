# Implementation Plan for Domain-Specific AI Agents

## Overview

This document outlines the technical implementation plan for each AI agent in RentUp's Mixture of Experts (MoE) architecture. It includes specifications, integration points, data flows, monitoring strategies, and sample code for each specialized agent.

## 1. Central Router Component

The central router is responsible for directing requests to the appropriate specialized agent based on the request type, context, and performance requirements.

### Technical Specifications

**Input Format:**
```json
{
  "request_type": "recommendation|fraud_detection|pricing|user_analysis|content_moderation",
  "context": {
    "user_id": "string",
    "item_id": "string|null",
    "session_data": {},
    "additional_parameters": {}
  },
  "performance_requirements": {
    "max_latency_ms": 500,
    "min_confidence": 0.7
  }
}
```

**Output Format:**
```json
{
  "agent_selected": "string",
  "confidence": 0.95,
  "processing_time_ms": 45,
  "result": {},
  "fallback_used": false,
  "explanation": "string"
}
```

### Integration Points

- **File:** `backend/app/ai/router.py`
- **Function:** `route_request(request_data: dict) -> dict`
- **Dependencies:**
  - `backend/app/ai/models/request_models.py` (Pydantic models for request/response)
  - `backend/app/ai/utils/performance_tracker.py` (Latency and performance monitoring)

### Data Flow

1. Request received by API endpoint
2. Request validated and normalized
3. Router analyzes request type and context
4. Router selects appropriate agent based on decision tree
5. Request forwarded to selected agent
6. Response received from agent
7. Response enriched with metadata and returned

### Monitoring and Evaluation

- **Metrics:**
  - Routing accuracy (% of requests routed to optimal agent)
  - Routing latency (time to make routing decision)
  - Fallback rate (% of requests using fallback agent)
  
- **Logging:**
  - All routing decisions with rationale
  - Performance metrics for each routed request
  - Errors and exceptions during routing

### Error Handling

- If routing decision confidence below threshold, use fallback strategy
- If selected agent unavailable, route to next best agent
- If all agents unavailable, use cached responses or default values
- Circuit breaker pattern for failing agents

### Sample Code

```python
# backend/app/ai/router.py
from fastapi import HTTPException
from typing import Dict, Any, Optional
from pydantic import BaseModel

from app.ai.models.request_models import AIRequest, AIResponse
from app.ai.utils.performance_tracker import track_performance
from app.ai.recommendation import recommendation_agent
from app.ai.fraud_detection import fraud_detection_agent
from app.ai.pricing import pricing_agent
from app.ai.user_analysis import user_analysis_agent
from app.ai.content_moderation import content_moderation_agent

class Router:
    def __init__(self):
        self.agents = {
            "recommendation": recommendation_agent,
            "fraud_detection": fraud_detection_agent,
            "pricing": pricing_agent,
            "user_analysis": user_analysis_agent,
            "content_moderation": content_moderation_agent
        }
        self.fallback_agents = {
            "recommendation": self._get_fallback_agent("recommendation"),
            "fraud_detection": self._get_fallback_agent("fraud_detection"),
            "pricing": self._get_fallback_agent("pricing"),
            "user_analysis": self._get_fallback_agent("user_analysis"),
            "content_moderation": self._get_fallback_agent("content_moderation")
        }
    
    def _get_fallback_agent(self, agent_type: str):
        # Return appropriate fallback agent based on type
        # Implementation details omitted for brevity
        pass
    
    @track_performance
    async def route_request(self, request: AIRequest) -> AIResponse:
        """Route the request to the appropriate agent based on type and context."""
        try:
            # Determine the appropriate agent
            agent_type = request.request_type
            
            # Check if we have an agent for this type
            if agent_type not in self.agents:
                raise HTTPException(status_code=400, detail=f"Unknown agent type: {agent_type}")
            
            # Select the agent
            agent = self.agents[agent_type]
            fallback_used = False
            confidence = 0.95  # Simplified for example
            
            # Apply performance requirements
            if request.performance_requirements:
                # Logic to select faster agent if needed
                if request.performance_requirements.max_latency_ms < 200:
                    agent = self.fallback_agents[agent_type]
                    fallback_used = True
                    confidence = 0.85
            
            # Process the request with the selected agent
            start_time = time.time()
            result = await agent.process(request.context)
            processing_time_ms = (time.time() - start_time) * 1000
            
            # Return the response
            return AIResponse(
                agent_selected=agent_type,
                confidence=confidence,
                processing_time_ms=processing_time_ms,
                result=result,
                fallback_used=fallback_used,
                explanation=f"Selected {agent_type} agent based on request type"
            )
            
        except Exception as e:
            # Log the error
            logger.error(f"Error routing request: {str(e)}")
            
            # Try fallback if available
            if agent_type in self.fallback_agents:
                try:
                    result = await self.fallback_agents[agent_type].process(request.context)
                    return AIResponse(
                        agent_selected=agent_type,
                        confidence=0.7,
                        processing_time_ms=0,  # Will be filled by decorator
                        result=result,
                        fallback_used=True,
                        explanation=f"Used fallback agent due to error: {str(e)}"
                    )
                except Exception as fallback_error:
                    logger.error(f"Fallback agent also failed: {str(fallback_error)}")
            
            # If we get here, both primary and fallback failed
            raise HTTPException(status_code=500, detail="AI processing failed")

# Initialize the router
router = Router()

# FastAPI endpoint
@router.post("/ai/process")
async def process_ai_request(request: AIRequest) -> AIResponse:
    return await router.route_request(request)
```

## 2. Recommendation Agent

The recommendation agent is responsible for item matching and personalized recommendations based on user preferences and behavior.

### Technical Specifications

**Input Format:**
```json
{
  "user_id": "string",
  "item_id": "string|null",
  "recommendation_type": "similar_items|personalized|trending|category_based",
  "filters": {
    "category": "string|null",
    "price_range": {"min": 0, "max": 1000},
    "location": "string|null"
  },
  "limit": 10
}
```

**Output Format:**
```json
{
  "recommendations": [
    {
      "item_id": "string",
      "score": 0.95,
      "explanation": "string"
    }
  ],
  "metadata": {
    "model_used": "string",
    "filters_applied": {},
    "processing_time_ms": 120
  }
}
```

### Integration Points

- **File:** `backend/app/ai/recommendation/agent.py`
- **Function:** `process(context: dict) -> dict`
- **Dependencies:**
  - `backend/app/models/item.py` (Item database model)
  - `backend/app/models/user.py` (User database model)
  - `backend/app/core/qdrant.py` (Vector database client)

### Data Flow

1. Receive recommendation request with context
2. Retrieve user data and preferences
3. Generate embeddings for query or reference item
4. Perform vector search in Qdrant
5. Apply business rules and filters
6. Return ranked recommendations

### Monitoring and Evaluation

- **Metrics:**
  - Recommendation relevance (via feedback and click-through rates)
  - Latency (time to generate recommendations)
  - Diversity score (variety in recommendations)
  
- **Evaluation:**
  - A/B testing different recommendation strategies
  - Offline evaluation using historical interaction data
  - User satisfaction surveys

### Error Handling

- Cache popular recommendations for fallback
- Default to category-based recommendations if personalization fails
- Graceful degradation to simpler models under high load

### Sample Code

```python
# backend/app/ai/recommendation/agent.py
from typing import Dict, List, Any, Optional
import time
import numpy as np
from fastapi import Depends
from sqlalchemy.orm import Session

from app.core.qdrant import search_vectors
from app.core.database import get_db
from app.models.item import Item
from app.models.user import User
from app.ai.utils.embedding_service import get_embedding
from app.ai.recommendation.models import RecommendationRequest, RecommendationResponse, RecommendationItem

class RecommendationAgent:
    async def process(self, context: Dict[str, Any], db: Session = Depends(get_db)) -> Dict[str, Any]:
        """Process a recommendation request and return personalized recommendations."""
        start_time = time.time()
        
        # Parse the request
        request = RecommendationRequest(**context)
        
        # Determine recommendation strategy
        if request.recommendation_type == "similar_items" and request.item_id:
            recommendations = await self._get_similar_items(request.item_id, request.filters, request.limit, db)
        elif request.recommendation_type == "personalized" and request.user_id:
            recommendations = await self._get_personalized_recommendations(request.user_id, request.filters, request.limit, db)
        elif request.recommendation_type == "trending":
            recommendations = await self._get_trending_items(request.filters, request.limit, db)
        else:
            recommendations = await self._get_category_based(request.filters.get("category"), request.filters, request.limit, db)
        
        # Calculate processing time
        processing_time_ms = (time.time() - start_time) * 1000
        
        # Return formatted response
        return RecommendationResponse(
            recommendations=recommendations,
            metadata={
                "model_used": "text-embedding-3-large",
                "filters_applied": request.filters,
                "processing_time_ms": processing_time_ms
            }
        ).dict()
    
    async def _get_similar_items(self, item_id: str, filters: Dict, limit: int, db: Session) -> List[RecommendationItem]:
        """Get items similar to the specified item."""
        # Get the reference item
        item = db.query(Item).filter(Item.id == item_id).first()
        if not item:
            return []
        
        # Get embedding for the item
        item_content = f"{item.name} {item.description} {item.category} {item.subcategory}"
        item_embedding = await get_embedding(item_content)
        
        # Search for similar items
        search_results = search_vectors(
            query_vector=item_embedding,
            limit=limit * 2,  # Get more than needed to allow for filtering
            filter_condition=self._convert_filters_to_qdrant(filters)
        )
        
        # Format results
        recommendations = []
        for result in search_results:
            if result["id"] != item_id:  # Exclude the reference item
                recommendations.append(
                    RecommendationItem(
                        item_id=result["id"],
                        score=float(result["score"]),
                        explanation=f"Similar to {item.name} in {item.category}"
                    )
                )
                if len(recommendations) >= limit:
                    break
        
        return recommendations
    
    # Other methods (_get_personalized_recommendations, _get_trending_items, etc.) omitted for brevity
    
    def _convert_filters_to_qdrant(self, filters: Dict) -> Dict:
        """Convert API filters to Qdrant filter format."""
        qdrant_filter = {}
        
        if filters.get("category"):
            qdrant_filter["category"] = {"$eq": filters["category"]}
        
        if filters.get("price_range"):
            price_range = filters["price_range"]
            if "min" in price_range:
                qdrant_filter["daily_price"] = {"$gte": price_range["min"]}
            if "max" in price_range:
                if "daily_price" not in qdrant_filter:
                    qdrant_filter["daily_price"] = {}
                qdrant_filter["daily_price"]["$lte"] = price_range["max"]
        
        if filters.get("location"):
            qdrant_filter["location"] = {"$eq": filters["location"]}
        
        return qdrant_filter

# Initialize the agent
recommendation_agent = RecommendationAgent()
```

## 3. Fraud Detection Agent

The fraud detection agent is responsible for identifying suspicious patterns and assessing risk in user behavior and transactions.

### Technical Specifications

**Input Format:**
```json
{
  "event_type": "user_registration|item_listing|rental_request|message|payment",
  "user_id": "string",
  "item_id": "string|null",
  "transaction_id": "string|null",
  "event_data": {},
  "user_history": {},
  "risk_threshold": 0.7
}
```

**Output Format:**
```json
{
  "risk_score": 0.85,
  "risk_level": "low|medium|high",
  "flagged": true,
  "risk_factors": [
    {
      "factor": "string",
      "contribution": 0.4,
      "explanation": "string"
    }
  ],
  "recommended_action": "allow|review|block",
  "confidence": 0.9
}
```

### Integration Points

- **File:** `backend/app/ai/fraud_detection/agent.py`
- **Function:** `process(context: dict) -> dict`
- **Dependencies:**
  - `backend/app/models/user.py` (User database model)
  - `backend/app/models/transaction.py` (Transaction database model)
  - `backend/app/models/fraud_alert.py` (Fraud alert database model)

### Data Flow

1. Receive event data for risk assessment
2. Retrieve historical user data and patterns
3. Apply risk scoring models based on event type
4. Generate risk factors and explanations
5. Determine recommended action based on risk level
6. Log assessment results for future analysis

### Monitoring and Evaluation

- **Metrics:**
  - False positive rate (legitimate users flagged)
  - False negative rate (fraud missed)
  - Risk distribution across user base
  - Action recommendation accuracy
  
- **Evaluation:**
  - Regular review of flagged cases by trust & safety team
  - Feedback loop for continuous model improvement
  - Periodic retraining with new fraud patterns

### Error Handling

- Default to conservative risk assessment if model fails
- Maintain backup statistical models for core fraud patterns
- Circuit breaker pattern for external API dependencies

### Sample Code

```python
# backend/app/ai/fraud_detection/agent.py
from typing import Dict, List, Any, Optional
import time
import cohere
from fastapi import Depends
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.database import get_db
from app.models.user import User
from app.models.transaction import Transaction
from app.models.fraud_alert import FraudAlert
from app.ai.fraud_detection.models import FraudDetectionRequest, FraudDetectionResponse, RiskFactor

class FraudDetectionAgent:
    def __init__(self):
        self.cohere_client = cohere.Client(settings.COHERE_API_KEY)
        
    async def process(self, context: Dict[str, Any], db: Session = Depends(get_db)) -> Dict[str, Any]:
        """Process a fraud detection request and return risk assessment."""
        start_time = time.time()
        
        # Parse the request
        request = FraudDetectionRequest(**context)
        
        # Get risk assessment based on event type
        if request.event_type == "user_registration":
            assessment = await self._assess_registration_risk(request, db)
        elif request.event_type == "item_listing":
            assessment = await self._assess_listing_risk(request, db)
        elif request.event_type == "rental_request":
            assessment = await self._assess_rental_risk(request, db)
        elif request.event_type == "payment":
            assessment = await self._assess_payment_risk(request, db)
        else:
            assessment = await self._assess_generic_risk(request, db)
        
        # Determine risk level based on score
        risk_level = "low"
        if assessment["risk_score"] > 0.7:
            risk_level = "high"
        elif assessment["risk_score"] > 0.4:
            risk_level = "medium"
        
        # Determine recommended action
        recommended_action = "allow"
        if risk_level == "high":
            recommended_action = "block"
        elif risk_level == "medium":
            recommended_action = "review"
        
        # Create response
        response = FraudDetectionResponse(
            risk_score=assessment["risk_score"],
            risk_level=risk_level,
            flagged=assessment["risk_score"] > request.risk_threshold,
            risk_factors=assessment["risk_factors"],
            recommended_action=recommended_action,
            confidence=assessment["confidence"]
        )
        
        # Log the assessment if high risk
        if response.flagged:
            self._log_fraud_alert(request, response, db)
        
        return response.dict()
    
    async def _assess_registration_risk(self, request: FraudDetectionRequest, db: Session) -> Dict[str, Any]:
        """Assess risk for new user registration."""
        # Implementation details omitted for brevity
        pass
    
    # Other assessment methods omitted for brevity
    
    def _log_fraud_alert(self, request: FraudDetectionRequest, response: FraudDetectionResponse, db: Session):
        """Log a fraud alert for review."""
        alert = FraudAlert(
            user_id=request.user_id,
            item_id=request.item_id,
            alert_type=request.event_type,
            severity=response.risk_level,
            risk_score=response.risk_score,
            details={
                "risk_factors": [rf.dict() for rf in response.risk_factors],
                "event_data": request.event_data
            },
            status="pending"
        )
        db.add(alert)
        db.commit()

# Initialize the agent
fraud_detection_agent = FraudDetectionAgent()
```

The remaining agents (Pricing, User Analysis, and Content Moderation) follow similar patterns and will be implemented in subsequent phases of the project.
