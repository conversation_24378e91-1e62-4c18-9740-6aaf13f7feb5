# RentUp Development Guide: Phase 0

## Phase 0: Development Environment Setup

This document outlines the steps for setting up the development environment for the RentUp project. Phase 0 focuses on establishing the foundation for development, including project structure, tooling, and initial configuration.

## Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Step-by-Step Setup Guide](#step-by-step-setup-guide)
- [Testing Your Setup](#testing-your-setup)
- [Troubleshooting](#troubleshooting)
- [Next Steps](#next-steps)

## Overview

Phase 0 is the initial setup phase for the RentUp project. The goal is to establish a consistent development environment that all team members can use. This includes setting up the project structure, configuring development tools, and ensuring that the basic infrastructure is in place for future development phases.

## Prerequisites

Before beginning Phase 0, ensure you have the following installed on your system:

- **Git**: Version control system (v2.30.0+)
- **Node.js**: JavaScript runtime (v20.0.0+)
- **npm**: Package manager for Node.js (v10.0.0+)
- **Python**: Programming language (v3.12.0+)
- **Docker**: Containerization platform (v24.0.0+)
- **Docker Compose**: Multi-container Docker applications (v2.20.0+)
- **Visual Studio Code**: Recommended code editor (or your preferred editor)

## Step-by-Step Setup Guide

### 1. Clone the Repository

```bash
# Clone the repository
git clone https://github.com/agentLabsNetwork/rentup.git

# Navigate to the project directory
cd rentup
```

### 2. Set Up Frontend Environment

```bash
# Navigate to the frontend directory
cd frontend

# Install dependencies
npm install

# Verify the installation
npm run dev
```

The development server should start, and you can access the frontend at http://localhost:3000.

### 3. Set Up Backend Environment

```bash
# Navigate to the backend directory from the project root
cd backend

# Create a virtual environment
python -m venv venv

# Activate the virtual environment
# On Windows:
# venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Verify the installation
uvicorn app.main:app --reload
```

The API server should start, and you can access the API documentation at http://localhost:8000/docs.

### 4. Set Up Docker Environment

```bash
# Navigate to the project root
cd ..

# Copy the example environment file
cp .env.example .env

# Edit the .env file with your preferred editor
# Fill in the required environment variables

# Start the Docker containers
docker-compose up -d

# Verify the containers are running
docker-compose ps
```

You should see the following containers running:
- rentup-frontend
- rentup-backend
- rentup-postgres
- rentup-qdrant

### 5. Configure Development Tools

```bash
# Install recommended VS Code extensions
# (if using VS Code)
code --install-extension dbaeumer.vscode-eslint
code --install-extension esbenp.prettier-vscode
code --install-extension ms-python.python
code --install-extension ms-azuretools.vscode-docker
```

## Testing Your Setup

### Frontend Testing

1. Open your browser and navigate to http://localhost:3000
2. Verify that the RentUp homepage loads correctly
3. Check the browser console for any errors

### Backend Testing

1. Open your browser and navigate to http://localhost:8000/docs
2. Verify that the Swagger UI loads correctly
3. Try executing a simple API call, such as GET /api/v1/health

### Docker Testing

1. Run `docker-compose logs` to check for any errors in the containers
2. Verify that you can connect to the PostgreSQL database:
   ```bash
   docker-compose exec postgres psql -U postgres -d rentup
   ```
3. Verify that you can connect to the Qdrant service:
   ```bash
   curl http://localhost:6333/collections
   ```

## Troubleshooting

### Common Issues

1. **Port Conflicts**: If ports 3000, 8000, 5432, or 6333 are already in use, you may need to stop other services or change the ports in the `.env` file.

2. **Docker Permission Issues**: On Linux, you might need to add your user to the docker group:
   ```bash
   sudo usermod -aG docker $USER
   # Log out and log back in for changes to take effect
   ```

3. **Node.js Version Mismatch**: If you encounter errors related to Node.js, ensure you're using Node.js v20.0.0 or higher:
   ```bash
   node --version
   ```

4. **Python Package Installation Errors**: If you encounter errors installing Python packages, ensure your pip is up to date:
   ```bash
   pip install --upgrade pip
   ```

## Next Steps

After successfully completing Phase 0, you are ready to proceed to [Phase 1: Frontend Scaffolding](./devPhase1.md). Phase 1 focuses on building the core UI components and establishing the frontend architecture.

---

Last Updated: 2025-07-11
