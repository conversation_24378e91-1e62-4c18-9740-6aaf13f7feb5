# RentUp AI Chatbot and Rent Planner

This document provides an overview of the AI Chatbot and Rent Planner features implemented in Phase 6 of the RentUp platform development.

## Overview

The AI Chatbot and Rent Planner are part of the Mixture of Experts (MoE) AI architecture for RentUp. These features enhance user experience by providing intelligent conversational support and personalized rental planning assistance.

### AI Chatbot

The AI Chatbot provides intelligent conversational support for users of the RentUp platform. It handles customer support inquiries, feedback collection, dispute resolution, and general information requests.

### Rent Planner

The Rent Planner is an AI-powered tool that helps users plan their rental needs for various events and occasions. It provides a conversational interface for users to describe their needs, then recommends relevant items and helps them create a comprehensive rental plan.

## Architecture

### AI Chatbot Architecture

The chatbot system consists of the following components:

1. **ChatbotAgent**: The primary AI-powered agent that uses Anthropic's Claude API
2. **FallbackChatbotAgent**: A rule-based fallback agent for when the primary agent is unavailable
3. **API Endpoints**: RESTful endpoints for chat and feedback
4. **Frontend Components**: React components for the chat interface

### Rent Planner Architecture

The Rent Planner consists of the following components:

1. **RentPlannerInterface**: The main component that handles the planning process
2. **RentPlannerButton**: A floating action button that opens the planner in a modal
3. **RentPlannerPage**: A dedicated page for the planner

## Implementation Details

### AI Chatbot Implementation

The AI Chatbot is implemented with the following features:

- **Intent Detection**: Automatically detects the user's intent (support, feedback, dispute, general)
- **Contextual Responses**: Generates responses based on conversation history and context
- **Follow-up Suggestions**: Provides relevant follow-up suggestions to guide the conversation
- **Fallback Mechanism**: Includes a rule-based fallback system for when the AI is unavailable
- **Feedback Collection**: Allows users to rate and provide feedback on chatbot responses

The chatbot uses Anthropic's Claude API for generating responses, with a fallback to a rule-based system when the API is unavailable or when faster response times are needed.

### Rent Planner Implementation

The Rent Planner guides users through a 4-step process:

1. **Chat**: Users describe their rental needs in a conversational interface
2. **Items**: Users select from recommended items and provide plan details
3. **Review**: Users review their rental plan before completion
4. **Complete**: Users receive confirmation and can proceed to checkout

The Rent Planner uses a combination of AI-powered recommendations and a user-friendly interface to help users create comprehensive rental plans.

## Integration with RentUp Platform

### Backend Integration

The AI Chatbot is integrated with the RentUp backend through the following components:

- **API Endpoints**: `/api/v1/chatbot/chat` and `/api/v1/chatbot/feedback`
- **Database Models**: Chat sessions, messages, and feedback are stored in the database
- **Notification Service**: Notifications are sent to users and administrators when needed

### Frontend Integration

The AI Chatbot and Rent Planner are integrated with the RentUp frontend through the following components:

- **Floating Buttons**: Accessible from any page in the application
- **Modal Interfaces**: Open in a modal overlay for easy access
- **Dedicated Pages**: Available as dedicated pages for more focused interaction

## Testing

### AI Chatbot Testing

The AI Chatbot is tested with the following test suites:

- **Unit Tests**: Test individual components of the chatbot system
- **Integration Tests**: Test the interaction between components
- **API Tests**: Test the API endpoints
- **Frontend Tests**: Test the frontend components

### Rent Planner Testing

The Rent Planner is tested with the following test suites:

- **Unit Tests**: Test individual components of the planner system
- **Integration Tests**: Test the interaction between components
- **Frontend Tests**: Test the frontend components

## Configuration

The AI Chatbot and Rent Planner can be configured in `app/core/config.py`:

```python
# Chatbot settings
CHATBOT_MODEL: str = "claude-3-sonnet-20240229"
CHATBOT_FALLBACK_ENABLED: bool = True
CHATBOT_MAX_HISTORY: int = 10
CHATBOT_RESPONSE_TIMEOUT: int = 15  # seconds

# Agent settings for chatbot
"chatbot": {
    "cache_ttl": 600,
    "max_retries": 2,
    "max_tokens": 1000,
    "temperature": 0.3,
    "fallback_threshold_ms": 5000
}
```

## Future Improvements

### AI Chatbot Future Improvements

- Add multi-language support
- Implement more sophisticated intent detection
- Add integration with the knowledge base
- Implement conversation memory for returning users
- Add support for image uploads in the chat

### Rent Planner Future Improvements

- Add support for custom item requests
- Implement plan saving and loading
- Add integration with user preferences
- Implement more sophisticated recommendation algorithms
- Add support for location-based recommendations
- Implement plan sharing functionality

## Documentation

Detailed documentation for the AI Chatbot and Rent Planner is available in the following locations:

- **AI Chatbot Backend**: `backend/app/ai/chatbot/README.md`
- **AI Chatbot Frontend**: `frontend/src/components/Chatbot/README.md`
- **Rent Planner Frontend**: `frontend/src/components/RentPlanner/README.md`

## Conclusion

The AI Chatbot and Rent Planner are powerful additions to the RentUp platform, providing intelligent conversational support and personalized rental planning assistance. These features enhance the user experience and help users get the most out of the platform.
