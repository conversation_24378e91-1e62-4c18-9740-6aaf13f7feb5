# RentUp Development Guide: Phase 2

## Phase 2: Backend Integration

This document outlines the scope, goals, and architecture for Phase 2 of the RentUp project, which focuses on backend integration and advanced search capabilities.

## Table of Contents

- [Overview](#overview)
- [Goals](#goals)
- [Architecture](#architecture)
- [Key Components](#key-components)
- [Development Workflow](#development-workflow)
- [Testing Strategy](#testing-strategy)
- [Deployment](#deployment)

## Overview

Phase 2 builds upon the frontend scaffolding completed in Phase 1 by implementing backend integration, database connectivity, and advanced search capabilities. The primary focus is on creating a functional API layer, implementing database models, and developing the multi-modal search functionality.

## Goals

1. **Backend API Development**
   - Implement RESTful API endpoints for all core functionality
   - Create authentication and authorization system
   - Develop data validation and error handling

2. **Database Integration**
   - Set up PostgreSQL database with proper schema
   - Implement ORM models with SQLAlchemy
   - Create migration system with Alembic

3. **Vector Search Implementation**
   - Integrate Qdrant vector database
   - Implement text and image embedding generation
   - Create hybrid search combining keyword and semantic search
   - Develop multi-modal search combining text and image search

4. **Frontend-Backend Connection**
   - Connect frontend components to backend API
   - Implement authentication flow in frontend
   - Create data fetching and state management

5. **Testing and Documentation**
   - Write comprehensive tests for backend functionality
   - Document API endpoints and data models
   - Create integration tests for frontend-backend interaction

## Architecture

### Backend Architecture

The backend follows a layered architecture:

1. **API Layer**: FastAPI endpoints that handle HTTP requests and responses
2. **Service Layer**: Business logic and operations
3. **Data Access Layer**: Database models and queries
4. **Infrastructure Layer**: External services and configurations

### Search Architecture

The multi-modal search system uses a hybrid approach:

1. **Text Processing**:
   - Text preprocessing (tokenization, normalization)
   - Embedding generation using Nomic
   - Vector storage in Qdrant

2. **Image Processing**:
   - Image preprocessing (resizing, normalization)
   - Feature extraction using vision models
   - Vector storage in Qdrant

3. **Hybrid Search**:
   - Combines keyword search and semantic search
   - Weighted scoring system
   - Filtering based on metadata

4. **Multi-Modal Search**:
   - Combines text and image search results
   - Configurable weights for text vs. image
   - Unified scoring system

## Key Components

### Backend Components

1. **FastAPI Application**
   - Main entry point: `backend/app/main.py`
   - API router: `backend/app/api/v1/router.py`
   - Endpoints: `backend/app/api/v1/endpoints/*.py`

2. **Database Models**
   - User model: `backend/app/models/user.py`
   - Item model: `backend/app/models/item.py`
   - Other models: `backend/app/models/*.py`

3. **Services**
   - Embedding service: `backend/app/services/embedding_service.py`
   - Image embedding service: `backend/app/services/image_embedding_service.py`
   - Other services: `backend/app/services/*.py`

4. **Core Infrastructure**
   - Database connection: `backend/app/core/database.py`
   - Qdrant connection: `backend/app/core/qdrant.py`
   - Authentication: `backend/app/core/auth.py`

### Frontend Components

1. **API Services**
   - Item service: `frontend/src/services/itemService.ts`
   - Auth service: `frontend/src/services/authService.ts`

2. **Search Components**
   - Multi-modal search: `frontend/src/components/Search/MultiModalSearch.tsx`
   - Search results: `frontend/src/components/Search/SearchResults.tsx`
   - Search metadata: `frontend/src/components/Search/SearchMetadataDisplay.tsx`

3. **Context Providers**
   - Auth context: `frontend/src/context/AuthContext.tsx`
   - Toast context: `frontend/src/context/ToastContext.tsx`

## Development Workflow

1. **Feature Development Process**
   - Create database models
   - Implement backend services
   - Develop API endpoints
   - Write backend tests
   - Connect frontend components
   - Write frontend tests
   - Document the feature

2. **Code Review Process**
   - All code changes require pull requests
   - At least one reviewer must approve
   - All tests must pass
   - Documentation must be updated

3. **Branching Strategy**
   - `main`: Production-ready code
   - `develop`: Integration branch
   - Feature branches: `feature/feature-name`
   - Bug fix branches: `fix/bug-name`

## Testing Strategy

1. **Backend Testing**
   - Unit tests for services and utilities
   - API tests for endpoints
   - Integration tests for database operations
   - Run tests with `pytest`

2. **Frontend Testing**
   - Component tests with React Testing Library
   - Integration tests with Cypress
   - End-to-end tests for critical flows
   - Run tests with `npm test`

3. **Vector Search Testing**
   - Embedding generation tests
   - Search accuracy tests
   - Performance benchmarks
   - Run tests with `pytest backend/app/tests/test_vector_search.py`

## Deployment

1. **Local Development**
   - Run backend: `uvicorn app.main:app --reload`
   - Run frontend: `npm run dev`
   - Run with Docker: `docker-compose up`

2. **Staging Deployment**
   - Automated deployment via GitHub Actions
   - Environment: AWS EC2 instances
   - Database: AWS RDS PostgreSQL
   - Vector database: Self-hosted Qdrant

3. **Production Deployment**
   - Manual approval after staging validation
   - Blue-green deployment strategy
   - Database migrations with zero downtime
   - Monitoring with Datadog

## Conclusion

Phase 2 represents a significant step in the development of RentUp, transforming the static frontend into a fully functional application with advanced search capabilities. By following this guide, developers can ensure consistent implementation and maintain high-quality standards throughout the development process.

For detailed tasks and progress tracking, refer to the [Phase 2 Tasks](./devPhase2_tasks.md) document.
