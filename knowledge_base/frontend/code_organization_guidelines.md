# Frontend Code Organization Guidelines (May 2025)

## File Size and Structure Recommendations

### Optimal File Size

Based on extensive research and performance analysis, we recommend the following guidelines for JavaScript file sizes in the RentUp project:

- **Ideal target**: 300-500 lines per JavaScript file
- **Maximum threshold**: 600 lines before refactoring is required
- **Absolute limit**: 800 lines (files exceeding this must be refactored immediately)

These guidelines apply to all JavaScript files, including React components, utility functions, hooks, and configuration files.

### Rationale

1. **Performance Benefits**:
   - Smaller files enable more efficient code splitting and tree shaking
   - Improved initial load times through better lazy loading opportunities
   - Reduced main thread blocking during JavaScript execution
   - Better cache utilization with granular file updates

2. **Development Workflow Improvements**:
   - Enhanced AI-assisted development with 8GB VRAM / 4K context window limitations
   - More effective code reviews with focused, single-responsibility files
   - Improved debugging and testing with clearer component boundaries
   - Better team collaboration with more manageable code units

3. **Maintenance Advantages**:
   - Easier onboarding for new developers
   - Reduced cognitive load when navigating the codebase
   - More predictable impact when making changes
   - Clearer ownership and responsibility boundaries

## Implementation Strategies

### Breaking Down Large Components

When a component exceeds or approaches the recommended size limits, consider these strategies:

1. **Extract Subcomponents**:
   ```jsx
   // Before: Large monolithic component
   function ProductPage({ product }) {
     // 700+ lines of code with many responsibilities
   }

   // After: Main component with extracted subcomponents
   function ProductPage({ product }) {
     return (
       <div>
         <ProductHeader product={product} />
         <ProductGallery images={product.images} />
         <ProductDetails details={product.details} />
         <RelatedProducts ids={product.relatedIds} />
       </div>
     );
   }
   ```

2. **Extract Custom Hooks**:
   ```jsx
   // Before: Component with complex state logic
   function ShoppingCart() {
     // 100+ lines of state management
     // 400+ lines of rendering logic
   }

   // After: Component with extracted hook
   function ShoppingCart() {
     const { 
       items, 
       total, 
       addItem, 
       removeItem 
     } = useShoppingCart();
     
     return (
       // 400 lines of rendering logic
     );
   }
   ```

3. **Extract Utility Functions**:
   ```jsx
   // Before: Component with complex calculations
   function PriceCalculator({ items }) {
     // 200+ lines of calculation functions
     // 300+ lines of rendering logic
   }

   // After: Component using imported utilities
   import { calculateSubtotal, calculateTax, calculateShipping } from './price-utils';

   function PriceCalculator({ items }) {
     // 300+ lines of rendering logic that uses the imported utilities
   }
   ```

### Organizing Related Files

When breaking down large files, follow these organization patterns:

1. **Feature-Based Organization**:
   ```
   features/
   ├── product/
   │   ├── ProductPage.jsx
   │   ├── ProductHeader.jsx
   │   ├── ProductGallery.jsx
   │   ├── ProductDetails.jsx
   │   ├── useProductData.js
   │   └── product-utils.js
   ```

2. **Component + Subcomponents Pattern**:
   ```
   components/
   ├── ShoppingCart/
   │   ├── index.jsx           // Main component
   │   ├── CartItem.jsx        // Subcomponent
   │   ├── CartSummary.jsx     // Subcomponent
   │   ├── useShoppingCart.js  // Custom hook
   │   └── cart-utils.js       // Utilities
   ```

## Monitoring and Enforcement

To ensure adherence to these guidelines:

1. **Automated Checks**:
   - ESLint rule to warn when files exceed 500 lines
   - ESLint error when files exceed 800 lines
   - Pre-commit hooks to prevent committing files that violate the absolute limit

2. **Code Review Process**:
   - Reviewers should flag files approaching size limits
   - Suggest specific refactoring strategies based on this guide
   - Consider file size as part of the review criteria

3. **Refactoring Priority**:
   - Files exceeding 800 lines: High priority
   - Files between 600-800 lines: Medium priority
   - Files between 500-600 lines: Low priority

## Examples from RentUp Codebase

### Before and After Examples

**Before**: `PropertyListingPage.jsx` (950 lines)
- Combined search, filtering, map view, list view, and pagination

**After**:
- `PropertyListingPage.jsx` (150 lines) - Main container and layout
- `PropertySearchFilters.jsx` (200 lines) - Search and filter controls
- `PropertyMapView.jsx` (250 lines) - Interactive map implementation
- `PropertyListView.jsx` (180 lines) - List view of properties
- `PropertyPagination.jsx` (80 lines) - Pagination controls
- `usePropertySearch.js` (120 lines) - Search and filter logic

## Additional Resources

- [React and Next.js Best Practices](./react_nextjs_best_practices.md)
- [Frontend Testing Guide](./frontend_testing_guide.md)
- [Frontend Trends 2025](./frontend_trends_2025.md)

Last Updated: May 23, 2025
