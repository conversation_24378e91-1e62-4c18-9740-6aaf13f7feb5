# React and Next.js Best Practices (2025)

## Introduction

This guide outlines the latest best practices for developing modern web applications with React and Next.js as of May 2025. Following these practices will help you build performant, maintainable, and scalable applications.

## Project Structure

### Next.js App Router Structure

The App Router is now the standard for Next.js applications:

```
app/
├── (auth)/                # Route group for authentication
│   ├── login/             # Login route
│   │   └── page.tsx       # Login page component
│   └── register/          # Register route
│       └── page.tsx       # Register page component
├── (dashboard)/           # Route group for dashboard
│   ├── layout.tsx         # Dashboard layout
│   ├── page.tsx           # Dashboard home page
│   └── [id]/              # Dynamic route
│       └── page.tsx       # Dynamic page component
├── api/                   # API routes
│   └── route.ts           # API route handlers
├── components/            # Shared components
│   ├── ui/                # UI components
│   │   ├── Button.tsx     # Button component
│   │   └── Card.tsx       # Card component
│   └── layout/            # Layout components
│       ├── Header.tsx     # Header component
│       └── Footer.tsx     # Footer component
├── lib/                   # Utility functions and libraries
│   ├── api.ts             # API client
│   └── utils.ts           # Utility functions
├── hooks/                 # Custom hooks
│   ├── useAuth.ts         # Authentication hook
│   └── useForm.ts         # Form handling hook
├── styles/                # Global styles
│   └── globals.css        # Global CSS
├── types/                 # TypeScript type definitions
│   └── index.ts           # Type definitions
└── layout.tsx             # Root layout
```

### Component Organization

Organize components by feature or domain:

```
components/
├── auth/                  # Authentication components
│   ├── LoginForm.tsx      # Login form
│   └── RegisterForm.tsx   # Registration form
├── dashboard/             # Dashboard components
│   ├── Overview.tsx       # Dashboard overview
│   └── Stats.tsx          # Statistics component
├── shared/                # Shared components
│   ├── Button.tsx         # Button component
│   └── Card.tsx           # Card component
└── layout/                # Layout components
    ├── Header.tsx         # Header component
    └── Footer.tsx         # Footer component
```

## Server Components vs. Client Components

### Server Components

Use Server Components for:
- Data fetching
- Access to backend resources
- Keeping sensitive information on the server
- Large dependencies that should be kept server-side

```tsx
// app/users/page.tsx
import { db } from '@/lib/db';

export default async function UsersPage() {
  const users = await db.user.findMany();
  
  return (
    <div>
      <h1>Users</h1>
      <ul>
        {users.map((user) => (
          <li key={user.id}>{user.name}</li>
        ))}
      </ul>
    </div>
  );
}
```

### Client Components

Use Client Components for:
- Interactivity and event listeners
- useState, useEffect, and other React hooks
- Browser-only APIs
- Custom hooks that depend on state or effects

```tsx
'use client';

import { useState } from 'react';

export default function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={() => setCount(count + 1)}>Increment</button>
    </div>
  );
}
```

## Data Fetching

### Server-Side Data Fetching

Fetch data directly in Server Components:

```tsx
// app/posts/page.tsx
import { getPosts } from '@/lib/api';

export default async function PostsPage() {
  const posts = await getPosts();
  
  return (
    <div>
      <h1>Posts</h1>
      <ul>
        {posts.map((post) => (
          <li key={post.id}>{post.title}</li>
        ))}
      </ul>
    </div>
  );
}
```

### Client-Side Data Fetching

Use SWR or React Query for client-side data fetching:

```tsx
'use client';

import { useQuery } from '@tanstack/react-query';
import { getPosts } from '@/lib/api';

export default function PostsList() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['posts'],
    queryFn: getPosts
  });
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return (
    <ul>
      {data.map((post) => (
        <li key={post.id}>{post.title}</li>
      ))}
    </ul>
  );
}
```

## State Management

### React Context + useReducer

For simple to moderate state management:

```tsx
// contexts/CartContext.tsx
'use client';

import { createContext, useContext, useReducer } from 'react';

type CartItem = {
  id: string;
  name: string;
  price: number;
  quantity: number;
};

type CartState = {
  items: CartItem[];
  total: number;
};

type CartAction =
  | { type: 'ADD_ITEM'; payload: CartItem }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'CLEAR_CART' };

const CartContext = createContext<{
  state: CartState;
  dispatch: React.Dispatch<CartAction>;
} | undefined>(undefined);

function cartReducer(state: CartState, action: CartAction): CartState {
  switch (action.type) {
    case 'ADD_ITEM':
      // Implementation
      return state;
    case 'REMOVE_ITEM':
      // Implementation
      return state;
    case 'CLEAR_CART':
      // Implementation
      return state;
    default:
      return state;
  }
}

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(cartReducer, { items: [], total: 0 });
  
  return (
    <CartContext.Provider value={{ state, dispatch }}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
```

### Zustand

For more complex state management:

```tsx
// stores/useCartStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type CartItem = {
  id: string;
  name: string;
  price: number;
  quantity: number;
};

type CartStore = {
  items: CartItem[];
  total: number;
  addItem: (item: CartItem) => void;
  removeItem: (id: string) => void;
  clearCart: () => void;
};

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      total: 0,
      addItem: (item) => {
        // Implementation
        set((state) => ({ items: [...state.items, item] }));
      },
      removeItem: (id) => {
        // Implementation
        set((state) => ({
          items: state.items.filter((item) => item.id !== id)
        }));
      },
      clearCart: () => {
        set({ items: [], total: 0 });
      }
    }),
    {
      name: 'cart-storage'
    }
  )
);
```

## Performance Optimization

### Component Optimization

Use React.memo for expensive components:

```tsx
import { memo } from 'react';

type ExpensiveComponentProps = {
  data: Array<{ id: string; name: string }>;
};

const ExpensiveComponent = memo(function ExpensiveComponent({ 
  data 
}: ExpensiveComponentProps) {
  return (
    <ul>
      {data.map((item) => (
        <li key={item.id}>{item.name}</li>
      ))}
    </ul>
  );
});

export default ExpensiveComponent;
```

### Image Optimization

Use Next.js Image component:

```tsx
import Image from 'next/image';

export default function ProfilePage() {
  return (
    <div>
      <h1>Profile</h1>
      <Image
        src="/profile.jpg"
        alt="Profile Picture"
        width={200}
        height={200}
        priority
      />
    </div>
  );
}
```

### Route Optimization

Use parallel routes for complex layouts:

```tsx
// app/@dashboard/page.tsx
export default function Dashboard() {
  return <div>Dashboard Content</div>;
}

// app/@sidebar/page.tsx
export default function Sidebar() {
  return <div>Sidebar Content</div>;
}

// app/layout.tsx
export default function Layout({ 
  children, 
  dashboard, 
  sidebar 
}: { 
  children: React.ReactNode;
  dashboard: React.ReactNode;
  sidebar: React.ReactNode;
}) {
  return (
    <div className="layout">
      <div className="sidebar">{sidebar}</div>
      <div className="main">
        {dashboard}
        {children}
      </div>
    </div>
  );
}
```

## Testing

### Component Testing with React Testing Library

```tsx
// Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import Button from './Button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByRole('button', { name: /click me/i }));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Integration Testing with Playwright

```tsx
// tests/login.spec.ts
import { test, expect } from '@playwright/test';

test('user can log in', async ({ page }) => {
  await page.goto('/login');
  
  await page.fill('input[name="email"]', '<EMAIL>');
  await page.fill('input[name="password"]', 'password123');
  await page.click('button[type="submit"]');
  
  await expect(page).toHaveURL('/dashboard');
  await expect(page.locator('h1')).toContainText('Dashboard');
});
```

## Accessibility

### Semantic HTML

Use semantic HTML elements:

```tsx
// Bad
<div onClick={handleClick}>Click me</div>

// Good
<button onClick={handleClick}>Click me</button>
```

### ARIA Attributes

Add ARIA attributes when necessary:

```tsx
<button
  aria-label="Close modal"
  aria-describedby="modal-description"
  onClick={closeModal}
>
  <span className="sr-only">Close</span>
  <XIcon />
</button>
```

### Focus Management

Manage focus for modals and dialogs:

```tsx
'use client';

import { useRef, useEffect } from 'react';

export default function Modal({ isOpen, onClose, children }) {
  const modalRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (isOpen) {
      modalRef.current?.focus();
    }
  }, [isOpen]);
  
  return isOpen ? (
    <div
      ref={modalRef}
      role="dialog"
      aria-modal="true"
      tabIndex={-1}
      className="modal"
    >
      <div className="modal-content">
        {children}
        <button onClick={onClose}>Close</button>
      </div>
    </div>
  ) : null;
}
```

## Conclusion

Building modern React and Next.js applications requires a combination of best practices for project structure, component design, data fetching, state management, performance optimization, testing, and accessibility. By following these guidelines, you can create applications that are not only functional but also maintainable, performant, and accessible.

Remember to:
1. **Organize** your code by feature or domain
2. **Choose** the right component type (Server vs. Client)
3. **Optimize** for performance with proper data fetching strategies
4. **Manage** state appropriately based on complexity
5. **Test** components and user flows thoroughly
6. **Ensure** accessibility for all users

Last Updated: May 22, 2025
