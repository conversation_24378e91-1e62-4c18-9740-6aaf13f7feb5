# Frontend Testing Guide (2025)

## Introduction

This guide outlines the latest best practices for testing frontend applications, with a focus on React and Next.js. Comprehensive testing is essential for ensuring reliability, performance, and user experience in modern web applications.

## Testing Pyramid for Frontend

The frontend testing pyramid provides a framework for balancing different types of tests:

1. **Unit Tests**: Fast, focused tests for individual components and functions
2. **Component Tests**: Tests for component rendering and behavior
3. **Integration Tests**: Tests for interactions between components
4. **End-to-End Tests**: Tests that simulate real user behavior

A recommended ratio is 50% unit tests, 30% component tests, 15% integration tests, and 5% end-to-end tests.

## Unit Testing

### Testing Functions and Hooks

Use Jest for testing utility functions and custom hooks:

```tsx
// utils.test.ts
import { formatCurrency, calculateTotal } from './utils';

describe('formatCurrency', () => {
  it('formats currency correctly', () => {
    expect(formatCurrency(1000)).toBe('$1,000.00');
    expect(formatCurrency(1000, 'EUR')).toBe('€1,000.00');
  });
});

describe('calculateTotal', () => {
  it('calculates total correctly', () => {
    const items = [
      { price: 10, quantity: 2 },
      { price: 15, quantity: 1 }
    ];
    expect(calculateTotal(items)).toBe(35);
  });
  
  it('returns 0 for empty array', () => {
    expect(calculateTotal([])).toBe(0);
  });
});
```

### Testing Custom Hooks

Use `@testing-library/react-hooks` for testing custom hooks:

```tsx
// useCounter.test.ts
import { renderHook, act } from '@testing-library/react-hooks';
import { useCounter } from './useCounter';

describe('useCounter', () => {
  it('should increment counter', () => {
    const { result } = renderHook(() => useCounter());
    
    act(() => {
      result.current.increment();
    });
    
    expect(result.current.count).toBe(1);
  });
  
  it('should decrement counter', () => {
    const { result } = renderHook(() => useCounter(10));
    
    act(() => {
      result.current.decrement();
    });
    
    expect(result.current.count).toBe(9);
  });
});
```

## Component Testing

### Testing React Components

Use React Testing Library for component testing:

```tsx
// Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import Button from './Button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });
  
  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByRole('button', { name: /click me/i }));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('applies variant class', () => {
    render(<Button variant="primary">Primary Button</Button>);
    const button = screen.getByRole('button', { name: /primary button/i });
    expect(button).toHaveClass('btn-primary');
  });
});
```

### Testing with Context

Test components that use React Context:

```tsx
// CartItem.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { CartProvider } from '../contexts/CartContext';
import CartItem from './CartItem';

const renderWithContext = (ui, { providerProps, ...renderOptions } = {}) => {
  return render(
    <CartProvider {...providerProps}>{ui}</CartProvider>,
    renderOptions
  );
};

describe('CartItem', () => {
  const item = {
    id: '1',
    name: 'Test Item',
    price: 10,
    quantity: 1
  };
  
  it('renders item details', () => {
    renderWithContext(<CartItem item={item} />);
    expect(screen.getByText('Test Item')).toBeInTheDocument();
    expect(screen.getByText('$10.00')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
  });
  
  it('increases quantity when + button is clicked', () => {
    renderWithContext(<CartItem item={item} />);
    fireEvent.click(screen.getByLabelText('Increase quantity'));
    expect(screen.getByText('2')).toBeInTheDocument();
  });
});
```

### Testing Server Components

For Next.js Server Components, use Jest with mocked data:

```tsx
// UserList.test.tsx
import { render } from '@testing-library/react';
import UserList from './UserList';

// Mock the fetch function
global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve([
      { id: 1, name: 'John Doe' },
      { id: 2, name: 'Jane Smith' }
    ])
  })
) as jest.Mock;

describe('UserList', () => {
  it('renders users', async () => {
    const { findByText } = render(await UserList());
    
    expect(await findByText('John Doe')).toBeInTheDocument();
    expect(await findByText('Jane Smith')).toBeInTheDocument();
  });
});
```

## Integration Testing

### Testing Component Interactions

Test interactions between components:

```tsx
// ShoppingCart.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import ShoppingCart from './ShoppingCart';

describe('ShoppingCart', () => {
  it('updates total when item quantity changes', () => {
    render(<ShoppingCart />);
    
    // Initial total
    expect(screen.getByText('Total: $20.00')).toBeInTheDocument();
    
    // Increase quantity of first item
    fireEvent.click(screen.getAllByLabelText('Increase quantity')[0]);
    
    // Check updated total
    expect(screen.getByText('Total: $30.00')).toBeInTheDocument();
  });
  
  it('removes item when remove button is clicked', () => {
    render(<ShoppingCart />);
    
    // Initial item count
    expect(screen.getAllByTestId('cart-item')).toHaveLength(2);
    
    // Remove first item
    fireEvent.click(screen.getAllByLabelText('Remove item')[0]);
    
    // Check updated item count
    expect(screen.getAllByTestId('cart-item')).toHaveLength(1);
  });
});
```

### Testing Forms

Test form submissions and validations:

```tsx
// LoginForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import LoginForm from './LoginForm';

describe('LoginForm', () => {
  it('submits form with valid data', async () => {
    const handleSubmit = jest.fn();
    render(<LoginForm onSubmit={handleSubmit} />);
    
    // Fill form
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    });
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' }
    });
    
    // Submit form
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));
    
    // Check if onSubmit was called with correct data
    await waitFor(() => {
      expect(handleSubmit).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123'
      });
    });
  });
  
  it('shows validation errors for invalid data', async () => {
    render(<LoginForm onSubmit={jest.fn()} />);
    
    // Submit form without filling it
    fireEvent.click(screen.getByRole('button', { name: /log in/i }));
    
    // Check for validation errors
    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });
});
```

## End-to-End Testing

### Playwright for E2E Testing

Use Playwright for comprehensive end-to-end testing:

```tsx
// tests/checkout.spec.ts
import { test, expect } from '@playwright/test';

test('complete checkout process', async ({ page }) => {
  // Navigate to product page
  await page.goto('/products/1');
  
  // Add to cart
  await page.click('button[data-testid="add-to-cart"]');
  
  // Go to cart
  await page.click('[data-testid="cart-icon"]');
  
  // Proceed to checkout
  await page.click('button[data-testid="checkout"]');
  
  // Fill shipping information
  await page.fill('input[name="name"]', 'John Doe');
  await page.fill('input[name="address"]', '123 Main St');
  await page.fill('input[name="city"]', 'Anytown');
  await page.fill('input[name="zip"]', '12345');
  await page.click('button[data-testid="continue"]');
  
  // Fill payment information
  await page.fill('input[name="cardNumber"]', '****************');
  await page.fill('input[name="cardExpiry"]', '12/25');
  await page.fill('input[name="cardCvc"]', '123');
  await page.click('button[data-testid="place-order"]');
  
  // Verify order confirmation
  await expect(page.locator('h1')).toContainText('Order Confirmed');
  await expect(page.locator('[data-testid="order-number"]')).toBeVisible();
});
```

### Testing Different Devices and Browsers

Test on multiple devices and browsers:

```tsx
// playwright.config.ts
import { PlaywrightTestConfig } from '@playwright/test';

const config: PlaywrightTestConfig = {
  projects: [
    {
      name: 'Chrome Desktop',
      use: {
        browserName: 'chromium',
        viewport: { width: 1280, height: 720 }
      }
    },
    {
      name: 'Firefox Desktop',
      use: {
        browserName: 'firefox',
        viewport: { width: 1280, height: 720 }
      }
    },
    {
      name: 'Safari Mobile',
      use: {
        browserName: 'webkit',
        viewport: { width: 375, height: 667 },
        deviceScaleFactor: 2,
        isMobile: true
      }
    }
  ]
};

export default config;
```

## Performance Testing

### Lighthouse CI

Integrate Lighthouse CI for performance testing:

```yaml
# .github/workflows/lighthouse.yml
name: Lighthouse CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lighthouse:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build
      - name: Start server
        run: npm run start & npx wait-on http://localhost:3000
      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.13.0
          lhci autorun
```

### Web Vitals Monitoring

Monitor Core Web Vitals in production:

```tsx
// app/layout.tsx
import { useReportWebVitals } from 'next/web-vitals';

export function Layout({ children }) {
  useReportWebVitals((metric) => {
    // Send to analytics
    console.log(metric);
    
    // Example: send to Google Analytics
    if (window.gtag) {
      window.gtag('event', metric.name, {
        value: metric.value,
        event_category: 'Web Vitals',
        event_label: metric.id,
        non_interaction: true,
      });
    }
  });
  
  return <div>{children}</div>;
}
```

## Visual Regression Testing

### Storybook with Chromatic

Use Storybook with Chromatic for visual regression testing:

```tsx
// Button.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import Button from './Button';

const meta: Meta<typeof Button> = {
  component: Button,
  title: 'Components/Button',
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'danger']
    }
  }
};

export default meta;

type Story = StoryObj<typeof Button>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Primary Button'
  }
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Secondary Button'
  }
};

export const Danger: Story = {
  args: {
    variant: 'danger',
    children: 'Danger Button'
  }
};
```

## Accessibility Testing

### Jest-Axe for Accessibility Testing

Use jest-axe for automated accessibility testing:

```tsx
// Button.test.tsx
import { render } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import Button from './Button';

expect.extend(toHaveNoViolations);

describe('Button accessibility', () => {
  it('should not have accessibility violations', async () => {
    const { container } = render(<Button>Click me</Button>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  
  it('should not have accessibility violations when disabled', async () => {
    const { container } = render(<Button disabled>Click me</Button>);
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
});
```

## Continuous Integration

### GitHub Actions for Frontend Testing

Set up automated testing with GitHub Actions:

```yaml
# .github/workflows/test.yml
name: Test

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Lint
      run: npm run lint
    
    - name: Type check
      run: npm run type-check
    
    - name: Unit and component tests
      run: npm run test
    
    - name: Build
      run: npm run build
    
    - name: E2E tests
      run: npm run test:e2e
```

## Conclusion

Implementing a comprehensive testing strategy is essential for building reliable, performant, and accessible frontend applications. By following these best practices, you can ensure your application meets the highest standards of quality and user experience.

Key takeaways:
1. **Balance** different types of tests according to the testing pyramid
2. **Automate** testing through continuous integration
3. **Test** for functionality, performance, and accessibility
4. **Use** the right tools for each type of testing
5. **Monitor** performance and user experience in production

Last Updated: May 22, 2025
