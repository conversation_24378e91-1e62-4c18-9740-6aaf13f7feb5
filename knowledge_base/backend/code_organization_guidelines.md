# Backend Code Organization Guidelines (May 2025)

## File Size and Structure Recommendations

### Optimal File Size

Based on extensive research and performance analysis, we recommend the following guidelines for Python file sizes in the RentUp backend project:

- **Ideal target**: 300-500 lines per Python file
- **Maximum threshold**: 600 lines before refactoring is required
- **Absolute limit**: 800 lines (files exceeding this must be refactored immediately)

These guidelines apply to all Python files, including FastAPI routes, models, services, and utility functions.

### Rationale

1. **Performance Benefits**:
   - Smaller files are easier to load and cache
   - Improved module import times
   - Better memory utilization
   - More efficient code reloading during development

2. **Development Workflow Improvements**:
   - Enhanced AI-assisted development with 8GB VRAM / 4K context window limitations
   - More effective code reviews with focused, single-responsibility files
   - Improved debugging and testing with clearer module boundaries
   - Better team collaboration with more manageable code units

3. **Maintenance Advantages**:
   - Easier onboarding for new developers
   - Reduced cognitive load when navigating the codebase
   - More predictable impact when making changes
   - Clearer ownership and responsibility boundaries

## Implementation Strategies

### Breaking Down Large Modules

When a module exceeds or approaches the recommended size limits, consider these strategies:

1. **Extract Service Classes**:
   ```python
   # Before: Large monolithic service
   class UserService:
       # 700+ lines of code with many responsibilities
       
   # After: Split into focused service classes
   class UserAuthService:
       # 200 lines for authentication logic
       
   class UserProfileService:
       # 250 lines for profile management
       
   class UserPreferencesService:
       # 150 lines for user preferences
   ```

2. **Extract Utility Functions**:
   ```python
   # Before: Service with embedded utilities
   class PaymentService:
       def __init__(self):
           # initialization
           
       # 200+ lines of utility functions
       # 400+ lines of core service logic
       
   # After: Move utilities to a separate module
   # payment_utils.py
   def calculate_tax(amount, tax_rate):
       # tax calculation logic
       
   def apply_discount(amount, discount_code):
       # discount logic
       
   # payment_service.py
   from .payment_utils import calculate_tax, apply_discount
   
   class PaymentService:
       # 400+ lines of core service logic that uses the imported utilities
   ```

3. **Split Route Handlers**:
   ```python
   # Before: Large router file
   # user_routes.py - 900 lines
   router = APIRouter()
   
   @router.get("/users")
   async def get_users():
       # implementation
   
   # Many more route handlers...
   
   # After: Split by functional area
   # user_auth_routes.py
   auth_router = APIRouter()
   
   @auth_router.post("/login")
   async def login():
       # implementation
       
   # user_profile_routes.py
   profile_router = APIRouter()
   
   @profile_router.get("/users/{user_id}/profile")
   async def get_user_profile():
       # implementation
   ```

### Organizing Related Files

When breaking down large files, follow these organization patterns:

1. **Feature-Based Organization**:
   ```
   app/
   ├── users/
   │   ├── routes.py
   │   ├── models.py
   │   ├── services.py
   │   ├── schemas.py
   │   └── utils.py
   ```

2. **Functional Area Pattern**:
   ```
   app/
   ├── auth/
   │   ├── routes.py
   │   ├── services.py
   │   └── utils.py
   ├── profiles/
   │   ├── routes.py
   │   ├── services.py
   │   └── utils.py
   ```

## Monitoring and Enforcement

To ensure adherence to these guidelines:

1. **Automated Checks**:
   - Flake8 plugin to warn when files exceed 500 lines
   - Pre-commit hooks to prevent committing files that violate the absolute limit
   - Regular code quality reports

2. **Code Review Process**:
   - Reviewers should flag files approaching size limits
   - Suggest specific refactoring strategies based on this guide
   - Consider file size as part of the review criteria

3. **Refactoring Priority**:
   - Files exceeding 800 lines: High priority
   - Files between 600-800 lines: Medium priority
   - Files between 500-600 lines: Low priority

## Examples from RentUp Codebase

### Before and After Examples

**Before**: `property_service.py` (950 lines)
- Combined property creation, updating, searching, and recommendation logic

**After**:
- `property_base_service.py` (200 lines) - Core property CRUD operations
- `property_search_service.py` (250 lines) - Search and filtering logic
- `property_recommendation_service.py` (180 lines) - Recommendation algorithms
- `property_validation_service.py` (150 lines) - Validation logic
- `property_utils.py` (120 lines) - Shared utility functions

## Additional Resources

- [FastAPI Optimization Guide](./fastapi_optimization_guide.md)
- [Database Optimization Guide](./database_optimization_guide.md)
- [Testing Best Practices](./testing_best_practices.md)

Last Updated: May 23, 2025
