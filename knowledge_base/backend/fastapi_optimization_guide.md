# FastAPI Optimization Guide (2025)

## Introduction

This guide provides advanced strategies for optimizing FastAPI applications to achieve maximum performance, scalability, and efficiency. As of 2025, FastAPI has become the leading Python framework for building high-performance APIs, capable of processing over 3,000 requests per second.

## Profiling Your FastAPI Application

Before optimizing, you need to identify bottlenecks through profiling.

### Using Profiling Tools

#### cProfile and pstats

Python's built-in profiler for getting an overall picture of your application's performance:

```python
import cProfile
import pstats
from fastapi.testclient import TestClient
from main import app  # your FastAPI application

client = TestClient(app)

def profile_api():
    client.get("/your-endpoint")

profiler = cProfile.Profile()
profiler.enable()
profile_api()
profiler.disable()

stats = pstats.Stats(profiler).sort_stats("cumtime")
stats.print_stats(10)
```

#### Py-Spy

For real-time, low-overhead profiling in production environments:

```bash
# Install Py-Spy
pip install py-spy

# Run Py-Spy with your application
py-spy top --pid <your_pid>

# Generate a flame graph
py-spy record -o profile.svg --pid <your_pid>
```

#### Yappi for Async Code

Specifically designed for profiling asynchronous code:

```python
import yappi
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

yappi.set_clock_type("wall")  # Use wall clock time for async code
yappi.start()

client.get("/your-endpoint")

yappi.stop()
stats = yappi.get_func_stats()
stats.sort("ttot")  # Sort by total time
stats.print_all()
```

## Caching Strategies

Implementing effective caching can dramatically improve performance.

### In-Memory Caching

For lightweight, high-speed caching:

```python
from cachetools import TTLCache, cached

cache = TTLCache(maxsize=1000, ttl=300)  # Cache up to 1000 items, with a TTL of 5 minutes

@cached(cache)
def compute_expensive_operation(param):
    # Perform an expensive calculation or DB query here
    return result
```

### Distributed Caching with Redis

For scalable, distributed caching:

```python
import aioredis
from fastapi import FastAPI, Depends

app = FastAPI()

async def get_redis():
    redis = aioredis.from_url("redis://localhost")
    try:
        yield redis
    finally:
        await redis.close()

@app.get("/items/{item_id}")
async def read_item(item_id: int, redis = Depends(get_redis)):
    cache_key = f"item:{item_id}"
    
    # Try to get from cache
    cached_item = await redis.get(cache_key)
    if cached_item:
        return {"item": cached_item.decode("utf-8"), "source": "cache"}
    
    # If not in cache, fetch from database
    item = await fetch_item_from_db(item_id)
    
    # Store in cache for future requests
    await redis.set(cache_key, item, ex=300)  # Cache for 5 minutes
    
    return {"item": item, "source": "database"}
```

### Cache Invalidation Strategies

1. **Time-to-Live (TTL)**: Set expiration times for cached items
2. **Cache Aside**: Load data into cache only when needed
3. **Event-Driven Invalidation**: Trigger cache invalidation when data changes

```python
# Event-driven cache invalidation example
@app.post("/items/")
async def create_item(item: Item, redis = Depends(get_redis)):
    # Create the item in the database
    item_id = await create_item_in_db(item)
    
    # Invalidate any cached lists that might include this item
    await redis.delete("items:list")
    
    return {"id": item_id}
```

## Asynchronous Optimization

### Using uvloop for Maximum Performance

```python
# Install uvloop
# pip install uvloop

# Run with uvloop
# uvicorn main:app --loop uvloop --workers 4
```

### Efficient Dependency Injection

```python
# Cache expensive dependencies
from functools import lru_cache

@lru_cache()
def get_settings():
    return Settings()

@app.get("/items/")
async def read_items(settings: Settings = Depends(get_settings)):
    # Use settings
    pass
```

### Connection Pooling for Databases

```python
from databases import Database

DATABASE_URL = "postgresql://user:password@localhost/dbname"
database = Database(DATABASE_URL)

@app.on_event("startup")
async def startup():
    await database.connect()

@app.on_event("shutdown")
async def shutdown():
    await database.disconnect()
```

## Server and Worker Configuration

### Gunicorn with Uvicorn Workers

For production deployment:

```bash
gunicorn -k uvicorn.workers.UvicornWorker main:app --workers 4 --bind 0.0.0.0:8000
```

### Worker Count Optimization

A good rule of thumb is `(2 * CPU cores) + 1`:

```python
import multiprocessing

def number_of_workers():
    return (multiprocessing.cpu_count() * 2) + 1
```

## Database Optimization

### Using Async Drivers

```python
import asyncpg

async def get_connection():
    conn = await asyncpg.connect(DATABASE_URL)
    try:
        yield conn
    finally:
        await conn.close()
```

### Query Optimization

1. Use specific column selection instead of `SELECT *`
2. Create appropriate indexes for frequently queried columns
3. Use pagination to limit result sets
4. Implement query caching for repeated queries

```python
@app.get("/items/")
async def read_items(
    skip: int = 0, 
    limit: int = 100, 
    conn = Depends(get_connection)
):
    # Efficient pagination
    query = "SELECT id, name, price FROM items LIMIT $1 OFFSET $2"
    results = await conn.fetch(query, limit, skip)
    return results
```

## Middleware Optimization

### Custom Middleware for Performance Monitoring

```python
import time
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

class PerformanceMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        
        response = await call_next(request)
        
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        
        return response

app.add_middleware(PerformanceMiddleware)
```

### Compression Middleware

```python
from fastapi.middleware.gzip import GZipMiddleware

app.add_middleware(GZipMiddleware, minimum_size=1000)
```

## Background Tasks and Concurrency

### Using Background Tasks

```python
from fastapi import BackgroundTasks

@app.post("/send-notification/")
async def send_notification(
    email: str,
    background_tasks: BackgroundTasks
):
    background_tasks.add_task(send_email_notification, email, message="Hello World")
    return {"message": "Notification scheduled"}
```

### Managing Concurrency

```python
import asyncio
from fastapi import FastAPI, Depends

app = FastAPI()

# Semaphore to limit concurrent database operations
db_semaphore = asyncio.Semaphore(10)

async def get_db_connection():
    async with db_semaphore:
        # Get database connection
        conn = await get_connection()
        try:
            yield conn
        finally:
            await conn.close()
```

## Conclusion

Optimizing FastAPI applications involves a multi-faceted approach:

1. **Profile** to identify bottlenecks
2. **Cache** to reduce redundant operations
3. **Optimize** database queries and connections
4. **Configure** servers and workers appropriately
5. **Monitor** performance continuously

By implementing these strategies, you can build FastAPI applications that handle high loads efficiently while maintaining responsiveness and reliability.

Last Updated: May 22, 2025
