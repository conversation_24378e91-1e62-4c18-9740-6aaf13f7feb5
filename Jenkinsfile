pipeline {
    agent {
        kubernetes {
            yaml """
apiVersion: v1
kind: Pod
metadata:
  labels:
    app: rentup-backend-ci
spec:
  containers:
  - name: python
    image: python:3.10-slim
    command:
    - cat
    tty: true
  - name: docker
    image: docker:20.10.16
    command:
    - cat
    tty: true
    volumeMounts:
    - name: docker-sock
      mountPath: /var/run/docker.sock
  - name: kubectl
    image: bitnami/kubectl:latest
    command:
    - cat
    tty: true
  volumes:
  - name: docker-sock
    hostPath:
      path: /var/run/docker.sock
"""
        }
    }
    
    environment {
        POSTGRES_USER = 'rentup'
        POSTGRES_PASSWORD = 'rentup_secure_password'
        POSTGRES_DB = 'rentup_test'
        POSTGRES_HOST = 'postgres'
        POSTGRES_PORT = '5432'
        SECRET_KEY = 'test_secret_key'
        ENVIRONMENT = 'test'
        DOCKER_IMAGE = "rentup/backend:${env.BRANCH_NAME}-${env.BUILD_NUMBER}"
        DOCKER_REGISTRY_CREDENTIALS = credentials('docker-registry-credentials')
        KUBE_CONFIG = credentials('kube-config')
    }
    
    stages {
        stage('Lint') {
            when {
                anyOf {
                    changeset 'backend/**/*'
                    changeset 'Jenkinsfile'
                }
            }
            steps {
                container('python') {
                    sh '''
                    pip install flake8 black isort mypy
                    pip install -r backend/requirements.txt
                    flake8 backend --count --select=E9,F63,F7,F82 --show-source --statistics
                    flake8 backend --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
                    black --check backend
                    isort --check-only --profile black backend
                    mypy backend
                    '''
                }
            }
        }
        
        stage('Test') {
            when {
                anyOf {
                    changeset 'backend/**/*'
                    changeset 'Jenkinsfile'
                }
            }
            steps {
                container('python') {
                    sh '''
                    apt-get update && apt-get install -y --no-install-recommends gcc libpq-dev
                    pip install pytest pytest-cov
                    pip install -r backend/requirements.txt
                    cd backend
                    pytest --cov=app --cov-report=xml
                    '''
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'backend/coverage.xml', allowEmptyArchive: true
                    publishCoverage adapters: [cobertura('backend/coverage.xml')]
                }
            }
        }
        
        stage('Security Scan') {
            when {
                anyOf {
                    changeset 'backend/**/*'
                    changeset 'Jenkinsfile'
                }
            }
            steps {
                container('python') {
                    sh '''
                    pip install bandit safety
                    pip install -r backend/requirements.txt
                    bandit -r backend/app -f json -o bandit-results.json || true
                    safety check -r backend/requirements.txt --json > safety-results.json || true
                    cd backend
                    python scripts/security_audit.py --all --output security_audit_report.json || true
                    '''
                }
            }
            post {
                always {
                    archiveArtifacts artifacts: 'bandit-results.json,safety-results.json,backend/security_audit_report.json', allowEmptyArchive: true
                }
            }
        }
        
        stage('Build') {
            when {
                anyOf {
                    branch 'main'
                    branch 'develop'
                }
                anyOf {
                    changeset 'backend/**/*'
                    changeset 'Jenkinsfile'
                }
            }
            steps {
                container('docker') {
                    sh '''
                    echo $DOCKER_REGISTRY_CREDENTIALS_PSW | docker login -u $DOCKER_REGISTRY_CREDENTIALS_USR --password-stdin
                    docker build -t $DOCKER_IMAGE -f backend/Dockerfile.prod ./backend
                    docker push $DOCKER_IMAGE
                    '''
                }
            }
        }
        
        stage('Deploy to Staging') {
            when {
                branch 'develop'
                anyOf {
                    changeset 'backend/**/*'
                    changeset 'Jenkinsfile'
                }
            }
            steps {
                container('kubectl') {
                    sh '''
                    echo "$KUBE_CONFIG" > kubeconfig
                    export KUBECONFIG=kubeconfig
                    
                    # Update image tag in deployment
                    sed -i "s|image: .*|image: $DOCKER_IMAGE|g" kubernetes/rentup-deployment.yaml
                    
                    # Apply Kubernetes manifests
                    kubectl apply -f kubernetes/rentup-deployment.yaml
                    
                    # Wait for deployment to complete
                    kubectl rollout status deployment/rentup-backend -n rentup --timeout=300s
                    
                    # Run database migrations
                    cat <<EOF | kubectl apply -f -
                    apiVersion: batch/v1
                    kind: Job
                    metadata:
                      name: rentup-migrations-${BUILD_NUMBER}
                      namespace: rentup
                    spec:
                      ttlSecondsAfterFinished: 100
                      template:
                        spec:
                          containers:
                          - name: migrations
                            image: $DOCKER_IMAGE
                            command: ["alembic", "upgrade", "head"]
                            env:
                            - name: POSTGRES_SERVER
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_host
                            - name: POSTGRES_USER
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_user
                            - name: POSTGRES_PASSWORD
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_password
                            - name: POSTGRES_DB
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_name
                          restartPolicy: Never
                      backoffLimit: 4
                    EOF
                    
                    # Wait for migration job to complete
                    kubectl wait --for=condition=complete job/rentup-migrations-${BUILD_NUMBER} -n rentup --timeout=300s
                    
                    # Run security tests
                    cat <<EOF | kubectl apply -f -
                    apiVersion: batch/v1
                    kind: Job
                    metadata:
                      name: rentup-security-tests-${BUILD_NUMBER}
                      namespace: rentup
                    spec:
                      ttlSecondsAfterFinished: 100
                      template:
                        spec:
                          containers:
                          - name: security-tests
                            image: $DOCKER_IMAGE
                            command: ["python", "scripts/test_security.py", "--all", "--api-url", "http://rentup-backend.rentup.svc.cluster.local"]
                            env:
                            - name: POSTGRES_SERVER
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_host
                            - name: POSTGRES_USER
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_user
                            - name: POSTGRES_PASSWORD
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_password
                            - name: POSTGRES_DB
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_name
                          restartPolicy: Never
                      backoffLimit: 4
                    EOF
                    
                    # Wait for security tests job to complete
                    kubectl wait --for=condition=complete job/rentup-security-tests-${BUILD_NUMBER} -n rentup --timeout=300s
                    '''
                }
                
                // Notify deployment
                slackSend channel: '#deployments', color: 'good', message: "RentUp backend deployed to staging: ${env.BRANCH_NAME}-${env.BUILD_NUMBER}"
            }
        }
        
        stage('Deploy to Production') {
            when {
                branch 'main'
                anyOf {
                    changeset 'backend/**/*'
                    changeset 'Jenkinsfile'
                }
            }
            input {
                message "Deploy to production?"
                ok "Yes"
            }
            steps {
                container('kubectl') {
                    sh '''
                    echo "$KUBE_CONFIG" > kubeconfig
                    export KUBECONFIG=kubeconfig
                    
                    # Update image tag in deployment
                    sed -i "s|image: .*|image: $DOCKER_IMAGE|g" kubernetes/rentup-deployment.yaml
                    
                    # Apply Kubernetes manifests
                    kubectl apply -f kubernetes/rentup-deployment.yaml
                    
                    # Wait for deployment to complete
                    kubectl rollout status deployment/rentup-backend -n rentup --timeout=300s
                    
                    # Run database migrations
                    cat <<EOF | kubectl apply -f -
                    apiVersion: batch/v1
                    kind: Job
                    metadata:
                      name: rentup-migrations-${BUILD_NUMBER}
                      namespace: rentup
                    spec:
                      ttlSecondsAfterFinished: 100
                      template:
                        spec:
                          containers:
                          - name: migrations
                            image: $DOCKER_IMAGE
                            command: ["alembic", "upgrade", "head"]
                            env:
                            - name: POSTGRES_SERVER
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_host
                            - name: POSTGRES_USER
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_user
                            - name: POSTGRES_PASSWORD
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_password
                            - name: POSTGRES_DB
                              valueFrom:
                                secretKeyRef:
                                  name: rentup-db-secrets
                                  key: db_name
                          restartPolicy: Never
                      backoffLimit: 4
                    EOF
                    
                    # Wait for migration job to complete
                    kubectl wait --for=condition=complete job/rentup-migrations-${BUILD_NUMBER} -n rentup --timeout=300s
                    '''
                }
                
                // Notify deployment
                slackSend channel: '#deployments', color: 'good', message: "RentUp backend deployed to production: ${env.BRANCH_NAME}-${env.BUILD_NUMBER}"
            }
        }
    }
    
    post {
        success {
            slackSend channel: '#builds', color: 'good', message: "Build successful: ${env.JOB_NAME} #${env.BUILD_NUMBER} (<${env.BUILD_URL}|Open>)"
        }
        failure {
            slackSend channel: '#builds', color: 'danger', message: "Build failed: ${env.JOB_NAME} #${env.BUILD_NUMBER} (<${env.BUILD_URL}|Open>)"
        }
        always {
            cleanWs()
        }
    }
}
