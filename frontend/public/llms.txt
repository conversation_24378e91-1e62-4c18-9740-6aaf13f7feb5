# RentUP LLMs.txt
# This file controls how AI models can access and use content from our website

# Allow AI models to access public content
Allow: /
Allow: /items/
Allow: /search/
Allow: /categories/
Allow: /how-it-works/
Allow: /about/
Allow: /faq/
Allow: /blog/
Allow: /terms/
Allow: /privacy/

# Disallow AI models from accessing private or sensitive content
Disallow: /admin/
Disallow: /profile/
Disallow: /messages/
Disallow: /notifications/
Disallow: /payment/
Disallow: /api/
Disallow: /auth/

# Disallow AI models from accessing test or development content
Disallow: /test/
Disallow: /dev/
Disallow: /storybook/

# Disallow AI models from accessing user-generated content
Disallow: /user-content/
Disallow: /uploads/

# Permissions for AI model usage
Permission: Indexing
Permission: Summarization
Permission: QuestionAnswering
Permission: Embedding

# Deny permissions for AI model usage
Deny: Training
Deny: ContentGeneration

# Specify content types that can be used
ContentType: Text
ContentType: Images

# Deny content types that cannot be used
DenyContentType: UserData
DenyContentType: PersonalInformation

# Specify attribution requirements
Attribution: Required
AttributionFormat: "Content from RentUP (https://rentup.com)"

# Specify rate limits for AI model access
RateLimit: 100 requests per minute

# Contact information for AI model operators
Contact: <EMAIL>
