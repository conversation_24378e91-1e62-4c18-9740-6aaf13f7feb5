import { test, expect } from '@playwright/test';

/**
 * End-to-end tests for the Comprehensive Visualization Dashboard
 * Tests the interactive features and navigation between tabs
 */
test.describe('Comprehensive Visualization Dashboard', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the visualization demo page
    await page.goto('/visualization-demo/comprehensive');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("AI Recommendation Visualization")');
  });

  test('should display the main visualization dashboard', async ({ page }) => {
    // Check if the main dashboard is displayed
    await expect(page.locator('h2:has-text("AI Recommendation Insights")')).toBeVisible();
    
    // Check if the time period filter is displayed
    await expect(page.locator('text=Time Period:')).toBeVisible();
    await expect(page.locator('button:has-text("Current")')).toBeVisible();
    await expect(page.locator('button:has-text("1 Month")')).toBeVisible();
    await expect(page.locator('button:has-text("3 Months")')).toBeVisible();
    await expect(page.locator('button:has-text("6 Months")')).toBeVisible();
    
    // Check if all tabs are displayed
    await expect(page.locator('button[role="tab"]:has-text("Overview")')).toBeVisible();
    await expect(page.locator('button[role="tab"]:has-text("Preferences")')).toBeVisible();
    await expect(page.locator('button[role="tab"]:has-text("Item Relationships")')).toBeVisible();
    await expect(page.locator('button[role="tab"]:has-text("Recommendations")')).toBeVisible();
    await expect(page.locator('button[role="tab"]:has-text("Dashboard")')).toBeVisible();
  });

  test('should navigate between tabs', async ({ page }) => {
    // Initially on Overview tab
    await expect(page.locator('div[role="tabpanel"] >> text=This dashboard provides insights')).toBeVisible();
    
    // Click on Preferences tab
    await page.click('button[role="tab"]:has-text("Preferences")');
    await expect(page.locator('div[role="tabpanel"] >> text=This visualization shows how your preferences are tracked')).toBeVisible();
    
    // Click on Item Relationships tab
    await page.click('button[role="tab"]:has-text("Item Relationships")');
    await expect(page.locator('div[role="tabpanel"] >> text=This visualization shows how items are related')).toBeVisible();
    
    // Click on Dashboard tab
    await page.click('button[role="tab"]:has-text("Dashboard")');
    await expect(page.locator('div[role="tabpanel"] >> text=Recommendation Performance')).toBeVisible();
    await expect(page.locator('div[role="tabpanel"] >> text=Recommendation Categories')).toBeVisible();
    await expect(page.locator('div[role="tabpanel"] >> text=Recommendation Insights')).toBeVisible();
  });

  test('should change time period', async ({ page }) => {
    // Click on 3 Months button
    await page.click('button:has-text("3 Months")');
    
    // Check if the button is highlighted
    await expect(page.locator('button:has-text("3 Months").bg-blue-600')).toBeVisible();
    
    // Click on 6 Months button
    await page.click('button:has-text("6 Months")');
    
    // Check if the button is highlighted
    await expect(page.locator('button:has-text("6 Months").bg-blue-600')).toBeVisible();
    
    // Click on Current button
    await page.click('button:has-text("Current")');
    
    // Check if the button is highlighted
    await expect(page.locator('button:has-text("Current").bg-blue-600')).toBeVisible();
  });

  test('should display the "How to Use This Dashboard" section', async ({ page }) => {
    // Scroll down to the "How to Use This Dashboard" section
    await page.locator('h2:has-text("How to Use This Dashboard")').scrollIntoViewIfNeeded();
    
    // Check if the section is displayed
    await expect(page.locator('h2:has-text("How to Use This Dashboard")')).toBeVisible();
    await expect(page.locator('h3:has-text("Overview Tab")')).toBeVisible();
    await expect(page.locator('h3:has-text("Preferences Tab")')).toBeVisible();
    await expect(page.locator('h3:has-text("Item Relationships Tab")')).toBeVisible();
    await expect(page.locator('h3:has-text("Recommendations Tab")')).toBeVisible();
    await expect(page.locator('h3:has-text("Dashboard Tab")')).toBeVisible();
  });

  test('should display the "About This Demo" section', async ({ page }) => {
    // Scroll up to the "About This Demo" section
    await page.locator('h2:has-text("About This Demo")').scrollIntoViewIfNeeded();
    
    // Check if the section is displayed
    await expect(page.locator('h2:has-text("About This Demo")')).toBeVisible();
    await expect(page.locator('h3:has-text("Personalized Recommendations")')).toBeVisible();
    await expect(page.locator('h3:has-text("Transparent AI")')).toBeVisible();
  });

  test('should have accessible elements', async ({ page }) => {
    // Check if the main heading has the correct role
    await expect(page.locator('h1')).toHaveAttribute('role', 'heading');
    
    // Check if the tabs have the correct roles
    await expect(page.locator('button[role="tab"]:has-text("Overview")')).toHaveAttribute('aria-selected', 'true');
    await expect(page.locator('div[role="tabpanel"]')).toBeVisible();
    
    // Check if the time period buttons have the correct attributes
    await expect(page.locator('button:has-text("Current")')).toHaveAttribute('aria-pressed', 'true');
    
    // Click on Preferences tab and check accessibility
    await page.click('button[role="tab"]:has-text("Preferences")');
    await expect(page.locator('button[role="tab"]:has-text("Preferences")')).toHaveAttribute('aria-selected', 'true');
    await expect(page.locator('div[role="tabpanel"]')).toBeVisible();
  });

  test('should be responsive', async ({ page }) => {
    // Test on mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Check if the dashboard is still visible
    await expect(page.locator('h2:has-text("AI Recommendation Insights")')).toBeVisible();
    
    // Check if the tabs are still accessible
    await expect(page.locator('button[role="tab"]:has-text("Overview")')).toBeVisible();
    
    // Test on tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    
    // Check if the dashboard is still visible
    await expect(page.locator('h2:has-text("AI Recommendation Insights")')).toBeVisible();
    
    // Check if the tabs are still accessible
    await expect(page.locator('button[role="tab"]:has-text("Overview")')).toBeVisible();
    
    // Test on desktop viewport
    await page.setViewportSize({ width: 1280, height: 800 });
    
    // Check if the dashboard is still visible
    await expect(page.locator('h2:has-text("AI Recommendation Insights")')).toBeVisible();
    
    // Check if the tabs are still accessible
    await expect(page.locator('button[role="tab"]:has-text("Overview")')).toBeVisible();
  });
});
