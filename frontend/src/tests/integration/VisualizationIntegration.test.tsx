// Import setup first
import './setup';

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import VisualizationPage from '../../pages/VisualizationPage';
import recommendationService from '../../services/recommendationService';

// Mock the useAuth hook
jest.mock('../../hooks/useAuth', () => ({
  useAuth: () => ({
    isAuthenticated: true,
    user: { id: 'user-123', name: 'Test User' },
  }),
}));

// Mock the useErrorHandler hook
jest.mock('../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn((err, options) => {
      if (options?.setError) {
        options.setError('Error message');
      }
    }),
  }),
}));

// Mock the useMediaQuery hook
jest.mock('../../hooks/useMediaQuery', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => false),
    useMediaQuery: jest.fn().mockImplementation(() => false),
    useIsMobile: jest.fn().mockImplementation(() => false),
    useIsTablet: jest.fn().mockImplementation(() => false),
    useIsDesktop: jest.fn().mockImplementation(() => true),
  };
});

// Mock the framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, whileTap, whileHover, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, whileTap, whileHover, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock the recommendation service
jest.mock('../../services/recommendationService', () => ({
  getPreferenceVisualization: jest.fn(),
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
}));

// Mock fetch for EmbeddingVisualization
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock data
const mockPreferenceData = [
  { preferenceType: 'liked', weight: 0.8, count: 12 },
  { preferenceType: 'viewed', weight: 0.5, count: 30 },
  { preferenceType: 'favorited', weight: 0.9, count: 8 },
];

const mockEmbeddingData = [
  { id: 'item1', name: 'Item 1', x: 0.2, y: 0.3, category: 'electronics' },
  { id: 'item2', name: 'Item 2', x: 0.5, y: 0.7, category: 'furniture' },
  { id: 'item3', name: 'Item 3', x: 0.8, y: 0.1, category: 'clothing' },
];

describe('Visualization Integration Tests', () => {
  // Setup before each test
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the preference visualization data
    recommendationService.getPreferenceVisualization.mockImplementation((userId, options) => {
      // Call the onDataLoaded callback if provided
      if (options?.onDataLoaded) {
        options.onDataLoaded(mockPreferenceData);
      }
      return Promise.resolve(mockPreferenceData);
    });

    // Mock the embedding visualization data
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockEmbeddingData),
      })
    );
  });

  // Cleanup after all tests
  afterAll(() => {
    jest.restoreAllMocks();
  });

  // Helper function to render the component with providers
  const renderWithProviders = (ui: React.ReactElement) => {
    return render(
      <BrowserRouter>
        <HelmetProvider>
          {ui}
        </HelmetProvider>
      </BrowserRouter>
    );
  };

  it('renders the visualization page with preference visualization by default', async () => {
    renderWithProviders(<VisualizationPage />);

    // Check that the page title is rendered
    expect(screen.getByText('AI Recommendation Insights')).toBeInTheDocument();

    // Check that the visualization demo is rendered
    const visualizationDemo = screen.getByTestId('visualization-page-demo');
    expect(visualizationDemo).toBeInTheDocument();

    // Check that the preference visualization is rendered by default
    await waitFor(() => {
      expect(recommendationService.getPreferenceVisualization).toHaveBeenCalledWith('user-123');
    });

    // Check that the preference tab is selected by default
    const preferencesTab = screen.getByText('Preference Visualization');
    expect(preferencesTab).toBeInTheDocument();
    expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
  });

  it('switches between preference and embedding visualizations', async () => {
    renderWithProviders(<VisualizationPage />);

    // Check that the preference tab is selected by default
    const preferencesTab = screen.getByText('Preference Visualization');
    expect(preferencesTab).toHaveAttribute('aria-selected', 'true');

    // Click on the embeddings tab
    const embeddingsTab = screen.getByText('Embedding Visualization');
    fireEvent.click(embeddingsTab);

    // Check that the embeddings tab is now selected
    expect(embeddingsTab).toHaveAttribute('aria-selected', 'true');
    expect(preferencesTab).toHaveAttribute('aria-selected', 'false');

    // Check that the embedding visualization is loaded
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalled();
    });

    // Click back on the preferences tab
    fireEvent.click(preferencesTab);

    // Check that the preferences tab is now selected again
    expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
    expect(embeddingsTab).toHaveAttribute('aria-selected', 'false');
  });

  it('displays information about how recommendations work', () => {
    renderWithProviders(<VisualizationPage />);

    // Check that the information sections are rendered
    expect(screen.getByText('How Our Recommendations Work')).toBeInTheDocument();
    expect(screen.getByText('Privacy and Your Data')).toBeInTheDocument();
    expect(screen.getByText('Frequently Asked Questions')).toBeInTheDocument();

    // Check specific content in the information sections
    expect(screen.getByText('Preference Tracking:')).toBeInTheDocument();
    expect(screen.getByText('Item Embeddings:')).toBeInTheDocument();
    expect(screen.getByText('Contextual Factors:')).toBeInTheDocument();
    expect(screen.getByText('Collaborative Signals:')).toBeInTheDocument();
  });

  it('displays privacy information', () => {
    renderWithProviders(<VisualizationPage />);

    // Check privacy information
    expect(screen.getByText('Privacy and Your Data')).toBeInTheDocument();
    expect(screen.getByText('Data Control:')).toBeInTheDocument();
    expect(screen.getByText('Anonymized Processing:')).toBeInTheDocument();
    expect(screen.getByText('No Third-Party Sharing:')).toBeInTheDocument();
    expect(screen.getByText('Transparency:')).toBeInTheDocument();
    expect(screen.getByText('Learn more about our privacy policy')).toBeInTheDocument();
  });

  it('displays FAQ section', () => {
    renderWithProviders(<VisualizationPage />);

    // Check FAQ section
    expect(screen.getByText('Frequently Asked Questions')).toBeInTheDocument();
    expect(screen.getByText('How can I improve my recommendations?')).toBeInTheDocument();
    expect(screen.getByText('Can I reset my recommendation preferences?')).toBeInTheDocument();
    expect(screen.getByText('Why am I seeing certain recommendations?')).toBeInTheDocument();
    expect(screen.getByText('How often are recommendations updated?')).toBeInTheDocument();
  });
});
