import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

/**
 * Accessibility tests for the Comprehensive Visualization Dashboard
 * Tests WCAG 2.1 AA compliance using axe-core
 */
test.describe('Comprehensive Visualization Dashboard Accessibility', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the visualization demo page
    await page.goto('/visualization-demo/comprehensive');
    
    // Wait for the page to load
    await page.waitForSelector('h1:has-text("AI Recommendation Visualization")');
  });

  test('should not have any automatically detectable accessibility violations on the main page', async ({ page }) => {
    // Run axe against the page
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
      .analyze();
    
    // Assert there are no violations
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should not have any automatically detectable accessibility violations on the Preferences tab', async ({ page }) => {
    // Click on Preferences tab
    await page.click('button[role="tab"]:has-text("Preferences")');
    
    // Wait for the tab content to load
    await page.waitForSelector('div[role="tabpanel"] >> text=This visualization shows how your preferences are tracked');
    
    // Run axe against the page
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
      .analyze();
    
    // Assert there are no violations
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should not have any automatically detectable accessibility violations on the Item Relationships tab', async ({ page }) => {
    // Click on Item Relationships tab
    await page.click('button[role="tab"]:has-text("Item Relationships")');
    
    // Wait for the tab content to load
    await page.waitForSelector('div[role="tabpanel"] >> text=This visualization shows how items are related');
    
    // Run axe against the page
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
      .analyze();
    
    // Assert there are no violations
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should not have any automatically detectable accessibility violations on the Dashboard tab', async ({ page }) => {
    // Click on Dashboard tab
    await page.click('button[role="tab"]:has-text("Dashboard")');
    
    // Wait for the tab content to load
    await page.waitForSelector('div[role="tabpanel"] >> text=Recommendation Performance');
    
    // Run axe against the page
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
      .analyze();
    
    // Assert there are no violations
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should have proper keyboard navigation', async ({ page }) => {
    // Focus on the first tab
    await page.keyboard.press('Tab');
    
    // Check if the Overview tab is focused
    await expect(page.locator('button[role="tab"]:has-text("Overview")')).toBeFocused();
    
    // Navigate to the next tab
    await page.keyboard.press('Tab');
    
    // Check if the Preferences tab is focused
    await expect(page.locator('button[role="tab"]:has-text("Preferences")')).toBeFocused();
    
    // Navigate to the next tab
    await page.keyboard.press('Tab');
    
    // Check if the Item Relationships tab is focused
    await expect(page.locator('button[role="tab"]:has-text("Item Relationships")')).toBeFocused();
    
    // Navigate to the next tab
    await page.keyboard.press('Tab');
    
    // Check if the Recommendations tab is focused
    await expect(page.locator('button[role="tab"]:has-text("Recommendations")')).toBeFocused();
    
    // Navigate to the next tab
    await page.keyboard.press('Tab');
    
    // Check if the Dashboard tab is focused
    await expect(page.locator('button[role="tab"]:has-text("Dashboard")')).toBeFocused();
    
    // Activate the Dashboard tab
    await page.keyboard.press('Enter');
    
    // Check if the Dashboard tab is selected
    await expect(page.locator('button[role="tab"]:has-text("Dashboard")')).toHaveAttribute('aria-selected', 'true');
    
    // Check if the Dashboard content is visible
    await expect(page.locator('div[role="tabpanel"] >> text=Recommendation Performance')).toBeVisible();
  });

  test('should have proper color contrast', async ({ page }) => {
    // Run axe against the page with only the color-contrast rule
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2aa'])
      .options({
        runOnly: {
          type: 'rule',
          values: ['color-contrast']
        }
      })
      .analyze();
    
    // Assert there are no color contrast violations
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('should have proper ARIA attributes', async ({ page }) => {
    // Check if the tabs have the correct ARIA attributes
    await expect(page.locator('button[role="tab"]:has-text("Overview")')).toHaveAttribute('aria-selected', 'true');
    await expect(page.locator('button[role="tab"]:has-text("Overview")')).toHaveAttribute('aria-controls');
    
    // Click on Preferences tab
    await page.click('button[role="tab"]:has-text("Preferences")');
    
    // Check if the Preferences tab has the correct ARIA attributes
    await expect(page.locator('button[role="tab"]:has-text("Preferences")')).toHaveAttribute('aria-selected', 'true');
    await expect(page.locator('button[role="tab"]:has-text("Preferences")')).toHaveAttribute('aria-controls');
    
    // Check if the tabpanel has the correct ARIA attributes
    const preferencesTabId = await page.locator('button[role="tab"]:has-text("Preferences")').getAttribute('id');
    await expect(page.locator('div[role="tabpanel"]')).toHaveAttribute('aria-labelledby', preferencesTabId);
  });

  test('should be accessible on mobile devices', async ({ page }) => {
    // Set viewport to mobile size
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Run axe against the page
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
      .analyze();
    
    // Assert there are no violations
    expect(accessibilityScanResults.violations).toEqual([]);
    
    // Check if the tabs are still accessible
    await expect(page.locator('button[role="tab"]:has-text("Overview")')).toBeVisible();
    
    // Click on Item Relationships tab
    await page.click('button[role="tab"]:has-text("Item Relationships")');
    
    // Check if the tab content is visible
    await expect(page.locator('div[role="tabpanel"] >> text=This visualization shows how items are related')).toBeVisible();
  });
});
