import i18n, { changeLanguage, getCurrentLanguage, isRTL, availableLanguages } from '../i18n';

// Mock document methods
document.documentElement.lang = '';
document.documentElement.dir = '';
document.body.classList.add = jest.fn();
document.body.classList.remove = jest.fn();

describe('i18n Configuration', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    document.documentElement.lang = '';
    document.documentElement.dir = '';
  });
  
  it('initializes i18n with correct configuration', () => {
    // Check that i18n is initialized
    expect(i18n).toBeDefined();
    
    // Check that the fallback language is English
    expect(i18n.options.fallbackLng).toBe('en');
    
    // Check that the default namespace is 'translation'
    expect(i18n.options.defaultNS).toBe('translation');
    
    // Check that interpolation escapeValue is false (for React)
    expect(i18n.options.interpolation?.escapeValue).toBe(false);
  });
  
  it('provides available languages', () => {
    // Check that availableLanguages is defined
    expect(availableLanguages).toBeDefined();
    
    // Check that it contains at least English
    const englishLanguage = availableLanguages.find(lang => lang.code === 'en');
    expect(englishLanguage).toBeDefined();
    expect(englishLanguage?.name).toBe('English');
    expect(englishLanguage?.flag).toBe('🇺🇸');
  });
  
  it('changes the language correctly', () => {
    // Mock i18n.changeLanguage
    const originalChangeLanguage = i18n.changeLanguage;
    i18n.changeLanguage = jest.fn();
    
    // Call changeLanguage
    changeLanguage('es');
    
    // Check that i18n.changeLanguage was called with the correct language
    expect(i18n.changeLanguage).toHaveBeenCalledWith('es');
    
    // Check that document.documentElement.lang was set correctly
    expect(document.documentElement.lang).toBe('es');
    
    // Check that document.documentElement.dir was set correctly
    expect(document.documentElement.dir).toBe('ltr');
    
    // Check that the RTL class was not added
    expect(document.body.classList.add).not.toHaveBeenCalled();
    expect(document.body.classList.remove).toHaveBeenCalledWith('rtl');
    
    // Restore original method
    i18n.changeLanguage = originalChangeLanguage;
  });
  
  it('handles RTL languages correctly', () => {
    // Mock i18n.changeLanguage
    const originalChangeLanguage = i18n.changeLanguage;
    i18n.changeLanguage = jest.fn();
    
    // Call changeLanguage with an RTL language
    changeLanguage('ar');
    
    // Check that i18n.changeLanguage was called with the correct language
    expect(i18n.changeLanguage).toHaveBeenCalledWith('ar');
    
    // Check that document.documentElement.lang was set correctly
    expect(document.documentElement.lang).toBe('ar');
    
    // Check that document.documentElement.dir was set correctly
    expect(document.documentElement.dir).toBe('rtl');
    
    // Check that the RTL class was added
    expect(document.body.classList.add).toHaveBeenCalledWith('rtl');
    expect(document.body.classList.remove).not.toHaveBeenCalled();
    
    // Restore original method
    i18n.changeLanguage = originalChangeLanguage;
  });
  
  it('gets the current language correctly', () => {
    // Mock i18n.language
    Object.defineProperty(i18n, 'language', {
      get: jest.fn().mockReturnValue('fr'),
      configurable: true
    });
    
    // Call getCurrentLanguage
    const currentLanguage = getCurrentLanguage();
    
    // Check that it returns the correct language
    expect(currentLanguage).toBe('fr');
  });
  
  it('returns English as fallback if no language is set', () => {
    // Mock i18n.language to be undefined
    Object.defineProperty(i18n, 'language', {
      get: jest.fn().mockReturnValue(undefined),
      configurable: true
    });
    
    // Call getCurrentLanguage
    const currentLanguage = getCurrentLanguage();
    
    // Check that it returns English as fallback
    expect(currentLanguage).toBe('en');
  });
  
  it('checks if the current language is RTL correctly', () => {
    // Mock getCurrentLanguage to return an RTL language
    const originalGetCurrentLanguage = getCurrentLanguage;
    (getCurrentLanguage as jest.Mock) = jest.fn().mockReturnValue('ar');
    
    // Call isRTL
    const rtl = isRTL();
    
    // Check that it returns true for RTL languages
    expect(rtl).toBe(true);
    
    // Mock getCurrentLanguage to return an LTR language
    (getCurrentLanguage as jest.Mock) = jest.fn().mockReturnValue('en');
    
    // Call isRTL again
    const ltr = isRTL();
    
    // Check that it returns false for LTR languages
    expect(ltr).toBe(false);
    
    // Restore original method
    (getCurrentLanguage as jest.Mock) = originalGetCurrentLanguage;
  });
});
