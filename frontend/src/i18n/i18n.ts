import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

// Import translation files
import enTranslation from './locales/en/translation.json';
import esTranslation from './locales/es/translation.json';
import frTranslation from './locales/fr/translation.json';
import deTranslation from './locales/de/translation.json';
import zhTranslation from './locales/zh/translation.json';

// Resources object containing all translations
const resources = {
  en: {
    translation: enTranslation
  },
  es: {
    translation: esTranslation
  },
  fr: {
    translation: frTranslation
  },
  de: {
    translation: deTranslation
  },
  zh: {
    translation: zhTranslation
  }
};

/**
 * Initialize i18next with configuration
 * 
 * Features:
 * - Language detection from browser/localStorage
 * - Backend loading for dynamic translation loading
 * - React integration
 * - Fallback language: English
 * - Debug mode in development
 */
i18n
  // Load translations from backend (for dynamic loading)
  .use(Backend)
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',
    
    // Common namespace used around the app
    defaultNS: 'translation',
    
    // Allow keys to be phrases having `:`, `.`
    keySeparator: false,
    
    interpolation: {
      escapeValue: false, // React already escapes values
      formatSeparator: ','
    },
    
    // Detection options
    detection: {
      order: ['querystring', 'cookie', 'localStorage', 'navigator', 'htmlTag'],
      lookupQuerystring: 'lng',
      lookupCookie: 'i18next',
      lookupLocalStorage: 'i18nextLng',
      caches: ['localStorage', 'cookie']
    },
    
    // React options
    react: {
      useSuspense: true,
      bindI18n: 'languageChanged',
      bindI18nStore: '',
      transEmptyNodeValue: '',
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],
      skipTranslationOnMissingKey: false
    }
  });

export default i18n;

// Type definitions for translation keys
export type TranslationKey = keyof typeof enTranslation;

/**
 * Available languages in the application
 */
export const availableLanguages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Español', flag: '🇪🇸' },
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
  { code: 'zh', name: '中文', flag: '🇨🇳' }
];

/**
 * Change the current language
 * @param language Language code to switch to
 */
export const changeLanguage = (language: string) => {
  i18n.changeLanguage(language);
  
  // Set HTML lang attribute
  document.documentElement.lang = language;
  
  // Set text direction
  const isRTL = ['ar', 'he'].includes(language);
  document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
  
  // Add RTL class to body if needed
  if (isRTL) {
    document.body.classList.add('rtl');
  } else {
    document.body.classList.remove('rtl');
  }
};

/**
 * Get the current language
 * @returns Current language code
 */
export const getCurrentLanguage = () => {
  return i18n.language || 'en';
};

/**
 * Check if the current language is RTL
 * @returns Boolean indicating if current language is RTL
 */
export const isRTL = () => {
  const currentLang = getCurrentLanguage();
  return ['ar', 'he'].includes(currentLang);
};
