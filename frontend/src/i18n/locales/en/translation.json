{"common": {"appName": "RentUP", "tagline": "The Community Marketplace for Endless Shared Possibilities", "loading": "Loading...", "error": "An error occurred", "retry": "Retry", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "view": "View", "all": "All", "more": "More", "less": "Less", "show": "Show", "hide": "<PERSON>de", "close": "Close", "submit": "Submit", "required": "Required", "optional": "Optional", "success": "Success", "warning": "Warning", "info": "Information", "notification": "Notification", "settings": "Settings", "profile": "Profile", "account": "Account", "help": "Help", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "signUp": "Sign Up", "signIn": "Sign In", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "username": "Username", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "address": "Address", "city": "City", "state": "State", "country": "Country", "zipCode": "Zip Code", "phone": "Phone", "dateOfBirth": "Date of Birth", "gender": "Gender", "male": "Male", "female": "Female", "other": "Other", "preferNotToSay": "Prefer not to say", "language": "Language", "currency": "<PERSON><PERSON><PERSON><PERSON>", "theme": "Theme", "light": "Light", "dark": "Dark", "system": "System", "notifications": "Notifications", "messages": "Messages", "noResults": "No results found", "noData": "No data available", "noItems": "No items available", "noNotifications": "No notifications", "noMessages": "No messages", "seeAll": "See All", "viewAll": "View All", "viewDetails": "View Details", "learnMore": "Learn More", "getStarted": "Get Started", "continue": "Continue", "goBack": "Go Back", "goHome": "Go Home", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "now": "Now", "never": "Never", "always": "Always", "yes": "Yes", "no": "No", "maybe": "Maybe", "on": "On", "off": "Off", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "pending": "Pending", "completed": "Completed", "cancelled": "Cancelled", "rejected": "Rejected", "approved": "Approved", "draft": "Draft", "published": "Published", "unpublished": "Unpublished", "public": "Public", "private": "Private", "free": "Free", "paid": "Paid", "premium": "Premium"}, "navigation": {"home": "Home", "search": "Search", "categories": "Categories", "listings": "Listings", "myListings": "My Listings", "favorites": "Favorites", "saved": "Saved", "history": "History", "recentlyViewed": "Recently Viewed", "recommendations": "Recommendations", "auctions": "Auctions", "deals": "Deals", "nearMe": "Near Me", "trending": "Trending", "popular": "Popular", "newArrivals": "New Arrivals", "featuredItems": "Featured Items", "community": "Community", "events": "Events", "blog": "Blog", "forum": "Forum", "support": "Support", "contactUs": "Contact Us", "aboutUs": "About Us", "howItWorks": "How It Works", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "cookiePolicy": "<PERSON><PERSON>", "faq": "FAQ", "sitemap": "Sitemap", "browseAll": "Browse All", "rentToBuy": "Rent-to-Buy", "aiInsights": "AI Insights", "designSystem": "Design System", "responsiveDesign": "Responsive Design", "performance": "Performance", "language": "Language", "settings": "Settings"}, "home": {"welcomeBack": "Welcome back, {{name}}!", "featuredListings": "Featured Listings", "popularCategories": "Popular Categories", "trendingNow": "Trending Now", "recentlyAdded": "Recently Added", "nearYou": "Near You", "recommendedForYou": "Recommended For You", "basedOnYourInterests": "Based on your interests", "exploreMore": "Explore More", "viewAll": "View All", "browseByCategory": "Browse by Category", "joinCommunity": "Join Our Community", "communityStats": "{{userCount}}+ Users • {{listingCount}}+ Listings • {{categoryCount}}+ Categories", "trustAndSafety": "Trust & Safety", "verifiedUsers": "Verified Users", "securePayments": "Secure Payments", "insuranceProtection": "Insurance Protection", "customerSupport": "24/7 Customer Support"}, "search": {"searchPlaceholder": "Search for items, categories, or keywords", "advancedSearch": "Advanced Search", "filters": "Filters", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters", "saveSearch": "Save Search", "savedSearches": "Saved Searches", "recentSearches": "Recent Searches", "popularSearches": "Popular Searches", "noSearchResults": "No results found for \"{{query}}\"", "searchSuggestions": "Search Suggestions", "didYouMean": "Did you mean: {{suggestion}}?", "resultsCount": "{{count}} results found", "sortBy": "Sort By", "relevance": "Relevance", "newest": "Newest", "oldest": "Oldest", "priceHighToLow": "Price: High to Low", "priceLowToHigh": "Price: Low to High", "distance": "Distance", "rating": "Rating", "popularity": "Popularity"}}