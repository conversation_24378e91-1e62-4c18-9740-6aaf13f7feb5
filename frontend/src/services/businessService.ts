import axios from 'axios';
import { 
  BusinessAccount, 
  BusinessAccountCreateRequest, 
  BusinessAccountUpdateRequest,
  BusinessMember,
  BusinessMemberCreateRequest,
  BusinessMemberUpdateRequest,
  BusinessMemberInvitation
} from '../types/BusinessAccount';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

/**
 * Business account service for managing business accounts and members
 */
const businessService = {
  /**
   * Get all business accounts for a user
   * @param userId - The user ID
   * @returns Promise with array of business accounts
   */
  getBusinessAccounts: async (userId: string): Promise<BusinessAccount[]> => {
    try {
      const response = await axios.get(`${API_URL}/api/v1/users/${encodeURIComponent(userId)}/businesses`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true // Include cookies for authentication
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching business accounts:', error);
      throw error;
    }
  },

  /**
   * Get a single business account by ID
   * @param businessId - The business account ID
   * @returns Promise with business account data
   */
  getBusinessAccount: async (businessId: string): Promise<BusinessAccount> => {
    try {
      const response = await axios.get(`${API_URL}/api/v1/businesses/${encodeURIComponent(businessId)}`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching business account:', error);
      throw error;
    }
  },

  /**
   * Create a new business account
   * @param data - The business account data
   * @returns Promise with created business account
   */
  createBusinessAccount: async (data: BusinessAccountCreateRequest): Promise<BusinessAccount> => {
    try {
      const response = await axios.post(`${API_URL}/api/v1/businesses`, data, {
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error('Error creating business account:', error);
      throw error;
    }
  },

  /**
   * Update a business account
   * @param businessId - The business account ID
   * @param data - The updated business account data
   * @returns Promise with updated business account
   */
  updateBusinessAccount: async (businessId: string, data: BusinessAccountUpdateRequest): Promise<BusinessAccount> => {
    try {
      const response = await axios.patch(`${API_URL}/api/v1/businesses/${encodeURIComponent(businessId)}`, data, {
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error('Error updating business account:', error);
      throw error;
    }
  },

  /**
   * Get members of a business account
   * @param businessId - The business account ID
   * @returns Promise with array of business members
   */
  getBusinessMembers: async (businessId: string): Promise<BusinessMember[]> => {
    try {
      const response = await axios.get(`${API_URL}/api/v1/businesses/${encodeURIComponent(businessId)}/members`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching business members:', error);
      throw error;
    }
  },

  /**
   * Invite a new member to a business account
   * @param businessId - The business account ID
   * @param data - The member invitation data
   * @returns Promise with invitation data
   */
  inviteBusinessMember: async (businessId: string, data: BusinessMemberCreateRequest): Promise<BusinessMemberInvitation> => {
    try {
      const response = await axios.post(`${API_URL}/api/v1/businesses/${encodeURIComponent(businessId)}/invitations`, data, {
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error('Error inviting business member:', error);
      throw error;
    }
  },

  /**
   * Update a business member
   * @param businessId - The business account ID
   * @param memberId - The member ID
   * @param data - The updated member data
   * @returns Promise with updated member data
   */
  updateBusinessMember: async (businessId: string, memberId: string, data: BusinessMemberUpdateRequest): Promise<BusinessMember> => {
    try {
      const response = await axios.patch(
        `${API_URL}/api/v1/businesses/${encodeURIComponent(businessId)}/members/${encodeURIComponent(memberId)}`,
        data,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          },
          withCredentials: true
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error updating business member:', error);
      throw error;
    }
  },

  /**
   * Remove a member from a business account
   * @param businessId - The business account ID
   * @param memberId - The member ID
   * @returns Promise with success status
   */
  removeBusinessMember: async (businessId: string, memberId: string): Promise<{ success: boolean }> => {
    try {
      const response = await axios.delete(
        `${API_URL}/api/v1/businesses/${encodeURIComponent(businessId)}/members/${encodeURIComponent(memberId)}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          },
          withCredentials: true
        }
      );
      return response.data;
    } catch (error) {
      console.error('Error removing business member:', error);
      throw error;
    }
  },

  /**
   * Get pending invitations for a business account
   * @param businessId - The business account ID
   * @returns Promise with array of pending invitations
   */
  getBusinessInvitations: async (businessId: string): Promise<BusinessMemberInvitation[]> => {
    try {
      const response = await axios.get(`${API_URL}/api/v1/businesses/${encodeURIComponent(businessId)}/invitations`, {
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching business invitations:', error);
      throw error;
    }
  }
};

export default businessService;
