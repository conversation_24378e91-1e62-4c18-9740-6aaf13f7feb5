import axios from 'axios';
import MockAdapter from 'axios-mock-adapter';
import businessService from '../businessService';
import { BusinessAccount, BusinessMember, BusinessRole } from '../../types/BusinessAccount';

// Mock axios
const mockAxios = new MockAdapter(axios);
const API_URL = process.env.VITE_API_URL || 'http://localhost:8000';

describe('Business Service', () => {
  // Reset mocks before each test
  beforeEach(() => {
    mockAxios.reset();
  });

  describe('getBusinessAccounts', () => {
    it('should fetch business accounts for a user', async () => {
      const mockBusinessAccounts: BusinessAccount[] = [
        {
          id: '1',
          name: 'Test Business',
          description: 'Test Description',
          industry: 'real_estate',
          size: 'small',
          tier: 'professional',
          website: 'https://example.com',
          email: '<EMAIL>',
          phone_number: '+**********',
          logo_url: 'https://example.com/logo.png',
          is_verified: true,
          created_at: '2025-01-01T00:00:00Z',
          updated_at: '2025-01-01T00:00:00Z',
          owner_id: 'user123',
          member_count: 5,
          listing_count: 10,
          rental_count: 2,
          is_active: true,
          subscription_status: 'active',
          features: {
            max_members: 20,
            max_listings: 200,
            bulk_upload: true,
            analytics: true,
            priority_support: true,
            custom_branding: false,
            api_access: false,
            dedicated_account_manager: false,
            insurance_discount: 5,
            transaction_fee_discount: 10
          }
        }
      ];

      mockAxios.onGet(`${API_URL}/api/v1/users/user123/businesses`).reply(200, mockBusinessAccounts);

      const result = await businessService.getBusinessAccounts('user123');
      expect(result).toEqual(mockBusinessAccounts);
    });

    it('should handle errors when fetching business accounts', async () => {
      mockAxios.onGet(`${API_URL}/api/v1/users/user123/businesses`).reply(500, { error: 'Server error' });

      await expect(businessService.getBusinessAccounts('user123')).rejects.toThrow();
    });
  });

  describe('getBusinessAccount', () => {
    it('should fetch a single business account by ID', async () => {
      const mockBusinessAccount: BusinessAccount = {
        id: '1',
        name: 'Test Business',
        description: 'Test Description',
        industry: 'real_estate',
        size: 'small',
        tier: 'professional',
        website: 'https://example.com',
        email: '<EMAIL>',
        phone_number: '+**********',
        logo_url: 'https://example.com/logo.png',
        is_verified: true,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
        owner_id: 'user123',
        member_count: 5,
        listing_count: 10,
        rental_count: 2,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      };

      mockAxios.onGet(`${API_URL}/api/v1/businesses/1`).reply(200, mockBusinessAccount);

      const result = await businessService.getBusinessAccount('1');
      expect(result).toEqual(mockBusinessAccount);
    });

    it('should handle errors when fetching a business account', async () => {
      mockAxios.onGet(`${API_URL}/api/v1/businesses/1`).reply(404, { error: 'Not found' });

      await expect(businessService.getBusinessAccount('1')).rejects.toThrow();
    });
  });

  describe('createBusinessAccount', () => {
    it('should create a new business account', async () => {
      const mockBusinessAccount: BusinessAccount = {
        id: '1',
        name: 'New Business',
        description: 'New Description',
        industry: 'real_estate',
        size: 'small',
        tier: 'starter',
        website: 'https://example.com',
        email: '<EMAIL>',
        phone_number: '+**********',
        logo_url: 'https://example.com/logo.png',
        is_verified: false,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
        owner_id: 'user123',
        member_count: 1,
        listing_count: 0,
        rental_count: 0,
        is_active: true,
        subscription_status: 'trial',
        features: {
          max_members: 5,
          max_listings: 50,
          bulk_upload: false,
          analytics: false,
          priority_support: false,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 0,
          transaction_fee_discount: 0
        }
      };

      const createData = {
        name: 'New Business',
        description: 'New Description',
        industry: 'real_estate',
        size: 'small',
        website: 'https://example.com',
        email: '<EMAIL>',
        phone_number: '+**********'
      };

      mockAxios.onPost(`${API_URL}/api/v1/businesses`).reply(201, mockBusinessAccount);

      const result = await businessService.createBusinessAccount(createData);
      expect(result).toEqual(mockBusinessAccount);
    });

    it('should handle errors when creating a business account', async () => {
      const createData = {
        name: 'New Business',
        description: 'New Description',
        industry: 'real_estate',
        size: 'small',
        website: 'https://example.com',
        email: '<EMAIL>',
        phone_number: '+**********'
      };

      mockAxios.onPost(`${API_URL}/api/v1/businesses`).reply(400, { error: 'Invalid data' });

      await expect(businessService.createBusinessAccount(createData)).rejects.toThrow();
    });
  });

  describe('getBusinessMembers', () => {
    it('should fetch members of a business account', async () => {
      const mockMembers: BusinessMember[] = [
        {
          id: 'member1',
          business_id: '1',
          user_id: 'user123',
          role: BusinessRole.OWNER,
          name: 'Test User',
          email: '<EMAIL>',
          avatar_url: 'https://example.com/avatar.png',
          joined_at: '2025-01-01T00:00:00Z',
          invited_by: 'user123',
          is_active: true,
          last_active: '2025-01-01T00:00:00Z',
          permissions: {
            manage_members: true,
            manage_listings: true,
            manage_rentals: true,
            manage_billing: true,
            manage_settings: true,
            view_analytics: true,
            approve_transactions: true
          }
        }
      ];

      mockAxios.onGet(`${API_URL}/api/v1/businesses/1/members`).reply(200, mockMembers);

      const result = await businessService.getBusinessMembers('1');
      expect(result).toEqual(mockMembers);
    });

    it('should handle errors when fetching business members', async () => {
      mockAxios.onGet(`${API_URL}/api/v1/businesses/1/members`).reply(500, { error: 'Server error' });

      await expect(businessService.getBusinessMembers('1')).rejects.toThrow();
    });
  });
});
