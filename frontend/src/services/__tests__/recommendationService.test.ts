import axios from 'axios';
import recommendationService from '../recommendationService';

// Mock the config/constants module
jest.mock('../../config/constants', () => ({
  API_URL: 'http://localhost:8000'
}));

// Import after mocking
import { API_URL } from '../../config/constants';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('recommendationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getApiUrl', () => {
    it('returns the API URL', () => {
      expect(recommendationService.getApiUrl()).toBe(API_URL);
    });
  });

  describe('getPersonalizedRecommendations', () => {
    it('fetches personalized recommendations successfully', async () => {
      const mockData = [{ id: '1', name: 'Item 1' }, { id: '2', name: 'Item 2' }];
      mockedAxios.get.mockResolvedValueOnce({ data: mockData });

      const result = await recommendationService.getPersonalizedRecommendations('user-123');

      expect(result).toEqual(mockData);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        `${API_URL}/api/v1/recommendations/personalized`,
        expect.objectContaining({
          params: { user_id: 'user-123', limit: 10 },
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          })
        })
      );
    });

    it('throws an error when userId is not provided', async () => {
      await expect(recommendationService.getPersonalizedRecommendations('')).rejects.toThrow('User ID is required');
    });

    it('handles API errors', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('API error'));

      await expect(recommendationService.getPersonalizedRecommendations('user-123')).rejects.toThrow('API error');
    });
  });

  describe('getSimilarItems', () => {
    it('fetches similar items successfully', async () => {
      const mockData = [{ id: '1', name: 'Item 1' }, { id: '2', name: 'Item 2' }];
      mockedAxios.get.mockResolvedValueOnce({ data: mockData });

      const result = await recommendationService.getSimilarItems('item-123');

      expect(result).toEqual(mockData);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        `${API_URL}/api/v1/recommendations/similar/item-123`,
        expect.objectContaining({
          params: { limit: 6 },
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          })
        })
      );
    });

    it('throws an error when itemId is not provided', async () => {
      await expect(recommendationService.getSimilarItems('')).rejects.toThrow('Item ID is required');
    });
  });

  describe('getPreferenceVisualization', () => {
    it('fetches preference visualization data successfully', async () => {
      const mockData = [
        { preferenceType: 'liked', weight: 0.8, count: 10 },
        { preferenceType: 'viewed', weight: 0.5, count: 20 }
      ];
      mockedAxios.get.mockResolvedValueOnce({ data: mockData });

      const result = await recommendationService.getPreferenceVisualization('user-123');

      expect(result).toEqual(mockData);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        `${API_URL}/api/v1/users/user-123/preferences/visualization`,
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          })
        })
      );
    });

    it('throws an error when userId is not provided', async () => {
      await expect(recommendationService.getPreferenceVisualization('')).rejects.toThrow('User ID is required');
    });

    it('validates and filters invalid data', async () => {
      const mockData = [
        { preferenceType: 'liked', weight: 0.8, count: 10 },
        { preferenceType: 'invalid', weight: -1, count: -5 }, // Invalid data
        { preferenceType: 'viewed', weight: 0.5, count: 20 }
      ];
      mockedAxios.get.mockResolvedValueOnce({ data: mockData });

      const result = await recommendationService.getPreferenceVisualization('user-123');

      // Should filter out the invalid data
      expect(result).toEqual([
        { preferenceType: 'liked', weight: 0.8, count: 10 },
        { preferenceType: 'viewed', weight: 0.5, count: 20 }
      ]);
    });
  });

  describe('getEmbeddingVisualization', () => {
    it('fetches embedding visualization data successfully', async () => {
      const mockData = [
        { id: '1', x: 0.1, y: 0.2, category: 'electronics', name: 'Item 1' },
        { id: '2', x: 0.3, y: 0.4, category: 'furniture', name: 'Item 2' }
      ];
      mockedAxios.get.mockResolvedValueOnce({ data: mockData });

      const result = await recommendationService.getEmbeddingVisualization('user-123');

      expect(result).toEqual(mockData);
      expect(mockedAxios.get).toHaveBeenCalledWith(
        `${API_URL}/api/v1/users/user-123/embeddings/visualization`,
        expect.objectContaining({
          params: { limit: 50 },
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          })
        })
      );
    });

    it('throws an error when userId is not provided', async () => {
      await expect(recommendationService.getEmbeddingVisualization('')).rejects.toThrow('User ID is required');
    });

    it('validates and filters invalid data', async () => {
      const mockData = [
        { id: '1', x: 0.1, y: 0.2, category: 'electronics', name: 'Item 1' },
        { id: '2', x: 'invalid', y: 0.4, category: 'furniture', name: 'Item 2' }, // Invalid data
        { id: '3', x: 0.5, y: 0.6, category: 'electronics', name: 'Item 3' }
      ];
      mockedAxios.get.mockResolvedValueOnce({ data: mockData });

      const result = await recommendationService.getEmbeddingVisualization('user-123');

      // Should filter out the invalid data
      expect(result).toEqual([
        { id: '1', x: 0.1, y: 0.2, category: 'electronics', name: 'Item 1' },
        { id: '3', x: 0.5, y: 0.6, category: 'electronics', name: 'Item 3' }
      ]);
    });
  });
});
