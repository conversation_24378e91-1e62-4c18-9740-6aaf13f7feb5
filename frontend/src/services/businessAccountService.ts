import { get, post, put, del } from '../utils/apiUtils';
import {
  BusinessAccount,
  BusinessAccountCreateRequest,
  BusinessAccountUpdateRequest,
  BusinessMember,
  BusinessMemberInvitation,
  BusinessMemberInviteRequest
} from '../types/BusinessAccount';

/**
 * Get all business accounts for the current user
 */
export async function getUserBusinessAccounts() {
  const response = await get<BusinessMember[]>('api/v1/business-accounts/me', {
    requireAuth: true,
  });

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Get a specific business account
 */
export async function getBusinessAccount(businessId: string) {
  const response = await get<BusinessAccount>(`api/v1/business-accounts/${businessId}`, {
    requireAuth: true,
  });

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Create a new business account
 */
export async function createBusinessAccount(businessData: BusinessAccountCreateRequest) {
  const response = await post<BusinessAccount>('api/v1/business-accounts', businessData, {
    requireAuth: true,
  });

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Update a business account
 */
export async function updateBusinessAccount(businessId: string, businessData: BusinessAccountUpdateRequest) {
  const response = await put<BusinessAccount>(`api/v1/business-accounts/${businessId}`, businessData, {
    requireAuth: true,
  });

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Delete a business account
 */
export async function deleteBusinessAccount(businessId: string) {
  const response = await del(`api/v1/business-accounts/${businessId}`, {
    requireAuth: true,
  });

  if (response.error) {
    throw new Error(response.error);
  }

  return true;
}

/**
 * Get all members of a business account
 */
export async function getBusinessMembers(businessId: string) {
  const response = await get<BusinessMember[]>(`api/v1/business-accounts/${businessId}/members`, {
    requireAuth: true,
  });

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Invite a new member to a business account
 */
export async function inviteBusinessMember(businessId: string, inviteData: BusinessMemberInviteRequest) {
  const response = await post<BusinessMemberInvitation>(
    `api/v1/business-accounts/${businessId}/invitations`,
    inviteData,
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Get all invitations for a business account
 */
export async function getBusinessInvitations(businessId: string) {
  const response = await get<BusinessMemberInvitation[]>(
    `api/v1/business-accounts/${businessId}/invitations`,
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Cancel an invitation
 */
export async function cancelBusinessInvitation(businessId: string, invitationId: string) {
  const response = await del(
    `api/v1/business-accounts/${businessId}/invitations/${invitationId}`,
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return true;
}

/**
 * Accept an invitation
 */
export async function acceptBusinessInvitation(invitationId: string) {
  const response = await post<BusinessMember>(
    `api/v1/business-invitations/${invitationId}/accept`,
    {},
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Decline an invitation
 */
export async function declineBusinessInvitation(invitationId: string) {
  const response = await post(
    `api/v1/business-invitations/${invitationId}/decline`,
    {},
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return true;
}

/**
 * Remove a member from a business account
 */
export async function removeBusinessMember(businessId: string, memberId: string) {
  const response = await del(
    `api/v1/business-accounts/${businessId}/members/${memberId}`,
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return true;
}

/**
 * Update a member's role and permissions
 */
export async function updateBusinessMember(
  businessId: string,
  memberId: string,
  updateData: Partial<BusinessMember>
) {
  const response = await put<BusinessMember>(
    `api/v1/business-accounts/${businessId}/members/${memberId}`,
    updateData,
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Get user's pending invitations
 */
export async function getUserPendingInvitations() {
  const response = await get<BusinessMemberInvitation[]>(
    'api/v1/business-invitations/pending',
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Get business account statistics
 */
export async function getBusinessStatistics(businessId: string) {
  const response = await get<any>(
    `api/v1/business-accounts/${businessId}/statistics`,
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Upgrade business account tier
 */
export async function upgradeBusinessTier(businessId: string, tier: string) {
  const response = await post<BusinessAccount>(
    `api/v1/business-accounts/${businessId}/upgrade`,
    { tier },
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return response.data;
}

/**
 * Cancel business account subscription
 */
export async function cancelBusinessSubscription(businessId: string) {
  const response = await post(
    `api/v1/business-accounts/${businessId}/cancel-subscription`,
    {},
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return true;
}

/**
 * Reactivate business account subscription
 */
export async function reactivateBusinessSubscription(businessId: string) {
  const response = await post(
    `api/v1/business-accounts/${businessId}/reactivate-subscription`,
    {},
    {
      requireAuth: true,
    }
  );

  if (response.error) {
    throw new Error(response.error);
  }

  return true;
}
