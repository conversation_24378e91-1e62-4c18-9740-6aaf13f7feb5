import { EmbeddingPoint, PreferenceData } from '../recommendationService';

/**
 * Mock data for testing
 */
export const mockEmbeddingData: EmbeddingPoint[] = [
  { id: 'item1', name: 'Item 1', x: 0.2, y: 0.3, category: 'electronics', price: 299.99 },
  { id: 'item2', name: 'Item 2', x: 0.5, y: 0.7, category: 'furniture', price: 499.99 },
  { id: 'item3', name: 'Item 3', x: 0.8, y: 0.1, category: 'clothing', price: 59.99 },
  { id: 'item4', name: 'Item 4', x: 0.3, y: 0.4, category: 'electronics', price: 199.99 },
  { id: 'item5', name: 'Item 5', x: 0.6, y: 0.2, category: 'furniture', price: 399.99 },
];

export const mockPreferenceData: PreferenceData[] = [
  { preferenceType: 'favorited', weight: 0.9, count: 8 },
  { preferenceType: 'liked', weight: 0.8, count: 12 },
  { preferenceType: 'viewed', weight: 0.5, count: 30 },
];

/**
 * Mock recommendation service for testing
 */
const mockRecommendationService = {
  /**
   * Get the API URL
   * @returns The API URL
   */
  getApiUrl: jest.fn(() => 'http://localhost:8000'),

  /**
   * Get embedding visualization data
   * @param itemId Optional item ID to get embeddings for
   * @param categoryId Optional category ID to filter by
   * @param limit Optional limit on the number of results
   * @returns Promise that resolves to embedding data
   */
  getEmbeddingVisualization: jest.fn(
    (itemId?: string, categoryId?: string, limit?: number): Promise<EmbeddingPoint[]> => {
      // Filter by item ID if provided
      let filteredData = mockEmbeddingData;
      if (itemId) {
        filteredData = filteredData.filter(item => item.id === itemId);
      }

      // Filter by category ID if provided
      if (categoryId) {
        filteredData = filteredData.filter(item => item.category === categoryId);
      }

      // Limit the number of results if provided
      if (limit && limit > 0 && limit < filteredData.length) {
        filteredData = filteredData.slice(0, limit);
      }

      return Promise.resolve(filteredData);
    }
  ),

  /**
   * Get preference visualization data
   * @returns Promise that resolves to preference data
   */
  getPreferenceVisualization: jest.fn(
    (): Promise<PreferenceData[]> => {
      return Promise.resolve(mockPreferenceData);
    }
  ),

  /**
   * Get recommendations for a user
   * @param userId User ID to get recommendations for
   * @param limit Optional limit on the number of results
   * @returns Promise that resolves to embedding data (representing recommendations)
   */
  getUserRecommendations: jest.fn(
    (userId: string, limit?: number): Promise<EmbeddingPoint[]> => {
      // Limit the number of results if provided
      let filteredData = mockEmbeddingData;
      if (limit && limit > 0 && limit < filteredData.length) {
        filteredData = filteredData.slice(0, limit);
      }

      return Promise.resolve(filteredData);
    }
  ),

  /**
   * Get similar items
   * @param itemId Item ID to get similar items for
   * @param limit Optional limit on the number of results
   * @returns Promise that resolves to embedding data (representing similar items)
   */
  getSimilarItems: jest.fn(
    (itemId: string, limit?: number): Promise<EmbeddingPoint[]> => {
      // Filter out the item itself
      let filteredData = mockEmbeddingData.filter(item => item.id !== itemId);

      // Limit the number of results if provided
      if (limit && limit > 0 && limit < filteredData.length) {
        filteredData = filteredData.slice(0, limit);
      }

      return Promise.resolve(filteredData);
    }
  ),

  /**
   * Configure the mock service to return an error
   * @param methodName The method to make fail
   * @param error The error to throw
   */
  mockError: (
    methodName: 'getEmbeddingVisualization' | 'getPreferenceVisualization' | 'getUserRecommendations' | 'getSimilarItems',
    error: Error
  ) => {
    mockRecommendationService[methodName].mockRejectedValue(error);
  },

  /**
   * Configure the mock service to return empty data
   * @param methodName The method to make return empty data
   */
  mockEmpty: (
    methodName: 'getEmbeddingVisualization' | 'getPreferenceVisualization' | 'getUserRecommendations' | 'getSimilarItems'
  ) => {
    if (methodName === 'getPreferenceVisualization') {
      mockRecommendationService[methodName].mockResolvedValue([]);
    } else {
      mockRecommendationService[methodName].mockResolvedValue([]);
    }
  },

  /**
   * Reset all mocks
   */
  reset: () => {
    jest.clearAllMocks();
    mockRecommendationService.getEmbeddingVisualization.mockImplementation(
      (itemId?: string, categoryId?: string, limit?: number): Promise<EmbeddingPoint[]> => {
        let filteredData = mockEmbeddingData;
        if (itemId) {
          filteredData = filteredData.filter(item => item.id === itemId);
        }
        if (categoryId) {
          filteredData = filteredData.filter(item => item.category === categoryId);
        }
        if (limit && limit > 0 && limit < filteredData.length) {
          filteredData = filteredData.slice(0, limit);
        }
        return Promise.resolve(filteredData);
      }
    );
    mockRecommendationService.getPreferenceVisualization.mockImplementation(
      (): Promise<PreferenceData[]> => {
        return Promise.resolve(mockPreferenceData);
      }
    );
    mockRecommendationService.getUserRecommendations.mockImplementation(
      (userId: string, limit?: number): Promise<EmbeddingPoint[]> => {
        let filteredData = mockEmbeddingData;
        if (limit && limit > 0 && limit < filteredData.length) {
          filteredData = filteredData.slice(0, limit);
        }
        return Promise.resolve(filteredData);
      }
    );
    mockRecommendationService.getSimilarItems.mockImplementation(
      (itemId: string, limit?: number): Promise<EmbeddingPoint[]> => {
        let filteredData = mockEmbeddingData.filter(item => item.id !== itemId);
        if (limit && limit > 0 && limit < filteredData.length) {
          filteredData = filteredData.slice(0, limit);
        }
        return Promise.resolve(filteredData);
      }
    );
  },
};

export default mockRecommendationService;
