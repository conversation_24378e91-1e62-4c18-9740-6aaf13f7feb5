/**
 * Business Account Types
 * 
 * This file contains types related to business accounts in the RentUP platform.
 */

/**
 * Business account subscription tiers
 */
export enum BusinessTier {
  STARTER = 'starter',
  PROFESSIONAL = 'professional',
  ENTERPRISE = 'enterprise'
}

/**
 * Business account member roles
 */
export enum BusinessRole {
  OWNER = 'owner',
  ADMIN = 'admin',
  MEMBER = 'member',
  VIEWER = 'viewer'
}

/**
 * Business account industry types
 */
export enum BusinessIndustry {
  REAL_ESTATE = 'real_estate',
  CONSTRUCTION = 'construction',
  EVENTS = 'events',
  PHOTOGRAPHY = 'photography',
  FILM_PRODUCTION = 'film_production',
  MUSIC = 'music',
  SPORTS = 'sports',
  TRAVEL = 'travel',
  EDUCATION = 'education',
  TECHNOLOGY = 'technology',
  OTHER = 'other'
}

/**
 * Business account size categories
 */
export enum BusinessSize {
  INDIVIDUAL = 'individual',
  SMALL = '2-10',
  MEDIUM = '11-50',
  LARGE = '51-200',
  ENTERPRISE = '201+'
}

/**
 * Business account interface
 */
export interface BusinessAccount {
  id: string;
  name: string;
  description?: string;
  logo_url?: string;
  website?: string;
  industry: BusinessIndustry;
  size: BusinessSize;
  tier: BusinessTier;
  address?: BusinessAddress;
  phone_number?: string;
  email?: string;
  tax_id?: string;
  is_verified: boolean;
  verification_documents?: string[];
  created_at: string;
  updated_at: string;
  owner_id: string;
  member_count: number;
  listing_count: number;
  rental_count: number;
  is_active: boolean;
  subscription_status: 'active' | 'past_due' | 'canceled' | 'trialing';
  subscription_end_date?: string;
  features: BusinessFeatures;
}

/**
 * Business address interface
 */
export interface BusinessAddress {
  street_address: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_verified: boolean;
}

/**
 * Business features interface
 */
export interface BusinessFeatures {
  max_members: number;
  max_listings: number;
  bulk_upload: boolean;
  analytics: boolean;
  priority_support: boolean;
  custom_branding: boolean;
  api_access: boolean;
  dedicated_account_manager: boolean;
  insurance_discount: number;
  transaction_fee_discount: number;
}

/**
 * Business account member interface
 */
export interface BusinessMember {
  id: string;
  user_id: string;
  business_id: string;
  role: BusinessRole;
  name: string;
  email: string;
  avatar_url?: string;
  joined_at: string;
  invited_by: string;
  is_active: boolean;
  last_active?: string;
  permissions: BusinessPermissions;
}

/**
 * Business permissions interface
 */
export interface BusinessPermissions {
  manage_members: boolean;
  manage_listings: boolean;
  manage_rentals: boolean;
  manage_billing: boolean;
  manage_settings: boolean;
  view_analytics: boolean;
  approve_transactions: boolean;
}

/**
 * Business account creation request interface
 */
export interface BusinessAccountCreateRequest {
  name: string;
  description?: string;
  logo_url?: string;
  website?: string;
  industry: BusinessIndustry;
  size: BusinessSize;
  address?: Partial<BusinessAddress>;
  phone_number?: string;
  email?: string;
  tax_id?: string;
}

/**
 * Business account update request interface
 */
export interface BusinessAccountUpdateRequest {
  name?: string;
  description?: string;
  logo_url?: string;
  website?: string;
  industry?: BusinessIndustry;
  size?: BusinessSize;
  address?: Partial<BusinessAddress>;
  phone_number?: string;
  email?: string;
  tax_id?: string;
}

/**
 * Business member invitation interface
 */
export interface BusinessMemberInvitation {
  id: string;
  business_id: string;
  email: string;
  role: BusinessRole;
  invited_by: string;
  invited_at: string;
  expires_at: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  permissions: BusinessPermissions;
}

/**
 * Business member invitation request interface
 */
export interface BusinessMemberInviteRequest {
  email: string;
  role: BusinessRole;
  permissions: Partial<BusinessPermissions>;
}

/**
 * Business tier feature limits
 */
export const BUSINESS_TIER_LIMITS: Record<BusinessTier, BusinessFeatures> = {
  [BusinessTier.STARTER]: {
    max_members: 5,
    max_listings: 50,
    bulk_upload: false,
    analytics: false,
    priority_support: false,
    custom_branding: false,
    api_access: false,
    dedicated_account_manager: false,
    insurance_discount: 0,
    transaction_fee_discount: 0
  },
  [BusinessTier.PROFESSIONAL]: {
    max_members: 20,
    max_listings: 200,
    bulk_upload: true,
    analytics: true,
    priority_support: true,
    custom_branding: false,
    api_access: false,
    dedicated_account_manager: false,
    insurance_discount: 5,
    transaction_fee_discount: 10
  },
  [BusinessTier.ENTERPRISE]: {
    max_members: 100,
    max_listings: 1000,
    bulk_upload: true,
    analytics: true,
    priority_support: true,
    custom_branding: true,
    api_access: true,
    dedicated_account_manager: true,
    insurance_discount: 15,
    transaction_fee_discount: 20
  }
};
