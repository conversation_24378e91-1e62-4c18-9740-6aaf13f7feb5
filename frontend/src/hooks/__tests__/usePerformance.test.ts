import { renderHook, act } from '@testing-library/react';
import usePerformance from '../usePerformance';
import * as performanceMonitoring from '../../utils/performanceMonitoring';

// Mock the performance monitoring utilities
jest.mock('../../utils/performanceMonitoring', () => ({
  startMeasure: jest.fn().mockReturnValue(jest.fn().mockReturnValue({ 
    type: 'component-render',
    name: 'TestComponent-render',
    duration: 100,
    timestamp: 1234567890
  }))
}));

describe('usePerformance Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('should start measuring render time on mount', () => {
    renderHook(() => usePerformance('TestComponent'));
    
    expect(performanceMonitoring.startMeasure).toHaveBeenCalledWith(
      'component-render',
      'TestComponent-render',
      undefined
    );
  });
  
  it('should include metadata if provided', () => {
    const metadata = { props: { id: 123 } };
    renderHook(() => usePerformance('TestComponent', metadata));
    
    expect(performanceMonitoring.startMeasure).toHaveBeenCalledWith(
      'component-render',
      'TestComponent-render',
      metadata
    );
  });
  
  it('should stop measuring render time on unmount', () => {
    const stopMeasure = jest.fn();
    (performanceMonitoring.startMeasure as jest.Mock).mockReturnValue(stopMeasure);
    
    const { unmount } = renderHook(() => usePerformance('TestComponent'));
    
    unmount();
    
    expect(stopMeasure).toHaveBeenCalled();
  });
  
  it('should provide measureFunction utility', () => {
    const { result } = renderHook(() => usePerformance('TestComponent'));
    
    expect(result.current.measureFunction).toBeDefined();
  });
  
  it('should provide measureEventHandler utility', () => {
    const { result } = renderHook(() => usePerformance('TestComponent'));
    
    expect(result.current.measureEventHandler).toBeDefined();
  });
  
  it('should provide measureApiCall utility', () => {
    const { result } = renderHook(() => usePerformance('TestComponent'));
    
    expect(result.current.measureApiCall).toBeDefined();
  });
  
  it('should measure synchronous function execution time', () => {
    const { result } = renderHook(() => usePerformance('TestComponent'));
    
    const fn = jest.fn().mockReturnValue('result');
    const measuredFn = result.current.measureFunction('testFunction')(fn);
    
    const fnResult = measuredFn();
    
    expect(fnResult).toBe('result');
    expect(fn).toHaveBeenCalled();
    expect(performanceMonitoring.startMeasure).toHaveBeenCalledWith(
      'component-render',
      'TestComponent-testFunction',
      expect.any(Object)
    );
  });
  
  it('should measure asynchronous function execution time', async () => {
    const { result } = renderHook(() => usePerformance('TestComponent'));
    
    const fn = jest.fn().mockResolvedValue('async result');
    const measuredFn = result.current.measureFunction('testAsyncFunction')(fn);
    
    const fnResult = await measuredFn();
    
    expect(fnResult).toBe('async result');
    expect(fn).toHaveBeenCalled();
    expect(performanceMonitoring.startMeasure).toHaveBeenCalledWith(
      'component-render',
      'TestComponent-testAsyncFunction',
      expect.any(Object)
    );
  });
  
  it('should measure event handler execution time', () => {
    const { result } = renderHook(() => usePerformance('TestComponent'));
    
    const handler = jest.fn().mockReturnValue('result');
    const measuredHandler = result.current.measureEventHandler('click')(handler);
    
    const event = { type: 'click' } as React.SyntheticEvent;
    const handlerResult = measuredHandler(event);
    
    expect(handlerResult).toBe('result');
    expect(handler).toHaveBeenCalledWith(event);
    expect(performanceMonitoring.startMeasure).toHaveBeenCalledWith(
      'interaction',
      'TestComponent-event-click',
      expect.any(Object)
    );
  });
  
  it('should measure API call execution time', async () => {
    const { result } = renderHook(() => usePerformance('TestComponent'));
    
    const apiCall = jest.fn().mockResolvedValue({ data: 'response' });
    const measuredApiCall = result.current.measureApiCall('fetchData')(apiCall);
    
    const apiResult = await measuredApiCall('param1', 'param2');
    
    expect(apiResult).toEqual({ data: 'response' });
    expect(apiCall).toHaveBeenCalledWith('param1', 'param2');
    expect(performanceMonitoring.startMeasure).toHaveBeenCalledWith(
      'api-call',
      'TestComponent-api-fetchData',
      expect.any(Object)
    );
  });
  
  it('should handle function errors', async () => {
    const { result } = renderHook(() => usePerformance('TestComponent'));
    
    const error = new Error('Test error');
    const fn = jest.fn().mockRejectedValue(error);
    const measuredFn = result.current.measureFunction('errorFunction')(fn);
    
    await expect(measuredFn()).rejects.toThrow('Test error');
    
    expect(fn).toHaveBeenCalled();
    expect(performanceMonitoring.startMeasure).toHaveBeenCalledWith(
      'component-render',
      'TestComponent-errorFunction',
      expect.any(Object)
    );
  });
});
