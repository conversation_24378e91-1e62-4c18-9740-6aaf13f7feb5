import { useState } from 'react';

/**
 * A testable version of the useMediaQuery hook
 * This hook can be easily mocked in tests
 * 
 * @param query The media query to match
 * @param initialValue The initial value to use (for testing)
 * @returns Whether the media query matches
 */
export const useTestMediaQuery = (
  query: string,
  initialValue = false
): boolean => {
  // In a test environment, just return the initial value
  if (process.env.NODE_ENV === 'test') {
    return initialValue;
  }

  // In a browser environment, use the real implementation
  const [matches, setMatches] = useState<boolean>(() => {
    // Check if window is defined (for SSR)
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return initialValue;
  });

  // Add event listener for changes
  if (typeof window !== 'undefined') {
    const mediaQuery = window.matchMedia(query);
    
    // Update matches when the media query changes
    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };
    
    // Add event listener
    mediaQuery.addEventListener('change', handleChange);
    
    // Remove event listener on cleanup
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }

  return matches;
};

/**
 * A testable version of the useIsMobile hook
 * 
 * @param initialValue The initial value to use (for testing)
 * @returns Whether the device is mobile
 */
export const useTestIsMobile = (initialValue = false): boolean => {
  return useTestMediaQuery('(max-width: 640px)', initialValue);
};

/**
 * A testable version of the useIsTablet hook
 * 
 * @param initialValue The initial value to use (for testing)
 * @returns Whether the device is a tablet
 */
export const useTestIsTablet = (initialValue = false): boolean => {
  return useTestMediaQuery('(min-width: 641px) and (max-width: 1024px)', initialValue);
};

/**
 * A testable version of the useIsDesktop hook
 * 
 * @param initialValue The initial value to use (for testing)
 * @returns Whether the device is a desktop
 */
export const useTestIsDesktop = (initialValue = true): boolean => {
  return useTestMediaQuery('(min-width: 1025px)', initialValue);
};
