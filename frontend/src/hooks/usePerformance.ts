import { useEffect, useRef } from 'react';
import { startMeasure, PerformanceMetricType } from '../utils/performanceMonitoring';

/**
 * Hook for measuring component performance
 * 
 * @param componentName The name of the component
 * @param metadata Additional metadata to include with the metric
 * @returns An object with performance measurement functions
 */
export const usePerformance = (
  componentName: string,
  metadata?: Record<string, any>
) => {
  // Ref to track if the component is mounted
  const isMounted = useRef(false);
  
  // Ref to store the render measurement function
  const measureRenderRef = useRef<ReturnType<typeof startMeasure> | null>(null);
  
  // Start measuring render time on mount
  useEffect(() => {
    isMounted.current = true;
    
    // Start measuring render time
    measureRenderRef.current = startMeasure(
      'component-render',
      `${componentName}-render`,
      metadata
    );
    
    // Stop measuring render time on unmount
    return () => {
      if (measureRenderRef.current) {
        measureRenderRef.current();
        measureRenderRef.current = null;
      }
      isMounted.current = false;
    };
  }, [componentName, metadata]);
  
  /**
   * Measure a function execution time
   * @param name The name of the function
   * @param type The type of metric
   * @returns A function that wraps the original function and measures its execution time
   */
  const measureFunction = <T extends (...args: any[]) => any>(
    name: string,
    type: PerformanceMetricType = 'component-render'
  ) => {
    return (fn: T): T => {
      return ((...args: Parameters<T>): ReturnType<T> => {
        // Start measuring
        const stopMeasure = startMeasure(
          type,
          `${componentName}-${name}`,
          {
            ...metadata,
            args: args.map(arg => 
              typeof arg === 'object' ? 
                (arg === null ? null : Object.keys(arg)) : 
                typeof arg
            ),
          }
        );
        
        try {
          // Execute the function
          const result = fn(...args);
          
          // If the result is a promise, wait for it to resolve
          if (result instanceof Promise) {
            return result
              .then(value => {
                stopMeasure();
                return value;
              })
              .catch(error => {
                stopMeasure();
                throw error;
              }) as ReturnType<T>;
          }
          
          // Otherwise, stop measuring and return the result
          stopMeasure();
          return result;
        } catch (error) {
          // Stop measuring if an error occurs
          stopMeasure();
          throw error;
        }
      }) as T;
    };
  };
  
  /**
   * Measure an event handler execution time
   * @param name The name of the event handler
   * @returns A function that wraps the event handler and measures its execution time
   */
  const measureEventHandler = <E extends React.SyntheticEvent>(
    name: string
  ) => {
    return <T extends (event: E, ...args: any[]) => any>(
      handler: T
    ): T => {
      return measureFunction<T>(
        `event-${name}`,
        'interaction'
      )(handler);
    };
  };
  
  /**
   * Measure an API call execution time
   * @param name The name of the API call
   * @returns A function that wraps the API call and measures its execution time
   */
  const measureApiCall = <T extends (...args: any[]) => Promise<any>>(
    name: string
  ) => {
    return (apiCall: T): T => {
      return measureFunction<T>(
        `api-${name}`,
        'api-call'
      )(apiCall);
    };
  };
  
  return {
    measureFunction,
    measureEventHandler,
    measureApiCall,
  };
};

export default usePerformance;
