// Mock constants for testing
export const API_URL = 'http://localhost:8000';
export const APP_NAME = 'RentUp';
export const APP_VERSION = '1.0.0';
export const DEFAULT_CURRENCY = 'USD';
export const DEFAULT_LOCALE = 'en-US';
export const DEFAULT_TIMEZONE = 'America/New_York';
export const DEFAULT_DATE_FORMAT = 'MM/DD/YYYY';
export const DEFAULT_TIME_FORMAT = 'h:mm A';
export const DEFAULT_DATETIME_FORMAT = 'MM/DD/YYYY h:mm A';
export const DEFAULT_PAGINATION_LIMIT = 10;
export const DEFAULT_PAGINATION_OFFSET = 0;
export const DEFAULT_PAGINATION_TOTAL = 0;
export const DEFAULT_PAGINATION_PAGE = 1;
export const DEFAULT_PAGINATION_PAGES = 1;
export const DEFAULT_PAGINATION_SIZE = 10;
export const DEFAULT_PAGINATION_SIZES = [10, 25, 50, 100];
export const DEFAULT_PAGINATION_SIZES_LABEL = 'Items per page';
export const DEFAULT_PAGINATION_PREV_LABEL = 'Previous';
export const DEFAULT_PAGINATION_NEXT_LABEL = 'Next';
export const DEFAULT_PAGINATION_FIRST_LABEL = 'First';
export const DEFAULT_PAGINATION_LAST_LABEL = 'Last';
export const DEFAULT_PAGINATION_PAGE_LABEL = 'Page';
export const DEFAULT_PAGINATION_OF_LABEL = 'of';
export const DEFAULT_PAGINATION_SHOWING_LABEL = 'Showing';
export const DEFAULT_PAGINATION_TO_LABEL = 'to';
export const DEFAULT_PAGINATION_OF_TOTAL_LABEL = 'of';
export const DEFAULT_PAGINATION_ENTRIES_LABEL = 'entries';
export const DEFAULT_PAGINATION_NO_DATA_LABEL = 'No data available';
export const DEFAULT_PAGINATION_LOADING_LABEL = 'Loading...';
export const DEFAULT_PAGINATION_ERROR_LABEL = 'Error loading data';
export const DEFAULT_PAGINATION_RETRY_LABEL = 'Retry';
