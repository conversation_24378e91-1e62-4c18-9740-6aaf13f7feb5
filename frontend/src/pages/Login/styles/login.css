/**
 * Login Component Styles
 * 
 * Comprehensive styles for the modular login component including
 * responsive design, accessibility features, and modern UI elements.
 */

/* Main Container */
.login-container {
  @apply min-h-screen flex;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Left Side - Branding */
.login-branding {
  @apply flex-1 flex flex-col justify-center items-center p-12 text-white;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.login-tagline {
  @apply text-3xl font-bold mb-6 text-center leading-tight;
}

.login-description {
  @apply text-lg text-center opacity-90 max-w-md;
}

/* Right Side - Login Content */
.login-content {
  @apply flex-1 flex items-center justify-center p-8;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.login-form-container {
  @apply w-full max-w-md space-y-6;
}

/* Login Tabs */
.login-tabs {
  @apply flex border-b border-gray-200 mb-6;
}

.login-tab {
  @apply flex-1 py-3 px-4 text-center cursor-pointer transition-all duration-200;
  @apply border-b-2 border-transparent text-gray-600 hover:text-gray-800;
}

.login-tab.active {
  @apply border-primary-500 text-primary-600 font-medium;
}

.login-tab:focus {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* Form Elements */
.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  @apply transition-colors duration-200;
}

.form-input.error {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
}

.form-input:disabled {
  @apply bg-gray-100 cursor-not-allowed opacity-60;
}

/* Password Field */
.password-input-container {
  @apply relative;
}

.password-input {
  @apply pr-10;
}

.password-toggle {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2;
  @apply text-gray-400 hover:text-gray-600 focus:outline-none;
  @apply focus:ring-2 focus:ring-primary-500 rounded;
}

/* Form Footer */
.form-footer {
  @apply flex items-center justify-between text-sm;
}

.remember-me {
  @apply flex items-center;
}

.forgot-password {
  @apply text-primary-600 hover:text-primary-500 font-medium;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 rounded;
}

/* Login Button */
.login-button {
  @apply w-full bg-primary-600 text-white py-3 px-4 rounded-md font-medium;
  @apply hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  @apply transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

/* Social Login */
.divider {
  @apply relative text-center;
}

.divider::before {
  @apply absolute inset-0 flex items-center;
  content: '';
}

.divider::before {
  @apply border-t border-gray-300;
}

.divider-text {
  @apply bg-white px-4 text-sm text-gray-500;
}

.social-login-container {
  @apply space-y-3;
}

.social-login-button {
  @apply w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md;
  @apply text-sm font-medium text-gray-700 bg-white hover:bg-gray-50;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  @apply transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.social-icon {
  @apply w-5 h-5 mr-3;
}

/* QR Code Login */
.qr-login-container {
  @apply space-y-6;
}

.qr-header {
  @apply text-center;
}

.qr-title {
  @apply text-lg font-semibold text-gray-900 mb-2;
}

.qr-description {
  @apply text-sm text-gray-600;
}

.qr-code-wrapper {
  @apply flex justify-center;
}

.qr-code-container {
  @apply relative;
}

.qr-code {
  @apply w-48 h-48 border border-gray-200 rounded-lg;
  @apply transition-opacity duration-200;
}

.qr-code.expired {
  @apply opacity-50;
}

.qr-overlay {
  @apply absolute inset-0 flex items-center justify-center;
  @apply bg-black bg-opacity-50 rounded-lg;
}

.qr-expired-message {
  @apply flex flex-col items-center text-white;
}

.qr-expired-icon {
  @apply w-8 h-8 mb-2;
}

.qr-loading {
  @apply flex flex-col items-center space-y-3;
}

.qr-skeleton {
  @apply w-48 h-48 bg-gray-200 rounded-lg animate-pulse;
}

/* QR Status */
.qr-status {
  @apply flex justify-center;
}

.status-waiting,
.status-scanned,
.status-expired {
  @apply flex items-center space-x-2 text-sm;
}

.status-indicator {
  @apply w-3 h-3 rounded-full;
}

.status-indicator.waiting {
  @apply bg-yellow-400 animate-pulse;
}

.status-indicator.scanned {
  @apply bg-green-400;
}

.status-indicator.expired {
  @apply bg-red-400;
}

/* QR Controls */
.qr-controls {
  @apply flex flex-col items-center space-y-3;
}

.qr-timer {
  @apply flex items-center space-x-2 text-sm text-gray-600;
}

.timer-icon {
  @apply w-4 h-4;
}

.qr-refresh-button {
  @apply flex items-center space-x-2 px-4 py-2 text-sm;
  @apply text-primary-600 hover:text-primary-500 font-medium;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 rounded;
}

.refresh-icon {
  @apply w-4 h-4;
}

/* QR Instructions */
.qr-instructions {
  @apply bg-gray-50 p-4 rounded-lg;
}

.instructions-title {
  @apply text-sm font-medium text-gray-900 mb-2;
}

.instructions-list {
  @apply text-sm text-gray-600 space-y-1 mb-4;
  @apply list-decimal list-inside;
}

.app-download-links {
  @apply text-center;
}

.download-text {
  @apply text-sm text-gray-600 mb-2;
}

.download-buttons {
  @apply flex justify-center space-x-3;
}

.app-store-button,
.google-play-button {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 rounded;
}

.app-store-button img,
.google-play-button img {
  @apply h-10;
}

/* CAPTCHA */
.captcha-container {
  @apply space-y-3 p-4 bg-gray-50 rounded-lg;
}

.captcha-question {
  @apply flex items-center justify-between;
}

.captcha-text {
  @apply text-lg font-mono font-bold;
}

.captcha-refresh {
  @apply p-1 text-gray-500 hover:text-gray-700;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 rounded;
}

.captcha-input-container {
  @apply flex space-x-2;
}

.captcha-input {
  @apply flex-1;
}

.captcha-verify {
  @apply px-4 py-2 bg-primary-600 text-white rounded-md;
  @apply hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500;
}

/* Messages */
.error-message {
  @apply text-sm text-red-600 mt-1;
}

.lockout-notice {
  @apply bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded-md;
}

/* Sign Up Link */
.signup-link-container {
  @apply text-center text-sm text-gray-600;
}

.signup-link {
  @apply text-primary-600 hover:text-primary-500 font-medium;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 rounded;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .login-branding {
    @apply hidden;
  }
  
  .login-content {
    @apply flex-none w-full;
  }
}

@media (max-width: 640px) {
  .login-content {
    @apply p-4;
  }
  
  .login-form-container {
    @apply max-w-none;
  }
  
  .qr-code {
    @apply w-40 h-40;
  }
  
  .qr-skeleton {
    @apply w-40 h-40;
  }
}

/* Accessibility */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for better accessibility */
*:focus {
  @apply outline-none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .form-input {
    @apply border-2 border-black;
  }
  
  .login-button {
    @apply border-2 border-black;
  }
}
