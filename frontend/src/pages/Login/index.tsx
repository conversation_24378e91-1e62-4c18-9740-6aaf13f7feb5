import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { LoginBranding } from './components/LoginBranding';
import { LoginTabs } from './components/LoginTabs';
import { LoginForm } from './components/LoginForm';
import { QRCodeLogin } from './components/QRCodeLogin';
import { MessageDisplay } from './components/MessageDisplay';
import { SignUpLink } from './components/SignUpLink';
import { useLoginState } from './hooks/useLoginState';
import { useLoginSecurity } from './hooks/useLoginSecurity';
import { useLoginMessages } from './hooks/useLoginMessages';
import { LoginTab } from './utils/types';
import { useToast } from '../../hooks/useToast';
import './styles/login.css';

/**
 * Login Component
 * 
 * Main login page component that provides multiple authentication methods
 * including password login, QR code login, and social media authentication.
 * Features comprehensive security measures and user-friendly interface.
 */
const Login: React.FC = () => {
  // Tab state management
  const [activeTab, setActiveTab] = useState<LoginTab>('password');

  // Custom hooks for state management
  const {
    showPassword,
    setShowPassword,
    qrRefreshKey,
    handleRefreshQR
  } = useLoginState();

  const {
    loginAttempts,
    isLocked,
    lockTimer,
    captchaVerified,
    setCaptchaVerified,
    incrementLoginAttempts,
    handleCaptchaVerify
  } = useLoginSecurity();

  const {
    loginError,
    setLoginError,
    successMessage,
    setSuccessMessage
  } = useLoginMessages();

  // Hooks
  const location = useLocation();
  const { showToast } = useToast();

  // Handle success message from registration
  useEffect(() => {
    if (location.state && location.state.message && location.state.type === 'success') {
      setSuccessMessage(location.state.message);
      showToast(location.state.message, 'success');

      // Clear the location state to prevent showing the message again on refresh
      window.history.replaceState({}, document.title);
    }
  }, [location, showToast, setSuccessMessage]);

  return (
    <div className="login-container">
      {/* Left side - Branding */}
      <LoginBranding />

      {/* Right side - Login Content */}
      <div className="login-content">
        <div className="login-form-container">
          {/* Header */}
          <div className="text-center mb-4">
            <h1 className="text-xl font-bold text-near-black">Log In</h1>
            <p className="text-medium-gray mt-1 text-sm">Welcome to rentUP</p>
          </div>

          {/* Login Tabs */}
          <LoginTabs
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Messages */}
          <MessageDisplay
            successMessage={successMessage}
            loginError={loginError}
            isLocked={isLocked}
            lockTimer={lockTimer}
          />

          {/* Password Login Form */}
          {activeTab === 'password' && (
            <LoginForm
              showPassword={showPassword}
              onTogglePassword={setShowPassword}
              loginAttempts={loginAttempts}
              isLocked={isLocked}
              captchaVerified={captchaVerified}
              onCaptchaVerify={handleCaptchaVerify}
              onLoginError={setLoginError}
              onIncrementAttempts={incrementLoginAttempts}
            />
          )}

          {/* QR Code Login */}
          {activeTab === 'qr' && (
            <QRCodeLogin
              qrRefreshKey={qrRefreshKey}
              onRefreshQR={handleRefreshQR}
            />
          )}

          {/* Sign Up Link */}
          <SignUpLink />
        </div>
      </div>
    </div>
  );
};

export default Login;
