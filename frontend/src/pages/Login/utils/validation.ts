import { FormData, ValidationResult } from './types';

/**
 * Validation utilities for the Login component
 */

/**
 * Email validation regex
 */
const EMAIL_REGEX = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;

/**
 * Password validation requirements
 */
const PASSWORD_MIN_LENGTH = 6;
const PASSWORD_MAX_LENGTH = 128;

/**
 * Validation schema for login form
 */
export const schema = {
  email: {
    required: 'Email is required',
    pattern: {
      value: EMAIL_REGEX,
      message: 'Please enter a valid email address'
    }
  },
  password: {
    required: 'Password is required',
    minLength: {
      value: PASSWORD_MIN_LENGTH,
      message: `Password must be at least ${PASSWORD_MIN_LENGTH} characters`
    },
    maxLength: {
      value: PASSWORD_MAX_LENGTH,
      message: `Password must be less than ${PASSWORD_MAX_LENGTH} characters`
    }
  }
};

/**
 * Validate email format
 */
export const validateEmail = (email: string): ValidationResult => {
  if (!email) {
    return { isValid: false, error: 'Email is required' };
  }

  if (!EMAIL_REGEX.test(email)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }

  return { isValid: true };
};

/**
 * Validate password strength
 */
export const validatePassword = (password: string): ValidationResult => {
  if (!password) {
    return { isValid: false, error: 'Password is required' };
  }

  if (password.length < PASSWORD_MIN_LENGTH) {
    return { 
      isValid: false, 
      error: `Password must be at least ${PASSWORD_MIN_LENGTH} characters` 
    };
  }

  if (password.length > PASSWORD_MAX_LENGTH) {
    return { 
      isValid: false, 
      error: `Password must be less than ${PASSWORD_MAX_LENGTH} characters` 
    };
  }

  return { isValid: true };
};

/**
 * Validate complete login form
 */
export const validateLoginForm = (data: FormData, validationSchema: any): ValidationResult => {
  // Validate email
  const emailValidation = validateEmail(data.email);
  if (!emailValidation.isValid) {
    return emailValidation;
  }

  // Validate password
  const passwordValidation = validatePassword(data.password);
  if (!passwordValidation.isValid) {
    return passwordValidation;
  }

  return { isValid: true };
};

/**
 * Sanitize form input to prevent XSS
 */
export const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 255); // Limit length
};

/**
 * Validate and sanitize form data
 */
export const sanitizeFormData = (data: FormData): FormData => {
  return {
    email: sanitizeInput(data.email).toLowerCase(),
    password: data.password, // Don't sanitize password as it may contain special chars
    rememberMe: Boolean(data.rememberMe)
  };
};

/**
 * Check if email domain is allowed (for enterprise restrictions)
 */
export const isAllowedEmailDomain = (email: string, allowedDomains?: string[]): boolean => {
  if (!allowedDomains || allowedDomains.length === 0) {
    return true; // No restrictions
  }

  const domain = email.split('@')[1]?.toLowerCase();
  return allowedDomains.some(allowedDomain => 
    domain === allowedDomain.toLowerCase()
  );
};

/**
 * Validate session token format
 */
export const validateSessionToken = (token: string): boolean => {
  // Basic token format validation
  return /^[a-zA-Z0-9_-]+$/.test(token) && token.length >= 10;
};
