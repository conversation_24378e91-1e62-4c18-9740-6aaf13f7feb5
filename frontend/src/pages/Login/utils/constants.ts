import { SecurityConfig, QRConfig } from './types';

/**
 * Constants for the Login component
 */

/**
 * Security configuration
 */
export const SECURITY_CONFIG: SecurityConfig = {
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 300, // 5 minutes in seconds
  CAPTCHA_THRESHOLD: 3 // Show CAPTCHA after 3 failed attempts
};

/**
 * QR code configuration
 */
export const QR_CONFIG: QRConfig = {
  EXPIRY_TIME: 300, // 5 minutes in seconds
  POLL_INTERVAL: 2000, // Poll every 2 seconds
  REFRESH_THRESHOLD: 30 // Show refresh warning at 30 seconds
};

/**
 * Form validation constants
 */
export const VALIDATION_CONSTANTS = {
  EMAIL_MAX_LENGTH: 255,
  PASSWORD_MIN_LENGTH: 6,
  PASSWORD_MAX_LENGTH: 128
} as const;

/**
 * UI constants
 */
export const UI_CONSTANTS = {
  ANIMATION_DURATION: 300,
  TOAST_DURATION: 5000,
  DEBOUNCE_DELAY: 300
} as const;

/**
 * API endpoints (would be environment-specific in real app)
 */
export const API_ENDPOINTS = {
  LOGIN: '/api/auth/login',
  SOCIAL_LOGIN: '/api/auth/social',
  QR_GENERATE: '/api/auth/qr/generate',
  QR_POLL: '/api/auth/qr/poll',
  REFRESH_TOKEN: '/api/auth/refresh'
} as const;

/**
 * Social login provider configurations
 */
export const SOCIAL_PROVIDERS = {
  GOOGLE: {
    name: 'Google',
    clientId: process.env.REACT_APP_GOOGLE_CLIENT_ID,
    scope: 'email profile'
  },
  FACEBOOK: {
    name: 'Facebook',
    appId: process.env.REACT_APP_FACEBOOK_APP_ID,
    scope: 'email'
  },
  APPLE: {
    name: 'Apple',
    clientId: process.env.REACT_APP_APPLE_CLIENT_ID,
    scope: 'email name'
  },
  GITHUB: {
    name: 'GitHub',
    clientId: process.env.REACT_APP_GITHUB_CLIENT_ID,
    scope: 'user:email'
  }
} as const;

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  INVALID_CREDENTIALS: 'Invalid email or password. Please try again.',
  ACCOUNT_LOCKED: 'Account temporarily locked due to too many failed attempts.',
  CAPTCHA_REQUIRED: 'Please complete the security verification.',
  SESSION_EXPIRED: 'Your session has expired. Please log in again.',
  SOCIAL_LOGIN_FAILED: 'Social login failed. Please try again.',
  QR_EXPIRED: 'QR code has expired. Please refresh to generate a new one.',
  FORM_VALIDATION: 'Please check your input and try again.'
} as const;

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful! Redirecting...',
  QR_SCANNED: 'QR code scanned! Please confirm on your mobile device.',
  PASSWORD_RESET_SENT: 'Password reset instructions sent to your email.'
} as const;

/**
 * CSS class names
 */
export const CSS_CLASSES = {
  CONTAINER: 'login-container',
  BRANDING: 'login-branding',
  CONTENT: 'login-content',
  FORM_CONTAINER: 'login-form-container',
  TAB: 'login-tab',
  TAB_ACTIVE: 'active',
  FORM_GROUP: 'form-group',
  FORM_LABEL: 'form-label',
  FORM_INPUT: 'form-input',
  ERROR: 'error',
  BUTTON_PRIMARY: 'login-button',
  BUTTON_SOCIAL: 'social-login-button',
  LOADING: 'loading',
  DISABLED: 'disabled'
} as const;

/**
 * Accessibility labels
 */
export const ARIA_LABELS = {
  LOGIN_FORM: 'Login form',
  EMAIL_INPUT: 'Email address',
  PASSWORD_INPUT: 'Password',
  SHOW_PASSWORD: 'Show password',
  HIDE_PASSWORD: 'Hide password',
  REMEMBER_ME: 'Remember me',
  LOGIN_BUTTON: 'Log in to your account',
  SOCIAL_LOGIN: 'Sign in with social media',
  QR_CODE: 'QR code for mobile login',
  REFRESH_QR: 'Refresh QR code',
  CAPTCHA: 'Security verification'
} as const;
