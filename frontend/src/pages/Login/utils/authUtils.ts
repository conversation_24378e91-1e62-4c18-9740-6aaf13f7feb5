import { FormData, LoginResult, SocialLoginR<PERSON>ult, QRLoginResult } from './types';
import { sanitizeFormData } from './validation';
import { API_ENDPOINTS, ERROR_MESSAGES } from './constants';

/**
 * Authentication utility functions for the Login component
 */

/**
 * Simulate API delay for development
 */
const simulateDelay = (ms: number = 1000): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Handle login API call
 */
export const loginUser = async (formData: FormData): Promise<LoginResult> => {
  try {
    // Sanitize form data
    const sanitizedData = sanitizeFormData(formData);

    // Simulate API call delay
    await simulateDelay();

    // Mock authentication logic (replace with real API call)
    if (sanitizedData.email === '<EMAIL>' && sanitizedData.password === 'password123') {
      return {
        success: true,
        user: {
          id: '1',
          email: sanitizedData.email,
          name: 'Demo User',
          avatar: '/default-avatar.png'
        }
      };
    } else {
      return {
        success: false,
        error: ERROR_MESSAGES.INVALID_CREDENTIALS
      };
    }
  } catch (error) {
    console.error('Login API error:', error);
    return {
      success: false,
      error: ERROR_MESSAGES.NETWORK_ERROR
    };
  }
};

/**
 * Handle social login API call
 */
export const socialLoginUser = async (provider: string): Promise<SocialLoginResult> => {
  try {
    // Simulate API call delay
    await simulateDelay(1500);

    // Mock social login logic (replace with real OAuth implementation)
    return {
      success: true,
      user: {
        id: '2',
        email: `user@${provider}.com`,
        name: `${provider} User`,
        avatar: `/avatars/${provider}.png`,
        provider
      }
    };
  } catch (error) {
    console.error(`${provider} login error:`, error);
    return {
      success: false,
      error: ERROR_MESSAGES.SOCIAL_LOGIN_FAILED
    };
  }
};

/**
 * Handle QR code authentication polling
 */
export const pollQRAuthentication = async (sessionId: string): Promise<QRLoginResult> => {
  try {
    // Simulate API call
    await simulateDelay(500);

    // Mock QR authentication logic (replace with real WebSocket or polling)
    const random = Math.random();
    
    if (random < 0.1) { // 10% chance of success
      return {
        success: true,
        user: {
          id: '3',
          email: '<EMAIL>',
          name: 'Mobile User',
          avatar: '/mobile-avatar.png'
        }
      };
    } else if (random < 0.3) { // 20% chance of being scanned
      return {
        success: false,
        scanned: true
      };
    } else {
      return {
        success: false,
        scanned: false
      };
    }
  } catch (error) {
    console.error('QR polling error:', error);
    return {
      success: false,
      error: ERROR_MESSAGES.NETWORK_ERROR
    };
  }
};

/**
 * Generate secure session ID for QR code
 */
export const generateSessionId = (): string => {
  const timestamp = Date.now().toString(36);
  const randomPart = Math.random().toString(36).substr(2, 9);
  return `qr_${timestamp}_${randomPart}`;
};

/**
 * Validate session token format
 */
export const isValidSessionToken = (token: string): boolean => {
  return /^qr_[a-z0-9]+_[a-z0-9]+$/.test(token);
};

/**
 * Get user-friendly error message
 */
export const getErrorMessage = (error: any): string => {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.response?.data?.message) {
    return error.response.data.message;
  }

  if (error?.message) {
    return error.message;
  }

  return ERROR_MESSAGES.NETWORK_ERROR;
};

/**
 * Store authentication token securely
 */
export const storeAuthToken = (token: string, rememberMe: boolean = false): void => {
  const storage = rememberMe ? localStorage : sessionStorage;
  storage.setItem('authToken', token);
};

/**
 * Remove authentication token
 */
export const removeAuthToken = (): void => {
  localStorage.removeItem('authToken');
  sessionStorage.removeItem('authToken');
};

/**
 * Get stored authentication token
 */
export const getAuthToken = (): string | null => {
  return localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = (): boolean => {
  const token = getAuthToken();
  return token !== null && token.length > 0;
};
