/**
 * TypeScript interfaces and types for the Login component
 */

/**
 * Login tab types
 */
export type LoginTab = 'password' | 'qr';

/**
 * Social login provider types
 */
export type SocialProvider = 'google' | 'facebook' | 'apple' | 'github';

/**
 * QR code status types
 */
export type QRStatus = 'waiting' | 'scanned' | 'expired' | 'authenticated';

/**
 * Form data interface for login form
 */
export interface FormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * Login result interface
 */
export interface LoginResult {
  success: boolean;
  error?: string;
  user?: any;
}

/**
 * Social login result interface
 */
export interface SocialLoginResult {
  success: boolean;
  error?: string;
  user?: any;
}

/**
 * QR login result interface
 */
export interface QRLoginResult {
  success: boolean;
  scanned?: boolean;
  error?: string;
  user?: any;
}

/**
 * Component props interfaces
 */
export interface LoginBrandingProps {
  // No props needed
}

export interface LoginTabsProps {
  activeTab: LoginTab;
  onTabChange: (tab: LoginTab) => void;
}

export interface MessageDisplayProps {
  successMessage: string | null;
  loginError: string | null;
  isLocked: boolean;
  lockTimer: number;
}

export interface LoginFormProps {
  showPassword: boolean;
  onTogglePassword: (show: boolean) => void;
  loginAttempts: number;
  isLocked: boolean;
  captchaVerified: boolean;
  onCaptchaVerify: () => void;
  onLoginError: (error: string | null) => void;
  onIncrementAttempts: () => void;
}

export interface EmailFieldProps {
  register: any;
  error?: any;
  disabled?: boolean;
}

export interface PasswordFieldProps {
  register: any;
  error?: any;
  showPassword: boolean;
  onTogglePassword: (show: boolean) => void;
  disabled?: boolean;
}

export interface CaptchaVerificationProps {
  onVerify: () => void;
}

export interface SocialLoginButtonsProps {
  onLoginError: (error: string | null) => void;
}

export interface QRCodeLoginProps {
  qrRefreshKey: number;
  onRefreshQR: () => void;
}

export interface SignUpLinkProps {
  // No props needed
}

/**
 * Hook return types
 */
export interface UseLoginStateReturn {
  showPassword: boolean;
  setShowPassword: (show: boolean) => void;
  qrRefreshKey: number;
  handleRefreshQR: () => void;
}

export interface UseLoginSecurityReturn {
  loginAttempts: number;
  isLocked: boolean;
  lockTimer: number;
  captchaVerified: boolean;
  setCaptchaVerified: (verified: boolean) => void;
  incrementLoginAttempts: () => void;
  handleCaptchaVerify: () => void;
  resetSecurity: () => void;
}

export interface UseLoginMessagesReturn {
  loginError: string | null;
  setLoginError: (error: string | null) => void;
  successMessage: string | null;
  setSuccessMessage: (message: string | null) => void;
  clearMessages: () => void;
}

export interface UseLoginSubmitReturn {
  onSubmit: (data: FormData) => Promise<void>;
  isLoading: boolean;
}

export interface UseSocialLoginReturn {
  handleGoogleLogin: () => void;
  handleFacebookLogin: () => void;
  handleAppleLogin: () => void;
  handleGitHubLogin: () => void;
  isLoading: boolean;
  loadingProvider: SocialProvider | null;
}

export interface UseQRCodeAuthReturn {
  qrCodeData: string | null;
  qrStatus: QRStatus;
  timeRemaining: number;
  isExpired: boolean;
  handleRefresh: () => void;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Security configuration interface
 */
export interface SecurityConfig {
  MAX_LOGIN_ATTEMPTS: number;
  LOCKOUT_DURATION: number;
  CAPTCHA_THRESHOLD: number;
}

/**
 * QR configuration interface
 */
export interface QRConfig {
  EXPIRY_TIME: number;
  POLL_INTERVAL: number;
  REFRESH_THRESHOLD: number;
}
