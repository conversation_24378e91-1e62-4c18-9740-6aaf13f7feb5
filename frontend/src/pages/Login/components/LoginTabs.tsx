import React from 'react';
import { LoginTab } from '../utils/types';

/**
 * LoginTabs Component
 * 
 * Provides tab navigation between password and QR code login methods.
 * Includes accessible tab controls with proper ARIA attributes.
 */
interface LoginTabsProps {
  activeTab: LoginTab;
  onTabChange: (tab: LoginTab) => void;
}

export const LoginTabs: React.FC<LoginTabsProps> = ({
  activeTab,
  onTabChange
}) => {
  return (
    <div className="login-tabs" role="tablist" aria-label="Login methods">
      <div
        className={`login-tab ${activeTab === 'password' ? 'active' : ''}`}
        onClick={() => onTabChange('password')}
        role="tab"
        aria-selected={activeTab === 'password'}
        aria-controls="password-login-panel"
        tabIndex={activeTab === 'password' ? 0 : -1}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onTabChange('password');
          }
        }}
      >
        Password
      </div>
      
      <div
        className={`login-tab ${activeTab === 'qr' ? 'active' : ''}`}
        onClick={() => onTabChange('qr')}
        role="tab"
        aria-selected={activeTab === 'qr'}
        aria-controls="qr-login-panel"
        tabIndex={activeTab === 'qr' ? 0 : -1}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onTabChange('qr');
          }
        }}
      >
        QR Code
      </div>
    </div>
  );
};
