import React from 'react';
import { UseFormRegister, FieldError } from 'react-hook-form';
import { FormData } from '../utils/types';

/**
 * PasswordField Component
 * 
 * Renders the password input field with show/hide toggle functionality.
 * Includes validation, error display, and accessibility features.
 */
interface PasswordFieldProps {
  register: UseFormRegister<FormData>;
  error?: FieldError;
  showPassword: boolean;
  onTogglePassword: (show: boolean) => void;
  disabled?: boolean;
}

export const PasswordField: React.FC<PasswordFieldProps> = ({
  register,
  error,
  showPassword,
  onTogglePassword,
  disabled = false
}) => {
  return (
    <div className="form-group">
      <label htmlFor="password" className="form-label">
        Password
      </label>
      <div className="password-input-container">
        <input
          id="password"
          type={showPassword ? 'text' : 'password'}
          className={`form-input password-input ${error ? 'error' : ''}`}
          placeholder="Enter your password"
          disabled={disabled}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? 'password-error' : 'password-toggle-desc'}
          {...register('password', {
            required: 'Password is required',
            minLength: {
              value: 6,
              message: 'Password must be at least 6 characters'
            }
          })}
        />
        <button
          type="button"
          className="password-toggle"
          onClick={() => onTogglePassword(!showPassword)}
          disabled={disabled}
          aria-label={showPassword ? 'Hide password' : 'Show password'}
          aria-describedby="password-toggle-desc"
        >
          {showPassword ? (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
            </svg>
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          )}
        </button>
      </div>
      <div id="password-toggle-desc" className="sr-only">
        Click to {showPassword ? 'hide' : 'show'} password
      </div>
      {error && (
        <div 
          id="password-error" 
          className="error-message"
          role="alert"
          aria-live="polite"
        >
          {error.message}
        </div>
      )}
    </div>
  );
};
