import React from 'react';

/**
 * MessageDisplay Component
 * 
 * Displays success messages, error messages, and account lockout notices.
 * Provides accessible message display with proper ARIA attributes.
 */
interface MessageDisplayProps {
  successMessage: string | null;
  loginError: string | null;
  isLocked: boolean;
  lockTimer: number;
}

export const MessageDisplay: React.FC<MessageDisplayProps> = ({
  successMessage,
  loginError,
  isLocked,
  lockTimer
}) => {
  return (
    <>
      {/* Success Message */}
      {successMessage && (
        <div 
          className="bg-green-100 border border-green-300 text-green-800 px-4 py-3 rounded-md mb-4 shadow-sm font-medium"
          role="alert"
          aria-live="polite"
        >
          {successMessage}
        </div>
      )}

      {/* Error Message */}
      {loginError && (
        <div 
          className="bg-red-100 border border-red-300 text-red-800 px-4 py-3 rounded-md mb-4 shadow-sm font-medium"
          role="alert"
          aria-live="assertive"
        >
          {loginError}
        </div>
      )}

      {/* Account Lockout Notice */}
      {isLocked && (
        <div 
          className="lockout-notice"
          role="alert"
          aria-live="assertive"
        >
          <p>Your account has been temporarily locked due to too many failed login attempts.</p>
          <p className="mt-2">
            Please try again in {Math.floor(lockTimer / 60)}:{(lockTimer % 60).toString().padStart(2, '0')}
          </p>
        </div>
      )}
    </>
  );
};
