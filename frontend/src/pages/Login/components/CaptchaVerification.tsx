import React, { useState, useEffect } from 'react';

/**
 * CaptchaVerification Component
 * 
 * Displays a simple math-based CAPTCHA for additional security
 * after multiple failed login attempts.
 */
interface CaptchaVerificationProps {
  onVerify: () => void;
}

export const CaptchaVerification: React.FC<CaptchaVerificationProps> = ({
  onVerify
}) => {
  const [captchaQuestion, setCaptchaQuestion] = useState('');
  const [captchaAnswer, setCaptchaAnswer] = useState(0);
  const [userAnswer, setUserAnswer] = useState('');
  const [captchaError, setCaptchaError] = useState('');

  // Generate new CAPTCHA question
  const generateCaptcha = () => {
    const num1 = Math.floor(Math.random() * 10) + 1;
    const num2 = Math.floor(Math.random() * 10) + 1;
    const operators = ['+', '-', '*'];
    const operator = operators[Math.floor(Math.random() * operators.length)];
    
    let answer;
    switch (operator) {
      case '+':
        answer = num1 + num2;
        break;
      case '-':
        answer = num1 - num2;
        break;
      case '*':
        answer = num1 * num2;
        break;
      default:
        answer = num1 + num2;
    }
    
    setCaptchaQuestion(`${num1} ${operator} ${num2} = ?`);
    setCaptchaAnswer(answer);
    setUserAnswer('');
    setCaptchaError('');
  };

  // Generate initial CAPTCHA
  useEffect(() => {
    generateCaptcha();
  }, []);

  // Handle CAPTCHA verification
  const handleVerify = () => {
    const userNum = parseInt(userAnswer);
    if (userNum === captchaAnswer) {
      onVerify();
      setCaptchaError('');
    } else {
      setCaptchaError('Incorrect answer. Please try again.');
      generateCaptcha();
    }
  };

  return (
    <div className="captcha-container">
      <label htmlFor="captcha" className="form-label">
        Security Verification
      </label>
      <div className="captcha-question">
        <span className="captcha-text">{captchaQuestion}</span>
        <button
          type="button"
          onClick={generateCaptcha}
          className="captcha-refresh"
          aria-label="Generate new CAPTCHA"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        </button>
      </div>
      <div className="captcha-input-container">
        <input
          id="captcha"
          type="number"
          className={`form-input captcha-input ${captchaError ? 'error' : ''}`}
          placeholder="Enter answer"
          value={userAnswer}
          onChange={(e) => setUserAnswer(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              handleVerify();
            }
          }}
          aria-invalid={captchaError ? 'true' : 'false'}
          aria-describedby={captchaError ? 'captcha-error' : 'captcha-help'}
        />
        <button
          type="button"
          onClick={handleVerify}
          className="captcha-verify"
          aria-label="Verify CAPTCHA answer"
        >
          Verify
        </button>
      </div>
      <div id="captcha-help" className="text-xs text-medium-gray mt-1">
        Solve the math problem to continue
      </div>
      {captchaError && (
        <div 
          id="captcha-error" 
          className="error-message"
          role="alert"
          aria-live="polite"
        >
          {captchaError}
        </div>
      )}
    </div>
  );
};
