import React from 'react';
import { useQRCodeAuth } from '../hooks/useQRCodeAuth';

/**
 * QRCodeLogin Component
 * 
 * Displays QR code for mobile app authentication with refresh functionality
 * and real-time status updates. Provides accessible QR code login interface.
 */
interface QRCodeLoginProps {
  qrRefreshKey: number;
  onRefreshQR: () => void;
}

export const QRCodeLogin: React.FC<QRCodeLoginProps> = ({
  qrRefreshKey,
  onRefreshQR
}) => {
  const {
    qrCodeData,
    qrStatus,
    timeRemaining,
    isExpired,
    handleRefresh
  } = useQRCodeAuth(qrRefreshKey);

  const handleRefreshClick = () => {
    handleRefresh();
    onRefreshQR();
  };

  return (
    <div role="tabpanel" id="qr-login-panel" aria-labelledby="qr-tab">
      <div className="qr-login-container">
        {/* QR Code Header */}
        <div className="qr-header">
          <h3 className="qr-title">Scan to Login</h3>
          <p className="qr-description">
            Use the rentUP mobile app to scan this QR code and login instantly
          </p>
        </div>

        {/* QR Code Display */}
        <div className="qr-code-wrapper">
          {qrCodeData ? (
            <div className="qr-code-container">
              <img
                src={qrCodeData}
                alt="QR Code for mobile login"
                className={`qr-code ${isExpired ? 'expired' : ''}`}
                aria-describedby="qr-status"
              />
              
              {/* QR Code Overlay for Expired State */}
              {isExpired && (
                <div className="qr-overlay">
                  <div className="qr-expired-message">
                    <svg className="qr-expired-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>QR Code Expired</span>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="qr-loading">
              <div className="qr-skeleton"></div>
              <p>Generating QR code...</p>
            </div>
          )}
        </div>

        {/* QR Code Status */}
        <div id="qr-status" className="qr-status" aria-live="polite">
          {qrStatus === 'waiting' && (
            <div className="status-waiting">
              <div className="status-indicator waiting"></div>
              <span>Waiting for scan...</span>
            </div>
          )}
          
          {qrStatus === 'scanned' && (
            <div className="status-scanned">
              <div className="status-indicator scanned"></div>
              <span>QR code scanned! Confirm on your mobile device</span>
            </div>
          )}
          
          {qrStatus === 'expired' && (
            <div className="status-expired">
              <div className="status-indicator expired"></div>
              <span>QR code expired. Please refresh to generate a new one</span>
            </div>
          )}
        </div>

        {/* Timer and Refresh */}
        <div className="qr-controls">
          {!isExpired && timeRemaining > 0 && (
            <div className="qr-timer">
              <svg className="timer-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span>Expires in {Math.floor(timeRemaining / 60)}:{(timeRemaining % 60).toString().padStart(2, '0')}</span>
            </div>
          )}
          
          <button
            type="button"
            onClick={handleRefreshClick}
            className="qr-refresh-button"
            aria-label="Refresh QR code"
          >
            <svg className="refresh-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Refresh QR Code
          </button>
        </div>

        {/* Instructions */}
        <div className="qr-instructions">
          <h4 className="instructions-title">How to use QR login:</h4>
          <ol className="instructions-list">
            <li>Open the rentUP mobile app</li>
            <li>Tap the QR scanner icon</li>
            <li>Point your camera at this QR code</li>
            <li>Confirm the login on your mobile device</li>
          </ol>
          
          <div className="app-download-links">
            <p className="download-text">Don't have the app?</p>
            <div className="download-buttons">
              <a href="#" className="app-store-button" aria-label="Download on the App Store">
                <img src="/app-store-badge.svg" alt="Download on the App Store" />
              </a>
              <a href="#" className="google-play-button" aria-label="Get it on Google Play">
                <img src="/google-play-badge.svg" alt="Get it on Google Play" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
