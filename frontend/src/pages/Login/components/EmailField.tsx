import React from 'react';
import { UseFormRegister, FieldError } from 'react-hook-form';
import { FormData } from '../utils/types';

/**
 * EmailField Component
 * 
 * Renders the email input field with validation and error display.
 * Includes proper accessibility attributes and error handling.
 */
interface EmailFieldProps {
  register: UseFormRegister<FormData>;
  error?: FieldError;
  disabled?: boolean;
}

export const EmailField: React.FC<EmailFieldProps> = ({
  register,
  error,
  disabled = false
}) => {
  return (
    <div className="form-group">
      <label htmlFor="email" className="form-label">
        Email Address
      </label>
      <input
        id="email"
        type="email"
        className={`form-input ${error ? 'error' : ''}`}
        placeholder="Enter your email"
        disabled={disabled}
        aria-invalid={error ? 'true' : 'false'}
        aria-describedby={error ? 'email-error' : undefined}
        {...register('email', {
          required: 'Email is required',
          pattern: {
            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
            message: 'Invalid email address'
          }
        })}
      />
      {error && (
        <div 
          id="email-error" 
          className="error-message"
          role="alert"
          aria-live="polite"
        >
          {error.message}
        </div>
      )}
    </div>
  );
};
