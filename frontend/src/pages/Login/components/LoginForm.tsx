import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { Email<PERSON>ield } from './EmailField';
import { PasswordField } from './PasswordField';
import { CaptchaVerification } from './CaptchaVerification';
import { SocialLoginButtons } from './SocialLoginButtons';
import { useLoginSubmit } from '../hooks/useLoginSubmit';
import { FormData } from '../utils/types';
import { schema } from '../utils/validation';

/**
 * LoginForm Component
 * 
 * Handles the password-based login form with email, password,
 * remember me option, and social login integration.
 */
interface LoginFormProps {
  showPassword: boolean;
  onTogglePassword: (show: boolean) => void;
  loginAttempts: number;
  isLocked: boolean;
  captchaVerified: boolean;
  onCaptchaVerify: () => void;
  onLoginError: (error: string | null) => void;
  onIncrementAttempts: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = ({
  showPassword,
  onTogglePassword,
  loginAttempts,
  isLocked,
  captchaVerified,
  onCaptchaVerify,
  onLoginError,
  onIncrementAttempts
}) => {
  // Form handling
  const { register, handleSubmit, formState: { errors } } = useForm<FormData>();

  // Login submission hook
  const { onSubmit, isLoading } = useLoginSubmit({
    loginAttempts,
    isLocked,
    captchaVerified,
    onLoginError,
    onIncrementAttempts,
    schema
  });

  return (
    <div role="tabpanel" id="password-login-panel" aria-labelledby="password-tab">
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Email Field */}
        <EmailField
          register={register}
          error={errors.email}
          disabled={isLocked || isLoading}
        />

        {/* Password Field */}
        <PasswordField
          register={register}
          error={errors.password}
          showPassword={showPassword}
          onTogglePassword={onTogglePassword}
          disabled={isLocked || isLoading}
        />

        {/* Remember Me & Forgot Password */}
        <div className="form-footer">
          <div className="remember-me">
            <input
              id="rememberMe"
              type="checkbox"
              {...register('rememberMe')}
              disabled={isLocked || isLoading}
            />
            <label htmlFor="rememberMe" className="text-sm text-dark-gray ml-2">
              Remember me
            </label>
          </div>
          <Link to="/forgot-password" className="forgot-password">
            Forgot password?
          </Link>
        </div>

        {/* CAPTCHA Verification */}
        {loginAttempts >= 3 && !captchaVerified && (
          <CaptchaVerification onVerify={onCaptchaVerify} />
        )}

        {/* Login Button */}
        <button
          type="submit"
          className="login-button"
          disabled={isLocked || isLoading}
          aria-describedby={isLoading ? "login-loading" : undefined}
        >
          {isLoading ? (
            <span className="flex items-center" id="login-loading">
              <svg 
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" 
                xmlns="http://www.w3.org/2000/svg" 
                fill="none" 
                viewBox="0 0 24 24"
                aria-hidden="true"
              >
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Signing in...
            </span>
          ) : (
            'LOG IN'
          )}
        </button>

        {/* Social Login Divider */}
        <div className="divider">
          <span className="divider-text">OR</span>
        </div>

        {/* Social Login Buttons */}
        <SocialLoginButtons onLoginError={onLoginError} />
      </form>
    </div>
  );
};
