import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../hooks/useAuth';

/**
 * useSocialLogin Hook
 * 
 * Handles social media authentication including Google, Facebook,
 * Apple, and GitHub OAuth flows with proper error handling.
 */
interface UseSocialLoginProps {
  onLoginError: (error: string | null) => void;
}

type SocialProvider = 'google' | 'facebook' | 'apple' | 'github';

export const useSocialLogin = ({ onLoginError }: UseSocialLoginProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingProvider, setLoadingProvider] = useState<SocialProvider | null>(null);
  const navigate = useNavigate();
  const { socialLogin } = useAuth();

  // Generic social login handler
  const handleSocialLogin = async (provider: SocialProvider) => {
    onLoginError(null);
    setIsLoading(true);
    setLoadingProvider(provider);

    try {
      const result = await socialLogin(provider);
      
      if (result.success) {
        navigate('/dashboard', { replace: true });
      } else {
        onLoginError(result.error || `Failed to sign in with ${provider}. Please try again.`);
      }
    } catch (error) {
      onLoginError(`${provider} authentication failed. Please try again.`);
      console.error(`${provider} login error:`, error);
    } finally {
      setIsLoading(false);
      setLoadingProvider(null);
    }
  };

  // Individual provider handlers
  const handleGoogleLogin = () => handleSocialLogin('google');
  const handleFacebookLogin = () => handleSocialLogin('facebook');
  const handleAppleLogin = () => handleSocialLogin('apple');
  const handleGitHubLogin = () => handleSocialLogin('github');

  return {
    handleGoogleLogin,
    handleFacebookLogin,
    handleAppleLogin,
    handleGitHubLogin,
    isLoading,
    loadingProvider
  };
};
