import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../hooks/useAuth';
import { FormData } from '../utils/types';
import { validateLoginForm } from '../utils/validation';
import { SECURITY_CONFIG } from '../utils/constants';

/**
 * useLoginSubmit Hook
 * 
 * Handles form submission logic including validation,
 * authentication, and error handling.
 */
interface UseLoginSubmitProps {
  loginAttempts: number;
  isLocked: boolean;
  captchaVerified: boolean;
  onLoginError: (error: string | null) => void;
  onIncrementAttempts: () => void;
  schema: any;
}

export const useLoginSubmit = ({
  loginAttempts,
  isLocked,
  captchaVerified,
  onLoginError,
  onIncrementAttempts,
  schema
}: UseLoginSubmitProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { login } = useAuth();

  const onSubmit = async (data: FormData) => {
    // Clear previous errors
    onLoginError(null);

    // Check if account is locked
    if (isLocked) {
      onLoginError('Account is temporarily locked. Please try again later.');
      return;
    }

    // Check CAPTCHA requirement
    if (loginAttempts >= SECURITY_CONFIG.CAPTCHA_THRESHOLD && !captchaVerified) {
      onLoginError('Please complete the security verification.');
      return;
    }

    // Validate form data
    const validation = validateLoginForm(data, schema);
    if (!validation.isValid) {
      onLoginError(validation.error || 'Please check your input and try again.');
      return;
    }

    setIsLoading(true);

    try {
      // Attempt login
      const result = await login({
        email: data.email,
        password: data.password,
        rememberMe: data.rememberMe || false
      });

      if (result.success) {
        // Successful login
        navigate('/dashboard', { replace: true });
      } else {
        // Login failed
        onIncrementAttempts();
        onLoginError(result.error || 'Invalid email or password. Please try again.');
      }
    } catch (error) {
      // Network or other errors
      onIncrementAttempts();
      onLoginError('Login failed. Please check your connection and try again.');
      console.error('Login error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    onSubmit,
    isLoading
  };
};
