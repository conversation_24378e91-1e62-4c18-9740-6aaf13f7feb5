import { useState } from 'react';

/**
 * useLoginState Hook
 * 
 * Manages UI state for the login component including password visibility
 * and QR code refresh functionality.
 */
export const useLoginState = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [qrRefreshKey, setQrRefreshKey] = useState(0);

  // Handle QR code refresh
  const handleRefreshQR = () => {
    setQrRefreshKey(prev => prev + 1);
  };

  return {
    showPassword,
    setShowPassword,
    qrRefreshKey,
    handleRefreshQR
  };
};
