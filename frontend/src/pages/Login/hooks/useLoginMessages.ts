import { useState } from 'react';

/**
 * useLoginMessages Hook
 * 
 * Manages message state for login component including
 * error messages and success notifications.
 */
export const useLoginMessages = () => {
  const [loginError, setLoginError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Clear all messages
  const clearMessages = () => {
    setLoginError(null);
    setSuccessMessage(null);
  };

  return {
    loginError,
    setLoginError,
    successMessage,
    setSuccessMessage,
    clearMessages
  };
};
