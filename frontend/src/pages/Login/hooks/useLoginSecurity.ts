import { useState, useEffect, useCallback } from 'react';
import { SECURITY_CONFIG } from '../utils/constants';

/**
 * useLoginSecurity Hook
 * 
 * Manages security features including login attempt tracking,
 * account lockout functionality, and CAPTCHA verification.
 */
export const useLoginSecurity = () => {
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [isLocked, setIsLocked] = useState(false);
  const [lockTimer, setLockTimer] = useState(0);
  const [captchaVerified, setCaptchaVerified] = useState(false);

  // Handle account lockout timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (isLocked && lockTimer > 0) {
      interval = setInterval(() => {
        setLockTimer(prev => {
          if (prev <= 1) {
            setIsLocked(false);
            setLoginAttempts(0);
            setCaptchaVerified(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isLocked, lockTimer]);

  // Increment login attempts and handle lockout
  const incrementLoginAttempts = useCallback(() => {
    setLoginAttempts(prev => {
      const newAttempts = prev + 1;
      
      if (newAttempts >= SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS) {
        setIsLocked(true);
        setLockTimer(SECURITY_CONFIG.LOCKOUT_DURATION);
        setCaptchaVerified(false);
      }
      
      return newAttempts;
    });
  }, []);

  // Handle CAPTCHA verification
  const handleCaptchaVerify = useCallback(() => {
    setCaptchaVerified(true);
  }, []);

  // Reset security state on successful login
  const resetSecurity = useCallback(() => {
    setLoginAttempts(0);
    setIsLocked(false);
    setLockTimer(0);
    setCaptchaVerified(false);
  }, []);

  return {
    loginAttempts,
    isLocked,
    lockTimer,
    captchaVerified,
    setCaptchaVerified,
    incrementLoginAttempts,
    handleCaptchaVerify,
    resetSecurity
  };
};
