import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../../hooks/useAuth';
import { QR_CONFIG } from '../utils/constants';

/**
 * useQRCodeAuth Hook
 * 
 * Manages QR code authentication including generation, status tracking,
 * expiration handling, and real-time authentication polling.
 */
type QRStatus = 'waiting' | 'scanned' | 'expired' | 'authenticated';

export const useQRCodeAuth = (refreshKey: number) => {
  const [qrCodeData, setQrCodeData] = useState<string | null>(null);
  const [qrStatus, setQrStatus] = useState<QRStatus>('waiting');
  const [timeRemaining, setTimeRemaining] = useState(QR_CONFIG.EXPIRY_TIME);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const navigate = useNavigate();
  const { qrLogin } = useAuth();

  // Generate new QR code
  const generateQRCode = useCallback(async () => {
    try {
      // Generate unique session ID
      const newSessionId = `qr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      setSessionId(newSessionId);
      
      // Create QR code data (in real app, this would be a secure token)
      const qrData = `rentup://login?session=${newSessionId}&timestamp=${Date.now()}`;
      
      // Generate QR code image (using a QR code library in real implementation)
      const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrData)}`;
      
      setQrCodeData(qrCodeUrl);
      setQrStatus('waiting');
      setTimeRemaining(QR_CONFIG.EXPIRY_TIME);
    } catch (error) {
      console.error('Failed to generate QR code:', error);
      setQrStatus('expired');
    }
  }, []);

  // Handle QR code refresh
  const handleRefresh = useCallback(() => {
    generateQRCode();
  }, [generateQRCode]);

  // Poll for authentication status
  useEffect(() => {
    if (!sessionId || qrStatus !== 'waiting') return;

    const pollInterval = setInterval(async () => {
      try {
        const result = await qrLogin(sessionId);
        
        if (result.success) {
          setQrStatus('authenticated');
          navigate('/dashboard', { replace: true });
        } else if (result.scanned) {
          setQrStatus('scanned');
        }
      } catch (error) {
        console.error('QR polling error:', error);
      }
    }, QR_CONFIG.POLL_INTERVAL);

    return () => clearInterval(pollInterval);
  }, [sessionId, qrStatus, qrLogin, navigate]);

  // Handle expiration timer
  useEffect(() => {
    if (qrStatus !== 'waiting' && qrStatus !== 'scanned') return;

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          setQrStatus('expired');
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [qrStatus]);

  // Generate initial QR code and regenerate on refresh
  useEffect(() => {
    generateQRCode();
  }, [generateQRCode, refreshKey]);

  const isExpired = qrStatus === 'expired' || timeRemaining <= 0;

  return {
    qrCodeData,
    qrStatus,
    timeRemaining,
    isExpired,
    handleRefresh
  };
};
