import React from 'react';
import { RentPlannerInterface } from '../components/RentPlanner';
import { useAuth } from '../hooks/useAuth';

const RentPlannerPage: React.FC = () => {
  const { user } = useAuth();
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Rent Planner</h1>
          <p className="text-gray-600">
            Plan your rental needs with our AI-powered assistant. Whether you're planning a wedding, 
            a camping trip, or a home renovation project, our Rent Planner can help you find the 
            perfect items to rent.
          </p>
        </div>
        
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <RentPlannerInterface userId={user?.id} />
        </div>
      </div>
    </div>
  );
};

export default RentPlannerPage;
