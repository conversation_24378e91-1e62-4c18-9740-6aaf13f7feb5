import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useAuth } from '../hooks/useAuth';
import { useMediaQuery } from '../hooks/useMediaQuery';
import { motion } from 'framer-motion';
import VisualizationComparison from '../components/Visualization/VisualizationComparison';
import { Link } from 'react-router-dom';

/**
 * Page for comparing visualizations over time
 * Allows users to compare preferences and embeddings over different time periods
 */
const VisualizationComparisonPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const isMobile = useMediaQuery('(max-width: 640px)');
  const [comparisonType, setComparisonType] = useState<'preferences' | 'embeddings'>('preferences');
  
  return (
    <>
      <Helmet>
        <title>Visualization Comparison | RentUp</title>
        <meta name="description" content="Compare your preferences and recommendations over time" />
      </Helmet>
      
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl md:text-3xl font-bold mb-6">
          AI Recommendation Insights Comparison
        </h1>
        
        {!isAuthenticated ? (
          <div 
            className="bg-white rounded-lg shadow-md p-6 mb-8"
            data-testid="visualization-comparison-page-unauthenticated"
          >
            <h2 className="text-xl font-semibold mb-4">Sign In to See Your Personalized Insights</h2>
            <p className="text-gray-600 mb-6">
              To view your personalized recommendation insights and compare them over time, please sign in to your account.
              This will allow us to show you how your preferences have evolved and how our recommendations have adapted to your needs.
            </p>
            <Link
              to="/login"
              className="inline-block bg-primary text-white px-6 py-2 rounded-md font-medium hover:bg-primary-dark transition-colors"
            >
              Sign In
            </Link>
          </div>
        ) : (
          <>
            {/* Comparison type selector */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-lg font-semibold mb-4">Select Comparison Type</h2>
              <div className="flex flex-wrap gap-4">
                <button
                  className={`px-4 py-2 rounded-md ${
                    comparisonType === 'preferences'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  onClick={() => setComparisonType('preferences')}
                  data-testid="visualization-comparison-page-preferences-button"
                >
                  Preference Comparison
                </button>
                <button
                  className={`px-4 py-2 rounded-md ${
                    comparisonType === 'embeddings'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                  onClick={() => setComparisonType('embeddings')}
                  data-testid="visualization-comparison-page-embeddings-button"
                >
                  Embedding Comparison
                </button>
              </div>
            </div>
            
            {/* Visualization comparison component */}
            <VisualizationComparison
              comparisonType={comparisonType}
              title={comparisonType === 'preferences' ? 'Preference Evolution Over Time' : 'Item Relationships Evolution'}
              description={
                comparisonType === 'preferences'
                  ? 'Compare how your preferences have changed over time and how they influence your recommendations.'
                  : 'Compare how item relationships and categories have evolved over time in the recommendation space.'
              }
              testId="visualization-comparison-page-comparison"
            />
            
            {/* Additional information */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white rounded-lg shadow-md p-6"
              >
                <h2 className="text-lg font-semibold mb-4">Understanding the Comparison</h2>
                <p className="text-gray-600 mb-4">
                  This comparison tool helps you understand how your preferences and recommendations have evolved over time.
                  By comparing different time periods, you can see how your interactions with the platform have shaped your
                  recommendation profile.
                </p>
                <ul className="list-disc pl-5 text-gray-600 space-y-2">
                  <li>
                    <strong>Preference Comparison:</strong> Shows how your preference weights and counts have changed over time.
                  </li>
                  <li>
                    <strong>Embedding Comparison:</strong> Shows how item relationships in the recommendation space have evolved.
                  </li>
                </ul>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="bg-white rounded-lg shadow-md p-6"
              >
                <h2 className="text-lg font-semibold mb-4">How to Use This Tool</h2>
                <p className="text-gray-600 mb-4">
                  To get the most out of this comparison tool, follow these steps:
                </p>
                <ol className="list-decimal pl-5 text-gray-600 space-y-2">
                  <li>Select the type of comparison you want to see (Preferences or Embeddings).</li>
                  <li>Choose the time periods you want to compare using the dropdown menus.</li>
                  <li>For embedding comparisons, you can also filter by category to focus on specific item types.</li>
                  <li>Analyze the differences between the two visualizations to understand how your preferences have evolved.</li>
                  <li>Check the insights section for automated analysis of the key differences.</li>
                </ol>
              </motion.div>
            </div>
            
            {/* Navigation links */}
            <div className="mt-8 flex flex-wrap gap-4">
              <Link
                to="/visualization"
                className="text-primary hover:text-primary-dark transition-colors"
                data-testid="visualization-comparison-page-back-link"
              >
                &larr; Back to Visualization Dashboard
              </Link>
              <Link
                to="/profile"
                className="text-primary hover:text-primary-dark transition-colors"
                data-testid="visualization-comparison-page-profile-link"
              >
                View Your Profile &rarr;
              </Link>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default VisualizationComparisonPage;
