import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useAuth } from '../hooks/useAuth';
import { Link } from 'react-router-dom';
import VisualizationDashboard from '../components/Visualization/VisualizationDashboard';

/**
 * Page for the visualization dashboard
 * Provides a comprehensive overview of all visualization components
 */
const VisualizationDashboardPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  
  return (
    <>
      <Helmet>
        <title>AI Recommendation Dashboard | RentUp</title>
        <meta name="description" content="View a comprehensive dashboard of your personalized AI recommendations and insights" />
      </Helmet>
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold mb-2">
              AI Recommendation Dashboard
            </h1>
            <p className="text-gray-600">
              A comprehensive overview of your personalized recommendations and insights
            </p>
          </div>
          
          <div className="mt-4 md:mt-0 flex space-x-4">
            <Link
              to="/visualization"
              className="text-primary hover:text-primary-dark transition-colors"
              data-testid="visualization-dashboard-page-insights-link"
            >
              View Insights
            </Link>
            <Link
              to="/visualization/comparison"
              className="text-primary hover:text-primary-dark transition-colors"
              data-testid="visualization-dashboard-page-comparison-link"
            >
              Compare Over Time
            </Link>
          </div>
        </div>
        
        {!isAuthenticated ? (
          <div 
            className="bg-white rounded-lg shadow-md p-6 mb-8"
            data-testid="visualization-dashboard-page-unauthenticated"
          >
            <h2 className="text-xl font-semibold mb-4">Sign In to See Your Personalized Dashboard</h2>
            <p className="text-gray-600 mb-6">
              To view your personalized recommendation dashboard, please sign in to your account.
              This will allow us to show you a comprehensive overview of your preferences, recommendations,
              and insights tailored to your activity.
            </p>
            <Link
              to="/login"
              className="inline-block bg-primary text-white px-6 py-2 rounded-md font-medium hover:bg-primary-dark transition-colors"
            >
              Sign In
            </Link>
          </div>
        ) : (
          <>
            {/* Main dashboard */}
            <VisualizationDashboard 
              className="mb-8"
              testId="visualization-dashboard-page-main"
            />
            
            {/* Additional information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Understanding Your Dashboard</h2>
                <p className="text-gray-600 mb-4">
                  This dashboard provides a comprehensive overview of your personalized recommendations and insights.
                  Here's what each section means:
                </p>
                <ul className="list-disc pl-5 text-gray-600 space-y-2">
                  <li>
                    <strong>Recommendation Strength:</strong> Shows how confident our system is in its recommendations for you.
                    Higher percentages indicate stronger, more personalized recommendations.
                  </li>
                  <li>
                    <strong>Dominant Preference:</strong> Identifies the preference type that has the strongest influence
                    on your recommendations.
                  </li>
                  <li>
                    <strong>Similar Items:</strong> Shows the most similar items in your profile based on their embeddings
                    in the recommendation space.
                  </li>
                  <li>
                    <strong>Preference Profile:</strong> Visualizes your preferences based on your interactions with items.
                  </li>
                  <li>
                    <strong>Item Relationships:</strong> Shows how items are related to each other in the recommendation space.
                  </li>
                </ul>
              </div>
              
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold mb-4">Improving Your Recommendations</h2>
                <p className="text-gray-600 mb-4">
                  Want to get better, more personalized recommendations? Here are some tips:
                </p>
                <ul className="list-disc pl-5 text-gray-600 space-y-2">
                  <li>
                    <strong>Interact with more items:</strong> The more items you view, like, and rent, the better
                    our system understands your preferences.
                  </li>
                  <li>
                    <strong>Provide feedback:</strong> Rate items after renting them to help our system learn what you like.
                  </li>
                  <li>
                    <strong>Update your preferences:</strong> You can manually adjust your preferences in your profile settings.
                  </li>
                  <li>
                    <strong>Explore different categories:</strong> Trying new categories helps our system discover your
                    broader interests.
                  </li>
                  <li>
                    <strong>Check your dashboard regularly:</strong> See how your recommendations evolve over time and
                    compare different time periods.
                  </li>
                </ul>
              </div>
            </div>
            
            {/* Navigation links */}
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                to="/profile"
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                data-testid="visualization-dashboard-page-profile-link"
              >
                View Your Profile
              </Link>
              <Link
                to="/items"
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
                data-testid="visualization-dashboard-page-browse-link"
              >
                Browse Recommended Items
              </Link>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default VisualizationDashboardPage;
