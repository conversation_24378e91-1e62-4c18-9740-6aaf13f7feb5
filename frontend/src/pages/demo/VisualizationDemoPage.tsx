import React from 'react';
import { Helmet } from 'react-helmet-async';
import { ComprehensiveVisualizationDashboard } from '../../components/Visualization';
import { useAuth } from '../../hooks/useAuth';
import { useMediaQuery } from '../../hooks/useMediaQuery';

/**
 * Demo page for the Comprehensive Visualization Dashboard
 * Showcases the AI recommendation visualization features
 */
const VisualizationDemoPage: React.FC = () => {
  const { user } = useAuth();
  const isMobile = useMediaQuery('(max-width: 640px)');

  return (
    <>
      <Helmet>
        <title>AI Recommendation Visualization | RentUp</title>
        <meta name="description" content="Explore how RentUp's AI recommendation system works with interactive visualizations" />
      </Helmet>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold mb-2">AI Recommendation Visualization</h1>
          <p className="text-gray-600 mb-8">
            Explore how our AI recommendation system works and how it personalizes content for you.
          </p>

          <div className="bg-white rounded-lg shadow-md p-4 md:p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">About This Demo</h2>
            <p className="text-gray-700 mb-4">
              This interactive dashboard demonstrates how RentUp's AI recommendation system works.
              You can explore your preferences, see how items are related to each other, and understand
              why specific items are recommended to you.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-blue-800 mb-2">Personalized Recommendations</h3>
                <p className="text-sm text-blue-700">
                  Our AI system analyzes your interactions and preferences to provide personalized
                  recommendations tailored to your needs and interests.
                </p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="text-lg font-medium text-purple-800 mb-2">Transparent AI</h3>
                <p className="text-sm text-purple-700">
                  We believe in transparency. This visualization helps you understand how our AI
                  makes recommendations and what factors influence those recommendations.
                </p>
              </div>
            </div>
          </div>

          {/* Main visualization dashboard */}
          <ComprehensiveVisualizationDashboard
            userId={user?.id}
            interactive={true}
            showExplanations={true}
            showComparison={true}
            showAnalytics={true}
          />

          <div className="mt-12 bg-white rounded-lg shadow-md p-4 md:p-6">
            <h2 className="text-xl font-semibold mb-4">How to Use This Dashboard</h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Overview Tab</h3>
                <p className="text-gray-700">
                  The Overview tab provides a high-level summary of the visualization features.
                  From here, you can navigate to the different visualization tabs.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Preferences Tab</h3>
                <p className="text-gray-700">
                  The Preferences tab shows how your interactions influence recommendations.
                  The bars represent different types of interactions and their weights.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Item Relationships Tab</h3>
                <p className="text-gray-700">
                  The Item Relationships tab shows how items are related to each other.
                  Items that are closer together are more similar and likely to be recommended together.
                  You can click on an item to select it and see connections to similar items.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Recommendations Tab</h3>
                <p className="text-gray-700">
                  The Recommendations tab explains why specific items are recommended to you.
                  It shows the factors that influence the recommendation and how they align with your preferences.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Comparison Tab</h3>
                <p className="text-gray-700">
                  The Comparison tab allows you to compare multiple recommendations side by side.
                  You can see common factors and unique influences for up to 3 items at once.
                  Select items from the Item Relationships tab to add them to the comparison.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Analytics Tab</h3>
                <p className="text-gray-700">
                  The Analytics tab provides detailed metrics about recommendation performance,
                  including click-through rates, conversion rates, and satisfaction scores.
                  You can also see trends over time and category distribution.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-2">Dashboard Tab</h3>
                <p className="text-gray-700">
                  The Dashboard tab provides a summary of your recommendations,
                  including performance metrics, category distribution, and insights.
                </p>
              </div>
            </div>
          </div>

          <div className="mt-12 bg-gray-50 rounded-lg p-4 md:p-6 text-center">
            <h2 className="text-xl font-semibold mb-4">Questions or Feedback?</h2>
            <p className="text-gray-700 mb-4">
              We're constantly improving our AI recommendation system and visualization tools.
              If you have any questions or feedback, please let us know.
            </p>
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              onClick={() => window.location.href = '/contact'}
            >
              Contact Us
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default VisualizationDemoPage;
