import React from 'react';
import { Tabs, Tab } from '@mui/material';
import { AGREEMENT_TABS } from '../utils/constants';

/**
 * TabNavigation Component
 * 
 * Provides tab navigation for filtering agreements by status.
 * Includes accessible tab controls with proper ARIA attributes.
 */
interface TabNavigationProps {
  activeTab: number;
  onTabChange: (tab: number) => void;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  onTabChange
}) => {
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    onTabChange(newValue);
  };

  return (
    <Tabs
      value={activeTab}
      onChange={handleTabChange}
      variant="scrollable"
      scrollButtons="auto"
      sx={{ mb: 3 }}
      aria-label="Agreement status tabs"
    >
      {AGREEMENT_TABS.map((tab, index) => (
        <Tab
          key={index}
          label={tab.label}
          id={`agreement-tab-${index}`}
          aria-controls={`agreement-tabpanel-${index}`}
        />
      ))}
    </Tabs>
  );
};
