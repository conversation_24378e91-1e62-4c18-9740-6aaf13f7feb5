import React from 'react';
import { Box, Alert } from '@mui/material';

/**
 * ErrorState Component
 * 
 * Displays error messages when agreement data fails to load.
 * Provides accessible error handling with proper ARIA attributes.
 */
interface ErrorStateProps {
  error: string;
}

export const ErrorState: React.FC<ErrorStateProps> = ({ error }) => {
  return (
    <Box sx={{ p: 3 }}>
      <Alert 
        severity="error"
        role="alert"
        aria-live="assertive"
      >
        {error}
      </Alert>
    </Box>
  );
};
