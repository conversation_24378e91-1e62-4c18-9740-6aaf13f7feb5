import React from 'react';
import { Grid, Card, CardContent, Typography, Box } from '@mui/material';
import {
  Description,
  CheckCircle,
  Pending,
  Assignment
} from '@mui/icons-material';
import { AgreementStats } from '../utils/types';

/**
 * StatisticsCards Component
 * 
 * Displays agreement statistics in card format including
 * total agreements, signed agreements, pending signatures, and drafts.
 */
interface StatisticsCardsProps {
  stats: AgreementStats;
}

export const StatisticsCards: React.FC<StatisticsCardsProps> = ({ stats }) => {
  return (
    <Grid container spacing={3} sx={{ mb: 4 }}>
      {/* Total Agreements */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                  Total Agreements
                </Typography>
                <Typography variant="h4">
                  {stats.total}
                </Typography>
              </Box>
              <Description color="primary" />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              All agreements in system
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Signed Agreements */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                  Fully Signed
                </Typography>
                <Typography variant="h4">
                  {stats.signed}
                </Typography>
              </Box>
              <CheckCircle color="success" />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {stats.total > 0 ? Math.round((stats.signed / stats.total) * 100) : 0}% completion rate
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Pending Signatures */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                  Pending Signatures
                </Typography>
                <Typography variant="h4">
                  {stats.pending}
                </Typography>
              </Box>
              <Pending color="warning" />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Awaiting signatures
            </Typography>
          </CardContent>
        </Card>
      </Grid>

      {/* Draft Agreements */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                  Drafts
                </Typography>
                <Typography variant="h4">
                  {stats.draft}
                </Typography>
              </Box>
              <Assignment color="info" />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              In preparation
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};
