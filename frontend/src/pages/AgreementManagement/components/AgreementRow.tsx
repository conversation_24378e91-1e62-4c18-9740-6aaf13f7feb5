import React from 'react';
import {
  TableRow,
  TableCell,
  Typography,
  Chip,
  Box,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Visibility,
  Edit,
  Download,
  Print,
  Delete
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { Agreement } from '../utils/types';
import { getStatusIcon, getStatusColor } from '../utils/statusUtils';

/**
 * AgreementRow Component
 * 
 * Renders a single agreement row in the table with all relevant information
 * and action buttons based on the agreement status.
 */
interface AgreementRowProps {
  agreement: Agreement;
}

export const AgreementRow: React.FC<AgreementRowProps> = ({ agreement }) => {
  return (
    <TableRow hover>
      <TableCell>
        <Typography variant="body2" fontWeight="medium">
          {agreement.itemTitle}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          ID: {agreement.id}
        </Typography>
      </TableCell>
      
      <TableCell>
        <Chip
          icon={getStatusIcon(agreement.status)}
          label={agreement.status.charAt(0).toUpperCase() + agreement.status.slice(1)}
          color={getStatusColor(agreement.status)}
          size="small"
        />
      </TableCell>
      
      <TableCell>
        <Typography variant="body2">
          {agreement.ownerName} (Owner)
        </Typography>
        <Typography variant="body2">
          {agreement.renterName} (Renter)
        </Typography>
      </TableCell>
      
      <TableCell>
        {new Date(agreement.createdAt).toLocaleDateString()}
      </TableCell>
      
      <TableCell>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
          <Chip
            label="Owner"
            size="small"
            color={agreement.ownerSigned ? "success" : "default"}
            variant={agreement.ownerSigned ? "filled" : "outlined"}
          />
          <Chip
            label="Renter"
            size="small"
            color={agreement.renterSigned ? "success" : "default"}
            variant={agreement.renterSigned ? "filled" : "outlined"}
          />
        </Box>
      </TableCell>
      
      <TableCell align="right">
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Tooltip title="View">
            <IconButton
              component={Link}
              to={`/agreements/${agreement.id}`}
              size="small"
              aria-label={`View agreement ${agreement.id}`}
            >
              <Visibility fontSize="small" />
            </IconButton>
          </Tooltip>
          
          {agreement.status === 'draft' && (
            <Tooltip title="Edit">
              <IconButton
                component={Link}
                to={`/agreements/${agreement.id}/edit`}
                size="small"
                aria-label={`Edit agreement ${agreement.id}`}
              >
                <Edit fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
          
          <Tooltip title="Download">
            <IconButton 
              size="small"
              aria-label={`Download agreement ${agreement.id}`}
            >
              <Download fontSize="small" />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Print">
            <IconButton 
              size="small"
              aria-label={`Print agreement ${agreement.id}`}
            >
              <Print fontSize="small" />
            </IconButton>
          </Tooltip>
          
          {agreement.status === 'draft' && (
            <Tooltip title="Delete">
              <IconButton 
                size="small"
                aria-label={`Delete agreement ${agreement.id}`}
              >
                <Delete fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </TableCell>
    </TableRow>
  );
};
