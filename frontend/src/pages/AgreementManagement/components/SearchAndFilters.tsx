import React from 'react';
import {
  Box,
  TextField,
  InputAdornment,
  IconButton,
  Collapse,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { Search, FilterList } from '@mui/icons-material';
import { AGREEMENT_STATUSES, SIGNATURE_FILTERS } from '../utils/constants';

/**
 * SearchAndFilters Component
 * 
 * Provides search functionality and advanced filtering options
 * for agreements including status and signature filters.
 */
interface SearchAndFiltersProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  statusFilter: string;
  onStatusFilterChange: (status: string) => void;
  signatureFilter: string;
  onSignatureFilterChange: (filter: string) => void;
  showFilters: boolean;
  onToggleFilters: (show: boolean) => void;
}

export const SearchAndFilters: React.FC<SearchAndFiltersProps> = ({
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  signatureFilter,
  onSignatureFilterChange,
  showFilters,
  onToggleFilters
}) => {
  return (
    <Box sx={{ mb: 3 }}>
      {/* Search Bar */}
      <TextField
        fullWidth
        placeholder="Search agreements..."
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <Search />
            </InputAdornment>
          ),
          endAdornment: (
            <InputAdornment position="end">
              <IconButton
                onClick={() => onToggleFilters(!showFilters)}
                aria-label="Toggle filters"
                aria-expanded={showFilters}
              >
                <FilterList />
              </IconButton>
            </InputAdornment>
          )
        }}
        sx={{ mb: 2 }}
        aria-label="Search agreements"
      />

      {/* Advanced Filters */}
      <Collapse in={showFilters}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={statusFilter}
                label="Status"
                onChange={(e) => onStatusFilterChange(e.target.value)}
                aria-label="Filter by status"
              >
                <MenuItem value="">All Statuses</MenuItem>
                {AGREEMENT_STATUSES.map((status) => (
                  <MenuItem key={status.value} value={status.value}>
                    {status.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth>
              <InputLabel>Signature Status</InputLabel>
              <Select
                value={signatureFilter}
                label="Signature Status"
                onChange={(e) => onSignatureFilterChange(e.target.value)}
                aria-label="Filter by signature status"
              >
                <MenuItem value="">All Signatures</MenuItem>
                {SIGNATURE_FILTERS.map((filter) => (
                  <MenuItem key={filter.value} value={filter.value}>
                    {filter.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Collapse>
    </Box>
  );
};
