import React from 'react';
import { Box, CircularProgress } from '@mui/material';

/**
 * LoadingState Component
 * 
 * Displays a centered loading spinner while agreement data is being fetched.
 * Provides accessible loading indicator with proper ARIA attributes.
 */
export const LoadingState: React.FC = () => {
  return (
    <Box 
      sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '50vh' 
      }}
      role="status"
      aria-live="polite"
      aria-label="Loading agreements"
    >
      <CircularProgress />
      <span className="sr-only">Loading agreement data...</span>
    </Box>
  );
};
