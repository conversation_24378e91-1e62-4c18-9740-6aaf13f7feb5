import React from 'react';
import { Box, Typography } from '@mui/material';
import { LoadingState } from './components/LoadingState';
import { ErrorState } from './components/ErrorState';
import { StatisticsCards } from './components/StatisticsCards';
import { TabNavigation } from './components/TabNavigation';
import { SearchAndFilters } from './components/SearchAndFilters';
import { AgreementsTable } from './components/AgreementsTable';
import { useAgreementData } from './hooks/useAgreementData';
import { useAgreementFiltering } from './hooks/useAgreementFiltering';
import { useAgreementPagination } from './hooks/useAgreementPagination';
import { useAgreementStats } from './hooks/useAgreementStats';

/**
 * AgreementManagement Component
 * 
 * Main page component for managing rental agreements.
 * Provides comprehensive agreement management including viewing, filtering,
 * searching, and performing actions on agreements.
 */
const AgreementManagement: React.FC = () => {
  // Data management hook
  const { agreements, loading, error } = useAgreementData();

  // Filtering and search hook
  const {
    filteredAgreements,
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    signatureFilter,
    setSignatureFilter,
    showFilters,
    setShowFilters,
    activeTab,
    setActiveTab
  } = useAgreementFiltering(agreements);

  // Pagination hook
  const {
    page,
    rowsPerPage,
    handleChangePage,
    handleChangeRowsPerPage,
    paginatedAgreements
  } = useAgreementPagination(filteredAgreements);

  // Statistics hook
  const stats = useAgreementStats(agreements);

  // Render loading state
  if (loading) {
    return <LoadingState />;
  }

  // Render error state
  if (error) {
    return <ErrorState error={error} />;
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Agreement Management
      </Typography>

      {/* Statistics Cards */}
      <StatisticsCards stats={stats} />

      {/* Tab Navigation */}
      <TabNavigation
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Search and Filters */}
      <SearchAndFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        statusFilter={statusFilter}
        onStatusFilterChange={setStatusFilter}
        signatureFilter={signatureFilter}
        onSignatureFilterChange={setSignatureFilter}
        showFilters={showFilters}
        onToggleFilters={setShowFilters}
      />

      {/* Agreements Table */}
      <AgreementsTable
        agreements={paginatedAgreements}
        totalCount={filteredAgreements.length}
        page={page}
        rowsPerPage={rowsPerPage}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Box>
  );
};

export default AgreementManagement;
