import { useState, useEffect } from 'react';
import { Agreement } from '../utils/types';

/**
 * Mock agreement data for development
 */
const MOCK_AGREEMENTS: Agreement[] = [
  {
    id: 1,
    itemId: 101,
    itemTitle: 'Professional Camera',
    status: 'signed',
    ownerId: 1,
    ownerName: '<PERSON>',
    renterId: 2,
    renterName: '<PERSON>',
    ownerSigned: true,
    renterSigned: true,
    createdAt: new Date('2024-01-15'),
    signedAt: new Date('2024-01-16'),
    startDate: new Date('2024-01-20'),
    endDate: new Date('2024-01-25'),
    totalAmount: 150.00
  },
  {
    id: 2,
    itemId: 102,
    itemTitle: 'Mountain Bike',
    status: 'pending',
    ownerId: 3,
    ownerName: '<PERSON>',
    renterId: 4,
    renterName: '<PERSON>',
    ownerSigned: true,
    renterSigned: false,
    createdAt: new Date('2024-01-18'),
    signedAt: null,
    startDate: new Date('2024-01-22'),
    endDate: new Date('2024-01-24'),
    totalAmount: 75.00
  },
  {
    id: 3,
    itemId: 103,
    itemTitle: 'Power Drill',
    status: 'draft',
    ownerId: 5,
    ownerName: 'Tom <PERSON>',
    renterId: 6,
    renterName: 'Lisa Davis',
    ownerSigned: false,
    renterSigned: false,
    createdAt: new Date('2024-01-20'),
    signedAt: null,
    startDate: new Date('2024-01-25'),
    endDate: new Date('2024-01-26'),
    totalAmount: 25.00
  },
  {
    id: 4,
    itemId: 104,
    itemTitle: 'Camping Tent',
    status: 'expired',
    ownerId: 7,
    ownerName: 'Chris Lee',
    renterId: 8,
    renterName: 'Amy Chen',
    ownerSigned: false,
    renterSigned: false,
    createdAt: new Date('2024-01-10'),
    signedAt: null,
    startDate: new Date('2024-01-15'),
    endDate: new Date('2024-01-17'),
    totalAmount: 40.00
  }
];

/**
 * useAgreementData Hook
 * 
 * Manages agreement data fetching and state management.
 * Provides loading states, error handling, and data refresh functionality.
 */
export const useAgreementData = () => {
  const [agreements, setAgreements] = useState<Agreement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simulate API call
  useEffect(() => {
    const fetchAgreements = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // In a real implementation, this would be an API call
        // const response = await agreementService.getAgreements();
        // setAgreements(response.data);
        
        setAgreements(MOCK_AGREEMENTS);
      } catch (err) {
        setError('Failed to load agreements. Please try again.');
        console.error('Error fetching agreements:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchAgreements();
  }, []);

  return {
    agreements,
    loading,
    error,
    setAgreements
  };
};
