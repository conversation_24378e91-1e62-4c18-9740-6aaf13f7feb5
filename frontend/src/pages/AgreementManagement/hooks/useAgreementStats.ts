import { useMemo } from 'react';
import { Agreement, AgreementStats } from '../utils/types';

/**
 * useAgreementStats Hook
 * 
 * Calculates and provides statistics about agreements including
 * total counts, status breakdowns, and completion rates.
 */
export const useAgreementStats = (agreements: Agreement[]): AgreementStats => {
  const stats = useMemo(() => {
    const total = agreements.length;
    const signed = agreements.filter(a => a.ownerSigned && a.renterSigned).length;
    const pending = agreements.filter(a => 
      (!a.ownerSigned || !a.renterSigned) && a.status !== 'draft'
    ).length;
    const draft = agreements.filter(a => a.status === 'draft').length;
    const expired = agreements.filter(a => a.status === 'expired').length;

    return {
      total,
      signed,
      pending,
      draft,
      expired,
      completionRate: total > 0 ? Math.round((signed / total) * 100) : 0
    };
  }, [agreements]);

  return stats;
};
