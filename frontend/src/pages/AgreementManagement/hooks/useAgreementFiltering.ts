import { useState, useMemo } from 'react';
import { Agreement } from '../utils/types';

/**
 * useAgreementFiltering Hook
 * 
 * Manages filtering and searching functionality for agreements.
 * Provides state management for search terms, status filters, and tab navigation.
 */
export const useAgreementFiltering = (agreements: Agreement[]) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [signatureFilter, setSignatureFilter] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Apply all filters to agreements
  const filteredAgreements = useMemo(() => {
    let filtered = [...agreements];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(agreement =>
        agreement.itemTitle.toLowerCase().includes(term) ||
        agreement.ownerName.toLowerCase().includes(term) ||
        agreement.renterName.toLowerCase().includes(term) ||
        agreement.id.toString().includes(term)
      );
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(agreement => agreement.status === statusFilter);
    }

    // Apply signature filter
    if (signatureFilter) {
      switch (signatureFilter) {
        case 'signed':
          filtered = filtered.filter(agreement => 
            agreement.ownerSigned && agreement.renterSigned
          );
          break;
        case 'unsigned':
          filtered = filtered.filter(agreement => 
            !agreement.ownerSigned && !agreement.renterSigned
          );
          break;
        case 'partial':
          filtered = filtered.filter(agreement => 
            (agreement.ownerSigned && !agreement.renterSigned) ||
            (!agreement.ownerSigned && agreement.renterSigned)
          );
          break;
        default:
          break;
      }
    }

    // Apply tab filter
    switch (activeTab) {
      case 1: // My Agreements (as owner)
        // In a real app, this would filter by current user ID
        filtered = filtered.filter(agreement => agreement.ownerId === 1);
        break;
      case 2: // Pending Signatures
        filtered = filtered.filter(agreement => 
          !agreement.ownerSigned || !agreement.renterSigned
        );
        break;
      default: // All Agreements
        break;
    }

    return filtered;
  }, [agreements, searchTerm, statusFilter, signatureFilter, activeTab]);

  return {
    filteredAgreements,
    searchTerm,
    setSearchTerm,
    statusFilter,
    setStatusFilter,
    signatureFilter,
    setSignatureFilter,
    showFilters,
    setShowFilters,
    activeTab,
    setActiveTab
  };
};
