import { useState, useMemo } from 'react';
import { Agreement } from '../utils/types';

/**
 * useAgreementPagination Hook
 * 
 * Manages pagination functionality for the agreements table.
 * Provides page state management and paginated data slicing.
 */
export const useAgreementPagination = (agreements: Agreement[]) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Get paginated agreements
  const paginatedAgreements = useMemo(() => {
    const startIndex = page * rowsPerPage;
    const endIndex = startIndex + rowsPerPage;
    return agreements.slice(startIndex, endIndex);
  }, [agreements, page, rowsPerPage]);

  // Reset page when agreements change
  useMemo(() => {
    setPage(0);
  }, [agreements.length]);

  return {
    page,
    rowsPerPage,
    handleChangePage,
    handleChangeRowsPerPage,
    paginatedAgreements
  };
};
