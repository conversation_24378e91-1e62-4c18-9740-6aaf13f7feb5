import { TabConfig, StatusConfig, FilterConfig } from './types';

/**
 * Constants for the AgreementManagement component
 */

/**
 * Tab configuration for agreement filtering
 */
export const AGREEMENT_TABS: TabConfig[] = [
  { label: 'All Agreements', value: 'all' },
  { label: 'My Agreements', value: 'mine' },
  { label: 'Pending Signatures', value: 'pending' }
];

/**
 * Agreement status configurations
 */
export const AGREEMENT_STATUSES: StatusConfig[] = [
  { value: 'draft', label: 'Draft', color: 'default' },
  { value: 'pending', label: 'Pending', color: 'warning' },
  { value: 'signed', label: 'Signed', color: 'success' },
  { value: 'expired', label: 'Expired', color: 'error' },
  { value: 'cancelled', label: 'Cancelled', color: 'error' }
];

/**
 * Signature filter configurations
 */
export const SIGNATURE_FILTERS: FilterConfig[] = [
  { value: 'signed', label: 'Fully Signed' },
  { value: 'unsigned', label: 'Unsigned' },
  { value: 'partial', label: 'Partially Signed' }
];

/**
 * Default pagination settings
 */
export const PAGINATION_DEFAULTS = {
  ROWS_PER_PAGE: 10,
  ROWS_PER_PAGE_OPTIONS: [5, 10, 25, 50]
} as const;

/**
 * Table column configurations
 */
export const TABLE_COLUMNS = [
  { id: 'item', label: 'Item', minWidth: 200 },
  { id: 'status', label: 'Status', minWidth: 120 },
  { id: 'parties', label: 'Parties', minWidth: 180 },
  { id: 'created', label: 'Created', minWidth: 120 },
  { id: 'signatures', label: 'Signatures', minWidth: 140 },
  { id: 'actions', label: 'Actions', minWidth: 150, align: 'right' as const }
];

/**
 * Action button configurations
 */
export const ACTION_BUTTONS = {
  VIEW: { label: 'View', icon: 'Visibility' },
  EDIT: { label: 'Edit', icon: 'Edit' },
  DOWNLOAD: { label: 'Download', icon: 'Download' },
  PRINT: { label: 'Print', icon: 'Print' },
  DELETE: { label: 'Delete', icon: 'Delete' }
} as const;

/**
 * Search and filter configurations
 */
export const SEARCH_CONFIG = {
  PLACEHOLDER: 'Search agreements...',
  DEBOUNCE_DELAY: 300
} as const;

/**
 * Date format configurations
 */
export const DATE_FORMATS = {
  DISPLAY: 'MMM dd, yyyy',
  ISO: 'yyyy-MM-dd'
} as const;

/**
 * Color scheme for status indicators
 */
export const STATUS_COLORS = {
  draft: '#9e9e9e',
  pending: '#ff9800',
  signed: '#4caf50',
  expired: '#f44336',
  cancelled: '#f44336'
} as const;

/**
 * Animation durations
 */
export const ANIMATION_DURATIONS = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500
} as const;
