import React from 'react';
import {
  Description,
  CheckCircle,
  PendingActions,
  ErrorOutline,
  Cancel
} from '@mui/icons-material';
import { AgreementStatus } from './types';

/**
 * Status utility functions for agreement management
 */

/**
 * Get the appropriate icon for an agreement status
 */
export const getStatusIcon = (status: AgreementStatus): React.ReactElement => {
  switch (status) {
    case 'signed':
      return <CheckCircle fontSize="small" />;
    case 'pending':
      return <PendingActions fontSize="small" />;
    case 'expired':
    case 'cancelled':
      return <ErrorOutline fontSize="small" />;
    case 'draft':
    default:
      return <Description fontSize="small" />;
  }
};

/**
 * Get the appropriate color for an agreement status
 */
export const getStatusColor = (status: AgreementStatus): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
  switch (status) {
    case 'signed':
      return 'success';
    case 'pending':
      return 'warning';
    case 'expired':
    case 'cancelled':
      return 'error';
    case 'draft':
    default:
      return 'default';
  }
};

/**
 * Get human-readable status text
 */
export const getStatusText = (status: AgreementStatus): string => {
  switch (status) {
    case 'draft':
      return 'Draft';
    case 'pending':
      return 'Pending Signature';
    case 'signed':
      return 'Fully Signed';
    case 'expired':
      return 'Expired';
    case 'cancelled':
      return 'Cancelled';
    default:
      return 'Unknown';
  }
};

/**
 * Check if an agreement can be edited
 */
export const canEditAgreement = (status: AgreementStatus): boolean => {
  return status === 'draft';
};

/**
 * Check if an agreement can be deleted
 */
export const canDeleteAgreement = (status: AgreementStatus): boolean => {
  return status === 'draft';
};

/**
 * Check if an agreement can be signed
 */
export const canSignAgreement = (status: AgreementStatus): boolean => {
  return status === 'pending' || status === 'draft';
};

/**
 * Get signature status text
 */
export const getSignatureStatus = (ownerSigned: boolean, renterSigned: boolean): string => {
  if (ownerSigned && renterSigned) {
    return 'Fully Signed';
  } else if (ownerSigned || renterSigned) {
    return 'Partially Signed';
  } else {
    return 'Unsigned';
  }
};

/**
 * Get signature completion percentage
 */
export const getSignatureCompletion = (ownerSigned: boolean, renterSigned: boolean): number => {
  if (ownerSigned && renterSigned) {
    return 100;
  } else if (ownerSigned || renterSigned) {
    return 50;
  } else {
    return 0;
  }
};
