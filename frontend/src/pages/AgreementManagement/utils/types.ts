/**
 * TypeScript interfaces and types for the AgreementManagement component
 */

/**
 * Agreement status types
 */
export type AgreementStatus = 'draft' | 'pending' | 'signed' | 'expired' | 'cancelled';

/**
 * Agreement interface
 */
export interface Agreement {
  id: number;
  itemId: number;
  itemTitle: string;
  status: AgreementStatus;
  ownerId: number;
  ownerName: string;
  renterId: number;
  renterName: string;
  ownerSigned: boolean;
  renterSigned: boolean;
  createdAt: Date;
  signedAt: Date | null;
  startDate: Date;
  endDate: Date;
  totalAmount: number;
}

/**
 * Agreement statistics interface
 */
export interface AgreementStats {
  total: number;
  signed: number;
  pending: number;
  draft: number;
  expired: number;
  completionRate: number;
}

/**
 * Tab configuration interface
 */
export interface TabConfig {
  label: string;
  value: string;
}

/**
 * Status configuration interface
 */
export interface StatusConfig {
  value: string;
  label: string;
  color: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
}

/**
 * Filter configuration interface
 */
export interface FilterConfig {
  value: string;
  label: string;
}

/**
 * Component props interfaces
 */
export interface LoadingStateProps {
  // No props needed for loading state
}

export interface ErrorStateProps {
  error: string;
}

export interface StatisticsCardsProps {
  stats: AgreementStats;
}

export interface TabNavigationProps {
  activeTab: number;
  onTabChange: (tab: number) => void;
}

export interface SearchAndFiltersProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  statusFilter: string;
  onStatusFilterChange: (status: string) => void;
  signatureFilter: string;
  onSignatureFilterChange: (filter: string) => void;
  showFilters: boolean;
  onToggleFilters: (show: boolean) => void;
}

export interface AgreementsTableProps {
  agreements: Agreement[];
  totalCount: number;
  page: number;
  rowsPerPage: number;
  onPageChange: (event: unknown, newPage: number) => void;
  onRowsPerPageChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

export interface AgreementRowProps {
  agreement: Agreement;
}

/**
 * Hook return types
 */
export interface UseAgreementDataReturn {
  agreements: Agreement[];
  loading: boolean;
  error: string | null;
  setAgreements: (agreements: Agreement[]) => void;
}

export interface UseAgreementFilteringReturn {
  filteredAgreements: Agreement[];
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  statusFilter: string;
  setStatusFilter: (status: string) => void;
  signatureFilter: string;
  setSignatureFilter: (filter: string) => void;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  activeTab: number;
  setActiveTab: (tab: number) => void;
}

export interface UseAgreementPaginationReturn {
  page: number;
  rowsPerPage: number;
  handleChangePage: (event: unknown, newPage: number) => void;
  handleChangeRowsPerPage: (event: React.ChangeEvent<HTMLInputElement>) => void;
  paginatedAgreements: Agreement[];
}
