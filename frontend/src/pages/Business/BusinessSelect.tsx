import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { BusinessMember, BusinessRole } from '../../types/BusinessAccount';
import { useTranslation } from 'react-i18next';

/**
 * BusinessSelect Component
 * 
 * Page for selecting which business account to use.
 */
const BusinessSelect: React.FC = () => {
  const { t } = useTranslation();
  const { user, switchToBusiness, switchToPersonal } = useAuth();
  const { showToast } = useToast();
  const navigate = useNavigate();
  
  // Handle business selection
  const handleSelectBusiness = (businessId: string) => {
    switchToBusiness(businessId);
    showToast(t('business.select.switched'), 'success');
    navigate('/business/dashboard');
  };
  
  // Handle personal account selection
  const handleSelectPersonal = () => {
    switchToPersonal();
    showToast(t('business.select.switchedToPersonal'), 'success');
    navigate('/');
  };
  
  // Get role display name
  const getRoleDisplay = (role: BusinessRole) => {
    switch (role) {
      case BusinessRole.OWNER:
        return t('business.roles.owner');
      case BusinessRole.ADMIN:
        return t('business.roles.admin');
      case BusinessRole.MEMBER:
        return t('business.roles.member');
      case BusinessRole.VIEWER:
        return t('business.roles.viewer');
      default:
        return role;
    }
  };
  
  // If user is not authenticated, redirect to login
  if (!user) {
    navigate('/login');
    return null;
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">{t('business.select.title')}</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">{t('business.select.personalAccount')}</h2>
          <div className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer" onClick={handleSelectPersonal}>
            <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center text-gray-700 text-xl font-medium">
              {user.name.charAt(0)}
            </div>
            <div className="ml-4">
              <h3 className="font-medium text-gray-900">{user.name}</h3>
              <p className="text-sm text-gray-500">{user.email}</p>
            </div>
            <button
              className="ml-auto px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              onClick={handleSelectPersonal}
            >
              {t('business.select.usePersonal')}
            </button>
          </div>
        </div>
        
        {user.businessAccounts && user.businessAccounts.length > 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">{t('business.select.businessAccounts')}</h2>
            <div className="space-y-4">
              {user.businessAccounts.map((account: BusinessMember) => (
                <div
                  key={account.business_id}
                  className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                  onClick={() => handleSelectBusiness(account.business_id)}
                >
                  <div className="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 text-xl font-bold">
                    {account.name.charAt(0)}
                  </div>
                  <div className="ml-4">
                    <h3 className="font-medium text-gray-900">{account.name}</h3>
                    <p className="text-sm text-gray-500">{getRoleDisplay(account.role)}</p>
                  </div>
                  <button
                    className="ml-auto px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    onClick={() => handleSelectBusiness(account.business_id)}
                  >
                    {t('business.select.select')}
                  </button>
                </div>
              ))}
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 text-center">
            <svg className="mx-auto h-12 w-12 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">{t('business.select.noBusinessAccounts')}</h3>
            <p className="mt-1 text-sm text-gray-500">{t('business.select.createBusinessPrompt')}</p>
            <div className="mt-6">
              <Link
                to="/business/create"
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                {t('business.select.createBusiness')}
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BusinessSelect;
