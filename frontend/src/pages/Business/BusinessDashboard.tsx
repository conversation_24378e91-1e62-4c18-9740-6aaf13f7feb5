import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { getBusinessStatistics } from '../../services/businessAccountService';
import { BusinessRole } from '../../types/BusinessAccount';
import { useTranslation } from 'react-i18next';

// Mock data for statistics
const mockStatistics = {
  listings: {
    total: 45,
    active: 32,
    inactive: 13,
    views: 1250,
    inquiries: 78
  },
  rentals: {
    total: 28,
    active: 12,
    completed: 16,
    revenue: 4850,
    averageDuration: 7.5
  },
  members: {
    total: 8,
    active: 7,
    pending: 2
  },
  performance: {
    responseRate: 92,
    responseTime: 2.5,
    completionRate: 95,
    rating: 4.8
  }
};

/**
 * BusinessDashboard Component
 * 
 * Dashboard for business accounts showing key metrics and quick actions.
 */
const BusinessDashboard: React.FC = () => {
  const { t } = useTranslation();
  const { user, currentBusiness, isBusinessAccount, hasBusinessRole } = useAuth();
  const { showToast } = useToast();
  const navigate = useNavigate();
  const [statistics, setStatistics] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Redirect if not a business account
  useEffect(() => {
    if (!isBusinessAccount) {
      navigate('/business/select');
      showToast(t('business.errors.notBusinessAccount'), 'error');
    }
  }, [isBusinessAccount, navigate, showToast, t]);
  
  // Fetch business statistics
  useEffect(() => {
    const fetchStatistics = async () => {
      if (!currentBusiness) return;
      
      try {
        setIsLoading(true);
        // In a real app, we would fetch the statistics from the API
        // const data = await getBusinessStatistics(currentBusiness.id);
        // setStatistics(data);
        
        // For now, use mock data
        setTimeout(() => {
          setStatistics(mockStatistics);
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching business statistics:', error);
        showToast(t('business.errors.fetchStatistics'), 'error');
        setIsLoading(false);
      }
    };
    
    fetchStatistics();
  }, [currentBusiness, showToast, t]);
  
  // Check if user can manage business settings
  const canManageSettings = hasBusinessRole([BusinessRole.OWNER, BusinessRole.ADMIN]);
  
  // Check if user can manage members
  const canManageMembers = hasBusinessRole([BusinessRole.OWNER, BusinessRole.ADMIN]);
  
  if (!currentBusiness) {
    return null;
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{currentBusiness.name}</h1>
          <p className="text-gray-600">{t('business.dashboard.welcomeMessage')}</p>
        </div>
        <div className="mt-4 md:mt-0 flex flex-wrap gap-3">
          <Link
            to="/business/listings/create"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <svg className="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            {t('business.dashboard.createListing')}
          </Link>
          {canManageSettings && (
            <Link
              to="/business/settings"
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <svg className="-ml-1 mr-2 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
              {t('business.dashboard.manageSettings')}
            </Link>
          )}
        </div>
      </div>
      
      {/* Subscription status */}
      <div className={`mb-8 p-4 rounded-lg ${
        currentBusiness.subscription_status === 'active' ? 'bg-green-50 border border-green-200' :
        currentBusiness.subscription_status === 'trialing' ? 'bg-blue-50 border border-blue-200' :
        currentBusiness.subscription_status === 'past_due' ? 'bg-yellow-50 border border-yellow-200' :
        'bg-red-50 border border-red-200'
      }`}>
        <div className="flex items-start">
          <div className={`flex-shrink-0 h-6 w-6 rounded-full flex items-center justify-center ${
            currentBusiness.subscription_status === 'active' ? 'bg-green-100 text-green-600' :
            currentBusiness.subscription_status === 'trialing' ? 'bg-blue-100 text-blue-600' :
            currentBusiness.subscription_status === 'past_due' ? 'bg-yellow-100 text-yellow-600' :
            'bg-red-100 text-red-600'
          }`}>
            {currentBusiness.subscription_status === 'active' && (
              <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
            {currentBusiness.subscription_status === 'trialing' && (
              <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
            )}
            {currentBusiness.subscription_status === 'past_due' && (
              <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            )}
            {currentBusiness.subscription_status === 'canceled' && (
              <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            )}
          </div>
          <div className="ml-3">
            <h3 className={`text-sm font-medium ${
              currentBusiness.subscription_status === 'active' ? 'text-green-800' :
              currentBusiness.subscription_status === 'trialing' ? 'text-blue-800' :
              currentBusiness.subscription_status === 'past_due' ? 'text-yellow-800' :
              'text-red-800'
            }`}>
              {currentBusiness.subscription_status === 'active' && t('business.subscription.active')}
              {currentBusiness.subscription_status === 'trialing' && t('business.subscription.trialing')}
              {currentBusiness.subscription_status === 'past_due' && t('business.subscription.pastDue')}
              {currentBusiness.subscription_status === 'canceled' && t('business.subscription.canceled')}
            </h3>
            <div className={`mt-1 text-sm ${
              currentBusiness.subscription_status === 'active' ? 'text-green-700' :
              currentBusiness.subscription_status === 'trialing' ? 'text-blue-700' :
              currentBusiness.subscription_status === 'past_due' ? 'text-yellow-700' :
              'text-red-700'
            }`}>
              {currentBusiness.subscription_status === 'active' && (
                <p>{t('business.subscription.activeMessage', { tier: currentBusiness.tier })}</p>
              )}
              {currentBusiness.subscription_status === 'trialing' && (
                <p>{t('business.subscription.trialingMessage', { tier: currentBusiness.tier })}</p>
              )}
              {currentBusiness.subscription_status === 'past_due' && (
                <p>{t('business.subscription.pastDueMessage')}</p>
              )}
              {currentBusiness.subscription_status === 'canceled' && (
                <p>{t('business.subscription.canceledMessage')}</p>
              )}
            </div>
          </div>
          <div className="ml-auto">
            {currentBusiness.subscription_status === 'active' && (
              <Link
                to="/business/billing"
                className="text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                {t('business.subscription.manageBilling')}
              </Link>
            )}
            {currentBusiness.subscription_status === 'trialing' && (
              <Link
                to="/business/billing"
                className="text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                {t('business.subscription.upgradePlan')}
              </Link>
            )}
            {currentBusiness.subscription_status === 'past_due' && (
              <Link
                to="/business/billing"
                className="text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                {t('business.subscription.updatePayment')}
              </Link>
            )}
            {currentBusiness.subscription_status === 'canceled' && (
              <Link
                to="/business/billing"
                className="text-sm font-medium text-primary-600 hover:text-primary-500"
              >
                {t('business.subscription.reactivate')}
              </Link>
            )}
          </div>
        </div>
      </div>
      
      {/* Statistics */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      ) : statistics ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Listings */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">{t('business.dashboard.listings')}</h3>
            <p className="text-3xl font-bold text-gray-900">{statistics.listings.total}</p>
            <div className="mt-2 flex items-baseline space-x-2">
              <p className="text-sm text-gray-600">{t('business.dashboard.activeListings')}: {statistics.listings.active}</p>
              <span className="text-green-600 text-xs font-medium">
                {Math.round((statistics.listings.active / statistics.listings.total) * 100)}%
              </span>
            </div>
          </div>
          
          {/* Rentals */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">{t('business.dashboard.rentals')}</h3>
            <p className="text-3xl font-bold text-gray-900">{statistics.rentals.total}</p>
            <div className="mt-2 flex items-baseline space-x-2">
              <p className="text-sm text-gray-600">{t('business.dashboard.activeRentals')}: {statistics.rentals.active}</p>
              <span className="text-green-600 text-xs font-medium">
                ${statistics.rentals.revenue.toLocaleString()}
              </span>
            </div>
          </div>
          
          {/* Members */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">{t('business.dashboard.members')}</h3>
            <p className="text-3xl font-bold text-gray-900">{statistics.members.total}</p>
            <div className="mt-2 flex items-baseline space-x-2">
              <p className="text-sm text-gray-600">{t('business.dashboard.pendingInvites')}: {statistics.members.pending}</p>
              {canManageMembers && (
                <Link to="/business/members" className="text-primary-600 text-xs font-medium">
                  {t('business.dashboard.manage')}
                </Link>
              )}
            </div>
          </div>
          
          {/* Performance */}
          <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
            <h3 className="text-sm font-medium text-gray-500 uppercase tracking-wider mb-2">{t('business.dashboard.performance')}</h3>
            <p className="text-3xl font-bold text-gray-900">{statistics.performance.rating}</p>
            <div className="mt-2 flex items-baseline space-x-2">
              <p className="text-sm text-gray-600">{t('business.dashboard.responseRate')}: {statistics.performance.responseRate}%</p>
              <span className="text-green-600 text-xs font-medium">
                {statistics.performance.responseTime}h
              </span>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
          <p className="text-gray-600">{t('business.dashboard.noStatistics')}</p>
        </div>
      )}
      
      {/* Quick links */}
      <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
        <h2 className="text-lg font-medium text-gray-900 mb-4">{t('business.dashboard.quickLinks')}</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            to="/business/listings"
            className="flex items-center p-3 rounded-md hover:bg-gray-50 transition-colors"
          >
            <div className="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center text-primary-600 mr-3">
              <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{t('business.dashboard.viewListings')}</h3>
              <p className="text-sm text-gray-500">{t('business.dashboard.viewListingsDesc')}</p>
            </div>
          </Link>
          
          <Link
            to="/business/rentals"
            className="flex items-center p-3 rounded-md hover:bg-gray-50 transition-colors"
          >
            <div className="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center text-primary-600 mr-3">
              <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{t('business.dashboard.manageRentals')}</h3>
              <p className="text-sm text-gray-500">{t('business.dashboard.manageRentalsDesc')}</p>
            </div>
          </Link>
          
          {canManageMembers ? (
            <Link
              to="/business/members"
              className="flex items-center p-3 rounded-md hover:bg-gray-50 transition-colors"
            >
              <div className="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center text-primary-600 mr-3">
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v1h8v-1zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-1a3 3 0 00-3-3h-2a3 3 0 00-3 3v1h8zM4 15a3 3 0 013-3h2a3 3 0 013 3v1H4v-1z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{t('business.dashboard.manageMembers')}</h3>
                <p className="text-sm text-gray-500">{t('business.dashboard.manageMembersDesc')}</p>
              </div>
            </Link>
          ) : (
            <Link
              to="/business/analytics"
              className="flex items-center p-3 rounded-md hover:bg-gray-50 transition-colors"
            >
              <div className="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center text-primary-600 mr-3">
                <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{t('business.dashboard.viewAnalytics')}</h3>
                <p className="text-sm text-gray-500">{t('business.dashboard.viewAnalyticsDesc')}</p>
              </div>
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};

export default BusinessDashboard;
