import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { createBusinessAccount } from '../../services/businessAccountService';
import { BusinessIndustry, BusinessSize, BusinessAccountCreateRequest } from '../../types/BusinessAccount';
import { useTranslation } from 'react-i18next';

/**
 * BusinessCreate Component
 * 
 * Form for creating a new business account.
 */
const BusinessCreate: React.FC = () => {
  const { t } = useTranslation();
  const { user, updateUser } = useAuth();
  const { showToast } = useToast();
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState<BusinessAccountCreateRequest>({
    name: '',
    industry: BusinessIndustry.OTHER,
    size: BusinessSize.SMALL
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };
  
  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = t('business.create.errors.nameRequired');
    } else if (formData.name.length < 3) {
      newErrors.name = t('business.create.errors.nameLength');
    }
    
    if (!formData.industry) {
      newErrors.industry = t('business.create.errors.industryRequired');
    }
    
    if (!formData.size) {
      newErrors.size = t('business.create.errors.sizeRequired');
    }
    
    if (formData.website && !/^(https?:\/\/)?(www\.)?[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(\/.*)?$/.test(formData.website)) {
      newErrors.website = t('business.create.errors.invalidWebsite');
    }
    
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = t('business.create.errors.invalidEmail');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // Create business account
      const business = await createBusinessAccount(formData);
      
      // Update user with new business account
      if (user) {
        const businessMember = {
          business_id: business.id,
          user_id: user.id,
          role: 'owner',
          name: business.name,
          email: user.email,
          joined_at: new Date().toISOString(),
          invited_by: user.id,
          is_active: true,
          permissions: {
            manage_members: true,
            manage_listings: true,
            manage_rentals: true,
            manage_billing: true,
            manage_settings: true,
            view_analytics: true,
            approve_transactions: true
          }
        };
        
        const businessAccounts = user.businessAccounts ? [...user.businessAccounts, businessMember] : [businessMember];
        
        updateUser({
          ...user,
          businessAccounts,
          currentBusinessId: business.id
        });
      }
      
      showToast(t('business.create.success'), 'success');
      navigate('/business/dashboard');
    } catch (error) {
      console.error('Error creating business account:', error);
      showToast(t('business.create.error'), 'error');
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">{t('business.create.title')}</h1>
        
        <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              {/* Business Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('business.create.businessName')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border ${
                    errors.name ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                  } rounded-md shadow-sm focus:outline-none focus:ring-2`}
                  placeholder={t('business.create.businessNamePlaceholder')}
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                )}
              </div>
              
              {/* Business Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('business.create.description')}
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description || ''}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder={t('business.create.descriptionPlaceholder')}
                />
              </div>
              
              {/* Industry and Size */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('business.create.industry')} <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="industry"
                    name="industry"
                    value={formData.industry}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border ${
                      errors.industry ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                    } rounded-md shadow-sm focus:outline-none focus:ring-2`}
                  >
                    <option value="">{t('business.create.selectIndustry')}</option>
                    {Object.entries(BusinessIndustry).map(([key, value]) => (
                      <option key={key} value={value}>
                        {t(`business.industries.${value}`)}
                      </option>
                    ))}
                  </select>
                  {errors.industry && (
                    <p className="mt-1 text-sm text-red-600">{errors.industry}</p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="size" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('business.create.size')} <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="size"
                    name="size"
                    value={formData.size}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border ${
                      errors.size ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                    } rounded-md shadow-sm focus:outline-none focus:ring-2`}
                  >
                    <option value="">{t('business.create.selectSize')}</option>
                    {Object.entries(BusinessSize).map(([key, value]) => (
                      <option key={key} value={value}>
                        {t(`business.sizes.${value}`)}
                      </option>
                    ))}
                  </select>
                  {errors.size && (
                    <p className="mt-1 text-sm text-red-600">{errors.size}</p>
                  )}
                </div>
              </div>
              
              {/* Website and Email */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('business.create.website')}
                  </label>
                  <input
                    type="text"
                    id="website"
                    name="website"
                    value={formData.website || ''}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border ${
                      errors.website ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                    } rounded-md shadow-sm focus:outline-none focus:ring-2`}
                    placeholder="https://example.com"
                  />
                  {errors.website && (
                    <p className="mt-1 text-sm text-red-600">{errors.website}</p>
                  )}
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    {t('business.create.email')}
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email || ''}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border ${
                      errors.email ? 'border-red-300 focus:ring-red-500' : 'border-gray-300 focus:ring-primary-500'
                    } rounded-md shadow-sm focus:outline-none focus:ring-2`}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                  )}
                </div>
              </div>
              
              {/* Phone Number */}
              <div>
                <label htmlFor="phone_number" className="block text-sm font-medium text-gray-700 mb-1">
                  {t('business.create.phoneNumber')}
                </label>
                <input
                  type="tel"
                  id="phone_number"
                  name="phone_number"
                  value={formData.phone_number || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="+****************"
                />
              </div>
              
              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => navigate(-1)}
                  className="mr-4 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  disabled={isSubmitting}
                >
                  {t('common.cancel')}
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 flex items-center"
                  disabled={isSubmitting}
                >
                  {isSubmitting && (
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}
                  {t('business.create.createAccount')}
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BusinessCreate;
