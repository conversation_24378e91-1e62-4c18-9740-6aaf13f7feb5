// Members table component
import React from 'react';
import { useTranslation } from 'react-i18next';
import { BusinessRole } from '../../../types/BusinessAccount';
import { BusinessMember } from '../utils/types';
import { getRoleBadgeColor, getRoleDisplay, formatDate } from '../utils/helpers';
import { MEMBER_TABLE_COLUMNS } from '../utils/constants';

interface MembersTableProps {
  members: BusinessMember[];
  onRemoveMember: (memberId: string) => void;
}

/**
 * Members table component displaying business team members
 */
const MembersTable: React.FC<MembersTableProps> = ({
  members,
  onRemoveMember,
}) => {
  const { t } = useTranslation();

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">
          {t('business.members.membersList')}
        </h2>
        <p className="mt-1 text-sm text-gray-500">
          {t('business.members.totalMembers', { count: members.length })}
        </p>
      </div>

      {members.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {MEMBER_TABLE_COLUMNS.map((column) => (
                  <th
                    key={column.key}
                    scope="col"
                    className={`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${
                      column.className || 'text-left'
                    }`}
                  >
                    {t(column.label)}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {members.map((member) => (
                <tr key={member.id} className="hover:bg-gray-50">
                  {/* Member Info */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        {member.avatar_url ? (
                          <img 
                            className="h-10 w-10 rounded-full" 
                            src={member.avatar_url} 
                            alt={member.name} 
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                            <span className="text-primary-800 font-medium text-sm">
                              {member.name.charAt(0)}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{member.name}</div>
                        <div className="text-sm text-gray-500">{member.email}</div>
                      </div>
                    </div>
                  </td>

                  {/* Role */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleBadgeColor(member.role)}`}>
                      {getRoleDisplay(member.role)}
                    </span>
                  </td>

                  {/* Joined Date */}
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(member.joined_at)}
                  </td>

                  {/* Last Active */}
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(member.last_active)}
                  </td>

                  {/* Status */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      member.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {member.is_active ? t('business.members.active') : t('business.members.inactive')}
                    </span>
                  </td>

                  {/* Actions */}
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {member.role !== BusinessRole.OWNER ? (
                      <div className="flex justify-end space-x-2">
                        <button
                          type="button"
                          className="text-primary-600 hover:text-primary-900"
                          onClick={() => {/* Edit member function would be implemented in a real app */}}
                        >
                          {t('business.members.edit')}
                        </button>
                        <button
                          type="button"
                          className="text-red-600 hover:text-red-900"
                          onClick={() => onRemoveMember(member.id)}
                        >
                          {t('business.members.remove')}
                        </button>
                      </div>
                    ) : (
                      <span className="text-gray-400">{t('business.members.owner')}</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="p-6 text-center">
          <p className="text-gray-500">{t('business.members.noMembers')}</p>
        </div>
      )}
    </div>
  );
};

export default MembersTable;
