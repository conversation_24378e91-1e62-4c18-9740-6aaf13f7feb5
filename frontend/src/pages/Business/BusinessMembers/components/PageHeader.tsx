// Page header component
import React from 'react';
import { useTranslation } from 'react-i18next';

interface PageHeaderProps {
  onInviteClick: () => void;
}

/**
 * Page header component with title and invite button
 */
const PageHeader: React.FC<PageHeaderProps> = ({ onInviteClick }) => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {t('business.members.title')}
        </h1>
        <p className="text-gray-600">
          {t('business.members.subtitle')}
        </p>
      </div>
      <div className="mt-4 md:mt-0 flex flex-wrap gap-3">
        <button
          onClick={onInviteClick}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <svg 
            className="-ml-1 mr-2 h-5 w-5" 
            xmlns="http://www.w3.org/2000/svg" 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z" />
          </svg>
          {t('business.members.inviteMember')}
        </button>
      </div>
    </div>
  );
};

export default PageHeader;
