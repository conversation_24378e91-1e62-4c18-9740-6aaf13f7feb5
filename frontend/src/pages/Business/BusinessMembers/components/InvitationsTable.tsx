// Invitations table component
import React from 'react';
import { useTranslation } from 'react-i18next';
import { BusinessInvitation } from '../utils/types';
import { getRoleBadgeColor, getRoleDisplay, formatDate } from '../utils/helpers';
import { INVITATION_TABLE_COLUMNS } from '../utils/constants';

interface InvitationsTableProps {
  invitations: BusinessInvitation[];
  onCancelInvitation: (invitationId: string) => void;
}

/**
 * Invitations table component displaying pending invitations
 */
const InvitationsTable: React.FC<InvitationsTableProps> = ({
  invitations,
  onCancelInvitation,
}) => {
  const { t } = useTranslation();

  if (invitations.length === 0) {
    return null;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">
          {t('business.members.pendingInvitations')}
        </h2>
        <p className="mt-1 text-sm text-gray-500">
          {t('business.members.totalInvitations', { count: invitations.length })}
        </p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {INVITATION_TABLE_COLUMNS.map((column) => (
                <th
                  key={column.key}
                  scope="col"
                  className={`px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider ${
                    column.className || 'text-left'
                  }`}
                >
                  {t(column.label)}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {invitations.map((invitation) => (
              <tr key={invitation.id} className="hover:bg-gray-50">
                {/* Email */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {invitation.email}
                  </div>
                </td>

                {/* Role */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleBadgeColor(invitation.role)}`}>
                    {getRoleDisplay(invitation.role)}
                  </span>
                </td>

                {/* Invited Date */}
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(invitation.invited_at)}
                </td>

                {/* Expires Date */}
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {formatDate(invitation.expires_at)}
                </td>

                {/* Status */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    {t('business.members.pending')}
                  </span>
                </td>

                {/* Actions */}
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    type="button"
                    className="text-red-600 hover:text-red-900"
                    onClick={() => onCancelInvitation(invitation.id)}
                  >
                    {t('business.members.cancel')}
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default InvitationsTable;
