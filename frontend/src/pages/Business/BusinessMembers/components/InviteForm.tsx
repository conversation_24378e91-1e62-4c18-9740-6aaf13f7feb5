// Invite form component
import React from 'react';
import { useTranslation } from 'react-i18next';
import { BusinessRole } from '../../../types/BusinessAccount';
import { InviteFormData, InviteFormErrors } from '../utils/types';
import { PERMISSION_LABELS } from '../utils/constants';

interface InviteFormProps {
  isVisible: boolean;
  formData: InviteFormData;
  errors: InviteFormErrors;
  isSubmitting: boolean;
  onSubmit: (e: React.FormEvent) => void;
  onCancel: () => void;
  onUpdateFormData: (updates: Partial<InviteFormData>) => void;
  onUpdatePermissions: (permission: string, value: boolean) => void;
}

/**
 * Invite form component for adding new business members
 */
const InviteForm: React.FC<InviteFormProps> = ({
  isVisible,
  formData,
  errors,
  isSubmitting,
  onSubmit,
  onCancel,
  onUpdateFormData,
  onUpdatePermissions,
}) => {
  const { t } = useTranslation();

  if (!isVisible) return null;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-8">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">
          {t('business.members.inviteMember')}
        </h2>
        <p className="mt-1 text-sm text-gray-500">
          {t('business.members.inviteDescription')}
        </p>
      </div>

      <form onSubmit={onSubmit} className="p-6">
        {errors.general && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{errors.general}</p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              {t('business.members.email')} *
            </label>
            <input
              type="email"
              id="email"
              value={formData.email}
              onChange={(e) => onUpdateFormData({ email: e.target.value })}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 ${
                errors.email ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder={t('business.members.emailPlaceholder')}
              disabled={isSubmitting}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email}</p>
            )}
          </div>

          {/* Role Field */}
          <div>
            <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-2">
              {t('business.members.role')} *
            </label>
            <select
              id="role"
              value={formData.role}
              onChange={(e) => onUpdateFormData({ role: e.target.value as BusinessRole })}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 ${
                errors.role ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={isSubmitting}
            >
              <option value={BusinessRole.MEMBER}>{t('business.roles.member')}</option>
              <option value={BusinessRole.ADMIN}>{t('business.roles.admin')}</option>
            </select>
            {errors.role && (
              <p className="mt-1 text-sm text-red-600">{errors.role}</p>
            )}
          </div>
        </div>

        {/* Permissions */}
        <div className="mt-6">
          <h3 className="text-sm font-medium text-gray-700 mb-3">
            {t('business.members.permissions')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(PERMISSION_LABELS).map(([key, label]) => (
              <div key={key} className="flex items-center">
                <input
                  type="checkbox"
                  id={`permission-${key}`}
                  checked={formData.permissions[key as keyof typeof formData.permissions]}
                  onChange={(e) => onUpdatePermissions(key, e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  disabled={isSubmitting}
                />
                <label htmlFor={`permission-${key}`} className="ml-2 text-sm text-gray-700">
                  {t(label)}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Form Actions */}
        <div className="mt-6 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            disabled={isSubmitting}
          >
            {t('common.cancel')}
          </button>
          <button
            type="submit"
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isSubmitting}
          >
            {isSubmitting ? t('business.members.sending') : t('business.members.sendInvitation')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default InviteForm;
