// Custom hook for managing business members data
import { useState, useEffect } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { useToast } from '../../../hooks/useToast';
import { useTranslation } from 'react-i18next';
import { BusinessMember, BusinessInvitation } from '../utils/types';
import { generateMockMembers, generateMockInvitations } from '../utils/helpers';
import { ANIMATION_DURATIONS } from '../utils/constants';

/**
 * Custom hook for fetching and managing business members and invitations data
 */
export const useBusinessMembersData = () => {
  const { user, currentBusiness } = useAuth();
  const { showToast } = useToast();
  const { t } = useTranslation();

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [members, setMembers] = useState<BusinessMember[]>([]);
  const [pendingInvitations, setPendingInvitations] = useState<BusinessInvitation[]>([]);

  // Load members and invitations data
  useEffect(() => {
    const loadData = async () => {
      if (!currentBusiness || !user) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // In a real implementation, this would fetch from the API
        // const [membersResponse, invitationsResponse] = await Promise.all([
        //   getBusinessMembers(currentBusiness.id),
        //   getBusinessInvitations(currentBusiness.id)
        // ]);

        // For now, use mock data
        setTimeout(() => {
          const mockMembers = generateMockMembers(currentBusiness.id, user.id);
          const mockInvitations = generateMockInvitations(currentBusiness.id, user.id);

          setMembers(mockMembers);
          setPendingInvitations(mockInvitations);
          setIsLoading(false);
        }, ANIMATION_DURATIONS.LOADING_DELAY);
      } catch (error) {
        console.error('Error loading members and invitations:', error);
        showToast(t('business.members.errors.loadFailed'), 'error');
        setIsLoading(false);
      }
    };

    loadData();
  }, [currentBusiness, user, showToast, t]);

  // Remove member
  const removeMember = async (memberId: string): Promise<boolean> => {
    if (!currentBusiness) return false;

    try {
      // In a real app, we would call the API to remove the member
      // await removeBusinessMember(currentBusiness.id, memberId);

      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, ANIMATION_DURATIONS.API_SIMULATION));

      // Remove the member from the list
      setMembers(prevMembers => prevMembers.filter(member => member.id !== memberId));

      showToast(t('business.members.memberRemoved'), 'success');
      return true;
    } catch (error) {
      console.error('Error removing member:', error);
      showToast(t('business.members.errors.removeFailed'), 'error');
      return false;
    }
  };

  // Cancel invitation
  const cancelInvitation = async (invitationId: string): Promise<boolean> => {
    if (!currentBusiness) return false;

    try {
      // In a real app, we would call the API to cancel the invitation
      // await cancelBusinessInvitation(currentBusiness.id, invitationId);

      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, ANIMATION_DURATIONS.API_SIMULATION));

      // Remove the invitation from the list
      setPendingInvitations(prevInvitations => 
        prevInvitations.filter(invitation => invitation.id !== invitationId)
      );

      showToast(t('business.members.invitationCancelled'), 'success');
      return true;
    } catch (error) {
      console.error('Error cancelling invitation:', error);
      showToast(t('business.members.errors.cancelFailed'), 'error');
      return false;
    }
  };

  // Add new invitation
  const addInvitation = (invitation: BusinessInvitation) => {
    setPendingInvitations(prevInvitations => [...prevInvitations, invitation]);
  };

  // Retry loading data
  const retryLoad = () => {
    setIsLoading(true);
    // Re-trigger the effect by updating dependencies
    setTimeout(() => {
      if (currentBusiness && user) {
        const mockMembers = generateMockMembers(currentBusiness.id, user.id);
        const mockInvitations = generateMockInvitations(currentBusiness.id, user.id);

        setMembers(mockMembers);
        setPendingInvitations(mockInvitations);
      }
      setIsLoading(false);
    }, ANIMATION_DURATIONS.LOADING_DELAY);
  };

  return {
    // State
    isLoading,
    members,
    pendingInvitations,
    
    // Actions
    removeMember,
    cancelInvitation,
    addInvitation,
    retryLoad,
  };
};
