// Custom hook for managing invite form state
import { useState, useCallback } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { useToast } from '../../../hooks/useToast';
import { useTranslation } from 'react-i18next';
import { BusinessRole } from '../../../types/BusinessAccount';
import { InviteFormData, InviteFormErrors, BusinessInvitation } from '../utils/types';
import { validateInviteForm, getDefaultInviteFormData, createNewInvitation } from '../utils/helpers';
import { DEFAULT_PERMISSIONS, ANIMATION_DURATIONS } from '../utils/constants';

interface UseInviteFormProps {
  onInvitationSent: (invitation: BusinessInvitation) => void;
}

/**
 * Custom hook for managing invite form functionality
 */
export const useInviteForm = ({ onInvitationSent }: UseInviteFormProps) => {
  const { user, currentBusiness } = useAuth();
  const { showToast } = useToast();
  const { t } = useTranslation();

  const [showInviteForm, setShowInviteForm] = useState<boolean>(false);
  const [inviteFormData, setInviteFormData] = useState<InviteFormData>(getDefaultInviteFormData());
  const [inviteFormErrors, setInviteFormErrors] = useState<InviteFormErrors>({});
  const [isInviting, setIsInviting] = useState<boolean>(false);

  // Toggle invite form
  const toggleInviteForm = useCallback(() => {
    setShowInviteForm(prev => !prev);
    // Reset form data and errors when toggling
    if (!showInviteForm) {
      setInviteFormData(getDefaultInviteFormData());
      setInviteFormErrors({});
    }
  }, [showInviteForm]);

  // Update form data
  const updateFormData = useCallback((updates: Partial<InviteFormData>) => {
    setInviteFormData(prev => ({ ...prev, ...updates }));
    // Clear related errors when updating
    if (updates.email !== undefined) {
      setInviteFormErrors(prev => ({ ...prev, email: undefined }));
    }
    if (updates.role !== undefined) {
      setInviteFormErrors(prev => ({ ...prev, role: undefined }));
      // Update permissions based on role
      setInviteFormData(prev => ({
        ...prev,
        permissions: { ...DEFAULT_PERMISSIONS[updates.role as BusinessRole] }
      }));
    }
  }, []);

  // Update permissions
  const updatePermissions = useCallback((permission: string, value: boolean) => {
    setInviteFormData(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permission]: value
      }
    }));
  }, []);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentBusiness || !user) {
      showToast(t('business.members.errors.invalidBusiness'), 'error');
      return;
    }

    // Validate form
    const errors = validateInviteForm(inviteFormData);
    if (Object.keys(errors).length > 0) {
      setInviteFormErrors(errors);
      return;
    }

    try {
      setIsInviting(true);
      setInviteFormErrors({});

      // In a real app, we would send the invitation to the API
      // await inviteBusinessMember(currentBusiness.id, inviteFormData);

      // For now, simulate API call
      await new Promise(resolve => setTimeout(resolve, ANIMATION_DURATIONS.LOADING_DELAY));

      // Create the new invitation
      const newInvitation = createNewInvitation(currentBusiness.id, inviteFormData, user.id);

      // Add the invitation to the list
      onInvitationSent(newInvitation);

      // Reset form and close
      setInviteFormData(getDefaultInviteFormData());
      setShowInviteForm(false);

      showToast(t('business.members.invitationSent'), 'success');
    } catch (error) {
      console.error('Error sending invitation:', error);
      setInviteFormErrors({ general: t('business.members.errors.inviteFailed') });
      showToast(t('business.members.errors.inviteFailed'), 'error');
    } finally {
      setIsInviting(false);
    }
  }, [currentBusiness, user, inviteFormData, onInvitationSent, showToast, t]);

  // Cancel form
  const cancelForm = useCallback(() => {
    setShowInviteForm(false);
    setInviteFormData(getDefaultInviteFormData());
    setInviteFormErrors({});
  }, []);

  return {
    // State
    showInviteForm,
    inviteFormData,
    inviteFormErrors,
    isInviting,
    
    // Actions
    toggleInviteForm,
    updateFormData,
    updatePermissions,
    handleSubmit,
    cancelForm,
  };
};
