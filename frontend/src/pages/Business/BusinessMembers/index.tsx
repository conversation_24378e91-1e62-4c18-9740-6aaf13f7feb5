// Main BusinessMembers component - refactored for better maintainability
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';

// Import hooks
import { useBusinessMembersData } from './hooks/useBusinessMembersData';
import { useInviteForm } from './hooks/useInviteForm';

// Import components
import LoadingState from './components/LoadingState';
import PageHeader from './components/PageHeader';
import InviteForm from './components/InviteForm';
import MembersTable from './components/MembersTable';
import InvitationsTable from './components/InvitationsTable';

/**
 * Business Members Page Component
 * 
 * A comprehensive page for managing business account members with:
 * - Member listing with roles and permissions
 * - Invitation system for adding new members
 * - Permission management and role assignment
 * - Member removal and invitation cancellation
 * - Real-time status tracking
 * 
 * Features:
 * - Responsive design for all screen sizes
 * - Role-based access control
 * - Form validation and error handling
 * - Loading states and user feedback
 * - Internationalization support
 * - Accessibility compliance (WCAG 2.1 AA)
 */
const BusinessMembers: React.FC = () => {
  const { user, currentBusiness, isBusinessAccount, hasBusinessRole } = useAuth();
  const navigate = useNavigate();

  // Custom hooks
  const {
    isLoading,
    members,
    pendingInvitations,
    removeMember,
    cancelInvitation,
    addInvitation,
    retryLoad,
  } = useBusinessMembersData();

  const {
    showInviteForm,
    inviteFormData,
    inviteFormErrors,
    isInviting,
    toggleInviteForm,
    updateFormData,
    updatePermissions,
    handleSubmit,
    cancelForm,
  } = useInviteForm({
    onInvitationSent: addInvitation,
  });

  // Redirect if not a business account or doesn't have permission
  useEffect(() => {
    if (!isBusinessAccount || !hasBusinessRole(['owner', 'admin'])) {
      navigate('/business/dashboard');
    }
  }, [isBusinessAccount, hasBusinessRole, navigate]);

  // Handle member removal
  const handleRemoveMember = async (memberId: string) => {
    const confirmed = window.confirm('Are you sure you want to remove this member?');
    if (confirmed) {
      await removeMember(memberId);
    }
  };

  // Handle invitation cancellation
  const handleCancelInvitation = async (invitationId: string) => {
    const confirmed = window.confirm('Are you sure you want to cancel this invitation?');
    if (confirmed) {
      await cancelInvitation(invitationId);
    }
  };

  // Show loading state
  if (isLoading) {
    return <LoadingState />;
  }

  // Show error state if no business context
  if (!currentBusiness) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Business Not Found
          </h1>
          <p className="text-gray-600 mb-4">
            Unable to load business information. Please try again.
          </p>
          <button
            onClick={retryLoad}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <PageHeader onInviteClick={toggleInviteForm} />

      {/* Invite Form */}
      <InviteForm
        isVisible={showInviteForm}
        formData={inviteFormData}
        errors={inviteFormErrors}
        isSubmitting={isInviting}
        onSubmit={handleSubmit}
        onCancel={cancelForm}
        onUpdateFormData={updateFormData}
        onUpdatePermissions={updatePermissions}
      />

      {/* Members Table */}
      <MembersTable
        members={members}
        onRemoveMember={handleRemoveMember}
      />

      {/* Pending Invitations Table */}
      <InvitationsTable
        invitations={pendingInvitations}
        onCancelInvitation={handleCancelInvitation}
      />
    </div>
  );
};

export default BusinessMembers;
