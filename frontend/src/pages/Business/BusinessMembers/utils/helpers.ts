// Helper functions for the BusinessMembers component
import { BusinessRole } from '../../../types/BusinessAccount';
import { BusinessMember, BusinessInvitation, InviteFormData, InviteFormErrors } from './types';
import { ROLE_BADGE_COLORS, DEFAULT_PERMISSIONS, ANIMATION_DURATIONS } from './constants';

/**
 * Get role badge color classes
 */
export const getRoleBadgeColor = (role: BusinessRole): string => {
  return ROLE_BADGE_COLORS[role] || 'bg-gray-100 text-gray-800';
};

/**
 * Get role display name
 */
export const getRoleDisplay = (role: BusinessRole): string => {
  const roleMap: Record<BusinessRole, string> = {
    [BusinessRole.OWNER]: 'Owner',
    [BusinessRole.ADMIN]: 'Admin',
    [BusinessRole.MEMBER]: 'Member',
  };
  return roleMap[role] || role;
};

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString();
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validate invite form data
 */
export const validateInviteForm = (data: InviteFormData): InviteFormErrors => {
  const errors: InviteFormErrors = {};

  if (!data.email.trim()) {
    errors.email = 'Email is required';
  } else if (!isValidEmail(data.email)) {
    errors.email = 'Please enter a valid email address';
  }

  if (!data.role) {
    errors.role = 'Role is required';
  }

  return errors;
};

/**
 * Check if user can manage members
 */
export const canManageMembers = (userRole: BusinessRole, targetRole: BusinessRole): boolean => {
  if (userRole === BusinessRole.OWNER) return true;
  if (userRole === BusinessRole.ADMIN && targetRole !== BusinessRole.OWNER) return true;
  return false;
};

/**
 * Generate mock member data
 */
export const generateMockMembers = (businessId: string, userId?: string): BusinessMember[] => {
  return [
    {
      id: '1',
      user_id: userId || 'user1',
      business_id: businessId,
      role: BusinessRole.OWNER,
      name: 'Owner User',
      email: '<EMAIL>',
      avatar_url: undefined,
      joined_at: new Date().toISOString(),
      invited_by: userId || 'user1',
      is_active: true,
      last_active: new Date().toISOString(),
      permissions: DEFAULT_PERMISSIONS[BusinessRole.OWNER],
    },
    {
      id: '2',
      user_id: 'user2',
      business_id: businessId,
      role: BusinessRole.ADMIN,
      name: 'Admin User',
      email: '<EMAIL>',
      avatar_url: undefined,
      joined_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      invited_by: userId || 'user1',
      is_active: true,
      last_active: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      permissions: DEFAULT_PERMISSIONS[BusinessRole.ADMIN],
    },
    {
      id: '3',
      user_id: 'user3',
      business_id: businessId,
      role: BusinessRole.MEMBER,
      name: 'Regular Member',
      email: '<EMAIL>',
      avatar_url: undefined,
      joined_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
      invited_by: userId || 'user1',
      is_active: true,
      last_active: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      permissions: DEFAULT_PERMISSIONS[BusinessRole.MEMBER],
    },
  ];
};

/**
 * Generate mock invitation data
 */
export const generateMockInvitations = (businessId: string, userId?: string): BusinessInvitation[] => {
  return [
    {
      id: 'inv1',
      business_id: businessId,
      email: '<EMAIL>',
      role: BusinessRole.MEMBER,
      invited_by: userId || 'user1',
      invited_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      expires_at: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(),
      status: 'pending',
      permissions: DEFAULT_PERMISSIONS[BusinessRole.MEMBER],
    },
  ];
};

/**
 * Create new invitation object
 */
export const createNewInvitation = (
  businessId: string,
  inviteData: InviteFormData,
  invitedBy: string
): BusinessInvitation => {
  return {
    id: `inv${Date.now()}`,
    business_id: businessId,
    email: inviteData.email,
    role: inviteData.role,
    invited_by: invitedBy,
    invited_at: new Date().toISOString(),
    expires_at: new Date(Date.now() + ANIMATION_DURATIONS.INVITATION_EXPIRY_DAYS * 24 * 60 * 60 * 1000).toISOString(),
    status: 'pending',
    permissions: inviteData.permissions,
  };
};

/**
 * Get default form data for invitations
 */
export const getDefaultInviteFormData = (): InviteFormData => {
  return {
    email: '',
    role: BusinessRole.MEMBER,
    permissions: { ...DEFAULT_PERMISSIONS[BusinessRole.MEMBER] },
  };
};
