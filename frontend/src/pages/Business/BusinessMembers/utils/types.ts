// Types for the BusinessMembers component
import { BusinessRole } from '../../../types/BusinessAccount';

export interface BusinessMember {
  id: string;
  user_id: string;
  business_id: string;
  role: BusinessRole;
  name: string;
  email: string;
  avatar_url?: string;
  joined_at: string;
  invited_by: string;
  is_active: boolean;
  last_active: string;
  permissions: BusinessPermissions;
}

export interface BusinessInvitation {
  id: string;
  business_id: string;
  email: string;
  role: BusinessRole;
  invited_by: string;
  invited_at: string;
  expires_at: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  permissions: BusinessPermissions;
}

export interface BusinessPermissions {
  manage_members: boolean;
  manage_listings: boolean;
  manage_rentals: boolean;
  manage_billing: boolean;
  manage_settings: boolean;
  view_analytics: boolean;
  approve_transactions: boolean;
}

export interface InviteFormData {
  email: string;
  role: BusinessRole;
  permissions: BusinessPermissions;
}

export interface InviteFormErrors {
  email?: string;
  role?: string;
  permissions?: string;
  general?: string;
}

export interface BusinessMembersState {
  isLoading: boolean;
  members: BusinessMember[];
  pendingInvitations: BusinessInvitation[];
  showInviteForm: boolean;
  inviteFormData: InviteFormData;
  inviteFormErrors: InviteFormErrors;
  isInviting: boolean;
}

export interface MemberTableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  className?: string;
}

export interface InvitationTableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  className?: string;
}
