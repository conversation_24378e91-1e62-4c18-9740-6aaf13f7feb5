// Constants for the BusinessMembers component
import { BusinessRole } from '../../../types/BusinessAccount';
import { BusinessPermissions, MemberTableColumn, InvitationTableColumn } from './types';

// Default permissions for different roles
export const DEFAULT_PERMISSIONS: Record<BusinessRole, BusinessPermissions> = {
  [BusinessRole.OWNER]: {
    manage_members: true,
    manage_listings: true,
    manage_rentals: true,
    manage_billing: true,
    manage_settings: true,
    view_analytics: true,
    approve_transactions: true,
  },
  [BusinessRole.ADMIN]: {
    manage_members: true,
    manage_listings: true,
    manage_rentals: true,
    manage_billing: false,
    manage_settings: true,
    view_analytics: true,
    approve_transactions: true,
  },
  [BusinessRole.MEMBER]: {
    manage_members: false,
    manage_listings: true,
    manage_rentals: true,
    manage_billing: false,
    manage_settings: false,
    view_analytics: true,
    approve_transactions: false,
  },
};

// Role badge colors
export const ROLE_BADGE_COLORS: Record<BusinessRole, string> = {
  [BusinessRole.OWNER]: 'bg-purple-100 text-purple-800',
  [BusinessRole.ADMIN]: 'bg-blue-100 text-blue-800',
  [BusinessRole.MEMBER]: 'bg-green-100 text-green-800',
};

// Member table columns
export const MEMBER_TABLE_COLUMNS: MemberTableColumn[] = [
  { key: 'member', label: 'business.members.member', sortable: true },
  { key: 'role', label: 'business.members.role', sortable: true },
  { key: 'joinedDate', label: 'business.members.joinedDate', sortable: true },
  { key: 'lastActive', label: 'business.members.lastActive', sortable: true },
  { key: 'status', label: 'business.members.status', sortable: true },
  { key: 'actions', label: 'business.members.actions', className: 'text-right' },
];

// Invitation table columns
export const INVITATION_TABLE_COLUMNS: InvitationTableColumn[] = [
  { key: 'email', label: 'business.members.email', sortable: true },
  { key: 'role', label: 'business.members.role', sortable: true },
  { key: 'invitedDate', label: 'business.members.invitedDate', sortable: true },
  { key: 'expiresDate', label: 'business.members.expiresDate', sortable: true },
  { key: 'status', label: 'business.members.status', sortable: true },
  { key: 'actions', label: 'business.members.actions', className: 'text-right' },
];

// Animation and timing constants
export const ANIMATION_DURATIONS = {
  LOADING_DELAY: 1000,
  API_SIMULATION: 500,
  INVITATION_EXPIRY_DAYS: 7,
} as const;

// Permission labels for display
export const PERMISSION_LABELS: Record<keyof BusinessPermissions, string> = {
  manage_members: 'business.permissions.manageMembers',
  manage_listings: 'business.permissions.manageListings',
  manage_rentals: 'business.permissions.manageRentals',
  manage_billing: 'business.permissions.manageBilling',
  manage_settings: 'business.permissions.manageSettings',
  view_analytics: 'business.permissions.viewAnalytics',
  approve_transactions: 'business.permissions.approveTransactions',
};
