import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import BusinessDashboard from '../BusinessDashboard';
import { useAuth } from '../../../hooks/useAuth';
import { useToast } from '../../../hooks/useToast';

// Mock the useAuth hook
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: jest.fn()
}));

// Mock the useToast hook
jest.mock('../../../hooks/useToast', () => ({
  useToast: jest.fn()
}));

// Mock the useTranslation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

// Mock the useNavigate hook
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

describe('BusinessDashboard Component', () => {
  const mockShowToast = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the useToast hook
    (useToast as jest.Mock).mockReturnValue({
      showToast: mockShowToast
    });
  });

  test('redirects to business select page if not a business account', () => {
    // Mock the useAuth hook to return a user with no current business
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>'
      },
      currentBusiness: null,
      isBusinessAccount: false,
      hasBusinessRole: jest.fn().mockReturnValue(false)
    });

    render(
      <BrowserRouter>
        <BusinessDashboard />
      </BrowserRouter>
    );

    // Check that navigate was called with the correct path
    expect(mockNavigate).toHaveBeenCalledWith('/business/select');

    // Check that showToast was called with the correct message
    expect(mockShowToast).toHaveBeenCalledWith('business.errors.notBusinessAccount', 'error');
  });

  test('renders the dashboard for a business account', async () => {
    // Mock the useAuth hook to return a user with a current business
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(true)
    });

    render(
      <BrowserRouter>
        <BusinessDashboard />
      </BrowserRouter>
    );

    // Check that the business name is displayed
    expect(screen.getByText('Test Business')).toBeInTheDocument();

    // Check that the welcome message is displayed
    expect(screen.getByText('business.dashboard.welcomeMessage')).toBeInTheDocument();

    // Check that the create listing button is displayed
    expect(screen.getByText('business.dashboard.createListing')).toBeInTheDocument();

    // Check that the manage settings button is displayed
    expect(screen.getByText('business.dashboard.manageSettings')).toBeInTheDocument();

    // Wait for statistics to load
    await waitFor(() => {
      // Check that the dashboard is displayed
      expect(screen.getByText('Test Business')).toBeInTheDocument();
      expect(screen.getByText('business.dashboard.welcomeMessage')).toBeInTheDocument();
      expect(screen.getByText('business.dashboard.createListing')).toBeInTheDocument();
    });

    // Check that the quick links are displayed
    expect(screen.getByText('business.dashboard.quickLinks')).toBeInTheDocument();
    expect(screen.getByText('business.dashboard.viewListings')).toBeInTheDocument();
    expect(screen.getByText('business.dashboard.manageRentals')).toBeInTheDocument();
    expect(screen.getByText('business.dashboard.manageMembers')).toBeInTheDocument();
  });

  test('renders the dashboard with past due subscription status', async () => {
    // Mock the useAuth hook to return a user with a current business with past due subscription
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'past_due',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(true)
    });

    render(
      <BrowserRouter>
        <BusinessDashboard />
      </BrowserRouter>
    );

    // Check that the past due subscription status is displayed
    expect(screen.getByText('business.subscription.pastDue')).toBeInTheDocument();
    expect(screen.getByText('business.subscription.pastDueMessage')).toBeInTheDocument();
    expect(screen.getByText('business.subscription.updatePayment')).toBeInTheDocument();
  });

  test('renders the dashboard with canceled subscription status', async () => {
    // Mock the useAuth hook to return a user with a current business with canceled subscription
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'canceled',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(true)
    });

    render(
      <BrowserRouter>
        <BusinessDashboard />
      </BrowserRouter>
    );

    // Check that the canceled subscription status is displayed
    expect(screen.getByText('business.subscription.canceled')).toBeInTheDocument();
    expect(screen.getByText('business.subscription.canceledMessage')).toBeInTheDocument();
    expect(screen.getByText('business.subscription.reactivate')).toBeInTheDocument();
  });

  test('renders the dashboard for a user without manage settings permission', async () => {
    // Mock the useAuth hook to return a user with a current business but without manage settings permission
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockImplementation((roles) => {
        // Return false for owner and admin roles, true for others
        return !roles.includes('owner') && !roles.includes('admin');
      })
    });

    render(
      <BrowserRouter>
        <BusinessDashboard />
      </BrowserRouter>
    );

    // Check that the manage settings button is not displayed
    expect(screen.queryByText('business.dashboard.manageSettings')).not.toBeInTheDocument();

    // Check that the view analytics link is displayed instead of manage members
    expect(screen.getByText('business.dashboard.viewAnalytics')).toBeInTheDocument();
    expect(screen.queryByText('business.dashboard.manageMembers')).not.toBeInTheDocument();
  });
});
