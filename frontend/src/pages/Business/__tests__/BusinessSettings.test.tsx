import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import BusinessSettings from '../BusinessSettings';
import { useAuth } from '../../../hooks/useAuth';
import { useToast } from '../../../hooks/useToast';

// Mock the useAuth hook
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: jest.fn()
}));

// Mock the useToast hook
jest.mock('../../../hooks/useToast', () => ({
  useToast: jest.fn()
}));

// Mock the useTranslation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

// Mock the useNavigate hook
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

// Mock the businessAccountService
jest.mock('../../../services/businessAccountService', () => ({
  getBusinessAccount: jest.fn(),
  updateBusinessAccount: jest.fn()
}));

describe('BusinessSettings Component', () => {
  const mockShowToast = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the useToast hook
    (useToast as jest.Mock).mockReturnValue({
      showToast: mockShowToast
    });
  });

  test('redirects to business select page if not a business account', () => {
    // Mock the useAuth hook to return a user with no current business
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>'
      },
      currentBusiness: null,
      isBusinessAccount: false,
      hasBusinessRole: jest.fn().mockReturnValue(false)
    });

    render(
      <BrowserRouter>
        <BusinessSettings />
      </BrowserRouter>
    );

    // Check that navigate was called with the correct path
    expect(mockNavigate).toHaveBeenCalledWith('/business/select');

    // Check that showToast was called with the correct message
    expect(mockShowToast).toHaveBeenCalledWith('business.errors.notBusinessAccount', 'error');
  });

  test('redirects to dashboard if user does not have permission to manage settings', () => {
    // Mock the useAuth hook to return a user with a current business but without permission
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(false)
    });

    render(
      <BrowserRouter>
        <BusinessSettings />
      </BrowserRouter>
    );

    // Check that navigate was called with the correct path
    expect(mockNavigate).toHaveBeenCalledWith('/business/dashboard');

    // Check that showToast was called with the correct message
    expect(mockShowToast).toHaveBeenCalledWith('business.errors.noPermission', 'error');
  });

  test('renders the settings page for a business account with permission', async () => {
    // Mock the useAuth hook to return a user with a current business and permission
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        description: 'Test Description',
        industry: 'other',
        size: 'small',
        website: 'https://example.com',
        email: '<EMAIL>',
        phone_number: '+**********',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(true)
    });

    render(
      <BrowserRouter>
        <BusinessSettings />
      </BrowserRouter>
    );

    // Check that the business name is displayed
    expect(screen.getByText('business.settings.title')).toBeInTheDocument();

    // Check that the settings page is displayed
    await waitFor(() => {
      expect(screen.getByText('business.settings.title')).toBeInTheDocument();
      expect(screen.getByText('business.settings.tabs.profile')).toBeInTheDocument();
    });

    // Check that the tabs are displayed
    expect(screen.getByText('business.settings.tabs.profile')).toBeInTheDocument();
    expect(screen.getByText('business.settings.tabs.branding')).toBeInTheDocument();
    expect(screen.getByText('business.settings.tabs.notifications')).toBeInTheDocument();
    expect(screen.getByText('business.settings.tabs.security')).toBeInTheDocument();
  });

  test('validates form fields correctly', async () => {
    // Mock the useAuth hook to return a user with a current business and permission
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(true)
    });

    render(
      <BrowserRouter>
        <BusinessSettings />
      </BrowserRouter>
    );

    // Wait for the form to load
    await waitFor(() => {
      expect(screen.getByLabelText(/business.settings.businessName/i)).toBeInTheDocument();
    });

    // Clear the name field
    const nameInput = screen.getByLabelText(/business.settings.businessName/i) as HTMLInputElement;
    fireEvent.change(nameInput, { target: { value: '' } });

    // Submit the form
    const submitButton = screen.getByText('business.settings.saveChanges');
    fireEvent.click(submitButton);

    // Check that validation error is displayed
    await waitFor(() => {
      expect(screen.getByText('business.settings.errors.nameRequired')).toBeInTheDocument();
    });

    // Enter an invalid website
    const websiteInput = screen.getByLabelText(/business.settings.website/i) as HTMLInputElement;
    fireEvent.change(websiteInput, { target: { value: 'invalid-website' } });

    // Submit the form again
    fireEvent.click(submitButton);

    // Check that validation error is displayed
    await waitFor(() => {
      expect(screen.getByText('business.settings.errors.invalidWebsite')).toBeInTheDocument();
    });
  });
});
