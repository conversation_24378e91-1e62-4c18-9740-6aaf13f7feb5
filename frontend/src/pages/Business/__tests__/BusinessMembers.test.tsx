import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import BusinessMembers from '../BusinessMembers';
import { useAuth } from '../../../hooks/useAuth';
import { useToast } from '../../../hooks/useToast';
import { BusinessRole } from '../../../types/BusinessAccount';

// Mock the useAuth hook
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: jest.fn()
}));

// Mock the useToast hook
jest.mock('../../../hooks/useToast', () => ({
  useToast: jest.fn()
}));

// Mock the useTranslation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

// Mock the useNavigate hook
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

describe('BusinessMembers Component', () => {
  const mockShowToast = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the useToast hook
    (useToast as jest.Mock).mockReturnValue({
      showToast: mockShowToast
    });
  });

  test('redirects to business select page if not a business account', () => {
    // Mock the useAuth hook to return a user with no current business
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>'
      },
      currentBusiness: null,
      isBusinessAccount: false,
      hasBusinessRole: jest.fn().mockReturnValue(false)
    });

    render(
      <BrowserRouter>
        <BusinessMembers />
      </BrowserRouter>
    );

    // Check that navigate was called with the correct path
    expect(mockNavigate).toHaveBeenCalledWith('/business/select');

    // Check that showToast was called with the correct message
    expect(mockShowToast).toHaveBeenCalledWith('business.errors.notBusinessAccount', 'error');
  });

  test('redirects to dashboard if user does not have permission to manage members', () => {
    // Mock the useAuth hook to return a user with a current business but without permission
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(false)
    });

    render(
      <BrowserRouter>
        <BusinessMembers />
      </BrowserRouter>
    );

    // Check that navigate was called with the correct path
    expect(mockNavigate).toHaveBeenCalledWith('/business/dashboard');

    // Check that showToast was called with the correct message
    expect(mockShowToast).toHaveBeenCalledWith('business.errors.noPermission', 'error');
  });

  test('renders the members page for a business account with permission', async () => {
    // Mock the useAuth hook to return a user with a current business and permission
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(true)
    });

    render(
      <BrowserRouter>
        <BusinessMembers />
      </BrowserRouter>
    );

    // Check that the page title is displayed
    expect(screen.getByText('business.members.title')).toBeInTheDocument();

    // Check that the invite member button is displayed
    expect(screen.getByText('business.members.inviteMember')).toBeInTheDocument();

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('business.members.title')).toBeInTheDocument();
    });
  });

  test('shows invite form when invite button is clicked', async () => {
    // Mock the useAuth hook to return a user with a current business and permission
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(true)
    });

    render(
      <BrowserRouter>
        <BusinessMembers />
      </BrowserRouter>
    );

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('business.members.inviteMember')).toBeInTheDocument();
    });

    // Click the invite button
    fireEvent.click(screen.getByText('business.members.inviteMember'));

    // Check that the invite form is displayed
    expect(screen.getByLabelText(/business.members.email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/business.members.role/i)).toBeInTheDocument();
    expect(screen.getByText('business.members.sendInvite')).toBeInTheDocument();
  });

  test('validates invite form correctly', async () => {
    // Mock the useAuth hook to return a user with a current business and permission
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(true)
    });

    render(
      <BrowserRouter>
        <BusinessMembers />
      </BrowserRouter>
    );

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('business.members.inviteMember')).toBeInTheDocument();
    });

    // Click the invite button
    fireEvent.click(screen.getByText('business.members.inviteMember'));

    // Submit the form without filling it
    fireEvent.click(screen.getByText('business.members.sendInvite'));

    // Check that validation error is displayed
    await waitFor(() => {
      expect(screen.getByText('business.members.errors.emailRequired')).toBeInTheDocument();
    });

    // Enter an invalid email
    const emailInput = screen.getByLabelText(/business.members.email/i) as HTMLInputElement;
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } });

    // Submit the form again
    fireEvent.click(screen.getByText('business.members.sendInvite'));

    // Check that validation error is displayed
    await waitFor(() => {
      expect(screen.getByText('business.members.errors.invalidEmail')).toBeInTheDocument();
    });
  });

  test('shows permission checkboxes when member role is selected', async () => {
    // Mock the useAuth hook to return a user with a current business and permission
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1'
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 5,
        listing_count: 10,
        rental_count: 20,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      hasBusinessRole: jest.fn().mockReturnValue(true)
    });

    render(
      <BrowserRouter>
        <BusinessMembers />
      </BrowserRouter>
    );

    // Wait for the page to load
    await waitFor(() => {
      expect(screen.getByText('business.members.inviteMember')).toBeInTheDocument();
    });

    // Click the invite button
    fireEvent.click(screen.getByText('business.members.inviteMember'));

    // Select the member role
    const roleSelect = screen.getByLabelText(/business.members.role/i) as HTMLSelectElement;
    fireEvent.change(roleSelect, { target: { value: BusinessRole.MEMBER } });

    // Check that permission checkboxes are displayed
    expect(screen.getByLabelText(/business.members.permissionLabels.manage_listings/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/business.members.permissionLabels.manage_rentals/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/business.members.permissionLabels.view_analytics/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/business.members.permissionLabels.approve_transactions/i)).toBeInTheDocument();
  });
});
