import React, { useState } from 'react';
import { Helmet } from 'react-helmet';
import PerformanceDashboard from '../components/Performance/PerformanceDashboard';
import Layout from '../components/Layout/Layout';

/**
 * Performance Dashboard Page
 * 
 * This page displays performance metrics and analytics for the application.
 * It includes real-time performance monitoring, historical data, and optimization suggestions.
 */
const PerformanceDashboardPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'realtime' | 'historical' | 'optimization'>('realtime');
  
  return (
    <Layout>
      <Helmet>
        <title>Performance Dashboard | RentUP</title>
        <meta name="description" content="Monitor and analyze application performance metrics" />
      </Helmet>
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Performance Dashboard</h1>
            <p className="text-gray-600 mt-2">
              Monitor and analyze application performance metrics
            </p>
          </div>
          
          <div className="mt-4 md:mt-0">
            <button
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              onClick={() => window.location.reload()}
            >
              Reset Metrics
            </button>
          </div>
        </div>
        
        {/* Tabs */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'realtime'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('realtime')}
            >
              Real-time Metrics
            </button>
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'historical'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('historical')}
            >
              Historical Data
            </button>
            <button
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'optimization'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab('optimization')}
            >
              Optimization Suggestions
            </button>
          </nav>
        </div>
        
        {/* Tab Content */}
        {activeTab === 'realtime' && (
          <div>
            <PerformanceDashboard />
          </div>
        )}
        
        {activeTab === 'historical' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Historical Performance Data</h2>
            <p className="text-gray-600 mb-4">
              View performance trends over time to identify patterns and improvements.
            </p>
            
            <div className="p-8 bg-gray-100 rounded-lg text-center">
              <p className="text-gray-500">
                Historical data visualization will be available in a future update.
              </p>
              <p className="text-gray-500 mt-2">
                This feature requires server-side storage of performance metrics.
              </p>
            </div>
            
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-2">Performance Trends</h3>
              <p className="text-gray-600">
                Historical performance data will show trends in:
              </p>
              <ul className="list-disc list-inside mt-2 text-gray-600">
                <li>Page load times</li>
                <li>API response times</li>
                <li>Component render performance</li>
                <li>Resource loading efficiency</li>
                <li>User interaction responsiveness</li>
              </ul>
            </div>
          </div>
        )}
        
        {activeTab === 'optimization' && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Optimization Suggestions</h2>
            <p className="text-gray-600 mb-4">
              Recommendations to improve application performance based on collected metrics.
            </p>
            
            <div className="space-y-4">
              <div className="p-4 border border-green-200 bg-green-50 rounded-lg">
                <h3 className="text-lg font-medium text-green-800">Image Optimization</h3>
                <p className="text-green-700 mt-1">
                  Images are being properly optimized with WebP/AVIF formats and responsive loading.
                </p>
              </div>
              
              <div className="p-4 border border-green-200 bg-green-50 rounded-lg">
                <h3 className="text-lg font-medium text-green-800">Code Splitting</h3>
                <p className="text-green-700 mt-1">
                  Effective code splitting is implemented, reducing initial bundle size.
                </p>
              </div>
              
              <div className="p-4 border border-green-200 bg-green-50 rounded-lg">
                <h3 className="text-lg font-medium text-green-800">List Virtualization</h3>
                <p className="text-green-700 mt-1">
                  Long lists are properly virtualized, improving rendering performance.
                </p>
              </div>
              
              <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
                <h3 className="text-lg font-medium text-yellow-800">API Caching</h3>
                <p className="text-yellow-700 mt-1">
                  Consider implementing more aggressive API response caching for frequently accessed data.
                </p>
              </div>
              
              <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
                <h3 className="text-lg font-medium text-yellow-800">Font Loading</h3>
                <p className="text-yellow-700 mt-1">
                  Consider using font-display: swap and preloading critical fonts to improve perceived performance.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default PerformanceDashboardPage;
