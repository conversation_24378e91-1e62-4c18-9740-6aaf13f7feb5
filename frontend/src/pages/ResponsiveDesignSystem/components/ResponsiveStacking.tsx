import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '../../../components/ui';

/**
 * Responsive Stacking Component
 * Showcases responsive stacking patterns for different screen sizes
 */
const ResponsiveStacking: React.FC = () => {
  return (
    <section className="mb-12">
      <h2 className="heading-display mb-4">Responsive Stacking</h2>
      <Card responsivePadding>
        <Card.Content>
          <div className="stack-responsive mb-6 border border-gray-200 rounded-lg p-responsive">
            <div className="bg-gray-100 p-responsive rounded-lg">
              <h3 className="font-semibold">First Item</h3>
              <p>This item will stack vertically on mobile and horizontally on larger screens.</p>
            </div>
            <div className="bg-gray-100 p-responsive rounded-lg">
              <h3 className="font-semibold">Second Item</h3>
              <p>This item will stack vertically on mobile and horizontally on larger screens.</p>
            </div>
          </div>

          <p className="text-caption mb-6">
            The <code>stack-responsive</code> class creates a layout that stacks elements vertically on mobile
            and horizontally on larger screens. The <code>p-responsive</code> class applies progressive
            spacing reduction on smaller screens.
          </p>

          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-responsive border border-gray-200 rounded-lg p-responsive">
            <div>
              <h3 className="font-semibold">Responsive Actions</h3>
              <p>The buttons below stack vertically on mobile and horizontally on larger screens.</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3">
              <Button variant="tertiary" className="touch-friendly">Cancel</Button>
              <Button className="touch-friendly">Submit</Button>
            </div>
          </div>
        </Card.Content>
      </Card>
    </section>
  );
};

export default ResponsiveStacking;
