import React from 'react';
import { Card } from '../../../components/ui';

/**
 * Design Guidelines Section Component
 * Showcases responsive design guidelines and best practices
 */
const DesignGuidelinesSection: React.FC = () => {
  return (
    <>
      <section className="mb-12">
        <h2 className="heading-display mb-4">Responsive Design Guidelines</h2>
        <Card responsivePadding>
          <Card.Content>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Breakpoints</h3>
                <p className="mb-2">Our responsive design system uses the following breakpoints:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li><code>xs</code>: 375px (Extra small devices, phones)</li>
                  <li><code>sm</code>: 640px (Small devices, large phones)</li>
                  <li><code>md</code>: 768px (Medium devices, tablets)</li>
                  <li><code>lg</code>: 1024px (Large devices, desktops)</li>
                  <li><code>xl</code>: 1280px (Extra large devices, large desktops)</li>
                  <li><code>2xl</code>: 1536px (Extra extra large devices, very large desktops)</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Mobile-First Approach</h3>
                <p>
                  We follow a mobile-first approach, designing for the smallest screen size first
                  and then progressively enhancing the experience for larger screens. This ensures
                  that our core functionality works well on all devices.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Fluid Typography</h3>
                <p>
                  Our typography system uses fluid sizing with CSS <code>clamp()</code> function
                  to ensure text scales smoothly between breakpoints. This provides better readability
                  across all device sizes without requiring multiple breakpoint-specific font sizes.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Progressive Enhancement</h3>
                <p>
                  We use progressive enhancement to provide a basic experience for all users and
                  then enhance it for users with more capable devices or browsers. This includes:
                </p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Using modern CSS features with appropriate fallbacks</li>
                  <li>Enhancing interactions on devices with hover capability</li>
                  <li>Providing touch-optimized interfaces for touch devices</li>
                  <li>Using advanced features when available (e.g., CSS Grid with flexbox fallback)</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Accessibility Considerations</h3>
                <p>
                  Our responsive design system is built with accessibility in mind, following
                  WCAG 2.1 AA standards. Key considerations include:
                </p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Minimum touch target size of 44×44px for interactive elements</li>
                  <li>Sufficient color contrast (4.5:1 for normal text, 3:1 for large text)</li>
                  <li>Keyboard navigation support</li>
                  <li>Screen reader compatibility</li>
                  <li>Focus indicators for interactive elements</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Performance Optimization</h3>
                <p>
                  Our responsive design system is optimized for performance across all devices:
                </p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Responsive images with appropriate sizing and formats</li>
                  <li>Minimal CSS with efficient selectors</li>
                  <li>Optimized JavaScript with code splitting</li>
                  <li>Lazy loading for off-screen content</li>
                  <li>Server-side rendering for faster initial load</li>
                </ul>
              </div>
            </div>
          </Card.Content>
        </Card>
      </section>
    </>
  );
};

export default DesignGuidelinesSection;
