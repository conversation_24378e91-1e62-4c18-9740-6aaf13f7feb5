import React from 'react';
import { But<PERSON>, Card, Input, Select } from '../../../components/ui';

interface TouchFriendlyControlsProps {
  selectOptions: Array<{
    value: string;
    label: string;
    disabled?: boolean;
  }>;
}

/**
 * Touch-Friendly Controls Component
 * Showcases controls optimized for touch devices with proper sizing
 */
const TouchFriendlyControls: React.FC<TouchFriendlyControlsProps> = ({ selectOptions }) => {
  return (
    <section className="mb-12">
      <h2 className="heading-display mb-4">Touch-Friendly Controls</h2>
      <Card responsivePadding>
        <Card.Content>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">Buttons with Appropriate Touch Targets</h3>
              <div className="flex flex-wrap gap-3">
                <Button className="touch-friendly">Primary Button</Button>
                <Button variant="secondary" className="touch-friendly">Secondary Button</Button>
                <Button variant="tertiary" className="touch-friendly">Tertiary Button</Button>
              </div>
              <p className="text-caption mt-2">
                These buttons use the <code>touch-friendly</code> class to ensure they have a minimum touch target
                size of 44×44px on mobile devices, as required by WCAG 2.1.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Form Controls with Proper Spacing</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Touch-friendly input"
                  placeholder="Enter text here"
                  className="touch-friendly-input"
                />
                <Select
                  label="Touch-friendly select"
                  options={selectOptions}
                  placeholder="Select an option"
                  className="touch-friendly-input"
                />
              </div>
              <p className="text-caption mt-2">
                These form controls use the <code>touch-friendly-input</code> class to ensure proper sizing
                and prevent iOS zoom on focus by using a minimum 16px font size.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Touch Target Spacing</h3>
              <div className="flex flex-wrap gap-2">
                {[1, 2, 3, 4, 5].map((item) => (
                  <button
                    key={item}
                    className="touch-friendly bg-primary-100 text-primary-800 rounded-md"
                  >
                    Item {item}
                  </button>
                ))}
              </div>
              <p className="text-caption mt-2">
                Touch targets maintain a minimum spacing of 8px between them to prevent accidental taps.
              </p>
            </div>
          </div>
        </Card.Content>
      </Card>
    </section>
  );
};

export default TouchFriendlyControls;
