import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from '../../../components/ui';

/**
 * Responsive Card Components
 * Showcases responsive card components with different layouts and features
 */
const ResponsiveCardComponents: React.FC = () => {
  return (
    <section className="mb-12">
      <h2 className="heading-display mb-4">Responsive Card Components</h2>
      <div className="space-y-6">
        <Card responsivePadding stackOnMobile>
          <Card.Header
            title="Responsive Card"
            subtitle="This card has responsive padding and stacks content on mobile"
            action={<Button size="sm" className="touch-friendly">Action</Button>}
          />
          <Card.Content>
            <p>
              This card uses the <code>responsivePadding</code> prop to adjust padding based on screen size
              with progressive reduction (35% less on mobile, 20% less on tablet).
              It also uses the <code>stackOnMobile</code> prop to stack content vertically on mobile devices.
            </p>
          </Card.Content>
          <Card.Footer>
            <Button variant="tertiary" className="touch-friendly">Cancel</Button>
            <Button className="touch-friendly">Submit</Button>
          </Card.Footer>
        </Card>

        <Card variant="elevated" responsivePadding>
          <Card.Media
            src="https://via.placeholder.com/800x400"
            alt="Placeholder image"
            responsiveAspectRatio
            aspectRatio="16-9"
          />
          <Card.Header title="Card with Responsive Media" />
          <Card.Content>
            <p>
              This card includes a media element with <code>responsiveAspectRatio</code> prop to maintain
              a consistent aspect ratio across all screen sizes. The image is responsive with
              <code>max-width: 100%; height: auto;</code> to prevent horizontal overflow.
            </p>
          </Card.Content>
        </Card>
      </div>
    </section>
  );
};

export default ResponsiveCardComponents;
