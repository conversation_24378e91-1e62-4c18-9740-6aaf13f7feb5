import React from 'react';
import { Card } from '../../../components/ui';

/**
 * Responsive Typography Component
 * Showcases the responsive typography system with fluid sizing
 */
const ResponsiveTypography: React.FC = () => {
  return (
    <section className="mb-12">
      <h2 className="heading-display mb-4">Responsive Typography</h2>
      <Card responsivePadding>
        <Card.Content>
          <div className="space-y-6">
            <div>
              <h1>Heading 1</h1>
              <p className="text-caption mt-1">Uses fluid typography: <code>--fluid-h1: clamp(2rem, 5vw, 3rem)</code></p>
            </div>
            <div>
              <h2>Heading 2</h2>
              <p className="text-caption mt-1">Uses fluid typography: <code>--fluid-h2: clamp(1.5rem, 4vw, 2.25rem)</code></p>
            </div>
            <div>
              <h3>Heading 3</h3>
              <p className="text-caption mt-1">Uses fluid typography: <code>--fluid-h3: clamp(1.25rem, 3vw, 1.875rem)</code></p>
            </div>
            <div>
              <p className="lead">
                This is a lead paragraph with responsive text that adjusts based on screen size.
                The font size scales fluidly between breakpoints using clamp() functions.
              </p>
              <p className="text-caption mt-1">Uses fluid typography: <code>--fluid-lead: clamp(1.125rem, 1.5vw, 1.375rem)</code></p>
            </div>
            <div>
              <p>
                This is a regular paragraph with responsive text. The minimum font size is 16px on all devices,
                ensuring readability without requiring users to zoom in. Line height also scales fluidly
                for optimal readability on different screen sizes.
              </p>
              <p className="text-caption mt-1">Uses fluid typography: <code>--fluid-body: clamp(1rem, 1.25vw, 1.125rem)</code></p>
            </div>
            <div>
              <p className="text-small">
                This is small text that remains readable on all devices. It scales proportionally
                with the rest of the typography system.
              </p>
              <p className="text-caption mt-1">Uses fluid typography: <code>--fluid-small: clamp(0.875rem, 1vw, 1rem)</code></p>
            </div>
          </div>
        </Card.Content>
      </Card>
    </section>
  );
};

export default ResponsiveTypography;
