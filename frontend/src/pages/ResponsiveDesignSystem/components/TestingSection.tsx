import React from 'react';
import { Card } from '../../../components/ui';

/**
 * Testing Section Component
 * Showcases responsive design testing approaches and tools
 */
const TestingSection: React.FC = () => {
  return (
    <>
      <section className="mb-12">
        <h2 className="heading-display mb-4">Responsive Design Testing</h2>
        <Card responsivePadding>
          <Card.Content>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">Testing Approaches</h3>
                <p className="mb-2">Our responsive design testing strategy includes:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li><strong>Device Testing:</strong> Testing on actual physical devices</li>
                  <li><strong>Emulator Testing:</strong> Using browser dev tools to emulate different devices</li>
                  <li><strong>Responsive Mode:</strong> Resizing the browser window to test fluid layouts</li>
                  <li><strong>Automated Testing:</strong> Using Playwright and Cypress for automated responsive tests</li>
                  <li><strong>Visual Regression Testing:</strong> Comparing screenshots across different viewport sizes</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Testing Tools</h3>
                <p className="mb-2">We use the following tools for responsive design testing:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li><strong>Browser Dev Tools:</strong> Chrome, Firefox, and Safari device emulation</li>
                  <li><strong>Playwright:</strong> For automated cross-browser testing</li>
                  <li><strong>Cypress:</strong> For end-to-end testing with viewport configuration</li>
                  <li><strong>Storybook:</strong> For component-level responsive testing</li>
                  <li><strong>Percy:</strong> For visual regression testing across viewports</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Testing Checklist</h3>
                <p className="mb-2">Our responsive design testing checklist includes:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Verify layout at all breakpoints (xs, sm, md, lg, xl, 2xl)</li>
                  <li>Test touch interactions on touch devices</li>
                  <li>Verify hover states on non-touch devices</li>
                  <li>Test keyboard navigation</li>
                  <li>Verify font sizes and readability</li>
                  <li>Test with screen readers</li>
                  <li>Verify performance metrics (Core Web Vitals)</li>
                  <li>Test in both portrait and landscape orientations</li>
                  <li>Verify form functionality and validation</li>
                  <li>Test with different network conditions</li>
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Automated Testing Example</h3>
                <pre className="bg-gray-100 p-3 rounded-md overflow-x-auto text-sm">
                  <code>{`
// Playwright test example for responsive testing
test.describe('Responsive design tests', () => {
  // Test on mobile viewport
  test('mobile viewport', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/');
    // Verify mobile layout
    await expect(page.locator('.mobile-menu-button')).toBeVisible();
  });

  // Test on tablet viewport
  test('tablet viewport', async ({ page }) => {
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.goto('/');
    // Verify tablet layout
    await expect(page.locator('.tablet-layout')).toBeVisible();
  });

  // Test on desktop viewport
  test('desktop viewport', async ({ page }) => {
    await page.setViewportSize({ width: 1280, height: 800 });
    await page.goto('/');
    // Verify desktop layout
    await expect(page.locator('.desktop-layout')).toBeVisible();
  });
});
                  `}</code>
                </pre>
              </div>
            </div>
          </Card.Content>
        </Card>
      </section>
    </>
  );
};

export default TestingSection;
