import React from 'react';
import { FaHeart, FaStar } from 'react-icons/fa';
import { ItemCard } from '../../../components/AI/ResponsiveAIFeatures';

/**
 * Enhanced Card Components
 * Showcases enhanced card components with hover effects, badges, and overlays
 */
const EnhancedCardComponents: React.FC = () => {
  return (
    <section className="mb-12">
      <h2 className="heading-display mb-4">Enhanced Card Components</h2>
      <p className="mb-6">
        These enhanced card components are based on design references from Airbnb, Fat Llama, and Zillow.
        They include hover effects, reliability badges, and overlay actions.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div>
          <h3 className="text-lg font-semibold mb-3">Property Card</h3>
          <div className="property-card">
            <div className="property-card-media">
              <img src="https://source.unsplash.com/random/600x400/?apartment" alt="Property" />
              <div className="property-card-badge">Featured</div>
              <button className="property-card-favorite">
                <FaHeart />
              </button>
            </div>
            <div className="property-card-content">
              <h3 className="property-card-title">Modern Apartment</h3>
              <div className="property-card-location">New York, NY</div>
              <div className="property-card-price">$150/day</div>
              <div className="property-card-rating">
                <FaStar className="text-warning-500" />
                <span>4.8</span>
                <span>(24 reviews)</span>
              </div>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3">Category Card</h3>
          <div className="category-card">
            <img
              src="https://source.unsplash.com/random/600x600/?electronics"
              alt="Electronics"
              className="category-card-image"
            />
            <div className="category-card-overlay">
              <h3 className="category-card-title">Electronics</h3>
              <div className="category-card-count">245 items</div>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-lg font-semibold mb-3">Item Card</h3>
          <ItemCard
            id="demo-item"
            title="Professional Camera"
            category="Electronics"
            price={35}
            priceUnit="day"
            imageUrl="https://source.unsplash.com/random/600x450/?camera"
            secondaryImageUrl="https://source.unsplash.com/random/600x450/?camera-lens"
            reliability={{
              score: 95,
              level: 'excellent'
            }}
            owner={{
              name: "John Doe",
              avatarUrl: "https://i.pravatar.cc/150?img=3",
              rating: 4.9,
              reviewCount: 42
            }}
          />
        </div>
      </div>
      <div className="bg-gray-100 p-responsive rounded-lg">
        <p className="text-sm text-gray-600">
          <strong>Responsive Behavior:</strong>
        </p>
        <ul className="text-sm text-gray-600 mt-2 space-y-1">
          <li>• Cards maintain proper spacing and sizing across all screen sizes</li>
          <li>• Hover effects are applied only on desktop devices (1024px+)</li>
          <li>• Touch-friendly controls for mobile devices</li>
          <li>• Proper text truncation for long titles and descriptions</li>
        </ul>
      </div>
    </section>
  );
};

export default EnhancedCardComponents;
