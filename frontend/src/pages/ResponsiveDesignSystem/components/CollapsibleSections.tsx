import React from 'react';

/**
 * Collapsible Sections Component
 * Showcases collapsible sections that adapt to different screen sizes
 */
const CollapsibleSections: React.FC = () => {
  return (
    <section className="mb-12">
      <h2 className="heading-display mb-4">Collapsible Sections</h2>
      <p className="mb-6">
        Collapsible sections are used to save space on mobile devices. They automatically
        expand on desktop screens.
      </p>

      <div className="space-y-4">
        {['First', 'Second', 'Third'].map((title, index) => (
          <div key={index} className={`collapsible-section ${index === 0 ? 'expanded' : ''}`}>
            <div className="collapsible-section-header">
              <h3>{title} Section</h3>
              <div className="collapsible-section-toggle">
                {index === 0 ? '−' : '+'}
              </div>
            </div>
            <div className="collapsible-section-content">
              <p>
                This section collapses on mobile devices to save space. On tablet devices,
                it uses tabs for navigation. On desktop devices, all sections are visible at once.
              </p>
              <p className="mt-2">
                This pattern is especially useful for complex interfaces like analytics dashboards,
                settings pages, and detailed product information.
              </p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default CollapsibleSections;
