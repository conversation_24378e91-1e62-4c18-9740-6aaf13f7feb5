import React from 'react';
import { Card } from '../../../components/ui';

/**
 * Responsive Grid Layouts Component
 * Showcases responsive grid layouts that adapt to different screen sizes
 */
const ResponsiveGridLayouts: React.FC = () => {
  return (
    <section className="mb-12">
      <h2 className="heading-display mb-4">Responsive Grid Layouts</h2>
      <div className="grid-responsive-cards mb-6">
        {[1, 2, 3, 4].map((item) => (
          <Card key={item} responsivePadding>
            <Card.Content>
              <h3 className="text-lg font-semibold mb-2">Card {item}</h3>
              <p>This card is part of a responsive grid that adjusts columns based on screen size.</p>
              <ul className="text-sm text-gray-600 mt-4">
                <li><strong>Mobile (320px-639px):</strong> Single column, full width</li>
                <li><strong>Tablet (640px-1023px):</strong> Two-column grid</li>
                <li><strong>Desktop (1024px+):</strong> Three-column grid</li>
              </ul>
            </Card.Content>
          </Card>
        ))}
      </div>
      <div className="bg-gray-100 p-responsive rounded-lg">
        <p className="text-sm text-gray-600">
          The <code>grid-responsive-cards</code> class creates a responsive grid layout that automatically
          adjusts the number of columns based on screen size, with progressive spacing reduction on smaller screens.
        </p>
      </div>
    </section>
  );
};

export default ResponsiveGridLayouts;
