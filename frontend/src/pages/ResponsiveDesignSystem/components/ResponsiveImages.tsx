import React from 'react';
import { Card } from '../../../components/ui';

/**
 * Responsive Images Component
 * Showcases responsive image techniques and patterns
 */
const ResponsiveImages: React.FC = () => {
  return (
    <section className="mb-12">
      <h2 className="heading-display mb-4">Responsive Images</h2>
      <Card responsivePadding>
        <Card.Content>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-3">Basic Responsive Image</h3>
              <img
                src="https://via.placeholder.com/1200x600"
                alt="Placeholder"
                className="img-responsive rounded-lg"
              />
              <p className="text-caption mt-2">
                Uses <code>max-width: 100%; height: auto;</code> to ensure the image scales down
                on smaller screens while maintaining its aspect ratio.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Responsive Aspect Ratio</h3>
              <div className="aspect-ratio-container aspect-ratio-16-9">
                <img
                  src="https://via.placeholder.com/1200x675"
                  alt="16:9 Aspect Ratio"
                  className="aspect-ratio-content"
                />
              </div>
              <p className="text-caption mt-2">
                Maintains a consistent 16:9 aspect ratio across all screen sizes using the
                <code>aspect-ratio-container</code> and <code>aspect-ratio-16-9</code> classes.
              </p>
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-3">Responsive Image Gallery</h3>
              <div className="image-gallery-responsive">
                {[1, 2, 3, 4, 5, 6].map((item) => (
                  <div key={item} className="aspect-ratio-container aspect-ratio-1-1">
                    <img
                      src={`https://via.placeholder.com/400x400?text=Image+${item}`}
                      alt={`Gallery Image ${item}`}
                      className="aspect-ratio-content"
                    />
                  </div>
                ))}
              </div>
              <p className="text-caption mt-2">
                A responsive image gallery that displays 1 column on mobile, 2 columns on tablet,
                and 3 columns on desktop.
              </p>
            </div>
          </div>
        </Card.Content>
      </Card>
    </section>
  );
};

export default ResponsiveImages;
