import React from 'react';
import ResponsiveRecommendationSection from '../../../components/Recommendations/ResponsiveRecommendationSection';
import ResponsiveAICurationInterface from '../../../components/AI/ResponsiveAICurationInterface';
import ResponsiveAnalyticsDashboard from '../../../components/Analytics/ResponsiveAnalyticsDashboard';
import {
  ResponsiveRecommendationSection as NewResponsiveRecommendationSection,
  ResponsiveAICurationInterface as NewResponsiveAICurationInterface,
  ResponsiveAnalyticsDashboard as NewResponsiveAnalyticsDashboard,
} from '../../../components/AI/ResponsiveAIFeatures';

/**
 * AI Features Section Component
 * Showcases AI-powered features with responsive design
 */
const AIFeaturesSection: React.FC = () => {
  return (
    <>
      {/* AI Recommendation Section */}
      <section className="mb-12">
        <h2 className="heading-display mb-4">AI Recommendation Section</h2>
        <p className="mb-6">
          AI recommendation components follow specific responsive patterns to ensure optimal
          display across all device sizes.
        </p>
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-3">Original Implementation</h3>
          <ResponsiveRecommendationSection
            title="Recommended for You"
            type="personalized"
            limit={4}
            collapsible={true}
            initiallyExpanded={true}
          />
        </div>

        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-3">Enhanced Implementation</h3>
          <NewResponsiveRecommendationSection
            title="Enhanced Recommendations"
            type="personalized"
            limit={3}
            collapsible={true}
            initiallyExpanded={true}
          />
        </div>

        <div className="mt-4 bg-gray-100 p-responsive rounded-lg">
          <p className="text-sm text-gray-600">
            <strong>Responsive Pattern:</strong> Recommendations display as a scrollable horizontal list on mobile,
            a 2-column grid on tablet, and a 3-column grid on desktop. The component also adapts its header
            and controls based on available space.
          </p>
        </div>
      </section>

      {/* AI Curation Interface */}
      <section className="mb-12">
        <h2 className="heading-display mb-4">AI Curation Interface</h2>
        <p className="mb-6">
          The AI curation interface allows administrators to manage content with AI assistance.
          It adapts to different screen sizes for optimal usability.
        </p>
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-3">Original Implementation</h3>
          <ResponsiveAICurationInterface />
        </div>

        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-3">Enhanced Implementation</h3>
          <NewResponsiveAICurationInterface />
        </div>

        <div className="mt-4 bg-gray-100 p-responsive rounded-lg">
          <p className="text-sm text-gray-600">
            <strong>Responsive Pattern:</strong> The curation interface uses a single-column layout on mobile,
            a two-column layout on tablet, and a three-column layout on desktop. Controls and filters
            adapt to available space, with a collapsible sidebar on mobile.
          </p>
        </div>
      </section>

      {/* Analytics Dashboard */}
      <section className="mb-12">
        <h2 className="heading-display mb-4">Analytics Dashboard</h2>
        <p className="mb-6">
          The analytics dashboard provides insights into platform performance and user behavior.
          It adapts to different screen sizes for optimal data visualization.
        </p>
        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-3">Original Implementation</h3>
          <ResponsiveAnalyticsDashboard />
        </div>

        <div className="mb-8">
          <h3 className="text-lg font-semibold mb-3">Enhanced Implementation</h3>
          <NewResponsiveAnalyticsDashboard />
        </div>

        <div className="mt-4 bg-gray-100 p-responsive rounded-lg">
          <p className="text-sm text-gray-600">
            <strong>Responsive Pattern:</strong> The dashboard uses a stacked layout on mobile,
            a two-column layout on tablet, and a three-column layout on desktop. Charts and graphs
            resize based on available space, with simplified visualizations on smaller screens.
          </p>
        </div>
      </section>
    </>
  );
};

export default AIFeaturesSection;
