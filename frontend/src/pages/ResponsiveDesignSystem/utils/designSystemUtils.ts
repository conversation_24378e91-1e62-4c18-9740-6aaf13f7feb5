/**
 * Design System Utilities
 * Helper functions and constants for the responsive design system
 */

/**
 * Breakpoint values in pixels
 */
export const breakpoints = {
  xs: 375,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

/**
 * Get the current breakpoint based on window width
 * @returns The current breakpoint name
 */
export const getCurrentBreakpoint = (): string => {
  if (typeof window === 'undefined') return 'lg'; // Default for SSR

  const width = window.innerWidth;
  
  if (width < breakpoints.sm) return 'xs';
  if (width < breakpoints.md) return 'sm';
  if (width < breakpoints.lg) return 'md';
  if (width < breakpoints.xl) return 'lg';
  if (width < breakpoints['2xl']) return 'xl';
  return '2xl';
};

/**
 * Check if the current device is touch-capable
 * @returns True if the device is touch-capable
 */
export const isTouchDevice = (): boolean => {
  if (typeof window === 'undefined') return false; // Default for SSR
  
  return 'ontouchstart' in window || 
    navigator.maxTouchPoints > 0 || 
    (navigator as any).msMaxTouchPoints > 0;
};

/**
 * Get the appropriate padding value based on the current breakpoint
 * @param base Base padding value in pixels
 * @returns Responsive padding value
 */
export const getResponsivePadding = (base: number): number => {
  const breakpoint = getCurrentBreakpoint();
  
  switch (breakpoint) {
    case 'xs':
      return Math.max(8, base * 0.65); // 35% reduction for mobile
    case 'sm':
    case 'md':
      return Math.max(12, base * 0.8); // 20% reduction for tablet
    default:
      return base; // Full padding for desktop
  }
};

/**
 * Get the appropriate font size value based on the current breakpoint
 * @param base Base font size value in pixels
 * @returns Responsive font size value
 */
export const getResponsiveFontSize = (base: number): number => {
  const breakpoint = getCurrentBreakpoint();
  
  switch (breakpoint) {
    case 'xs':
      return Math.max(16, base * 0.85); // 15% reduction for mobile, min 16px
    case 'sm':
    case 'md':
      return Math.max(16, base * 0.9); // 10% reduction for tablet, min 16px
    default:
      return base; // Full font size for desktop
  }
};

/**
 * Get the appropriate number of columns based on the current breakpoint
 * @returns Number of columns for the current breakpoint
 */
export const getResponsiveColumns = (): number => {
  const breakpoint = getCurrentBreakpoint();
  
  switch (breakpoint) {
    case 'xs':
      return 1; // Single column for mobile
    case 'sm':
    case 'md':
      return 2; // Two columns for tablet
    case 'lg':
    case 'xl':
      return 3; // Three columns for desktop
    default:
      return 4; // Four columns for large desktop
  }
};

export default {
  breakpoints,
  getCurrentBreakpoint,
  isTouchDevice,
  getResponsivePadding,
  getResponsiveFontSize,
  getResponsiveColumns,
};
