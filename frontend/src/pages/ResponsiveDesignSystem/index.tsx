import React, { useState } from 'react';
import { useMediaQuery } from '../../hooks/useMediaQuery';

// Import components
import ResponsiveTypography from './components/ResponsiveTypography';
import ResponsiveGridLayouts from './components/ResponsiveGridLayouts';
import ResponsiveCardComponents from './components/ResponsiveCardComponents';
import EnhancedCardComponents from './components/EnhancedCardComponents';
import TouchFriendlyControls from './components/TouchFriendlyControls';
import ResponsiveStacking from './components/ResponsiveStacking';
import ResponsiveImages from './components/ResponsiveImages';
import CollapsibleSections from './components/CollapsibleSections';
import AIFeaturesSection from './components/AIFeaturesSection';
import DesignGuidelinesSection from './components/DesignGuidelinesSection';
import TestingSection from './components/TestingSection';

/**
 * Responsive Design System Page
 * Showcases responsive components and patterns optimized for all device sizes
 * Implements the exact breakpoints and responsive patterns specified in requirements
 * 
 * This file has been refactored according to the May 2025 code optimization guidelines
 * to improve maintainability and performance.
 */
const ResponsiveDesignSystem: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'components' | 'ai-features' | 'guidelines' | 'testing'>('components');

  const selectOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
    { value: 'option4', label: 'Option 4', disabled: true },
  ];

  return (
    <div className="container-responsive mx-auto py-8">
      <h1 className="heading-hero mb-4">Responsive Design System</h1>
      <p className="lead text-gray-600 mb-8">
        A comprehensive showcase of responsive components optimized for all device sizes
      </p>

      {/* Device Size Indicator - Visible only in development */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 z-50 bg-black bg-opacity-75 text-white px-3 py-2 rounded-full text-sm font-mono">
          <span className="block xs:hidden">xs (375px)</span>
          <span className="hidden xs:block sm:hidden">sm (640px)</span>
          <span className="hidden sm:block md:hidden">md (768px)</span>
          <span className="hidden md:block lg:hidden">lg (1024px)</span>
          <span className="hidden lg:block xl:hidden">xl (1280px)</span>
          <span className="hidden xl:block 2xl:hidden">2xl (1536px)</span>
          <span className="hidden 2xl:block">2xl+</span>
        </div>
      )}

      {/* Responsive Tabs */}
      <div className="tabs-responsive mb-8">
        <button
          className={`tabs-responsive-tab ${activeTab === 'components' ? 'active' : ''}`}
          onClick={() => setActiveTab('components')}
        >
          Core Components
        </button>
        <button
          className={`tabs-responsive-tab ${activeTab === 'ai-features' ? 'active' : ''}`}
          onClick={() => setActiveTab('ai-features')}
        >
          AI Features
        </button>
        <button
          className={`tabs-responsive-tab ${activeTab === 'guidelines' ? 'active' : ''}`}
          onClick={() => setActiveTab('guidelines')}
        >
          Design Guidelines
        </button>
        <button
          className={`tabs-responsive-tab ${activeTab === 'testing' ? 'active' : ''}`}
          onClick={() => setActiveTab('testing')}
        >
          Testing
        </button>
      </div>

      {/* Render content based on active tab */}
      {activeTab === 'components' && (
        <>
          <ResponsiveTypography />
          <ResponsiveGridLayouts />
          <ResponsiveCardComponents />
          <EnhancedCardComponents />
          <TouchFriendlyControls selectOptions={selectOptions} />
          <ResponsiveStacking />
          <ResponsiveImages />
          <CollapsibleSections />
        </>
      )}

      {activeTab === 'ai-features' && (
        <AIFeaturesSection />
      )}

      {activeTab === 'guidelines' && (
        <DesignGuidelinesSection />
      )}

      {activeTab === 'testing' && (
        <TestingSection />
      )}
    </div>
  );
};

export default ResponsiveDesignSystem;
