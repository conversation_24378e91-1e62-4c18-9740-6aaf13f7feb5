import React from 'react';
import { render, screen } from '@testing-library/react';
import RentPlannerPage from '../RentPlannerPage';
import '@testing-library/jest-dom';

// Mock the RentPlannerInterface component
jest.mock('../../components/RentPlanner', () => ({
  RentPlannerInterface: function MockRentPlannerInterface() {
    return <div data-testid="rent-planner-interface">Mock Rent Planner Interface</div>;
  }
}));

// Mock the useAuth hook
jest.mock('../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'user123', name: 'Test User' }
  })
}));

describe('RentPlannerPage', () => {
  it('renders the page title and description', () => {
    render(<RentPlannerPage />);
    
    // Check for the title
    expect(screen.getByText('Rent Planner')).toBeInTheDocument();
    
    // Check for the description
    expect(screen.getByText(/Plan your rental needs with our AI-powered assistant/)).toBeInTheDocument();
  });

  it('renders the RentPlannerInterface component', () => {
    render(<RentPlannerPage />);
    
    // Check that the RentPlannerInterface is rendered
    expect(screen.getByTestId('rent-planner-interface')).toBeInTheDocument();
  });

  it('passes the user ID to the RentPlannerInterface', () => {
    // Create a spy on the RentPlannerInterface mock
    const { RentPlannerInterface } = require('../../components/RentPlanner');
    const spy = jest.spyOn(RentPlannerInterface, 'render');
    
    render(<RentPlannerPage />);
    
    // Check that userId was passed to RentPlannerInterface
    expect(spy).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user123'
      }),
      expect.anything()
    );
    
    // Clean up
    spy.mockRestore();
  });
});
