import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BrowserRouter } from 'react-router-dom';
import ProfilePage from '../ProfilePage';
import { AuthProvider } from '../../contexts/AuthContext';

// Mock the components used in ProfilePage
jest.mock('../../components/User/UserProfile', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-user-profile">Mock User Profile</div>,
}));

jest.mock('../../components/User/UserStats', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-user-stats">Mock User Stats</div>,
}));

jest.mock('../../components/User/UserDashboardAIInsights', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-user-dashboard-ai-insights">Mock User Dashboard AI Insights</div>,
}));

// Mock the auth hook
jest.mock('../../hooks/useAuth', () => ({
  useAuth: () => ({
    isAuthenticated: true,
    user: { id: 'test-user-id', name: 'Test User' },
  }),
}));

describe('ProfilePage', () => {
  it('renders the profile tab by default', () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <ProfilePage />
        </AuthProvider>
      </BrowserRouter>
    );

    // Check that the page title is rendered
    expect(screen.getByText('My Account')).toBeInTheDocument();
    
    // Check that the tabs are rendered
    expect(screen.getByText('Profile & Settings')).toBeInTheDocument();
    expect(screen.getByText('AI Insights')).toBeInTheDocument();
    
    // Check that the profile tab content is rendered
    expect(screen.getByTestId('mock-user-profile')).toBeInTheDocument();
    expect(screen.getByTestId('mock-user-stats')).toBeInTheDocument();
    
    // Check that the quick links are rendered
    expect(screen.getByText('Quick Links')).toBeInTheDocument();
    expect(screen.getByText('List a New Item')).toBeInTheDocument();
    expect(screen.getByText('View My Items')).toBeInTheDocument();
    expect(screen.getByText('My Bookings')).toBeInTheDocument();
    expect(screen.getByText('Messages')).toBeInTheDocument();
  });

  it('switches to the AI Insights tab when clicked', async () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <ProfilePage />
        </AuthProvider>
      </BrowserRouter>
    );

    // Initially, the profile tab should be active
    expect(screen.getByTestId('mock-user-profile')).toBeInTheDocument();
    
    // Click on the AI Insights tab
    fireEvent.click(screen.getByText('AI Insights'));
    
    // The AI Insights content should now be visible
    await waitFor(() => {
      expect(screen.getByTestId('mock-user-dashboard-ai-insights')).toBeInTheDocument();
    });
    
    // The profile content should no longer be visible
    expect(screen.queryByTestId('mock-user-profile')).not.toBeInTheDocument();
    expect(screen.queryByTestId('mock-user-stats')).not.toBeInTheDocument();
  });

  it('switches back to the Profile tab when clicked', async () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <ProfilePage />
        </AuthProvider>
      </BrowserRouter>
    );

    // Click on the AI Insights tab first
    fireEvent.click(screen.getByText('AI Insights'));
    
    // The AI Insights content should be visible
    await waitFor(() => {
      expect(screen.getByTestId('mock-user-dashboard-ai-insights')).toBeInTheDocument();
    });
    
    // Click on the Profile tab
    fireEvent.click(screen.getByText('Profile & Settings'));
    
    // The profile content should now be visible again
    await waitFor(() => {
      expect(screen.getByTestId('mock-user-profile')).toBeInTheDocument();
      expect(screen.getByTestId('mock-user-stats')).toBeInTheDocument();
    });
    
    // The AI Insights content should no longer be visible
    expect(screen.queryByTestId('mock-user-dashboard-ai-insights')).not.toBeInTheDocument();
  });
});
