import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { HelmetProvider } from 'react-helmet-async';
import PerformanceDashboardPage from '../PerformanceDashboard';

// Mock the Layout component
jest.mock('../../components/Layout/Layout', () => ({
  __esModule: true,
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="layout">{children}</div>
}));

// Mock the PerformanceDashboard component
jest.mock('../../components/Performance/PerformanceDashboard', () => ({
  __esModule: true,
  default: () => <div data-testid="performance-dashboard">Performance Dashboard Component</div>
}));

describe('PerformanceDashboardPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('renders the performance dashboard page with layout', () => {
    render(
      <HelmetProvider>
        <PerformanceDashboardPage />
      </HelmetProvider>
    );
    
    // Check that the layout is rendered
    expect(screen.getByTestId('layout')).toBeInTheDocument();
    
    // Check that the page title is rendered
    expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    
    // Check that the dashboard component is rendered
    expect(screen.getByTestId('performance-dashboard')).toBeInTheDocument();
  });
  
  it('switches between tabs when clicked', () => {
    render(
      <HelmetProvider>
        <PerformanceDashboardPage />
      </HelmetProvider>
    );
    
    // Initially the real-time tab should be active
    expect(screen.getByText('Real-time Metrics')).toHaveClass('border-blue-500');
    expect(screen.getByTestId('performance-dashboard')).toBeInTheDocument();
    
    // Click on the historical data tab
    fireEvent.click(screen.getByText('Historical Data'));
    
    // Now the historical data tab should be active
    expect(screen.getByText('Historical Data')).toHaveClass('border-blue-500');
    expect(screen.getByText('Historical Performance Data')).toBeInTheDocument();
    expect(screen.queryByTestId('performance-dashboard')).not.toBeInTheDocument();
    
    // Click on the optimization suggestions tab
    fireEvent.click(screen.getByText('Optimization Suggestions'));
    
    // Now the optimization suggestions tab should be active
    expect(screen.getByText('Optimization Suggestions')).toHaveClass('border-blue-500');
    expect(screen.getByText('Recommendations to improve application performance based on collected metrics.')).toBeInTheDocument();
    expect(screen.queryByTestId('performance-dashboard')).not.toBeInTheDocument();
    
    // Click back on the real-time tab
    fireEvent.click(screen.getByText('Real-time Metrics'));
    
    // Now the real-time tab should be active again
    expect(screen.getByText('Real-time Metrics')).toHaveClass('border-blue-500');
    expect(screen.getByTestId('performance-dashboard')).toBeInTheDocument();
  });
  
  it('reloads the page when reset metrics button is clicked', () => {
    // Mock window.location.reload
    const originalReload = window.location.reload;
    window.location.reload = jest.fn();
    
    render(
      <HelmetProvider>
        <PerformanceDashboardPage />
      </HelmetProvider>
    );
    
    // Click the reset metrics button
    fireEvent.click(screen.getByText('Reset Metrics'));
    
    // Check that reload was called
    expect(window.location.reload).toHaveBeenCalled();
    
    // Restore original reload
    window.location.reload = originalReload;
  });
  
  it('displays optimization suggestions in the optimization tab', () => {
    render(
      <HelmetProvider>
        <PerformanceDashboardPage />
      </HelmetProvider>
    );
    
    // Click on the optimization suggestions tab
    fireEvent.click(screen.getByText('Optimization Suggestions'));
    
    // Check that optimization suggestions are displayed
    expect(screen.getByText('Image Optimization')).toBeInTheDocument();
    expect(screen.getByText('Code Splitting')).toBeInTheDocument();
    expect(screen.getByText('List Virtualization')).toBeInTheDocument();
    expect(screen.getByText('API Caching')).toBeInTheDocument();
    expect(screen.getByText('Font Loading')).toBeInTheDocument();
  });
  
  it('displays historical data information in the historical tab', () => {
    render(
      <HelmetProvider>
        <PerformanceDashboardPage />
      </HelmetProvider>
    );
    
    // Click on the historical data tab
    fireEvent.click(screen.getByText('Historical Data'));
    
    // Check that historical data information is displayed
    expect(screen.getByText('Historical Performance Data')).toBeInTheDocument();
    expect(screen.getByText('View performance trends over time to identify patterns and improvements.')).toBeInTheDocument();
    expect(screen.getByText('Historical data visualization will be available in a future update.')).toBeInTheDocument();
    expect(screen.getByText('Performance Trends')).toBeInTheDocument();
    expect(screen.getByText('Page load times')).toBeInTheDocument();
  });
});
