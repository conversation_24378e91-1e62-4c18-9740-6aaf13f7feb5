import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useMediaQuery } from '../hooks/useMediaQuery';

// FAQ item interface
interface FAQItem {
  question: string;
  answer: string;
}

// Help section interface
interface HelpSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  description: string;
  faqs: FAQItem[];
}

const AuctionHelp: React.FC = () => {
  const [activeSection, setActiveSection] = useState<string>('basics');
  const [expandedFAQs, setExpandedFAQs] = useState<Record<string, boolean>>({});

  // Responsive design
  const isMobile = useMediaQuery('(max-width: 640px)');

  // Toggle FAQ expansion
  const toggleFAQ = (sectionId: string, index: number) => {
    const key = `${sectionId}-${index}`;
    setExpandedFAQs(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Help sections data
  const helpSections: HelpSection[] = [
    {
      id: 'basics',
      title: 'Auction Basics',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      description: 'Learn the fundamentals of how auctions work on RentUp.',
      faqs: [
        {
          question: 'What is an auction on RentUp?',
          answer: 'An auction on RentUp is a time-limited bidding process where users can compete to rent an item. The highest bidder at the end of the auction period wins the right to rent the item for the specified duration.'
        },
        {
          question: 'How do I participate in an auction?',
          answer: 'To participate in an auction, browse the available auctions, review the item details and auction terms, and place a bid that is higher than the current bid. You can continue to bid until the auction ends.'
        },
        {
          question: 'What happens if I win an auction?',
          answer: 'If you win an auction, you will receive a notification and will be directed to complete the rental agreement and payment process. You will need to finalize these steps within 24 hours of the auction ending.'
        },
        {
          question: 'What is the difference between standard and reserve auctions?',
          answer: 'In a standard auction, the item goes to the highest bidder regardless of the final price. In a reserve auction, the seller sets a minimum price (the reserve price) that must be met for the item to be rented.'
        }
      ]
    },
    {
      id: 'bidding',
      title: 'Bidding Strategies',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      description: 'Tips and strategies for successful bidding in auctions.',
      faqs: [
        {
          question: 'What is proxy bidding?',
          answer: 'Proxy bidding allows you to set a maximum bid amount, and the system will automatically bid on your behalf up to that amount as needed. This saves you from having to constantly monitor the auction.'
        },
        {
          question: 'What is sniping and how can I avoid it?',
          answer: 'Sniping is the practice of placing a bid in the final seconds of an auction to win without giving other bidders time to respond. RentUp offers anti-sniping protection that extends the auction time when bids are placed near the end.'
        },
        {
          question: 'How do I know if my bid is winning?',
          answer: 'Your bid status is clearly displayed on the auction page. You will also receive notifications if you are outbid or if you are the current highest bidder when the auction ends.'
        },
        {
          question: 'What is the minimum bid increment?',
          answer: 'The minimum bid increment is the smallest amount by which a new bid must exceed the current bid. This is set by the seller when creating the auction and can be a fixed amount or a percentage of the current bid.'
        }
      ]
    },
    {
      id: 'creating',
      title: 'Creating Auctions',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
      ),
      description: 'How to create and manage your own auctions.',
      faqs: [
        {
          question: 'How do I create an auction?',
          answer: 'To create an auction, go to your dashboard, click "Create Auction," and follow the step-by-step process. You'll need to provide item details, set pricing parameters, upload images, and specify auction settings.'
        },
        {
          question: 'What auction types can I choose from?',
          answer: 'RentUp offers several auction types: Standard Auction, Reserve Auction, Dutch Auction, Sealed Bid Auction, and Proxy Bidding Auction. Each has different rules and is suitable for different situations.'
        },
        {
          question: 'How long should my auction run?',
          answer: 'Auction duration depends on your goals. Shorter auctions (1-3 days) create urgency but may reach fewer potential bidders. Longer auctions (7-14 days) reach more people but may have less bidding intensity. Most successful auctions run for 5-7 days.'
        },
        {
          question: 'Can I cancel an auction after it has started?',
          answer: 'You can cancel an auction before any bids have been placed. Once bidding has started, you can only cancel in exceptional circumstances and may be subject to cancellation fees or penalties.'
        }
      ]
    },
    {
      id: 'payment',
      title: 'Payment & Security',
      icon: (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
      ),
      description: 'Understanding payment processes and security measures.',
      faqs: [
        {
          question: 'How do payments work for auctions?',
          answer: 'When you win an auction, you'll complete the payment process through RentUp's secure payment system. This includes the final bid amount plus any applicable fees. The payment is held in escrow until the rental period begins.'
        },
        {
          question: 'What happens if a winning bidder doesn't pay?',
          answer: 'If a winning bidder fails to complete payment within 24 hours, they may be subject to penalties and the item may be offered to the next highest bidder or relisted for auction.'
        },
        {
          question: 'Is there a deposit required for auction items?',
          answer: 'Yes, most auction items require a security deposit that is refundable after the rental period if the item is returned in the expected condition. The deposit amount is clearly displayed on the auction page.'
        },
        {
          question: 'How does RentUp protect against fraudulent auctions?',
          answer: 'RentUp verifies sellers, monitors auction activity for suspicious behavior, and provides a secure payment system. We also offer a dispute resolution process and protection policies for both buyers and sellers.'
        }
      ]
    }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl md:text-3xl font-bold mb-3 text-gray-900">Auction Help Center</h1>
        <p className="text-gray-700 mb-8 text-lg">Everything you need to know about using auctions on RentUp</p>

        {/* Navigation */}
        <div className="flex overflow-x-auto pb-3 mb-8 border-b border-gray-300">
          {helpSections.map((section) => (
            <button
              key={section.id}
              onClick={() => setActiveSection(section.id)}
              className={`flex items-center px-5 py-2.5 mr-3 whitespace-nowrap rounded-md transition-colors shadow-sm font-medium ${
                activeSection === section.id
                  ? 'bg-primary text-white border border-primary-600'
                  : 'bg-gray-200 text-gray-800 hover:bg-gray-300 border border-gray-300'
              }`}
            >
              <span className="mr-2">{section.icon}</span>
              <span>{section.title}</span>
            </button>
          ))}
        </div>

        {/* Active section content */}
        {helpSections.map((section) => (
          activeSection === section.id && (
            <motion.div
              key={section.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              <div className="bg-gray-100 rounded-lg p-6 mb-6 border border-gray-300 shadow-sm">
                <h2 className="text-xl font-semibold mb-3 text-gray-900">{section.title}</h2>
                <p className="text-gray-700">{section.description}</p>
              </div>

              {/* FAQs */}
              <div className="space-y-5">
                <h3 className="text-lg font-semibold text-gray-800">Frequently Asked Questions</h3>
                {section.faqs.map((faq, index) => {
                  const faqKey = `${section.id}-${index}`;
                  const isExpanded = expandedFAQs[faqKey] || false;

                  return (
                    <div
                      key={index}
                      className="border border-gray-300 rounded-lg overflow-hidden shadow-sm"
                    >
                      <button
                        onClick={() => toggleFAQ(section.id, index)}
                        className="flex justify-between items-center w-full p-4 text-left bg-white hover:bg-gray-50 transition-colors"
                      >
                        <span className="font-medium text-gray-800">{faq.question}</span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-5 w-5 text-gray-700 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`}
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>

                      {isExpanded && (
                        <div className="p-5 bg-gray-100 border-t border-gray-300">
                          <p className="text-gray-800">{faq.answer}</p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>

              {/* Additional resources */}
              <div className="bg-blue-100 border border-blue-300 rounded-lg p-6 mt-8 shadow-sm">
                <h3 className="text-lg font-semibold text-blue-900 mb-4">Additional Resources</h3>
                <ul className="space-y-3">
                  <li>
                    <Link to="/auctions" className="text-blue-700 hover:text-blue-900 flex items-center font-medium p-2 hover:bg-blue-50 rounded-md transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      Browse Active Auctions
                    </Link>
                  </li>
                  <li>
                    <Link to="/auctions/create" className="text-blue-700 hover:text-blue-900 flex items-center font-medium p-2 hover:bg-blue-50 rounded-md transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      Create Your First Auction
                    </Link>
                  </li>
                  <li>
                    <Link to="/contact" className="text-blue-700 hover:text-blue-900 flex items-center font-medium p-2 hover:bg-blue-50 rounded-md transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                      Contact Support
                    </Link>
                  </li>
                </ul>
              </div>
            </motion.div>
          )
        ))}
      </div>
    </div>
  );
};

export default AuctionHelp;
