// Results display component with different view modes
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Item } from '../../../types/Item';
import { UnifiedItemCard } from '../../../components/Item';
import { ListViewItem, MapView, SimilarItemsSection, SearchInsights } from '../../../components/Search';
import { ViewMode } from '../../../components/Search/SearchResultsHeader';
import { SearchQueryInfo } from '../utils/types';

interface ResultsDisplayProps {
  loading: boolean;
  filteredResults: Item[];
  allResults: Item[];
  viewMode: ViewMode;
  searchMode: string;
  queryInfo: SearchQueryInfo;
  onItemSelect: (itemId: string) => void;
  onClearFilters: () => void;
}

/**
 * Component for displaying search results in different view modes
 */
const ResultsDisplay: React.FC<ResultsDisplayProps> = ({
  loading,
  filteredResults,
  allResults,
  viewMode,
  searchMode,
  queryInfo,
  onItemSelect,
  onClearFilters,
}) => {
  const navigate = useNavigate();

  // Handle item selection for map view
  const handleItemSelect = (itemId: string) => {
    onItemSelect(itemId);
    // Navigate to item details page
    navigate(`/items/${itemId}`);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (filteredResults.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6 flex flex-col items-center justify-center py-12 text-center">
        <p className="text-lg text-dark-gray mb-4">
          We couldn't find any items matching your search criteria.
        </p>
        <button
          onClick={onClearFilters}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
        >
          Clear Filters
        </button>
      </div>
    );
  }

  return (
    <>
      {/* Grid View */}
      {viewMode === 'grid' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredResults.map((item) => (
            <div key={item.id} className="w-full">
              <UnifiedItemCard item={item} />
            </div>
          ))}
        </div>
      )}

      {/* List View */}
      {viewMode === 'list' && (
        <div className="space-y-4">
          {filteredResults.map((item) => (
            <ListViewItem key={item.id} item={item} />
          ))}
        </div>
      )}

      {/* Map View */}
      {viewMode === 'map' && (
        <MapView items={filteredResults} onItemSelect={handleItemSelect} />
      )}

      {/* Similar Items Section (only for multi-modal search) */}
      {searchMode === 'multimodal' && filteredResults.length > 0 && (
        <div className="mt-8">
          <SimilarItemsSection
            searchResults={allResults}
            limit={4}
          />
        </div>
      )}

      {/* Search Insights (only for multi-modal search) */}
      {searchMode === 'multimodal' && filteredResults.length > 0 && (
        <div className="mt-8">
          <SearchInsights
            searchResults={allResults}
            queryText={queryInfo.queryText}
            usedImage={!!queryInfo.queryImageUrl}
          />
        </div>
      )}
    </>
  );
};

export default ResultsDisplay;
