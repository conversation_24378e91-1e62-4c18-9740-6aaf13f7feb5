// Development notice component
import React from 'react';

/**
 * Component to display development mode notice
 */
const DevelopmentNotice: React.FC = () => {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4 text-blue-800">
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-blue-800">Development Mode</h3>
          <div className="mt-1 text-sm text-blue-700">
            <p>This page is currently using mock data for demonstration purposes. The "View Details" and "Book Now" buttons are disabled for mock items.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DevelopmentNotice;
