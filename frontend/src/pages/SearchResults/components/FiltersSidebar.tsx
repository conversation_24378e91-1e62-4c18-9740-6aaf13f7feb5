// Filters sidebar component
import React from 'react';
import { FilterSidebar } from '../../../components/Search';
import { SearchFilters } from '../utils/types';
import { mockItems } from '../utils/mockData';
import { CONDITION_OPTIONS, FILTER_PRICE_RANGES } from '../utils/constants';

interface FiltersSidebarProps {
  activeFilters: SearchFilters;
  onFilterChange: (filterType: string, value: string) => void;
  onPriceRangeChange: (min: number, max: number | null) => void;
  onClearFilters: () => void;
}

/**
 * Sidebar component containing all search filters
 */
const FiltersSidebar: React.FC<FiltersSidebarProps> = ({
  activeFilters,
  onFilterChange,
  onPriceRangeChange,
  onClearFilters,
}) => {
  // Calculate category counts from mock data
  const categoryOptions = [
    { value: 'vehicles', label: 'Vehicles & Transportation', count: mockItems.filter(i => i.category === 'vehicles').length },
    { value: 'real-estate', label: 'Real Estate & Spaces', count: mockItems.filter(i => i.category === 'real-estate').length },
    { value: 'electronics', label: 'Electronics & Technology', count: mockItems.filter(i => i.category === 'electronics').length },
    { value: 'home-furniture', label: 'Home & Furniture', count: mockItems.filter(i => i.category === 'home-furniture').length },
    { value: 'tools-equipment', label: 'Tools & Equipment', count: mockItems.filter(i => i.category === 'tools-equipment').length },
    { value: 'sports-recreation', label: 'Sports & Recreation', count: mockItems.filter(i => i.category === 'sports-recreation').length },
    { value: 'medical-equipment', label: 'Medical Equipment', count: mockItems.filter(i => i.category === 'medical-equipment').length },
  ];

  // Calculate location counts from mock data
  const locationOptions = [
    { value: 'Kuala Lumpur', label: 'Kuala Lumpur', count: mockItems.filter(i => i.location === 'Kuala Lumpur').length },
  ];

  // Calculate condition counts from mock data
  const conditionOptions = CONDITION_OPTIONS.map(condition => ({
    ...condition,
    count: mockItems.filter(i => i.condition?.status === condition.value).length
  }));

  // Calculate listing type counts from mock data
  const listingTypeOptions = [
    { value: 'regular', label: 'Regular Rental', count: mockItems.filter(i => i.listingType === 'regular').length },
    { value: 'rent-to-buy', label: 'Rent-to-Buy', count: mockItems.filter(i => i.listingType === 'rent-to-buy').length },
  ];

  return (
    <div className="w-full md:w-1/4">
      <FilterSidebar
        categories={categoryOptions}
        locations={locationOptions}
        conditions={conditionOptions}
        priceRanges={FILTER_PRICE_RANGES}
        listingTypes={listingTypeOptions}
        onFilterChange={onFilterChange}
        onPriceRangeChange={onPriceRangeChange}
        onClearFilters={onClearFilters}
        activeFilters={activeFilters}
      />
    </div>
  );
};

export default FiltersSidebar;
