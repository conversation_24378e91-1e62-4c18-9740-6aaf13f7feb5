// Results header component with controls and filters
import React from 'react';
import { SearchResultsHeader } from '../../../components/Search';
import MatchTypeFilter from '../../../components/Search/MatchTypeFilter';
import { ViewMode, SortOption } from '../../../components/Search/SearchResultsHeader';
import { MatchType } from '../utils/types';
import { getMatchTypeCounts } from '../utils/searchHelpers';
import { Item } from '../../../types/Item';

interface ResultsHeaderProps {
  filteredResults: Item[];
  allResults: Item[];
  viewMode: ViewMode;
  sortOption: SortOption;
  query: string;
  searchMode: string;
  selectedMatchType: MatchType;
  onViewModeChange: (mode: ViewMode) => void;
  onSortChange: (option: SortOption) => void;
  onMatchTypeChange: (type: MatchType) => void;
}

/**
 * Component for search results header with view controls and filters
 */
const ResultsHeader: React.FC<ResultsHeaderProps> = ({
  filteredResults,
  allResults,
  viewMode,
  sortOption,
  query,
  searchMode,
  selectedMatchType,
  onViewModeChange,
  onSortChange,
  onMatchTypeChange,
}) => {
  const matchTypeCounts = getMatchTypeCounts(allResults);

  return (
    <div className="mb-6">
      <SearchResultsHeader
        resultCount={filteredResults.length}
        viewMode={viewMode}
        sortOption={sortOption}
        onViewModeChange={onViewModeChange}
        onSortChange={onSortChange}
        query={query}
      />

      {/* Match Type Filter (only show for multi-modal search) */}
      {searchMode === 'multimodal' && (
        <div className="mt-4">
          <MatchTypeFilter
            selectedType={selectedMatchType}
            onSelectType={onMatchTypeChange}
            counts={matchTypeCounts}
          />
        </div>
      )}
    </div>
  );
};

export default ResultsHeader;
