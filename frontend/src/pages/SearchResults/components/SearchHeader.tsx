// Search header component with search bar
import React from 'react';
import { SearchBar } from '../../../components/Search';
import { CATEGORY_OPTIONS, PRICE_RANGE_OPTIONS, LISTING_TYPE_OPTIONS } from '../utils/constants';

interface SearchHeaderProps {
  className?: string;
}

/**
 * Search header component containing the main search bar
 */
const SearchHeader: React.FC<SearchHeaderProps> = ({ className = '' }) => {
  return (
    <div className={`bg-white rounded-lg shadow-sm p-4 mb-8 ${className}`}>
      <SearchBar
        categories={CATEGORY_OPTIONS}
        priceRanges={PRICE_RANGE_OPTIONS}
        listingTypes={LISTING_TYPE_OPTIONS}
        buttonText="Search"
        placeholderText="Search for items..."
      />
    </div>
  );
};

export default SearchHeader;
