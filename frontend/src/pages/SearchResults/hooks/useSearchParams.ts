// Custom hook for managing search parameters
import { useSearchParams as useRouterSearchParams } from 'react-router-dom';
import { SortOption } from '../../../components/Search/SearchResultsHeader';
import { SearchParams } from '../utils/types';

/**
 * Custom hook for managing search URL parameters
 */
export const useSearchParams = () => {
  const [searchParams, setSearchParams] = useRouterSearchParams();

  // Get current search parameters
  const getSearchParams = (): SearchParams => {
    return {
      query: searchParams.get('q') || '',
      category: searchParams.get('category') || '',
      location: searchParams.get('location') || '',
      priceRange: searchParams.get('priceRange') || '',
      listingType: searchParams.get('listingType') || '',
      condition: searchParams.get('condition') || '',
      sort: (searchParams.get('sort') as SortOption) || 'relevance',
      searchMode: searchParams.get('mode') || 'text',
    };
  };

  // Update a single filter parameter
  const updateFilter = (filterType: string, value: string) => {
    const newParams = new URLSearchParams(searchParams);

    if (value) {
      newParams.set(filterType, value);
    } else {
      newParams.delete(filterType);
    }

    setSearchParams(newParams);
  };

  // Update price range parameter
  const updatePriceRange = (min: number, max: number | null) => {
    const newParams = new URLSearchParams(searchParams);
    const formattedRange = max === null ? `$${min}+` : `$${min} - $${max}`;

    newParams.set('priceRange', formattedRange);
    setSearchParams(newParams);
  };

  // Update sort parameter
  const updateSort = (option: SortOption) => {
    const newParams = new URLSearchParams(searchParams);

    if (option !== 'relevance') {
      newParams.set('sort', option);
    } else {
      newParams.delete('sort');
    }

    setSearchParams(newParams);
  };

  // Clear all filters except query and sort
  const clearAllFilters = () => {
    const currentParams = getSearchParams();
    const newParams = new URLSearchParams();

    if (currentParams.query) {
      newParams.set('q', currentParams.query);
    }

    if (currentParams.sort !== 'relevance') {
      newParams.set('sort', currentParams.sort);
    }

    setSearchParams(newParams);
  };

  return {
    searchParams: getSearchParams(),
    updateFilter,
    updatePriceRange,
    updateSort,
    clearAllFilters,
  };
};
