// Custom hook for managing search results and state
import { useState, useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { Item } from '../../../types/Item';
import { ViewMode } from '../../../components/Search/SearchResultsHeader';
import { SearchState, SearchQueryInfo, MatchType } from '../utils/types';
import { 
  searchItemsFromAPI, 
  sortItems, 
  filterResultsByMatchType 
} from '../utils/searchHelpers';
import { 
  SESSION_STORAGE_KEYS, 
  DEFAULT_QUERY_INFO, 
  SEARCH_DEBOUNCE_DELAY 
} from '../utils/constants';
import { mockItems } from '../utils/mockData';

interface UseSearchResultsProps {
  initialResults?: Item[];
  searchParams: {
    query: string;
    category: string;
    location: string;
    priceRange: string;
    listingType: string;
    condition: string;
    sort: any;
    searchMode: string;
  };
}

/**
 * Custom hook for managing search results state and operations
 */
export const useSearchResults = ({ initialResults, searchParams }: UseSearchResultsProps) => {
  const location = useLocation();

  // Initialize state
  const [state, setState] = useState<SearchState>({
    results: [],
    filteredResults: [],
    loading: false,
    viewMode: 'grid' as ViewMode,
    selectedItemId: null,
    selectedMatchType: 'all' as MatchType,
    queryInfo: DEFAULT_QUERY_INFO,
    activeFilters: {
      category: searchParams.category,
      location: searchParams.location,
      priceRange: searchParams.priceRange,
      listingType: searchParams.listingType,
      condition: searchParams.condition,
    },
  });

  // Update filtered results when match type changes
  useEffect(() => {
    setState(prev => ({
      ...prev,
      filteredResults: filterResultsByMatchType(prev.results, prev.selectedMatchType)
    }));
  }, [state.selectedMatchType, state.results]);

  // Initialize results from props or session storage
  useEffect(() => {
    if (initialResults && initialResults.length > 0) {
      // If initialResults are provided, use them
      setState(prev => ({
        ...prev,
        results: initialResults,
        filteredResults: filterResultsByMatchType(initialResults, prev.selectedMatchType),
        loading: false,
        activeFilters: {
          category: searchParams.category,
          location: searchParams.location,
          priceRange: searchParams.priceRange,
          listingType: searchParams.listingType,
          condition: searchParams.condition,
        }
      }));
    } else if (searchParams.searchMode === 'multimodal') {
      // Check if there are results in sessionStorage
      const storedResults = sessionStorage.getItem(SESSION_STORAGE_KEYS.SEARCH_RESULTS);
      const storedQueryInfo = sessionStorage.getItem(SESSION_STORAGE_KEYS.SEARCH_QUERY_INFO);

      if (storedResults) {
        try {
          const parsedResults = JSON.parse(storedResults);
          setState(prev => ({
            ...prev,
            results: parsedResults,
            filteredResults: filterResultsByMatchType(parsedResults, prev.selectedMatchType),
            loading: false,
            activeFilters: {
              category: searchParams.category,
              location: searchParams.location,
              priceRange: searchParams.priceRange,
              listingType: searchParams.listingType,
              condition: searchParams.condition,
            }
          }));

          // Load search query information if available
          if (storedQueryInfo) {
            try {
              const parsedQueryInfo = JSON.parse(storedQueryInfo);
              setState(prev => ({ ...prev, queryInfo: parsedQueryInfo }));
            } catch (error) {
              console.error('Error parsing stored query info:', error);
            }
          }

          // Clear sessionStorage after retrieving results
          sessionStorage.removeItem(SESSION_STORAGE_KEYS.SEARCH_RESULTS);
          sessionStorage.removeItem(SESSION_STORAGE_KEYS.SEARCH_QUERY_INFO);
        } catch (error) {
          console.error('Error parsing stored results:', error);
          fetchItems();
        }
      } else {
        fetchItems();
      }
    } else {
      fetchItems();
    }
  }, [initialResults]);

  // Fetch items when search parameters change
  useEffect(() => {
    // Skip if we're using initialResults or just loaded from sessionStorage
    if ((initialResults && initialResults.length > 0) ||
        (searchParams.searchMode === 'multimodal' && state.results.length > 0 && !searchParams.query)) {
      return;
    }

    const timeoutId = setTimeout(() => {
      fetchItems();
    }, SEARCH_DEBOUNCE_DELAY);

    return () => clearTimeout(timeoutId);
  }, [
    searchParams.query, 
    searchParams.category, 
    searchParams.location, 
    searchParams.priceRange, 
    searchParams.listingType, 
    searchParams.condition, 
    searchParams.sort
  ]);

  // Function to fetch items
  const fetchItems = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true }));
    
    try {
      // Get filtered items from API
      const foundItems = await searchItemsFromAPI(
        mockItems,
        searchParams.query,
        searchParams.category,
        searchParams.location,
        searchParams.priceRange,
        searchParams.listingType,
        searchParams.condition,
        searchParams.sort
      );

      // Apply any additional client-side sorting if needed
      let sortedItems = foundItems;
      if (searchParams.sort === 'rating') {
        // Rating sort is handled client-side
        sortedItems = sortItems(foundItems, searchParams.sort);
      }

      setState(prev => ({
        ...prev,
        results: sortedItems,
        filteredResults: filterResultsByMatchType(sortedItems, prev.selectedMatchType),
        activeFilters: {
          category: searchParams.category,
          location: searchParams.location,
          priceRange: searchParams.priceRange,
          listingType: searchParams.listingType,
          condition: searchParams.condition,
        }
      }));
    } catch (error) {
      console.error('Error fetching items:', error);
      // Fallback to local search in case of error
      const localItems = sortItems(mockItems, searchParams.sort);
      setState(prev => ({
        ...prev,
        results: localItems,
        filteredResults: filterResultsByMatchType(localItems, prev.selectedMatchType),
        activeFilters: {
          category: searchParams.category,
          location: searchParams.location,
          priceRange: searchParams.priceRange,
          listingType: searchParams.listingType,
          condition: searchParams.condition,
        }
      }));
    } finally {
      setState(prev => ({ ...prev, loading: false }));
    }
  }, [searchParams]);

  // Update view mode
  const setViewMode = (mode: ViewMode) => {
    setState(prev => ({ ...prev, viewMode: mode }));
  };

  // Update selected match type
  const setSelectedMatchType = (matchType: MatchType) => {
    setState(prev => ({ ...prev, selectedMatchType: matchType }));
  };

  // Handle item selection
  const handleItemSelect = (itemId: string) => {
    setState(prev => ({ ...prev, selectedItemId: itemId }));
  };

  return {
    ...state,
    setViewMode,
    setSelectedMatchType,
    handleItemSelect,
    fetchItems,
  };
};
