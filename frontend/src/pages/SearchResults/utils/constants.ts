// Constants for the SearchResults page
import { CategoryOption, LocationOption, ConditionOption, PriceRangeOption, ListingTypeOption } from './types';

// Search debounce delay
export const SEARCH_DEBOUNCE_DELAY = 300;

// Session storage keys
export const SESSION_STORAGE_KEYS = {
  SEARCH_RESULTS: 'searchResults',
  SEARCH_QUERY_INFO: 'searchQueryInfo',
} as const;

// Default search query info
export const DEFAULT_QUERY_INFO = {
  queryText: '',
  queryImageUrl: '',
  searchMode: 'text' as const,
  weights: {
    text: 0.5,
    image: 0.5,
  },
};

// Category options
export const CATEGORY_OPTIONS: CategoryOption[] = [
  { value: 'vehicles', label: 'Vehicles & Transportation' },
  { value: 'real-estate', label: 'Real Estate & Spaces' },
  { value: 'electronics', label: 'Electronics & Technology' },
  { value: 'home-furniture', label: 'Home & Furniture' },
  { value: 'tools-equipment', label: 'Tools & Equipment' },
  { value: 'sports-recreation', label: 'Sports & Recreation' },
  { value: 'fashion', label: 'Fashion & Accessories' },
  { value: 'entertainment', label: 'Entertainment & Media' },
  { value: 'business', label: 'Business & Professional' },
  { value: 'art-creative', label: 'Art & Creative' },
  { value: 'baby-kids', label: 'Baby & Kids' },
  { value: 'digital-assets', label: 'Digital Assets & Virtual Goods' },
  { value: 'services', label: 'Services & Expertise' },
  { value: 'medical-equipment', label: 'Medical Equipment' },
];

// Price range options
export const PRICE_RANGE_OPTIONS = [
  { value: '0-50', label: '$0 - $50' },
  { value: '50-100', label: '$50 - $100' },
  { value: '100-200', label: '$100 - $200' },
  { value: '200+', label: '$200+' },
];

// Listing type options
export const LISTING_TYPE_OPTIONS = [
  { value: 'regular', label: 'Regular Rental' },
  { value: 'rent-to-buy', label: 'Rent-to-Buy' },
];

// Condition options for filtering
export const CONDITION_OPTIONS: ConditionOption[] = [
  { value: 'new', label: 'New' },
  { value: 'like-new', label: 'Like New' },
  { value: 'good', label: 'Good' },
  { value: 'fair', label: 'Fair' },
];

// Price ranges for filtering
export const FILTER_PRICE_RANGES: PriceRangeOption[] = [
  { min: 0, max: 50 },
  { min: 50, max: 100 },
  { min: 100, max: 200 },
  { min: 200, max: null },
];

// Sort option mapping for API
export const SORT_MAPPING = {
  'relevance': 'relevance',
  'price_low': 'price_low',
  'price_high': 'price_high',
  'rating': 'relevance', // We'll handle rating client-side for now
  'newest': 'newest'
} as const;
