// Helper functions for search functionality
import { Item } from '../../../types/Item';
import { SortOption } from '../../../components/Search/SearchResultsHeader';
import { MatchType } from './types';
import { SORT_MAPPING } from './constants';
import * as itemService from '../../../services/itemService';

/**
 * Search items locally using mock data
 */
export const searchItemsLocally = (
  allItems: Item[],
  query: string,
  category: string,
  location: string,
  priceRange: string,
  listingType: string,
  condition: string
): Item[] => {
  return allItems.filter(item => {
    // Match search query
    const matchesQuery = !query ||
      item.name.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase());

    // Match category
    const matchesCategory = !category || item.category === category;

    // Match location
    const matchesLocation = !location || item.location === location;

    // Match price range
    const matchesPriceRange = !priceRange || item.priceRange === priceRange;

    // Match listing type
    const matchesListingType = !listingType || item.listingType === listingType;

    // Match condition
    const matchesCondition = !condition || (item.condition && item.condition.status === condition);

    return matchesQuery && matchesCategory && matchesLocation && matchesPriceRange && matchesListingType && matchesCondition;
  });
};

/**
 * Search items from API with fallback to local search
 */
export const searchItemsFromAPI = async (
  allItems: Item[],
  query: string,
  category: string,
  location: string,
  priceRange: string,
  listingType: string,
  condition: string,
  sort: SortOption
): Promise<Item[]> => {
  try {
    // Parse price range
    let minPrice: number | undefined;
    let maxPrice: number | undefined;

    if (priceRange) {
      const priceMatch = priceRange.match(/\$(\d+)(?:\s*-\s*\$(\d+))?/);
      if (priceMatch) {
        minPrice = parseInt(priceMatch[1]);
        if (priceMatch[2]) {
          maxPrice = parseInt(priceMatch[2]);
        }
      }
    }

    // In development, return mock data
    // In production, this would call the actual API
    // return await itemService.searchItems({
    //   query: query || undefined,
    //   category: category || undefined,
    //   location: location || undefined,
    //   min_price: minPrice,
    //   max_price: maxPrice,
    //   condition: condition || undefined,
    //   sort_by: SORT_MAPPING[sort] as any,
    //   limit: 100,
    //   use_semantic: true
    // });

    // For now, use local search
    return searchItemsLocally(allItems, query, category, location, priceRange, listingType, condition);
  } catch (error) {
    console.error('API search error:', error);
    // Fallback to local search in case of error
    return searchItemsLocally(allItems, query, category, location, priceRange, listingType, condition);
  }
};

/**
 * Sort items based on sort option
 */
export const sortItems = (items: Item[], sortOption: SortOption): Item[] => {
  const itemsCopy = [...items];

  switch (sortOption) {
    case 'price_low':
      return itemsCopy.sort((a, b) => a.pricePerDay - b.pricePerDay);
    case 'price_high':
      return itemsCopy.sort((a, b) => b.pricePerDay - a.pricePerDay);
    case 'rating':
      return itemsCopy.sort((a, b) => {
        const ratingA = a.rating || 0;
        const ratingB = b.rating || 0;
        return ratingB - ratingA;
      });
    case 'newest':
      // In a real app, you would sort by creation date
      // For now, we'll just return the items as is
      return itemsCopy;
    case 'relevance':
    default:
      // In a real app, you would implement a relevance algorithm
      // For now, we'll just return the items as is
      return itemsCopy;
  }
};

/**
 * Filter results by match type for multi-modal search
 */
export const filterResultsByMatchType = (items: Item[], matchType: MatchType): Item[] => {
  if (matchType === 'all') {
    return items;
  }

  return items.filter(item => {
    const metadata = item.attributes?.search_metadata;
    if (!metadata) return false;

    switch (matchType) {
      case 'text':
        return metadata.source === 'text' || metadata.source === 'semantic' || metadata.source === 'keyword';
      case 'image':
        return metadata.source === 'image';
      case 'multi-modal':
        return metadata.source === 'multi-modal' || metadata.source === 'hybrid';
      default:
        return true;
    }
  });
};

/**
 * Get match type counts for filtering
 */
export const getMatchTypeCounts = (items: Item[]) => {
  return {
    all: items.length,
    text: items.filter(item => {
      const metadata = item.attributes?.search_metadata;
      return metadata && (metadata.source === 'text' || metadata.source === 'semantic' || metadata.source === 'keyword');
    }).length,
    image: items.filter(item => {
      const metadata = item.attributes?.search_metadata;
      return metadata && metadata.source === 'image';
    }).length,
    multiModal: items.filter(item => {
      const metadata = item.attributes?.search_metadata;
      return metadata && (metadata.source === 'multi-modal' || metadata.source === 'hybrid');
    }).length
  };
};

/**
 * Parse price range string
 */
export const parsePriceRange = (priceRange: string): { min?: number; max?: number } => {
  if (!priceRange) return {};

  const priceMatch = priceRange.match(/\$(\d+)(?:\s*-\s*\$(\d+))?/);
  if (priceMatch) {
    const min = parseInt(priceMatch[1]);
    const max = priceMatch[2] ? parseInt(priceMatch[2]) : undefined;
    return { min, max };
  }

  return {};
};

/**
 * Format price range for display
 */
export const formatPriceRange = (min: number, max: number | null): string => {
  return max === null ? `$${min}+` : `$${min} - $${max}`;
};
