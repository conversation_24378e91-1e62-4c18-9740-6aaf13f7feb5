// Types for the SearchResults page
import { Item } from '../../../types/Item';
import { ViewMode, SortOption } from '../../../components/Search/SearchResultsHeader';

export interface SearchResultsProps {
  initialResults?: Item[];
}

export interface SearchFilters {
  category: string;
  location: string;
  priceRange: string;
  listingType: string;
  condition: string;
}

export interface SearchQueryInfo {
  queryText: string;
  queryImageUrl: string;
  searchMode: 'text' | 'image' | 'multimodal';
  weights: {
    text: number;
    image: number;
  };
}

export interface SearchState {
  results: Item[];
  filteredResults: Item[];
  loading: boolean;
  viewMode: ViewMode;
  selectedItemId: string | null;
  selectedMatchType: 'all' | 'text' | 'image' | 'multi-modal';
  queryInfo: SearchQueryInfo;
  activeFilters: SearchFilters;
}

export interface SearchParams {
  query: string;
  category: string;
  location: string;
  priceRange: string;
  listingType: string;
  condition: string;
  sort: SortOption;
  searchMode: string;
}

export type MatchType = 'all' | 'text' | 'image' | 'multi-modal';

export interface CategoryOption {
  value: string;
  label: string;
  count?: number;
}

export interface LocationOption {
  value: string;
  label: string;
  count?: number;
}

export interface ConditionOption {
  value: string;
  label: string;
  count?: number;
}

export interface PriceRangeOption {
  min: number;
  max: number | null;
}

export interface ListingTypeOption {
  value: string;
  label: string;
  count?: number;
}
