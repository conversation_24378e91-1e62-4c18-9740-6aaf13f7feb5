// Main SearchResults page component - refactored for better maintainability
import React from 'react';

// Import types
import { SearchResultsProps } from './utils/types';

// Import hooks
import { useSearchParams } from './hooks/useSearchParams';
import { useSearchResults } from './hooks/useSearchResults';

// Import components
import SearchHeader from './components/SearchHeader';
import MultiModalInfo from './components/MultiModalInfo';
import DevelopmentNotice from './components/DevelopmentNotice';
import ResultsHeader from './components/ResultsHeader';
import FiltersSidebar from './components/FiltersSidebar';
import ResultsDisplay from './components/ResultsDisplay';

/**
 * Main SearchResults page component
 * 
 * This component handles:
 * - Search parameter management
 * - Results fetching and filtering
 * - Multiple view modes (grid, list, map)
 * - Multi-modal search support
 * - Filter management
 * 
 * Features:
 * - Responsive design for all screen sizes
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Performance optimized with debounced search
 * - Error handling and fallback to mock data
 * - Session storage for multi-modal search results
 * - SEO-friendly URL parameters
 */
const SearchResults: React.FC<SearchResultsProps> = ({ initialResults }) => {
  // URL parameter management
  const {
    searchParams,
    updateFilter,
    updatePriceRange,
    updateSort,
    clearAllFilters,
  } = useSearchParams();

  // Search results state management
  const {
    results,
    filteredResults,
    loading,
    viewMode,
    selectedMatchType,
    queryInfo,
    activeFilters,
    setViewMode,
    setSelectedMatchType,
    handleItemSelect,
  } = useSearchResults({
    initialResults,
    searchParams,
  });

  return (
    <div className="bg-light-gray py-8">
      <div className="container mx-auto px-4">
        {/* Search Header */}
        <SearchHeader />

        {/* Multi-Modal Search Information */}
        <MultiModalInfo 
          searchMode={searchParams.searchMode}
          queryInfo={queryInfo}
        />

        {/* Development Notice */}
        <DevelopmentNotice />

        {/* Search Results Header with Controls */}
        <ResultsHeader
          filteredResults={filteredResults}
          allResults={results}
          viewMode={viewMode}
          sortOption={searchParams.sort}
          query={searchParams.query}
          searchMode={searchParams.searchMode}
          selectedMatchType={selectedMatchType}
          onViewModeChange={setViewMode}
          onSortChange={updateSort}
          onMatchTypeChange={setSelectedMatchType}
        />

        {/* Main Content Area */}
        <div className="flex flex-col md:flex-row gap-8">
          {/* Filters Sidebar */}
          <FiltersSidebar
            activeFilters={activeFilters}
            onFilterChange={updateFilter}
            onPriceRangeChange={updatePriceRange}
            onClearFilters={clearAllFilters}
          />

          {/* Results Display */}
          <div className="w-full md:w-3/4">
            <ResultsDisplay
              loading={loading}
              filteredResults={filteredResults}
              allResults={results}
              viewMode={viewMode}
              searchMode={searchParams.searchMode}
              queryInfo={queryInfo}
              onItemSelect={handleItemSelect}
              onClearFilters={clearAllFilters}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchResults;
