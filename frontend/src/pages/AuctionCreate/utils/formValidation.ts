/**
 * Form Validation Utilities
 * Contains validation functions for the auction creation form
 */

import { AuctionFormData, FormErrors } from './auctionTypes';

/**
 * Validates the basic information step (Step 1)
 * @param formData Current form data
 * @returns Object containing validation errors, if any
 */
export const validateBasicInfoStep = (formData: AuctionFormData): FormErrors => {
  const { title, description, mainCategory, location } = formData;
  const errors: FormErrors = {};

  if (!title.trim()) errors.title = 'Title is required';
  if (!description.trim()) errors.description = 'Description is required';
  if (!mainCategory) errors.category = 'Category is required';
  if (!location.trim()) errors.location = 'Location is required';

  return errors;
};

/**
 * Validates the pricing and settings step (Step 2)
 * @param formData Current form data
 * @returns Object containing validation errors, if any
 */
export const validatePricingStep = (formData: AuctionFormData): FormErrors => {
  const { 
    startingPrice, 
    reservePrice, 
    auctionType, 
    bidIncrementStrategy, 
    bidIncrement, 
    bidIncrementPercentage 
  } = formData;
  const errors: FormErrors = {};

  if (!startingPrice.trim()) {
    errors.startingPrice = 'Starting price is required';
  } else if (parseFloat(startingPrice) <= 0) {
    errors.startingPrice = 'Starting price must be greater than 0';
  }

  if (auctionType === 'reserve' && !reservePrice.trim()) {
    errors.reservePrice = 'Reserve price is required for reserve auctions';
  }

  if (reservePrice && parseFloat(reservePrice) < parseFloat(startingPrice)) {
    errors.reservePrice = 'Reserve price must be greater than or equal to starting price';
  }

  if (bidIncrementStrategy === 'fixed' && (!bidIncrement.trim() || parseFloat(bidIncrement) <= 0)) {
    errors.bidIncrement = 'Bid increment must be greater than 0';
  }

  if (bidIncrementStrategy === 'percentage' && (!bidIncrementPercentage.trim() || parseFloat(bidIncrementPercentage) <= 0)) {
    errors.bidIncrementPercentage = 'Bid increment percentage must be greater than 0';
  }

  return errors;
};

/**
 * Validates the images and review step (Step 3)
 * @param formData Current form data
 * @returns Object containing validation errors, if any
 */
export const validateImagesStep = (formData: AuctionFormData): FormErrors => {
  const { images } = formData;
  const errors: FormErrors = {};

  if (images.length === 0) {
    errors.images = 'At least one image is required';
  }

  return errors;
};

/**
 * Validates the current step based on the step number
 * @param formData Current form data
 * @param currentStep Current step number
 * @returns Object containing validation errors, if any
 */
export const validateCurrentStep = (formData: AuctionFormData, currentStep: number): FormErrors => {
  switch (currentStep) {
    case 1:
      return validateBasicInfoStep(formData);
    case 2:
      return validatePricingStep(formData);
    case 3:
      return validateImagesStep(formData);
    default:
      return {};
  }
};

/**
 * Validates the entire form
 * @param formData Current form data
 * @returns Object containing validation errors, if any
 */
export const validateForm = (formData: AuctionFormData): FormErrors => {
  return {
    ...validateBasicInfoStep(formData),
    ...validatePricingStep(formData),
    ...validateImagesStep(formData)
  };
};

/**
 * Validates image files for type and size
 * @param file File to validate
 * @returns Error message if invalid, empty string if valid
 */
export const validateImageFile = (file: File): string => {
  // Check file type
  const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  if (!validTypes.includes(file.type)) {
    return 'Only JPEG, PNG, GIF, and WebP images are allowed';
  }

  // Check file size (5MB max)
  if (file.size > 5 * 1024 * 1024) {
    return 'Images must be less than 5MB';
  }

  return '';
};
