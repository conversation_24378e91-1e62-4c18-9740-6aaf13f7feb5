/**
 * Auction Types and Constants
 * Contains type definitions and constants used in the auction creation process
 */

// Auction types
export interface AuctionType {
  id: string;
  name: string;
  description: string;
}

export const auctionTypes: AuctionType[] = [
  {
    id: 'standard',
    name: 'Standard Auction',
    description: 'Traditional auction where the highest bidder wins at the end of the auction period.'
  },
  {
    id: 'reserve',
    name: 'Reserve Auction',
    description: 'Auction with a minimum price that must be met for the item to be sold.'
  },
  {
    id: 'dutch',
    name: 'Dutch Auction',
    description: 'Price starts high and gradually decreases until someone places a bid.'
  },
  {
    id: 'sealed',
    name: 'Sealed Bid Auction',
    description: 'Bidders submit sealed bids, and the highest bidder wins at the end of the auction period.'
  },
  {
    id: 'proxy',
    name: 'Proxy Bidding Auction',
    description: 'Bidders set a maximum bid, and the system automatically bids on their behalf up to that amount.'
  }
];

// Bid increment strategies
export interface BidIncrementStrategy {
  id: string;
  name: string;
  description: string;
}

export const bidIncrementStrategies: BidIncrementStrategy[] = [
  {
    id: 'fixed',
    name: 'Fixed Amount',
    description: 'Each bid must increase by a fixed dollar amount.'
  },
  {
    id: 'percentage',
    name: 'Percentage',
    description: 'Each bid must increase by a percentage of the current bid.'
  },
  {
    id: 'tiered',
    name: 'Tiered',
    description: 'Bid increments increase as the current bid increases.'
  }
];

// Duration options
export interface DurationOption {
  value: string;
  label: string;
}

export const durationOptions: DurationOption[] = [
  { value: '1', label: '1 day' },
  { value: '3', label: '3 days' },
  { value: '5', label: '5 days' },
  { value: '7', label: '7 days' },
  { value: '14', label: '14 days' }
];

// Anti-sniping time options
export interface AntiSnipingTimeOption {
  value: string;
  label: string;
}

export const antiSnipingTimeOptions: AntiSnipingTimeOption[] = [
  { value: '1', label: '1 minute' },
  { value: '5', label: '5 minutes' },
  { value: '10', label: '10 minutes' },
  { value: '15', label: '15 minutes' },
  { value: '30', label: '30 minutes' }
];

// Form field validation
export interface FormErrors {
  [key: string]: string;
}

// Auction form data interface
export interface AuctionFormData {
  title: string;
  description: string;
  startingPrice: string;
  reservePrice: string;
  duration: string;
  images: File[];
  mainCategory?: string;
  subCategory?: string;
  location: string;
  auctionType: string;
  bidIncrementStrategy: string;
  bidIncrement: string;
  bidIncrementPercentage: string;
  enableAntiSniping: boolean;
  antiSnipingTime: string;
}

// Default form values
export const defaultFormValues: AuctionFormData = {
  title: '',
  description: '',
  startingPrice: '',
  reservePrice: '',
  duration: '3',
  images: [],
  mainCategory: undefined,
  subCategory: undefined,
  location: '',
  auctionType: 'standard',
  bidIncrementStrategy: 'fixed',
  bidIncrement: '5',
  bidIncrementPercentage: '5',
  enableAntiSniping: true,
  antiSnipingTime: '5'
};
