/**
 * Custom hook for managing auction form state and logic
 */

import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  AuctionFormData, 
  FormErrors, 
  defaultFormValues 
} from '../utils/auctionTypes';
import { 
  validateCurrentStep, 
  validateForm, 
  validateImageFile 
} from '../utils/formValidation';

export const useAuctionForm = () => {
  const navigate = useNavigate();
  
  // Form state
  const [formData, setFormData] = useState<AuctionFormData>(defaultFormValues);
  const [errors, setErrors] = useState<FormErrors>({});
  const [currentStep, setCurrentStep] = useState(1);
  const [formProgress, setFormProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Destructure form data for easier access
  const {
    title,
    description,
    startingPrice,
    mainCategory,
    location,
    images,
    auctionType,
    bidIncrementStrategy
  } = formData;

  // Update form progress when fields are filled
  useEffect(() => {
    let filledFields = 0;
    const totalFields = 8; // Count of required fields

    if (title.trim()) filledFields++;
    if (description.trim()) filledFields++;
    if (startingPrice.trim()) filledFields++;
    if (mainCategory) filledFields++;
    if (location.trim()) filledFields++;
    if (images.length > 0) filledFields++;
    if (auctionType) filledFields++;
    if (bidIncrementStrategy) filledFields++;

    setFormProgress((filledFields / totalFields) * 100);
  }, [title, description, startingPrice, mainCategory, location, images, auctionType, bidIncrementStrategy]);

  // Form field update handlers
  const updateField = <K extends keyof AuctionFormData>(
    field: K, 
    value: AuctionFormData[K]
  ) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = e.target.files;
    if (!fileList) return;

    // Convert FileList to array
    const newFiles = Array.from(fileList);

    // Validate file types and sizes
    const validFiles = newFiles.filter(file => {
      const errorMessage = validateImageFile(file);
      if (errorMessage) {
        setErrors(prev => ({ ...prev, images: errorMessage }));
        return false;
      }
      return true;
    });

    // Add new files to existing images
    if (validFiles.length > 0) {
      const updatedImages = [...formData.images, ...validFiles];
      updateField('images', updatedImages);
      
      // Clear error if we now have images
      if (errors.images && updatedImages.length > 0) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.images;
          return newErrors;
        });
      }
    }
  };

  // Remove image
  const removeImage = (index: number) => {
    const updatedImages = formData.images.filter((_, i) => i !== index);
    updateField('images', updatedImages);

    // Set error if no images left
    if (updatedImages.length === 0) {
      setErrors(prev => ({ ...prev, images: 'At least one image is required' }));
    }
  };

  // Navigate between form steps
  const goToNextStep = () => {
    if (currentStep < 3) {
      // Validate current step before proceeding
      const stepErrors = validateCurrentStep(formData, currentStep);
      setErrors(stepErrors);
      
      if (Object.keys(stepErrors).length === 0) {
        setCurrentStep(currentStep + 1);
        window.scrollTo(0, 0);
      }
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const formErrors = validateForm(formData);
    setErrors(formErrors);

    if (Object.keys(formErrors).length > 0) {
      // If validation fails, go to the step with errors
      if (formErrors.title || formErrors.description || formErrors.category || formErrors.location) {
        setCurrentStep(1);
      } else if (formErrors.startingPrice || formErrors.reservePrice || formErrors.bidIncrement || formErrors.bidIncrementPercentage) {
        setCurrentStep(2);
      } else if (formErrors.images) {
        setCurrentStep(3);
      }
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // In a real app, this would be an API call to create the auction
      console.log('Auction created:', {
        ...formData,
        startingPrice: parseFloat(formData.startingPrice),
        reservePrice: formData.reservePrice ? parseFloat(formData.reservePrice) : null,
        duration: parseInt(formData.duration),
        bidIncrement: formData.bidIncrementStrategy === 'fixed' ? parseFloat(formData.bidIncrement) : null,
        bidIncrementPercentage: formData.bidIncrementStrategy === 'percentage' ? parseFloat(formData.bidIncrementPercentage) : null,
        antiSnipingTime: formData.enableAntiSniping ? parseInt(formData.antiSnipingTime) : null,
        images: formData.images.map(img => img.name) // In a real app, we would upload these
      });

      // Show success message and redirect
      navigate('/auctions', { state: { success: true, message: 'Auction created successfully!' } });
    } catch (error) {
      console.error('Error creating auction:', error);
      setErrors({ submit: 'Failed to create auction. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    formData,
    errors,
    currentStep,
    formProgress,
    isLoading,
    updateField,
    handleImageUpload,
    removeImage,
    goToNextStep,
    goToPreviousStep,
    handleSubmit
  };
};
