import React from 'react';
import { motion } from 'framer-motion';
import { AuctionFormData, FormErrors, auctionTypes } from '../utils/auctionTypes';

interface ImagesReviewStepProps {
  formData: AuctionFormData;
  errors: FormErrors;
  isLoading: boolean;
  handleImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
  removeImage: (index: number) => void;
  goToPreviousStep: () => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
}

/**
 * Images and Review Step Component
 * Third step in the auction creation process
 */
const ImagesReviewStep: React.FC<ImagesReviewStepProps> = ({ 
  formData, 
  errors, 
  isLoading, 
  handleImageUpload, 
  removeImage, 
  goToPreviousStep, 
  handleSubmit 
}) => {
  const { 
    title, 
    description, 
    mainCategory, 
    subCategory, 
    location, 
    auctionType, 
    startingPrice, 
    reservePrice, 
    duration, 
    images 
  } = formData;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="space-y-4"
    >
      <h2 className="text-xl font-semibold mb-4">Images & Review</h2>

      {/* Images */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Images <span className="text-red-500">*</span>
        </label>

        <div className={`border-2 border-dashed rounded-lg p-4 text-center ${errors.images ? 'border-red-500' : 'border-gray-300'}`}>
          <input
            type="file"
            id="images"
            onChange={handleImageUpload}
            multiple
            accept="image/*"
            className="hidden"
          />
          <label htmlFor="images" className="cursor-pointer block touch-friendly">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="mt-2 text-sm text-gray-600">Click to upload images</p>
            <p className="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
          </label>
        </div>

        {errors.images && <p className="mt-1 text-sm text-red-500">{errors.images}</p>}

        {/* Image previews */}
        {images.length > 0 && (
          <div className="mt-4">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Uploaded Images ({images.length})</h3>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
              {images.map((image, index) => (
                <div key={index} className="relative group">
                  <img
                    src={URL.createObjectURL(image)}
                    alt={`Preview ${index}`}
                    className="w-full h-24 object-cover rounded-md"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-md"></div>
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity touch-friendly"
                    aria-label="Remove image"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Auction Summary */}
      <div className="bg-gray-50 rounded-lg p-4 mb-6">
        <h3 className="text-lg font-semibold mb-3">Auction Summary</h3>

        <div className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500">Basic Information</h4>
              <div className="mt-2 space-y-2">
                <div>
                  <span className="text-sm font-medium">Title:</span>
                  <p className="text-sm">{title || 'Not provided'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium">Category:</span>
                  <p className="text-sm">{mainCategory || 'Not selected'}{subCategory ? ` > ${subCategory}` : ''}</p>
                </div>
                <div>
                  <span className="text-sm font-medium">Location:</span>
                  <p className="text-sm">{location || 'Not provided'}</p>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-500">Pricing & Settings</h4>
              <div className="mt-2 space-y-2">
                <div>
                  <span className="text-sm font-medium">Auction Type:</span>
                  <p className="text-sm">{auctionTypes.find(type => type.id === auctionType)?.name || 'Standard Auction'}</p>
                </div>
                <div>
                  <span className="text-sm font-medium">Starting Price:</span>
                  <p className="text-sm">${parseFloat(startingPrice || '0').toFixed(2)}</p>
                </div>
                {auctionType === 'reserve' && (
                  <div>
                    <span className="text-sm font-medium">Reserve Price:</span>
                    <p className="text-sm">${parseFloat(reservePrice || '0').toFixed(2)}</p>
                  </div>
                )}
                <div>
                  <span className="text-sm font-medium">Duration:</span>
                  <p className="text-sm">{duration} {parseInt(duration) === 1 ? 'day' : 'days'}</p>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-500">Description</h4>
            <p className="text-sm mt-1 whitespace-pre-wrap">{description || 'No description provided'}</p>
          </div>
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between mt-6">
        <button
          type="button"
          onClick={goToPreviousStep}
          className="px-6 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 transition-colors touch-friendly font-medium border border-gray-400"
        >
          Back
        </button>
        <button
          type="submit"
          disabled={isLoading}
          onClick={handleSubmit}
          className={`px-6 py-2 rounded-md text-white font-medium touch-friendly shadow-md ${isLoading ? 'bg-gray-500 cursor-not-allowed' : 'bg-primary hover:bg-primary-dark transition-colors'}`}
        >
          {isLoading ? (
            <span className="flex items-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Creating Auction...
            </span>
          ) : 'Create Auction'}
        </button>
      </div>
    </motion.div>
  );
};

export default ImagesReviewStep;
