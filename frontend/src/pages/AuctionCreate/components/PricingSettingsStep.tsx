import React from 'react';
import { motion } from 'framer-motion';
import { 
  AuctionFormData, 
  FormErrors, 
  auctionTypes, 
  bidIncrementStrategies,
  durationOptions,
  antiSnipingTimeOptions
} from '../utils/auctionTypes';

interface PricingSettingsStepProps {
  formData: AuctionFormData;
  errors: FormErrors;
  updateField: <K extends keyof AuctionFormData>(field: K, value: AuctionFormData[K]) => void;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
}

/**
 * Pricing and Settings Step Component
 * Second step in the auction creation process
 */
const PricingSettingsStep: React.FC<PricingSettingsStepProps> = ({ 
  formData, 
  errors, 
  updateField, 
  goToNextStep, 
  goToPreviousStep 
}) => {
  const { 
    auctionType, 
    startingPrice, 
    reservePrice, 
    duration, 
    bidIncrementStrategy, 
    bidIncrement, 
    bidIncrementPercentage, 
    enableAntiSniping, 
    antiSnipingTime 
  } = formData;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="space-y-4"
    >
      <h2 className="text-xl font-semibold mb-4">Pricing & Settings</h2>

      {/* Auction Type */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Auction Type <span className="text-red-500">*</span>
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {auctionTypes.map((type) => (
            <div
              key={type.id}
              className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                auctionType === type.id
                  ? 'border-primary bg-primary-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => updateField('auctionType', type.id)}
            >
              <div className="flex items-start">
                <div className={`w-5 h-5 rounded-full border flex-shrink-0 mr-2 mt-0.5 flex items-center justify-center ${
                  auctionType === type.id ? 'border-primary' : 'border-gray-300'
                }`}>
                  {auctionType === type.id && (
                    <div className="w-3 h-3 rounded-full bg-primary"></div>
                  )}
                </div>
                <div>
                  <h3 className="font-medium text-gray-800">{type.name}</h3>
                  <p className="text-xs text-gray-500 mt-1">{type.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Pricing */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label htmlFor="startingPrice" className="block text-sm font-medium text-gray-700 mb-1">
            Starting Price ($) <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            id="startingPrice"
            value={startingPrice}
            onChange={(e) => updateField('startingPrice', e.target.value)}
            min="0"
            step="0.01"
            className={`w-full p-2 border rounded-md ${errors.startingPrice ? 'border-red-500' : 'border-gray-300'} touch-friendly`}
            placeholder="0.00"
          />
          {errors.startingPrice && <p className="mt-1 text-sm text-red-500">{errors.startingPrice}</p>}
        </div>

        {/* Reserve Price - Only show for reserve auctions */}
        <div>
          <label htmlFor="reservePrice" className="block text-sm font-medium text-gray-700 mb-1">
            Reserve Price ($) {auctionType === 'reserve' ? <span className="text-red-500">*</span> : <span className="text-xs text-gray-500">(Optional)</span>}
          </label>
          <input
            type="number"
            id="reservePrice"
            value={reservePrice}
            onChange={(e) => updateField('reservePrice', e.target.value)}
            min="0"
            step="0.01"
            className={`w-full p-2 border rounded-md ${errors.reservePrice ? 'border-red-500' : 'border-gray-300'} touch-friendly`}
            placeholder="0.00"
          />
          {errors.reservePrice && <p className="mt-1 text-sm text-red-500">{errors.reservePrice}</p>}
          <p className="mt-1 text-xs text-gray-500">
            {auctionType === 'reserve'
              ? 'Minimum price you\'re willing to accept (required for reserve auctions)'
              : 'Minimum price you\'re willing to accept'}
          </p>
        </div>
      </div>

      {/* Duration */}
      <div className="mb-4">
        <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-1">
          Auction Duration
        </label>
        <select
          id="duration"
          value={duration}
          onChange={(e) => updateField('duration', e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md touch-friendly"
        >
          {durationOptions.map(option => (
            <option key={option.value} value={option.value}>{option.label}</option>
          ))}
        </select>
      </div>

      {/* Bid Increment Strategy */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Bid Increment Strategy
        </label>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {bidIncrementStrategies.map((strategy) => (
            <div
              key={strategy.id}
              className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                bidIncrementStrategy === strategy.id
                  ? 'border-primary bg-primary-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => updateField('bidIncrementStrategy', strategy.id)}
            >
              <div className="flex items-start">
                <div className={`w-5 h-5 rounded-full border flex-shrink-0 mr-2 mt-0.5 flex items-center justify-center ${
                  bidIncrementStrategy === strategy.id ? 'border-primary' : 'border-gray-300'
                }`}>
                  {bidIncrementStrategy === strategy.id && (
                    <div className="w-3 h-3 rounded-full bg-primary"></div>
                  )}
                </div>
                <div>
                  <h3 className="font-medium text-gray-800">{strategy.name}</h3>
                  <p className="text-xs text-gray-500 mt-1">{strategy.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Bid Increment Amount - Show based on selected strategy */}
      {bidIncrementStrategy === 'fixed' && (
        <div className="mb-4">
          <label htmlFor="bidIncrement" className="block text-sm font-medium text-gray-700 mb-1">
            Bid Increment Amount ($) <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            id="bidIncrement"
            value={bidIncrement}
            onChange={(e) => updateField('bidIncrement', e.target.value)}
            min="0.01"
            step="0.01"
            className={`w-full p-2 border rounded-md ${errors.bidIncrement ? 'border-red-500' : 'border-gray-300'} touch-friendly`}
            placeholder="5.00"
          />
          {errors.bidIncrement && <p className="mt-1 text-sm text-red-500">{errors.bidIncrement}</p>}
          <p className="mt-1 text-xs text-gray-500">Each new bid must be at least this amount higher than the current bid</p>
        </div>
      )}

      {bidIncrementStrategy === 'percentage' && (
        <div className="mb-4">
          <label htmlFor="bidIncrementPercentage" className="block text-sm font-medium text-gray-700 mb-1">
            Bid Increment Percentage (%) <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            id="bidIncrementPercentage"
            value={bidIncrementPercentage}
            onChange={(e) => updateField('bidIncrementPercentage', e.target.value)}
            min="0.1"
            step="0.1"
            className={`w-full p-2 border rounded-md ${errors.bidIncrementPercentage ? 'border-red-500' : 'border-gray-300'} touch-friendly`}
            placeholder="5.0"
          />
          {errors.bidIncrementPercentage && <p className="mt-1 text-sm text-red-500">{errors.bidIncrementPercentage}</p>}
          <p className="mt-1 text-xs text-gray-500">Each new bid must be at least this percentage higher than the current bid</p>
        </div>
      )}

      {/* Anti-Sniping Protection */}
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <label htmlFor="enableAntiSniping" className="text-sm font-medium text-gray-700">
            Enable Anti-Sniping Protection
          </label>
          <div className="relative inline-block w-10 mr-2 align-middle select-none">
            <input
              type="checkbox"
              id="enableAntiSniping"
              checked={enableAntiSniping}
              onChange={(e) => updateField('enableAntiSniping', e.target.checked)}
              className="sr-only"
            />
            <div className={`block w-10 h-6 rounded-full transition-colors ${enableAntiSniping ? 'bg-primary' : 'bg-gray-300'}`}></div>
            <div className={`absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform transform ${enableAntiSniping ? 'translate-x-4' : ''}`}></div>
          </div>
        </div>
        <p className="mt-1 text-xs text-gray-500">Automatically extends auction time when bids are placed near the end</p>

        {enableAntiSniping && (
          <div className="mt-3">
            <label htmlFor="antiSnipingTime" className="block text-sm font-medium text-gray-700 mb-1">
              Extension Time (minutes)
            </label>
            <select
              id="antiSnipingTime"
              value={antiSnipingTime}
              onChange={(e) => updateField('antiSnipingTime', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md touch-friendly"
            >
              {antiSnipingTimeOptions.map(option => (
                <option key={option.value} value={option.value}>{option.label}</option>
              ))}
            </select>
            <p className="mt-1 text-xs text-gray-500">Auction will be extended by this amount if a bid is placed in the final minutes</p>
          </div>
        )}
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between mt-6">
        <button
          type="button"
          onClick={goToPreviousStep}
          className="px-6 py-2 bg-gray-300 text-gray-800 rounded-md hover:bg-gray-400 transition-colors touch-friendly font-medium border border-gray-400"
        >
          Back
        </button>
        <button
          type="button"
          onClick={goToNextStep}
          className="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors touch-friendly font-medium shadow-md"
        >
          Next: Images & Review
        </button>
      </div>
    </motion.div>
  );
};

export default PricingSettingsStep;
