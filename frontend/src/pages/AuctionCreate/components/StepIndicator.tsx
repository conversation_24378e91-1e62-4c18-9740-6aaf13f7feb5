import React from 'react';

interface StepIndicatorProps {
  currentStep: number;
  formProgress: number;
}

/**
 * Step Indicator Component
 * Displays the current step and overall progress in the auction creation process
 */
const StepIndicator: React.FC<StepIndicatorProps> = ({ currentStep, formProgress }) => {
  return (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {/* Step indicators */}
        <div className="flex items-center w-full">
          {/* Step 1 */}
          <div className="relative flex flex-col items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center shadow-sm font-semibold ${
              currentStep >= 1 ? 'bg-primary text-white' : 'bg-gray-300 text-gray-700 border border-gray-400'
            }`}>
              1
            </div>
            <div className={`mt-2 text-xs ${currentStep === 1 ? 'text-primary font-semibold' : 'text-gray-700'}`}>
              Basic Info
            </div>
          </div>

          {/* Line between steps */}
          <div className={`flex-1 h-1 mx-2 ${
            currentStep > 1 ? 'bg-primary' : 'bg-gray-300'
          }`}></div>

          {/* Step 2 */}
          <div className="relative flex flex-col items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center shadow-sm font-semibold ${
              currentStep >= 2 ? 'bg-primary text-white' : 'bg-gray-300 text-gray-700 border border-gray-400'
            }`}>
              2
            </div>
            <div className={`mt-2 text-xs ${currentStep === 2 ? 'text-primary font-semibold' : 'text-gray-700'}`}>
              Pricing & Settings
            </div>
          </div>

          {/* Line between steps */}
          <div className={`flex-1 h-1 mx-2 ${
            currentStep > 2 ? 'bg-primary' : 'bg-gray-300'
          }`}></div>

          {/* Step 3 */}
          <div className="relative flex flex-col items-center">
            <div className={`w-10 h-10 rounded-full flex items-center justify-center shadow-sm font-semibold ${
              currentStep >= 3 ? 'bg-primary text-white' : 'bg-gray-300 text-gray-700 border border-gray-400'
            }`}>
              3
            </div>
            <div className={`mt-2 text-xs ${currentStep === 3 ? 'text-primary font-semibold' : 'text-gray-700'}`}>
              Images & Review
            </div>
          </div>
        </div>
      </div>

      {/* Progress bar */}
      <div className="w-full bg-gray-300 rounded-full h-3 mt-6 shadow-inner">
        <div
          className="bg-primary h-3 rounded-full transition-all duration-300 shadow-sm"
          style={{ width: `${formProgress}%` }}
        ></div>
      </div>
    </div>
  );
};

export default StepIndicator;
