import React from 'react';
import { motion } from 'framer-motion';
import CategorySelector from '../../../components/CategorySelector';
import { AuctionFormData, FormErrors } from '../utils/auctionTypes';

interface BasicInfoStepProps {
  formData: AuctionFormData;
  errors: FormErrors;
  updateField: <K extends keyof AuctionFormData>(field: K, value: AuctionFormData[K]) => void;
  goToNextStep: () => void;
}

/**
 * Basic Information Step Component
 * First step in the auction creation process
 */
const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ 
  formData, 
  errors, 
  updateField, 
  goToNextStep 
}) => {
  const { title, description, mainCategory, subCategory, location } = formData;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="space-y-4"
    >
      <h2 className="text-xl font-semibold mb-4">Basic Information</h2>

      {/* Title */}
      <div className="mb-4">
        <label htmlFor="title" className="block text-sm font-medium text-gray-800 mb-1">
          Title <span className="text-red-600">*</span>
        </label>
        <input
          type="text"
          id="title"
          value={title}
          onChange={(e) => updateField('title', e.target.value)}
          className={`w-full p-2 border rounded-md ${errors.title ? 'border-red-500' : 'border-gray-400'} touch-friendly focus:border-primary focus:ring-1 focus:ring-primary`}
          placeholder="Enter a descriptive title"
        />
        {errors.title && <p className="mt-1 text-sm text-red-600 font-medium">{errors.title}</p>}
      </div>

      {/* Description */}
      <div className="mb-4">
        <label htmlFor="description" className="block text-sm font-medium text-gray-800 mb-1">
          Description <span className="text-red-600">*</span>
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => updateField('description', e.target.value)}
          rows={4}
          className={`w-full p-2 border rounded-md ${errors.description ? 'border-red-500' : 'border-gray-400'} touch-friendly focus:border-primary focus:ring-1 focus:ring-primary`}
          placeholder="Provide a detailed description of the item"
        />
        {errors.description && <p className="mt-1 text-sm text-red-600 font-medium">{errors.description}</p>}
      </div>

      {/* Category */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-800 mb-1">
          Category <span className="text-red-600">*</span>
        </label>
        <CategorySelector
          selectedMainCategory={mainCategory}
          selectedSubCategory={subCategory}
          onMainCategoryChange={(category) => updateField('mainCategory', category)}
          onSubCategoryChange={(category) => updateField('subCategory', category)}
          allowSuggestion={true}
        />
        {errors.category && <p className="mt-1 text-sm text-red-600 font-medium">{errors.category}</p>}
      </div>

      {/* Location */}
      <div className="mb-4">
        <label htmlFor="location" className="block text-sm font-medium text-gray-800 mb-1">
          Location <span className="text-red-600">*</span>
        </label>
        <input
          type="text"
          id="location"
          value={location}
          onChange={(e) => updateField('location', e.target.value)}
          className={`w-full p-2 border rounded-md ${errors.location ? 'border-red-500' : 'border-gray-400'} touch-friendly focus:border-primary focus:ring-1 focus:ring-primary`}
          placeholder="Enter item location"
        />
        {errors.location && <p className="mt-1 text-sm text-red-600 font-medium">{errors.location}</p>}
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-end mt-6">
        <button
          type="button"
          onClick={goToNextStep}
          className="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors touch-friendly font-medium shadow-md"
        >
          Next: Pricing & Settings
        </button>
      </div>
    </motion.div>
  );
};

export default BasicInfoStep;
