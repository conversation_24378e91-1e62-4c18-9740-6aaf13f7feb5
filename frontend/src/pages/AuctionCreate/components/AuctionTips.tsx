import React from 'react';
import { motion } from 'framer-motion';

interface AuctionTipsProps {
  currentStep: number;
}

/**
 * Auction Tips Component
 * Displays helpful tips based on the current step in the auction creation process
 */
const AuctionTips: React.FC<AuctionTipsProps> = ({ currentStep }) => {
  return (
    <motion.div
      className="mt-8 bg-blue-100 border border-blue-300 rounded-lg p-5 shadow-sm"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    >
      <h3 className="text-lg font-semibold text-blue-900 mb-3">Auction Tips</h3>

      {currentStep === 1 && (
        <ul className="list-disc list-inside text-sm text-blue-800 space-y-2">
          <li>Use a clear, descriptive title that includes key item details</li>
          <li>Be specific about the item's condition, age, and any unique features</li>
          <li>Choose the most accurate category to help buyers find your item</li>
          <li>Include your general location to help with local pickup options</li>
          <li>Be honest about any flaws or imperfections</li>
        </ul>
      )}

      {currentStep === 2 && (
        <ul className="list-disc list-inside text-sm text-blue-800 space-y-2">
          <li>Set a competitive starting price to attract initial bids</li>
          <li>Consider setting a reserve price for valuable items</li>
          <li>Choose the auction type that best fits your item and goals</li>
          <li>Standard auctions work well for most items</li>
          <li>Longer durations give more potential bidders a chance to see your auction</li>
          <li>Enable anti-sniping to prevent last-second bidding tactics</li>
        </ul>
      )}

      {currentStep === 3 && (
        <ul className="list-disc list-inside text-sm text-blue-800 space-y-2">
          <li>Use high-quality images that clearly show the item's condition</li>
          <li>Include multiple angles and close-ups of important details</li>
          <li>Ensure good lighting to showcase the item accurately</li>
          <li>Review all information before submitting to ensure accuracy</li>
          <li>Respond promptly to questions from potential bidders once your auction is live</li>
        </ul>
      )}
    </motion.div>
  );
};

export default AuctionTips;
