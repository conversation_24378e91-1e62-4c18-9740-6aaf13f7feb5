import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useMediaQuery } from '../../hooks/useMediaQuery';

// Custom hooks
import { useAuctionForm } from './hooks/useAuctionForm';

// Components
import StepIndicator from './components/StepIndicator';
import BasicInfoStep from './components/BasicInfoStep';
import PricingSettingsStep from './components/PricingSettingsStep';
import ImagesReviewStep from './components/ImagesReviewStep';
import AuctionTips from './components/AuctionTips';

/**
 * Auction Create Page
 * Allows users to create a new auction with a multi-step form
 * 
 * This file has been refactored according to the May 2025 code optimization guidelines
 * to improve maintainability and performance.
 */
const AuctionCreate: React.FC = () => {
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isTablet = useMediaQuery('(min-width: 641px) and (max-width: 1024px)');
  
  const {
    formData,
    errors,
    currentStep,
    formProgress,
    isLoading,
    updateField,
    handleImageUpload,
    removeImage,
    goToNextStep,
    goToPreviousStep,
    handleSubmit
  } = useAuctionForm();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Create Auction</h1>
        <Link
          to="/auctions"
          className="text-primary hover:text-primary-dark transition-colors font-medium flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
          </svg>
          Back to Auctions
        </Link>
      </div>

      {/* Step indicator */}
      <StepIndicator currentStep={currentStep} formProgress={formProgress} />

      <motion.div
        className="bg-white rounded-lg shadow-lg p-5 md:p-7 border border-gray-200"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <form onSubmit={(e) => { e.preventDefault(); handleSubmit(e); }}>
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <BasicInfoStep
              formData={formData}
              errors={errors}
              updateField={updateField}
              goToNextStep={goToNextStep}
            />
          )}

          {/* Step 2: Pricing & Settings */}
          {currentStep === 2 && (
            <PricingSettingsStep
              formData={formData}
              errors={errors}
              updateField={updateField}
              goToNextStep={goToNextStep}
              goToPreviousStep={goToPreviousStep}
            />
          )}

          {/* Step 3: Images & Review */}
          {currentStep === 3 && (
            <ImagesReviewStep
              formData={formData}
              errors={errors}
              isLoading={isLoading}
              handleImageUpload={handleImageUpload}
              removeImage={removeImage}
              goToPreviousStep={goToPreviousStep}
              handleSubmit={handleSubmit}
            />
          )}

          {/* General error message */}
          {errors.submit && (
            <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded-md shadow-sm">
              <p className="text-sm text-red-700 font-medium">{errors.submit}</p>
            </div>
          )}
        </form>
      </motion.div>

      {/* Auction Tips */}
      <AuctionTips currentStep={currentStep} />
    </div>
  );
};

export default AuctionCreate;
