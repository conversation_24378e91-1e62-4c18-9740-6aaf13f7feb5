# VisualizationPage Component

A comprehensive visualization page that showcases AI recommendation insights through interactive visualizations. This component provides users with detailed views of their preferences, item relationships, and personalized recommendations.

## Overview

The VisualizationPage component has been refactored from a single 654-line file into a modular architecture following the May 2025 code optimization guidelines. Each file is now focused on a specific responsibility and maintains 50-200 lines for optimal maintainability.

## Architecture

```
VisualizationPage/
├── index.tsx                           # Main component (80 lines)
├── components/                         # UI Components
│   ├── TabNavigation.tsx               # Tab navigation with keyboard support (80 lines)
│   ├── TabContent.tsx                  # Tab content management (60 lines)
│   ├── AuthenticationPrompt.tsx        # Unauthenticated user prompt (30 lines)
│   ├── InformationSection.tsx          # How recommendations work info (60 lines)
│   ├── FAQSection.tsx                  # Frequently asked questions (30 lines)
│   ├── VisualizationControls.tsx       # Control buttons for visualizations (60 lines)
│   └── tabs/                           # Individual tab components
│       ├── OverviewTab.tsx             # Overview tab content (50 lines)
│       ├── PreferencesTab.tsx          # Preferences visualization tab (40 lines)
│       ├── RelationshipsTab.tsx        # Item relationships tab (40 lines)
│       ├── RecommendationsTab.tsx      # Recommendations tab (20 lines)
│       ├── DashboardTab.tsx            # Dashboard tab (20 lines)
│       └── ComparisonTab.tsx           # Comparison tab (20 lines)
├── hooks/                              # Custom Hooks
│   ├── useTabManagement.ts             # Tab state and URL management (80 lines)
│   └── useAccessibility.ts             # Accessibility features (40 lines)
├── utils/                              # Utility Functions
│   ├── types.ts                        # TypeScript interfaces (120 lines)
│   └── constants.ts                    # Configuration constants (150 lines)
└── README.md                           # Component documentation (300 lines)
```

## Features

### Core Functionality
- **Multi-tab Interface**: Six different visualization views with smooth transitions
- **URL Synchronization**: Browser URL updates with tab changes and supports back/forward navigation
- **Keyboard Navigation**: Full keyboard accessibility with arrow key navigation
- **Authentication Handling**: Different experiences for authenticated and unauthenticated users
- **Responsive Design**: Works seamlessly on all device sizes

### Visualization Tabs
- **Overview**: General introduction with demo and navigation links
- **Preferences**: Interactive visualization of user preferences and behavior patterns
- **Relationships**: Item relationship analysis using embedding visualizations
- **Recommendations**: Personalized recommendations with explanations
- **Dashboard**: Comprehensive dashboard combining all insights
- **Comparison**: Historical comparison of preferences and recommendations

### Accessibility Features
- **Screen Reader Support**: Live region announcements for tab changes
- **Keyboard Navigation**: Full keyboard support with proper focus management
- **ARIA Attributes**: Comprehensive ARIA labeling for assistive technologies
- **Focus Management**: Proper focus handling during tab transitions

## Components

### Main Component (`index.tsx`)
The orchestrating component that brings together all sub-components and hooks.

**Features:**
- Centralized state management
- Authentication checking
- SEO optimization with Helmet
- Accessibility support

### TabNavigation Component
Provides accessible tab navigation with visual indicators and keyboard support.

**Features:**
- Dynamic tab generation from configuration
- Keyboard arrow key navigation
- Visual active state indicators
- ARIA compliance

### TabContent Component
Manages the rendering of different tab content with smooth animations.

**Features:**
- Animated tab transitions
- Lazy loading of tab content
- Proper ARIA panel attributes
- Responsive content handling

### Individual Tab Components
Each tab is implemented as a separate component for better organization:

- **OverviewTab**: Demo and navigation links
- **PreferencesTab**: Enhanced preference visualization with controls
- **RelationshipsTab**: Interactive embedding visualization
- **RecommendationsTab**: Personalized recommendation display
- **DashboardTab**: Comprehensive visualization dashboard
- **ComparisonTab**: Historical comparison views

### AuthenticationPrompt Component
Handles the experience for unauthenticated users.

**Features:**
- Clear call-to-action for sign-in
- Explanation of personalized features
- Accessible design

### InformationSection Component
Displays educational content about the recommendation system.

**Features:**
- How recommendations work explanation
- Privacy and data handling information
- Links to privacy policy

### FAQSection Component
Provides answers to frequently asked questions.

**Features:**
- Common user questions
- Clear, helpful answers
- Easy to maintain content

## Custom Hooks

### useTabManagement
Manages tab state, URL synchronization, and keyboard navigation.

**Returns:**
- `activeTab`: Current active tab
- `handleTabChange`: Function to change tabs
- `tabRefs`: Refs for keyboard navigation

**Features:**
- URL synchronization
- Browser history support
- Keyboard navigation handling
- Initial tab detection from URL

### useAccessibility
Manages accessibility features including screen reader announcements.

**Returns:**
- `screenReaderMessage`: Current message for screen readers

**Features:**
- Automatic announcements for tab changes
- Timed message clearing
- Screen reader optimization

## Utilities

### types.ts
Comprehensive TypeScript interfaces for type safety.

**Includes:**
- `TabType`: Tab identifier union type
- `TabConfig`: Tab configuration interface
- `FAQItem`: FAQ item structure
- Component prop interfaces
- Hook return type interfaces

### constants.ts
Configuration constants and mappings.

**Includes:**
- Tab configuration with icons and labels
- FAQ items content
- Animation configuration
- URL path mappings
- Accessibility settings
- CSS class constants

## Usage

```tsx
import VisualizationPage from './pages/VisualizationPage';

function App() {
  return (
    <div>
      <VisualizationPage />
    </div>
  );
}
```

## Development

### Testing
Each component and hook should be tested individually:
- Unit tests for utility functions
- Component tests for UI interactions
- Integration tests for hook behavior
- E2E tests for complete workflows
- Accessibility tests for WCAG compliance

### Performance Considerations
- Lazy loading of visualization components
- Optimized tab switching with animations
- Efficient re-rendering with proper memoization
- Responsive image loading

### Accessibility
- All interactive elements have proper ARIA labels
- Keyboard navigation support throughout
- Screen reader announcements for state changes
- High contrast mode compatibility
- Focus management during transitions

## Migration Notes

This refactored version maintains full backward compatibility with the original VisualizationPage component. The API remains the same, but the internal structure is now modular and maintainable.

### Benefits of Refactoring
1. **Improved Maintainability**: Smaller, focused files are easier to understand and modify
2. **Better Testing**: Individual components and hooks can be tested in isolation
3. **Enhanced Reusability**: Components can be reused in other visualization features
4. **Improved Performance**: Better code splitting and optimized rendering
5. **Developer Experience**: Faster IDE loading and better IntelliSense support
6. **Accessibility**: Enhanced accessibility features and compliance

### Breaking Changes
None. The component maintains the same public API as the original implementation.
