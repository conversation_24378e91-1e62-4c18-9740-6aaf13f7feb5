import { useState, useEffect } from 'react';
import { TabType } from '../utils/types';

/**
 * useAccessibility Hook
 * 
 * Manages accessibility features including screen reader announcements
 * and focus management for the visualization page.
 */
export const useAccessibility = (activeTab: TabType) => {
  const [screenReaderMessage, setScreenReaderMessage] = useState<string>('');

  // Update screen reader message when tab changes
  useEffect(() => {
    const tabLabel = activeTab.charAt(0).toUpperCase() + activeTab.slice(1);
    setScreenReaderMessage(`${tabLabel} tab selected`);
  }, [activeTab]);

  // Clear screen reader message after announcement
  useEffect(() => {
    if (screenReaderMessage) {
      const timer = setTimeout(() => {
        setScreenReaderMessage('');
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [screenReaderMessage]);

  return {
    screenReaderMessage
  };
};
