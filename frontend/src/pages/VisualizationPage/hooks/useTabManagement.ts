import { useState, useRef, useEffect } from 'react';
import { TabType } from '../utils/types';

/**
 * useTabManagement Hook
 * 
 * Manages tab state, URL synchronization, and keyboard navigation.
 * Provides centralized tab management functionality.
 */
export const useTabManagement = () => {
  // Determine initial tab based on URL
  const getInitialTab = (): TabType => {
    const location = window.location.pathname;
    if (location.includes('/visualization/preferences')) return 'preferences';
    if (location.includes('/visualization/relationships')) return 'relationships';
    if (location.includes('/visualization/recommendations')) return 'recommendations';
    if (location.includes('/visualization/dashboard')) return 'dashboard';
    if (location.includes('/visualization/comparison')) return 'comparison';
    return 'overview';
  };

  // State for active tab
  const [activeTab, setActiveTab] = useState<TabType>(getInitialTab());

  // Refs for tab elements
  const tabRefs = useRef<Record<TabType, HTMLButtonElement | null>>({
    overview: null,
    preferences: null,
    relationships: null,
    recommendations: null,
    dashboard: null,
    comparison: null
  });

  // Handle tab change
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);

    // Update URL without page reload
    window.history.pushState(
      {},
      '',
      tab === 'overview' ? '/visualization' : `/visualization/${tab}`
    );
  };

  // Effect to handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only handle keyboard navigation when a tab is focused
      if (document.activeElement?.getAttribute('role') !== 'tab') return;

      // Handle arrow key navigation
      if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
        e.preventDefault();

        const tabOrder: TabType[] = ['overview', 'preferences', 'relationships', 'recommendations', 'dashboard', 'comparison'];
        const currentIndex = tabOrder.indexOf(activeTab);

        if (e.key === 'ArrowRight') {
          const nextIndex = (currentIndex + 1) % tabOrder.length;
          handleTabChange(tabOrder[nextIndex]);
          tabRefs.current[tabOrder[nextIndex]]?.focus();
        } else if (e.key === 'ArrowLeft') {
          const prevIndex = (currentIndex - 1 + tabOrder.length) % tabOrder.length;
          handleTabChange(tabOrder[prevIndex]);
          tabRefs.current[tabOrder[prevIndex]]?.focus();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [activeTab]);

  // Handle browser back/forward navigation
  useEffect(() => {
    const handlePopState = () => {
      setActiveTab(getInitialTab());
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  return {
    activeTab,
    handleTabChange,
    tabRefs
  };
};
