import React from 'react';
import { Helmet } from 'react-helmet-async';
import { TabNavigation } from './components/TabNavigation';
import { TabContent } from './components/TabContent';
import { AuthenticationPrompt } from './components/AuthenticationPrompt';
import { InformationSection } from './components/InformationSection';
import { FAQSection } from './components/FAQSection';
import { useAuth } from '../../hooks/useAuth';
import { useTabManagement } from './hooks/useTabManagement';
import { useAccessibility } from './hooks/useAccessibility';
import { LiveRegion } from '../../utils/accessibility';

/**
 * VisualizationPage Component
 * 
 * Main page component that showcases AI recommendation visualization components
 * and provides information about how the recommendation system works.
 * Includes interactive visualizations for preferences, item relationships, and recommendations.
 */
const VisualizationPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  
  // Tab management hook
  const {
    activeTab,
    handleTabChange,
    tabRefs
  } = useTabManagement();
  
  // Accessibility hook
  const { screenReaderMessage } = useAccessibility(activeTab);
  
  return (
    <>
      <Helmet>
        <title>AI Recommendation Insights | RentUp</title>
        <meta
          name="description"
          content="Explore how RentUp's AI recommendation system works with interactive visualizations of your preferences and item relationships."
        />
      </Helmet>

      {/* Screen reader announcements */}
      <LiveRegion message={screenReaderMessage} />

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            AI Recommendation Insights
          </h1>
          <p className="text-lg text-gray-600">
            Explore how our AI recommendation system works to provide you with personalized suggestions.
          </p>
        </div>

        {/* Authentication Check */}
        {!isAuthenticated ? (
          <AuthenticationPrompt />
        ) : (
          <div>
            {/* Tab Navigation */}
            <TabNavigation
              activeTab={activeTab}
              onTabChange={handleTabChange}
              tabRefs={tabRefs}
            />

            {/* Tab Content */}
            <TabContent
              activeTab={activeTab}
              user={user}
              onTabChange={handleTabChange}
            />
          </div>
        )}

        {/* Information Sections */}
        <InformationSection />

        {/* FAQ Section */}
        <FAQSection />
      </div>
    </>
  );
};

export default VisualizationPage;
