import { TabConfig, FAQItem, AnimationConfig, URLConfig, AccessibilityConfig, PageConfig } from './types';

/**
 * Constants for the VisualizationPage component
 */

/**
 * Tab configuration with icons and labels
 */
export const TAB_CONFIG: Record<string, TabConfig> = {
  overview: {
    label: 'Overview',
    icon: 'M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z',
    description: 'General overview of AI recommendation insights',
    ariaLabel: 'Overview of recommendation system'
  },
  preferences: {
    label: 'Your Preferences',
    icon: 'M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z',
    description: 'Visualization of your personal preferences',
    ariaLabel: 'Your preference analysis and visualization'
  },
  relationships: {
    label: 'Item Relationships',
    icon: 'M7 3l-4 4m0 0l4 4m-4-4h18M3 16l4 4m0 0l4-4m-4 4V7',
    description: 'How items relate to each other',
    ariaLabel: 'Item relationship analysis and visualization'
  },
  recommendations: {
    label: 'Recommendations',
    icon: 'M13 10V3L4 14h7v7l9-11h-7z',
    description: 'Your personalized recommendations',
    ariaLabel: 'Personalized recommendation insights'
  },
  dashboard: {
    label: 'Dashboard',
    icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
    description: 'Comprehensive visualization dashboard',
    ariaLabel: 'Comprehensive recommendation dashboard'
  },
  comparison: {
    label: 'Comparison',
    icon: 'M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2',
    description: 'Compare visualizations over time',
    ariaLabel: 'Visualization comparison and trends'
  }
};

/**
 * FAQ items for the page
 */
export const FAQ_ITEMS: FAQItem[] = [
  {
    question: 'How can I improve my recommendations?',
    answer: 'The more you interact with items on RentUp, the better our recommendations become. Like items you\'re interested in, create favorites, and browse categories you enjoy.'
  },
  {
    question: 'Can I reset my recommendation preferences?',
    answer: 'Yes, you can reset your recommendation preferences in your account settings. This will clear your preference data and start fresh.'
  },
  {
    question: 'Why am I seeing certain recommendations?',
    answer: 'The visualizations on this page help explain why you\'re seeing certain recommendations. Your preferences, browsing history, and similar users all influence what you see.'
  },
  {
    question: 'How often are recommendations updated?',
    answer: 'Your recommendations are updated in real-time as you interact with the platform. The system continuously learns from your behavior to improve suggestions.'
  }
];

/**
 * Animation configuration
 */
export const ANIMATION_CONFIG: AnimationConfig = {
  duration: 0.3,
  easing: 'ease-in-out'
};

/**
 * URL configuration
 */
export const URL_CONFIG: URLConfig = {
  basePath: '/visualization',
  tabPaths: {
    overview: '/visualization',
    preferences: '/visualization/preferences',
    relationships: '/visualization/relationships',
    recommendations: '/visualization/recommendations',
    dashboard: '/visualization/dashboard',
    comparison: '/visualization/comparison'
  }
};

/**
 * Accessibility configuration
 */
export const ACCESSIBILITY_CONFIG: AccessibilityConfig = {
  announceDelay: 1000,
  focusDelay: 100
};

/**
 * Page configuration
 */
export const PAGE_CONFIG: PageConfig = {
  title: 'AI Recommendation Insights | RentUp',
  description: 'Explore how RentUp\'s AI recommendation system works with interactive visualizations of your preferences and item relationships.',
  keywords: ['AI', 'recommendations', 'visualization', 'preferences', 'machine learning', 'RentUp']
};

/**
 * Tab order for keyboard navigation
 */
export const TAB_ORDER = ['overview', 'preferences', 'relationships', 'recommendations', 'dashboard', 'comparison'] as const;

/**
 * Default visualization limits
 */
export const VISUALIZATION_LIMITS = {
  preferences: 100,
  embedding: 50,
  recommendations: 6
} as const;

/**
 * Export formats
 */
export const EXPORT_FORMATS = ['csv', 'json', 'png', 'svg'] as const;

/**
 * Share platforms
 */
export const SHARE_PLATFORMS = ['twitter', 'facebook', 'linkedin', 'email', 'copy'] as const;

/**
 * CSS classes for consistent styling
 */
export const CSS_CLASSES = {
  tabButton: 'py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200',
  tabButtonActive: 'border-primary-600 text-primary-600',
  tabButtonInactive: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
  tabPanel: 'mb-8',
  card: 'bg-white rounded-lg shadow-md p-6',
  button: 'px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2',
  primaryButton: 'inline-block bg-primary text-white px-6 py-2 rounded-md font-medium hover:bg-primary-dark transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2',
  secondaryButton: 'inline-block bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2'
} as const;
