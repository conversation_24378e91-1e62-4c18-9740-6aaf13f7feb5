/**
 * TypeScript interfaces and types for the VisualizationPage component
 */

/**
 * Tab types for the visualization page
 */
export type TabType = 'overview' | 'preferences' | 'relationships' | 'recommendations' | 'dashboard' | 'comparison';

/**
 * Tab configuration interface
 */
export interface TabConfig {
  label: string;
  icon: string;
  description: string;
  ariaLabel: string;
}

/**
 * FAQ item interface
 */
export interface FAQItem {
  question: string;
  answer: string;
}

/**
 * Visualization control type
 */
export type VisualizationControlType = 'preferences' | 'embedding';

/**
 * Component props interfaces
 */
export interface TabNavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  tabRefs: React.MutableRefObject<Record<TabType, HTMLButtonElement | null>>;
}

export interface TabContentProps {
  activeTab: TabType;
  user: any;
  onTabChange: (tab: TabType) => void;
}

export interface OverviewTabProps {
  onTabChange: (tab: TabType) => void;
}

export interface PreferencesTabProps {
  user: any;
}

export interface VisualizationControlsProps {
  type: VisualizationControlType;
}

/**
 * Hook return types
 */
export interface UseTabManagementReturn {
  activeTab: TabType;
  handleTabChange: (tab: TabType) => void;
  tabRefs: React.MutableRefObject<Record<TabType, HTMLButtonElement | null>>;
}

export interface UseAccessibilityReturn {
  screenReaderMessage: string;
}

/**
 * Animation configuration
 */
export interface AnimationConfig {
  duration: number;
  easing: string;
}

/**
 * URL configuration
 */
export interface URLConfig {
  basePath: string;
  tabPaths: Record<TabType, string>;
}

/**
 * Accessibility configuration
 */
export interface AccessibilityConfig {
  announceDelay: number;
  focusDelay: number;
}

/**
 * Page configuration
 */
export interface PageConfig {
  title: string;
  description: string;
  keywords: string[];
}

/**
 * Visualization data types
 */
export interface VisualizationData {
  id: string;
  type: string;
  data: any;
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    version: string;
  };
}

/**
 * Export configuration
 */
export interface ExportConfig {
  format: 'csv' | 'json' | 'png' | 'svg';
  filename: string;
  includeMetadata: boolean;
}

/**
 * Share configuration
 */
export interface ShareConfig {
  platform: 'twitter' | 'facebook' | 'linkedin' | 'email' | 'copy';
  title: string;
  description: string;
  url: string;
}
