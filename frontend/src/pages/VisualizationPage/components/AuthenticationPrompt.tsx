import React from 'react';
import { Link } from 'react-router-dom';

/**
 * AuthenticationPrompt Component
 * 
 * Displays a prompt for unauthenticated users to sign in
 * to access personalized visualization features.
 */
export const AuthenticationPrompt: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-8">
      <h2 className="text-xl font-semibold mb-4">
        Sign In to See Your Personalized Insights
      </h2>
      <p className="text-gray-600 mb-4">
        To view your personalized recommendation insights, please sign in to your account.
        This will allow us to show you how your preferences influence the recommendations you receive.
      </p>
      <Link
        to="/login"
        className="inline-block bg-primary text-white px-4 py-2 rounded-md font-medium hover:bg-primary-dark transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
      >
        Sign In
      </Link>
    </div>
  );
};
