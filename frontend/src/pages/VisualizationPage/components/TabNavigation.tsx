import React from 'react';
import { TabType } from '../utils/types';
import { TAB_CONFIG } from '../utils/constants';

/**
 * TabNavigation Component
 * 
 * Provides accessible tab navigation with keyboard support and visual indicators.
 * Handles tab switching and maintains proper ARIA attributes for accessibility.
 */
interface TabNavigationProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
  tabRefs: React.MutableRefObject<Record<TabType, HTMLButtonElement | null>>;
}

export const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  onTabChange,
  tabRefs
}) => {
  const handleKeyDown = (e: React.KeyboardEvent, tab: TabType) => {
    const tabOrder: TabType[] = ['overview', 'preferences', 'relationships', 'recommendations', 'dashboard', 'comparison'];
    const currentIndex = tabOrder.indexOf(tab);

    if (e.key === 'ArrowRight') {
      e.preventDefault();
      const nextIndex = (currentIndex + 1) % tabOrder.length;
      const nextTab = tabOrder[nextIndex];
      onTabChange(nextTab);
      tabRefs.current[nextTab]?.focus();
    } else if (e.key === 'ArrowLeft') {
      e.preventDefault();
      const prevIndex = (currentIndex - 1 + tabOrder.length) % tabOrder.length;
      const prevTab = tabOrder[prevIndex];
      onTabChange(prevTab);
      tabRefs.current[prevTab]?.focus();
    }
  };

  return (
    <div className="mb-8 border-b border-gray-200">
      <nav
        className="flex flex-wrap space-x-4 md:space-x-8"
        aria-label="Visualization tabs"
        role="tablist"
      >
        {Object.entries(TAB_CONFIG).map(([tabKey, config]) => {
          const tab = tabKey as TabType;
          const isActive = activeTab === tab;
          
          return (
            <button
              key={tab}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                isActive
                  ? 'border-primary-600 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => onTabChange(tab)}
              onKeyDown={(e) => handleKeyDown(e, tab)}
              aria-current={isActive ? 'page' : undefined}
              aria-selected={isActive}
              role="tab"
              tabIndex={isActive ? 0 : -1}
              id={`tab-${tab}`}
              aria-controls={`panel-${tab}`}
              data-testid={`visualization-tab-${tab}`}
              ref={(el) => (tabRefs.current[tab] = el)}
            >
              <span className="flex items-center">
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  className="h-4 w-4 mr-1" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d={config.icon} 
                  />
                </svg>
                {config.label}
              </span>
            </button>
          );
        })}
      </nav>
    </div>
  );
};
