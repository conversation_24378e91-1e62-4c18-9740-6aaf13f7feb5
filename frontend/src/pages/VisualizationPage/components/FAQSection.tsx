import React from 'react';
import { FAQ_ITEMS } from '../utils/constants';

/**
 * FAQSection Component
 * 
 * Displays frequently asked questions about the recommendation system.
 * Provides helpful information to users about how recommendations work.
 */
export const FAQSection: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-4">Frequently Asked Questions</h2>
      <div className="space-y-4">
        {FAQ_ITEMS.map((faq, index) => (
          <div key={index}>
            <h3 className="font-medium text-gray-900">{faq.question}</h3>
            <p className="text-gray-600">{faq.answer}</p>
          </div>
        ))}
      </div>
    </div>
  );
};
