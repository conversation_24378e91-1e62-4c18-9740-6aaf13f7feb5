import React from 'react';
import { motion } from 'framer-motion';
import { TabType } from '../utils/types';
import { OverviewTab } from './tabs/OverviewTab';
import { PreferencesTab } from './tabs/PreferencesTab';
import { RelationshipsTab } from './tabs/RelationshipsTab';
import { RecommendationsTab } from './tabs/RecommendationsTab';
import { DashboardTab } from './tabs/DashboardTab';
import { ComparisonTab } from './tabs/ComparisonTab';

/**
 * TabContent Component
 * 
 * Manages the rendering of different tab content with smooth animations.
 * Each tab is rendered as a separate component for better organization.
 */
interface TabContentProps {
  activeTab: TabType;
  user: any;
  onTabChange: (tab: TabType) => void;
}

export const TabContent: React.FC<TabContentProps> = ({
  activeTab,
  user,
  onTabChange
}) => {
  const renderTabContent = (tab: TabType) => {
    switch (tab) {
      case 'overview':
        return <OverviewTab onTabChange={onTabChange} />;
      case 'preferences':
        return <PreferencesTab user={user} />;
      case 'relationships':
        return <RelationshipsTab />;
      case 'recommendations':
        return <RecommendationsTab />;
      case 'dashboard':
        return <DashboardTab />;
      case 'comparison':
        return <ComparisonTab />;
      default:
        return null;
    }
  };

  return (
    <div className="mb-8">
      {(['overview', 'preferences', 'relationships', 'recommendations', 'dashboard', 'comparison'] as TabType[]).map((tab) => (
        <motion.div
          key={tab}
          initial={{ opacity: 0 }}
          animate={{ opacity: activeTab === tab ? 1 : 0 }}
          transition={{ duration: 0.3 }}
          className={`${activeTab !== tab ? 'hidden' : ''}`}
          data-testid={`visualization-content-${tab}`}
          role="tabpanel"
          id={`panel-${tab}`}
          aria-labelledby={`tab-${tab}`}
          tabIndex={0}
        >
          {activeTab === tab && renderTabContent(tab)}
        </motion.div>
      ))}
    </div>
  );
};
