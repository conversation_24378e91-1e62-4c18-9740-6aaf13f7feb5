import React from 'react';
import { InteractiveEmbeddingVisualization } from '../../../../components/Visualization';
import { VisualizationControls } from '../VisualizationControls';

/**
 * RelationshipsTab Component
 * 
 * Displays the relationships tab content with interactive embedding visualization
 * and controls for data management.
 */
export const RelationshipsTab: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-6">Item Relationship Analysis</h2>
      <p className="text-gray-600 mb-6">
        This visualization shows how items are related to each other based on their features.
        Items that are closer together are more similar. The colors represent different categories.
      </p>

      <InteractiveEmbeddingVisualization
        testId="visualization-page-embedding-viz"
        limit={50}
      />

      {/* Additional controls for embedding visualization */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <h3 className="text-lg font-medium mb-3">Visualization Controls</h3>
        <VisualizationControls type="embedding" />
      </div>
    </div>
  );
};
