import React from 'react';
import { EnhancedPreferenceVisualization } from '../../../../components/Visualization';
import { useIsMobile } from '../../../../hooks/useMediaQuery';
import { VisualizationControls } from '../VisualizationControls';

/**
 * PreferencesTab Component
 * 
 * Displays the preferences tab content with enhanced preference visualization
 * and controls for data management.
 */
interface PreferencesTabProps {
  user: any;
}

export const PreferencesTab: React.FC<PreferencesTabProps> = ({ user }) => {
  const isMobile = useIsMobile();

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-6">Your Preference Analysis</h2>
      <p className="text-gray-600 mb-6">
        This visualization shows how your preferences are tracked based on your interactions.
        Items you've liked, favorited, or rented have higher weights and influence your recommendations more.
      </p>

      <EnhancedPreferenceVisualization
        userId={user?.id}
        testId="visualization-page-preference-viz"
        compact={isMobile}
        interactive={true}
      />

      {/* Additional controls for preference visualization */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <h3 className="text-lg font-medium mb-3">Visualization Controls</h3>
        <VisualizationControls type="preferences" />
      </div>
    </div>
  );
};
