import React from 'react';
import VisualizationDemo from '../../../../components/Visualization/VisualizationDemo';
import { TabType } from '../../utils/types';

/**
 * OverviewTab Component
 * 
 * Displays the overview tab content with visualization demo
 * and navigation links to other tabs.
 */
interface OverviewTabProps {
  onTabChange: (tab: TabType) => void;
}

export const OverviewTab: React.FC<OverviewTabProps> = ({ onTabChange }) => {
  return (
    <>
      <VisualizationDemo className="mb-8" testId="visualization-page-demo" />

      {/* Links to other visualization tabs */}
      <div className="mt-6 mb-8 text-center">
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <div>
            <button
              onClick={() => onTabChange('preferences')}
              className="inline-block bg-primary text-white px-6 py-2 rounded-md font-medium hover:bg-primary-dark transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              data-testid="visualization-page-preferences-link"
              aria-label="Explore your preferences visualization"
            >
              Explore Your Preferences
            </button>
            <p className="mt-2 text-sm text-gray-600">
              See how your preferences influence your recommendations.
            </p>
          </div>

          <div>
            <button
              onClick={() => onTabChange('dashboard')}
              className="inline-block bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              data-testid="visualization-page-dashboard-link"
              aria-label="View comprehensive dashboard"
            >
              View Comprehensive Dashboard
            </button>
            <p className="mt-2 text-sm text-gray-600">
              See all your recommendation insights in one place.
            </p>
          </div>
        </div>
      </div>
    </>
  );
};
