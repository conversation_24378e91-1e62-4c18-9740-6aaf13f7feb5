import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../hooks/useAuth';
import { useMediaQuery } from '../hooks/useMediaQuery';
import { Tabs, Tab } from '../components/UI/Tabs';
import { Button } from '../components/UI/Button';
import EmbeddingVisualization from '../components/Visualization/EmbeddingVisualization';
import PreferenceVisualization from '../components/Visualization/PreferenceVisualization';
import { RecommendationExplanation } from '../components/Recommendations';

/**
 * A demo page showcasing the enhanced visualization and recommendation explanation components
 */
const EnhancedVisualizationDemo: React.FC = () => {
  const { user } = useAuth();
  const isMobile = useMediaQuery('(max-width: 640px)');
  const [activeTab, setActiveTab] = useState<string>('embedding');
  const [showExplanation, setShowExplanation] = useState<boolean>(false);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  
  // Mock item IDs for demonstration purposes
  const mockItemIds = [
    { id: 'item-1', name: 'Modern Desk Chair' },
    { id: 'item-2', name: 'Professional Camera' },
    { id: 'item-3', name: 'Leather Jacket' },
    { id: 'item-4', name: 'Mountain Bike' },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Enhanced Visualization Demo</h1>
        <p className="text-gray-700">
          This demo showcases the enhanced visualization components with interactive features
          and the recommendation explanation component.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Interactive Visualizations</h2>
            
            <Tabs
              activeTab={activeTab}
              onChange={setActiveTab}
              className="mb-6"
            >
              <Tab id="embedding" label="Item Relationships">
                <div className="mb-4">
                  <p className="text-sm text-gray-700 mb-4">
                    This visualization shows how items are related to each other based on their features.
                    You can zoom, pan, and filter the visualization to explore the relationships.
                  </p>
                  
                  <EmbeddingVisualization
                    interactive={true}
                    limit={50}
                    width={isMobile ? 320 : 700}
                    height={isMobile ? 350 : 500}
                  />
                </div>
              </Tab>
              
              <Tab id="preference" label="Your Preferences">
                <div className="mb-4">
                  <p className="text-sm text-gray-700 mb-4">
                    This visualization shows your preference profile based on your interactions with the platform.
                    It helps you understand how your preferences influence your recommendations.
                  </p>
                  
                  <PreferenceVisualization
                    userId={user?.id}
                  />
                </div>
              </Tab>
            </Tabs>
          </div>
        </div>
        
        <div>
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-xl font-semibold mb-4">Recommendation Explanation</h2>
            
            <p className="text-sm text-gray-700 mb-4">
              This component explains why specific items are recommended to you and allows you to
              adjust your preference weights to see how recommendations would change.
            </p>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select an item to explain:
              </label>
              
              <select
                value={selectedItemId || ''}
                onChange={(e) => setSelectedItemId(e.target.value || null)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">Select an item</option>
                {mockItemIds.map((item) => (
                  <option key={item.id} value={item.id}>
                    {item.name}
                  </option>
                ))}
              </select>
            </div>
            
            <Button
              onClick={() => setShowExplanation(true)}
              disabled={!selectedItemId}
              className="w-full"
            >
              Show Explanation
            </Button>
            
            {showExplanation && selectedItemId && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-6"
              >
                <RecommendationExplanation
                  itemId={selectedItemId}
                  userId={user?.id}
                  onClose={() => setShowExplanation(false)}
                />
              </motion.div>
            )}
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">A/B Testing</h2>
            
            <p className="text-sm text-gray-700 mb-4">
              This section demonstrates how different recommendation algorithms can be compared
              using A/B testing to evaluate their performance.
            </p>
            
            <div className="space-y-4">
              <div className="p-4 border border-gray-200 rounded-md">
                <h3 className="font-medium mb-2">Algorithm A: Content-Based</h3>
                <p className="text-sm text-gray-700">
                  Recommends items based on similarity to items you've interacted with.
                </p>
                <div className="mt-2">
                  <Button variant="secondary" size="sm" className="w-full">
                    View Recommendations
                  </Button>
                </div>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-md">
                <h3 className="font-medium mb-2">Algorithm B: Collaborative Filtering</h3>
                <p className="text-sm text-gray-700">
                  Recommends items based on preferences of similar users.
                </p>
                <div className="mt-2">
                  <Button variant="secondary" size="sm" className="w-full">
                    View Recommendations
                  </Button>
                </div>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-md">
                <h3 className="font-medium mb-2">Algorithm C: Hybrid Approach</h3>
                <p className="text-sm text-gray-700">
                  Combines content-based and collaborative filtering approaches.
                </p>
                <div className="mt-2">
                  <Button variant="secondary" size="sm" className="w-full">
                    View Recommendations
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-semibold mb-4">Visualization Dashboard</h2>
        
        <p className="text-sm text-gray-700 mb-6">
          The enhanced visualization dashboard provides insights into your preferences,
          recommendation history, and personalized improvement suggestions.
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">Recommendation Metrics</h3>
            <p className="text-sm text-gray-700">
              View metrics on recommendation accuracy, diversity, and relevance.
            </p>
          </div>
          
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">Recommendation History</h3>
            <p className="text-sm text-gray-700">
              Track how your recommendations have evolved over time.
            </p>
          </div>
          
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-medium mb-2">Improvement Suggestions</h3>
            <p className="text-sm text-gray-700">
              Get personalized suggestions to improve your recommendations.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedVisualizationDemo;
