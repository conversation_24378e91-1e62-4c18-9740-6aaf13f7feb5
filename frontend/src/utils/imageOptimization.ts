/**
 * Image Optimization Utilities
 *
 * This file contains utilities for optimizing images in the application.
 * It provides functions for responsive images, lazy loading, and WebP support.
 */

/**
 * Interface for responsive image sources
 */
export interface ResponsiveImageSource {
  src: string;
  width: number;
  format?: 'webp' | 'jpeg' | 'png' | 'avif';
}

/**
 * Interface for responsive image props
 */
export interface ResponsiveImageProps {
  sources: ResponsiveImageSource[];
  alt: string;
  className?: string;
  sizes?: string;
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
  fetchPriority?: 'high' | 'low' | 'auto';
  width?: number;
  height?: number;
}

/**
 * Check if the browser supports WebP format
 * @returns A promise that resolves to a boolean indicating WebP support
 */
export const supportsWebP = async (): Promise<boolean> => {
  if (!window.createImageBitmap) return false;

  const webpData = 'data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA=';
  const blob = await fetch(webpData).then(r => r.blob());

  return createImageBitmap(blob).then(() => true, () => false);
};

/**
 * Check if the browser supports AVIF format
 * @returns A promise that resolves to a boolean indicating AVIF support
 */
export const supportsAVIF = async (): Promise<boolean> => {
  if (!window.createImageBitmap) return false;

  const avifData = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  const blob = await fetch(avifData).then(r => r.blob());

  return createImageBitmap(blob).then(() => true, () => false);
};

/**
 * Generate a srcset string from responsive image sources
 * @param sources Array of responsive image sources
 * @returns A srcset string
 */
export const generateSrcSet = (sources: ResponsiveImageSource[]): string => {
  return sources
    .map(source => `${source.src} ${source.width}w`)
    .join(', ');
};

/**
 * Generates a srcset string for responsive images from a single source
 *
 * @param src - The base image URL
 * @param widths - An array of widths to generate srcset entries for
 * @param format - The image format to use (webp, avif, jpg, png)
 * @returns A srcset string for use in an img tag
 */
export const generateSrcSetFromSingleSource = (
  src: string,
  widths: number[] = [640, 750, 828, 1080, 1200, 1920, 2048],
  format?: 'webp' | 'avif' | 'jpg' | 'png'
): string => {
  if (!src) return '';

  // Extract the file extension and base path
  const extension = src.split('.').pop() || 'jpg';
  const basePath = src.substring(0, src.lastIndexOf('.'));

  // Use the specified format or the original extension
  const outputFormat = format || extension;

  // Generate the srcset entries
  return widths
    .map(width => `${basePath}-${width}.${outputFormat} ${width}w`)
    .join(', ');
};

/**
 * Get the best format for an image based on browser support
 * @returns The best supported image format
 */
export const getBestImageFormat = async (): Promise<'avif' | 'webp' | 'jpeg'> => {
  if (await supportsAVIF()) {
    return 'avif';
  }

  if (await supportsWebP()) {
    return 'webp';
  }

  return 'jpeg';
};

/**
 * Generate optimized image URL with correct format and dimensions
 * @param url Original image URL
 * @param width Desired width
 * @param height Optional desired height
 * @param format Optional image format
 * @returns Optimized image URL
 */
export const getOptimizedImageUrl = async (
  url: string,
  width: number,
  height?: number,
  format?: 'webp' | 'jpeg' | 'png' | 'avif'
): Promise<string> => {
  // If the URL is already optimized or is a data URL, return it as is
  if (url.startsWith('data:') || url.includes('?w=')) {
    return url;
  }

  // Determine the best format if not specified
  const bestFormat = format || await getBestImageFormat();

  // If the URL is from an external source that doesn't support optimization
  if (url.startsWith('http') && !url.includes('rentup.com')) {
    return url;
  }

  // Build the optimized URL
  const baseUrl = url.split('?')[0];
  const query = new URLSearchParams();

  query.append('w', width.toString());
  if (height) {
    query.append('h', height.toString());
  }
  query.append('fmt', bestFormat);
  query.append('q', bestFormat === 'avif' ? '80' : bestFormat === 'webp' ? '85' : '90');

  return `${baseUrl}?${query.toString()}`;
};

/**
 * Preload critical images
 * @param urls Array of image URLs to preload
 */
export const preloadCriticalImages = (urls: string[]): void => {
  urls.forEach(url => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = url;
    document.head.appendChild(link);
  });
};

/**
 * Create a blur hash placeholder for an image
 * @param url Image URL
 * @returns A data URL with a blur hash placeholder
 */
export const createBlurPlaceholder = async (url: string): Promise<string> => {
  // This is a simplified implementation
  // In a real app, you would use a library like blurhash or a server-side API
  return `${url}?blur=80&q=20&w=50`;
};

/**
 * Calculates the aspect ratio of an image
 *
 * @param width - The image width
 * @param height - The image height
 * @returns A string representation of the aspect ratio (e.g., "16/9")
 */
export const calculateAspectRatio = (width: number, height: number): string => {
  if (!width || !height) return 'auto';

  // Calculate the greatest common divisor
  const gcd = (a: number, b: number): number => {
    return b === 0 ? a : gcd(b, a % b);
  };

  const divisor = gcd(width, height);

  // Return the simplified aspect ratio
  return `${width / divisor}/${height / divisor}`;
};

/**
 * Generates sizes attribute for responsive images
 *
 * @param breakpoints - An object mapping breakpoints to image widths
 * @returns A sizes string for use in an img tag
 */
export const generateSizes = (
  breakpoints: Record<string, string> = {
    '(max-width: 640px)': '100vw',
    '(max-width: 768px)': '50vw',
    '(max-width: 1024px)': '33vw',
    '(max-width: 1280px)': '25vw',
    'default': '20vw'
  }
): string => {
  // Convert the breakpoints object to a sizes string
  return Object.entries(breakpoints)
    .map(([breakpoint, size]) => {
      if (breakpoint === 'default') {
        return size;
      }
      return `${breakpoint} ${size}`;
    })
    .join(', ');
};
