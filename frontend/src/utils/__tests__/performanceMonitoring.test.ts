import {
  startMeasure,
  measureFunction,
  getMetrics,
  clearMetrics,
  PerformanceMetricType
} from '../performanceMonitoring';

// Mock the performance API
const originalPerformance = global.performance;

beforeEach(() => {
  // Clear metrics before each test
  clearMetrics();
  
  // Mock performance API
  global.performance = {
    mark: jest.fn(),
    measure: jest.fn(),
    now: jest.fn().mockReturnValue(100),
    getEntriesByType: jest.fn().mockReturnValue([{ duration: 100 }]),
    ...originalPerformance
  };
  
  // Mock Date.now
  jest.spyOn(Date, 'now').mockReturnValue(1234567890);
});

afterEach(() => {
  // Restore original performance API
  global.performance = originalPerformance;
  
  // Restore Date.now
  jest.restoreAllMocks();
});

describe('Performance Monitoring Utilities', () => {
  describe('startMeasure', () => {
    it('should create a performance mark when started', () => {
      const stopMeasure = startMeasure('component-render', 'TestComponent');
      
      expect(global.performance.mark).toHaveBeenCalledWith('component-render-TestComponent-start');
      
      // Call the returned function to stop measuring
      stopMeasure();
      
      expect(global.performance.mark).toHaveBeenCalledWith('component-render-TestComponent-end');
      expect(global.performance.measure).toHaveBeenCalledWith(
        'TestComponent',
        'component-render-TestComponent-start',
        'component-render-TestComponent-end'
      );
    });
    
    it('should return a metric object when stopped', () => {
      const stopMeasure = startMeasure('component-render', 'TestComponent');
      const metric = stopMeasure();
      
      expect(metric).toEqual({
        type: 'component-render',
        name: 'TestComponent',
        duration: 0, // Mock returns same time for start and end
        timestamp: 1234567890,
        metadata: undefined
      });
    });
    
    it('should include metadata in the metric object', () => {
      const metadata = { componentProps: { id: 123 } };
      const stopMeasure = startMeasure('component-render', 'TestComponent', metadata);
      const metric = stopMeasure();
      
      expect(metric.metadata).toEqual(metadata);
    });
  });
  
  describe('measureFunction', () => {
    it('should measure synchronous function execution time', async () => {
      const fn = jest.fn().mockReturnValue('result');
      
      const result = await measureFunction('api-call', 'testFunction', fn);
      
      expect(result).toBe('result');
      expect(fn).toHaveBeenCalled();
      expect(global.performance.mark).toHaveBeenCalledWith('api-call-testFunction-start');
      expect(global.performance.mark).toHaveBeenCalledWith('api-call-testFunction-end');
    });
    
    it('should measure asynchronous function execution time', async () => {
      const fn = jest.fn().mockResolvedValue('async result');
      
      const result = await measureFunction('api-call', 'testAsyncFunction', fn);
      
      expect(result).toBe('async result');
      expect(fn).toHaveBeenCalled();
      expect(global.performance.mark).toHaveBeenCalledWith('api-call-testAsyncFunction-start');
      expect(global.performance.mark).toHaveBeenCalledWith('api-call-testAsyncFunction-end');
    });
    
    it('should handle function errors', async () => {
      const error = new Error('Test error');
      const fn = jest.fn().mockRejectedValue(error);
      
      await expect(measureFunction('api-call', 'errorFunction', fn)).rejects.toThrow('Test error');
      
      expect(fn).toHaveBeenCalled();
      expect(global.performance.mark).toHaveBeenCalledWith('api-call-errorFunction-start');
      expect(global.performance.mark).toHaveBeenCalledWith('api-call-errorFunction-end');
    });
  });
  
  describe('getMetrics and clearMetrics', () => {
    it('should return all recorded metrics', () => {
      // Record some metrics
      const stopMeasure1 = startMeasure('component-render', 'Component1');
      stopMeasure1();
      
      const stopMeasure2 = startMeasure('api-call', 'ApiCall1');
      stopMeasure2();
      
      // Get metrics
      const metrics = getMetrics();
      
      expect(metrics.length).toBe(2);
      expect(metrics[0].name).toBe('Component1');
      expect(metrics[1].name).toBe('ApiCall1');
    });
    
    it('should clear all recorded metrics', () => {
      // Record some metrics
      const stopMeasure = startMeasure('component-render', 'Component1');
      stopMeasure();
      
      // Clear metrics
      clearMetrics();
      
      // Get metrics
      const metrics = getMetrics();
      
      expect(metrics.length).toBe(0);
    });
  });
});
