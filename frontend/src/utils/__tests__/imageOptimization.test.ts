import {
  generateSrcSet,
  getBestImageFormat,
  getOptimizedImageUrl,
  supportsWebP,
  supportsAVIF
} from '../imageOptimization';

// Mock global objects
const originalCreateImageBitmap = global.createImageBitmap;
const originalFetch = global.fetch;

beforeEach(() => {
  // Mock createImageBitmap
  global.createImageBitmap = jest.fn();
  
  // Mock fetch
  global.fetch = jest.fn();
});

afterEach(() => {
  // Restore original functions
  global.createImageBitmap = originalCreateImageBitmap;
  global.fetch = originalFetch;
  
  // Clear all mocks
  jest.clearAllMocks();
});

describe('Image Optimization Utilities', () => {
  describe('generateSrcSet', () => {
    it('should generate a proper srcset string from sources', () => {
      const sources = [
        { src: '/images/image-400.jpg', width: 400 },
        { src: '/images/image-800.jpg', width: 800 },
        { src: '/images/image-1200.jpg', width: 1200 }
      ];
      
      const srcset = generateSrcSet(sources);
      
      expect(srcset).toBe('/images/image-400.jpg 400w, /images/image-800.jpg 800w, /images/image-1200.jpg 1200w');
    });
    
    it('should handle empty sources array', () => {
      const srcset = generateSrcSet([]);
      
      expect(srcset).toBe('');
    });
  });
  
  describe('supportsWebP', () => {
    it('should return false if createImageBitmap is not available', async () => {
      // Remove createImageBitmap
      delete global.createImageBitmap;
      
      const result = await supportsWebP();
      
      expect(result).toBe(false);
    });
    
    it('should return true if WebP is supported', async () => {
      // Mock successful WebP support
      (global.fetch as jest.Mock).mockResolvedValue({
        blob: jest.fn().mockResolvedValue('blob')
      });
      (global.createImageBitmap as jest.Mock).mockResolvedValue('bitmap');
      
      const result = await supportsWebP();
      
      expect(result).toBe(true);
      expect(global.fetch).toHaveBeenCalled();
      expect(global.createImageBitmap).toHaveBeenCalled();
    });
    
    it('should return false if WebP is not supported', async () => {
      // Mock failed WebP support
      (global.fetch as jest.Mock).mockResolvedValue({
        blob: jest.fn().mockResolvedValue('blob')
      });
      (global.createImageBitmap as jest.Mock).mockRejectedValue(new Error('Not supported'));
      
      const result = await supportsWebP();
      
      expect(result).toBe(false);
      expect(global.fetch).toHaveBeenCalled();
      expect(global.createImageBitmap).toHaveBeenCalled();
    });
  });
  
  describe('supportsAVIF', () => {
    it('should return false if createImageBitmap is not available', async () => {
      // Remove createImageBitmap
      delete global.createImageBitmap;
      
      const result = await supportsAVIF();
      
      expect(result).toBe(false);
    });
    
    it('should return true if AVIF is supported', async () => {
      // Mock successful AVIF support
      (global.fetch as jest.Mock).mockResolvedValue({
        blob: jest.fn().mockResolvedValue('blob')
      });
      (global.createImageBitmap as jest.Mock).mockResolvedValue('bitmap');
      
      const result = await supportsAVIF();
      
      expect(result).toBe(true);
      expect(global.fetch).toHaveBeenCalled();
      expect(global.createImageBitmap).toHaveBeenCalled();
    });
    
    it('should return false if AVIF is not supported', async () => {
      // Mock failed AVIF support
      (global.fetch as jest.Mock).mockResolvedValue({
        blob: jest.fn().mockResolvedValue('blob')
      });
      (global.createImageBitmap as jest.Mock).mockRejectedValue(new Error('Not supported'));
      
      const result = await supportsAVIF();
      
      expect(result).toBe(false);
      expect(global.fetch).toHaveBeenCalled();
      expect(global.createImageBitmap).toHaveBeenCalled();
    });
  });
  
  describe('getBestImageFormat', () => {
    it('should return avif if AVIF is supported', async () => {
      // Mock AVIF support
      jest.spyOn(global, 'supportsAVIF').mockResolvedValue(true);
      jest.spyOn(global, 'supportsWebP').mockResolvedValue(true);
      
      const result = await getBestImageFormat();
      
      expect(result).toBe('avif');
    });
    
    it('should return webp if WebP is supported but AVIF is not', async () => {
      // Mock WebP support but not AVIF
      jest.spyOn(global, 'supportsAVIF').mockResolvedValue(false);
      jest.spyOn(global, 'supportsWebP').mockResolvedValue(true);
      
      const result = await getBestImageFormat();
      
      expect(result).toBe('webp');
    });
    
    it('should return jpeg if neither AVIF nor WebP is supported', async () => {
      // Mock no support for modern formats
      jest.spyOn(global, 'supportsAVIF').mockResolvedValue(false);
      jest.spyOn(global, 'supportsWebP').mockResolvedValue(false);
      
      const result = await getBestImageFormat();
      
      expect(result).toBe('jpeg');
    });
  });
  
  describe('getOptimizedImageUrl', () => {
    it('should return data URLs as is', async () => {
      const dataUrl = 'data:image/png;base64,abc123';
      
      const result = await getOptimizedImageUrl(dataUrl, 800);
      
      expect(result).toBe(dataUrl);
    });
    
    it('should return URLs with width parameter as is', async () => {
      const url = 'https://example.com/image.jpg?w=800';
      
      const result = await getOptimizedImageUrl(url, 800);
      
      expect(result).toBe(url);
    });
    
    it('should add width, format and quality parameters to URLs', async () => {
      const url = 'https://rentup.com/image.jpg';
      
      // Mock best format
      jest.spyOn(global, 'getBestImageFormat').mockResolvedValue('webp');
      
      const result = await getOptimizedImageUrl(url, 800);
      
      expect(result).toBe('https://rentup.com/image.jpg?w=800&fmt=webp&q=85');
    });
    
    it('should add height parameter if provided', async () => {
      const url = 'https://rentup.com/image.jpg';
      
      // Mock best format
      jest.spyOn(global, 'getBestImageFormat').mockResolvedValue('webp');
      
      const result = await getOptimizedImageUrl(url, 800, 600);
      
      expect(result).toBe('https://rentup.com/image.jpg?w=800&h=600&fmt=webp&q=85');
    });
    
    it('should use provided format if specified', async () => {
      const url = 'https://rentup.com/image.jpg';
      
      const result = await getOptimizedImageUrl(url, 800, undefined, 'avif');
      
      expect(result).toBe('https://rentup.com/image.jpg?w=800&fmt=avif&q=80');
    });
    
    it('should not modify external URLs', async () => {
      const url = 'https://external-site.com/image.jpg';
      
      const result = await getOptimizedImageUrl(url, 800);
      
      expect(result).toBe(url);
    });
  });
});
