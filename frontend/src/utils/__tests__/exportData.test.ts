import { convertToCSV, downloadCSV, downloadJSON, shareData } from '../exportData';

describe('exportData utility', () => {
  describe('convertToCSV', () => {
    it('should convert an array of objects to CSV format', () => {
      const data = [
        { id: 1, name: 'Item 1', category: 'Category A' },
        { id: 2, name: 'Item 2', category: 'Category B' },
        { id: 3, name: 'Item 3', category: 'Category A' }
      ];

      const result = convertToCSV(data);
      
      // Check that the result is a string
      expect(typeof result).toBe('string');
      
      // Check that the result has the correct number of lines (header + 3 data rows)
      const lines = result.split('\n');
      expect(lines.length).toBe(4);
      
      // Check that the header contains all the keys
      expect(lines[0]).toContain('id');
      expect(lines[0]).toContain('name');
      expect(lines[0]).toContain('category');
      
      // Check that the data rows contain the correct values
      expect(lines[1]).toContain('1');
      expect(lines[1]).toContain('Item 1');
      expect(lines[1]).toContain('Category A');
    });

    it('should use custom headers if provided', () => {
      const data = [
        { id: 1, name: 'Item 1', category: 'Category A' },
        { id: 2, name: 'Item 2', category: 'Category B' }
      ];

      const headers = [
        { key: 'id', label: 'ID' },
        { key: 'name', label: 'Item Name' },
        { key: 'category', label: 'Item Category' }
      ];

      const result = convertToCSV(data, headers);
      
      // Check that the result has the correct number of lines (header + 2 data rows)
      const lines = result.split('\n');
      expect(lines.length).toBe(3);
      
      // Check that the header contains the custom labels
      expect(lines[0]).toContain('ID');
      expect(lines[0]).toContain('Item Name');
      expect(lines[0]).toContain('Item Category');
    });

    it('should handle empty arrays', () => {
      const data: any[] = [];
      const result = convertToCSV(data);
      expect(result).toBe('');
    });

    it('should handle special characters in data', () => {
      const data = [
        { id: 1, name: 'Item, with comma', category: 'Category "with quotes"' },
        { id: 2, name: 'Item\nwith newline', category: 'Category B' }
      ];

      const result = convertToCSV(data);
      
      // Check that the result is a string
      expect(typeof result).toBe('string');
      
      // Check that the commas and quotes are properly escaped
      expect(result).toContain('"Item, with comma"');
      expect(result).toContain('"Category ""with quotes"""');
      expect(result).toContain('"Item\nwith newline"');
    });
  });

  describe('downloadCSV', () => {
    beforeEach(() => {
      // Mock the URL.createObjectURL and URL.revokeObjectURL methods
      global.URL.createObjectURL = jest.fn().mockReturnValue('mock-url');
      global.URL.revokeObjectURL = jest.fn();
      
      // Mock the document.createElement and appendChild methods
      document.createElement = jest.fn().mockImplementation((tag) => {
        if (tag === 'a') {
          return {
            setAttribute: jest.fn(),
            style: {},
            click: jest.fn()
          };
        }
        return {};
      });
      document.body.appendChild = jest.fn();
      document.body.removeChild = jest.fn();
    });

    it('should create a download link with the correct attributes', () => {
      const data = [
        { id: 1, name: 'Item 1', category: 'Category A' },
        { id: 2, name: 'Item 2', category: 'Category B' }
      ];

      downloadCSV(data, 'test-file');
      
      // Check that URL.createObjectURL was called with a Blob
      expect(global.URL.createObjectURL).toHaveBeenCalled();
      
      // Check that document.createElement was called with 'a'
      expect(document.createElement).toHaveBeenCalledWith('a');
      
      // Check that the link was appended to the document body
      expect(document.body.appendChild).toHaveBeenCalled();
      
      // Check that the link was clicked
      const link = (document.createElement as jest.Mock).mock.results[0].value;
      expect(link.click).toHaveBeenCalled();
      
      // Check that the link was removed from the document body
      expect(document.body.removeChild).toHaveBeenCalled();
      
      // Check that URL.revokeObjectURL was called
      expect(global.URL.revokeObjectURL).toHaveBeenCalled();
    });
  });

  describe('downloadJSON', () => {
    beforeEach(() => {
      // Mock the URL.createObjectURL and URL.revokeObjectURL methods
      global.URL.createObjectURL = jest.fn().mockReturnValue('mock-url');
      global.URL.revokeObjectURL = jest.fn();
      
      // Mock the document.createElement and appendChild methods
      document.createElement = jest.fn().mockImplementation((tag) => {
        if (tag === 'a') {
          return {
            setAttribute: jest.fn(),
            style: {},
            click: jest.fn()
          };
        }
        return {};
      });
      document.body.appendChild = jest.fn();
      document.body.removeChild = jest.fn();
    });

    it('should create a download link with the correct attributes', () => {
      const data = {
        id: 1,
        name: 'Test Data',
        items: [
          { id: 1, name: 'Item 1' },
          { id: 2, name: 'Item 2' }
        ]
      };

      downloadJSON(data, 'test-file');
      
      // Check that URL.createObjectURL was called with a Blob
      expect(global.URL.createObjectURL).toHaveBeenCalled();
      
      // Check that document.createElement was called with 'a'
      expect(document.createElement).toHaveBeenCalledWith('a');
      
      // Check that the link was appended to the document body
      expect(document.body.appendChild).toHaveBeenCalled();
      
      // Check that the link was clicked
      const link = (document.createElement as jest.Mock).mock.results[0].value;
      expect(link.click).toHaveBeenCalled();
      
      // Check that the link was removed from the document body
      expect(document.body.removeChild).toHaveBeenCalled();
      
      // Check that URL.revokeObjectURL was called
      expect(global.URL.revokeObjectURL).toHaveBeenCalled();
    });
  });

  describe('shareData', () => {
    beforeEach(() => {
      // Mock the navigator.share method
      global.navigator.share = jest.fn().mockResolvedValue(undefined);
      
      // Mock the navigator.clipboard.writeText method
      global.navigator.clipboard = {
        writeText: jest.fn().mockResolvedValue(undefined)
      };
    });

    it('should use the Web Share API if available', async () => {
      const data = { id: 1, name: 'Test Data' };
      const title = 'Test Title';
      const text = 'Test Text';

      const result = await shareData(data, title, text);
      
      // Check that navigator.share was called with the correct arguments
      expect(global.navigator.share).toHaveBeenCalledWith({
        title,
        text,
        url: expect.any(String)
      });
      
      // Check that the function returns true
      expect(result).toBe(true);
    });

    it('should fall back to clipboard if Web Share API is not available', async () => {
      // Mock the navigator.share method to be undefined
      global.navigator.share = undefined;
      
      const data = { id: 1, name: 'Test Data' };
      const title = 'Test Title';
      const text = 'Test Text';

      const result = await shareData(data, title, text);
      
      // Check that navigator.clipboard.writeText was called
      expect(global.navigator.clipboard.writeText).toHaveBeenCalled();
      
      // Check that the function returns true
      expect(result).toBe(true);
    });

    it('should handle errors and return false', async () => {
      // Mock the navigator.share method to throw an error
      global.navigator.share = jest.fn().mockRejectedValue(new Error('Share failed'));
      
      const data = { id: 1, name: 'Test Data' };
      const title = 'Test Title';
      const text = 'Test Text';

      const result = await shareData(data, title, text);
      
      // Check that the function returns false
      expect(result).toBe(false);
    });
  });
});
