/**
 * Service Worker Registration
 * 
 * This file handles the registration of the service worker for offline support
 * and caching. It's used by the PWA plugin to register the service worker.
 */

// Check if the browser supports service workers
const isServiceWorkerSupported = 'serviceWorker' in navigator;

/**
 * Register the service worker
 * @returns A promise that resolves when the service worker is registered
 */
export const registerServiceWorker = async (): Promise<ServiceWorkerRegistration | undefined> => {
  if (!isServiceWorkerSupported) {
    console.warn('Service workers are not supported in this browser');
    return;
  }

  try {
    // Register the service worker
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/',
    });

    // Log the registration
    if (import.meta.env.DEV) {
      console.log('Service worker registered successfully:', registration);
    }

    return registration;
  } catch (error) {
    console.error('Service worker registration failed:', error);
  }
};

/**
 * Unregister the service worker
 * @returns A promise that resolves when the service worker is unregistered
 */
export const unregisterServiceWorker = async (): Promise<boolean> => {
  if (!isServiceWorkerSupported) {
    return false;
  }

  try {
    const registration = await navigator.serviceWorker.getRegistration();
    if (registration) {
      const result = await registration.unregister();
      if (import.meta.env.DEV) {
        console.log('Service worker unregistered successfully:', result);
      }
      return result;
    }
    return false;
  } catch (error) {
    console.error('Service worker unregistration failed:', error);
    return false;
  }
};

/**
 * Check if the app is installed (PWA)
 * @returns A boolean indicating if the app is installed
 */
export const isAppInstalled = (): boolean => {
  return window.matchMedia('(display-mode: standalone)').matches ||
    (window.navigator as any).standalone === true;
};

/**
 * Check if the app can be installed (PWA)
 * @param callback A callback function to be called when the app can be installed
 * @returns A function to remove the event listener
 */
export const onAppInstallAvailable = (callback: () => void): (() => void) => {
  let installPromptEvent: any = null;
  
  const handleBeforeInstallPrompt = (event: Event) => {
    // Prevent the default browser install prompt
    event.preventDefault();
    
    // Store the event for later use
    installPromptEvent = event;
    
    // Call the callback
    callback();
  };
  
  // Add the event listener
  window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
  
  // Return a function to remove the event listener
  return () => {
    window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
  };
};

/**
 * Show the install prompt
 * @returns A promise that resolves with the user's choice
 */
export const showInstallPrompt = async (): Promise<{ outcome: string } | null> => {
  // Get the stored event
  const installPromptEvent = (window as any).installPromptEvent;
  
  if (!installPromptEvent) {
    console.warn('No install prompt event available');
    return null;
  }
  
  // Show the install prompt
  installPromptEvent.prompt();
  
  // Wait for the user to respond to the prompt
  const choiceResult = await installPromptEvent.userChoice;
  
  // Clear the stored event
  (window as any).installPromptEvent = null;
  
  return choiceResult;
};
