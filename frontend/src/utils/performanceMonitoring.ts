/**
 * Performance Monitoring Utilities
 *
 * This file contains utilities for monitoring and reporting performance metrics.
 * It provides functions for measuring component render times, tracking web vitals,
 * and reporting performance data.
 */

// Define performance metric types
export type PerformanceMetricType =
  | 'component-render'
  | 'api-call'
  | 'page-load'
  | 'interaction'
  | 'animation'
  | 'resource-load';

// Define performance metric data
export interface PerformanceMetric {
  type: PerformanceMetricType;
  name: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

// In-memory storage for metrics
const metrics: PerformanceMetric[] = [];

// Maximum number of metrics to store
const MAX_METRICS = 100;

/**
 * Start measuring a performance metric
 * @param type The type of metric
 * @param name The name of the metric
 * @returns A function to stop measuring and record the metric
 */
export const startMeasure = (
  type: PerformanceMetricType,
  name: string,
  metadata?: Record<string, any>
): () => PerformanceMetric => {
  const start = performance.now();
  const markName = `${type}-${name}-start`;

  // Create a performance mark
  performance.mark(markName);

  // Return a function to stop measuring
  return () => {
    const end = performance.now();
    const duration = end - start;
    const endMarkName = `${type}-${name}-end`;

    // Create performance marks and measure
    performance.mark(endMarkName);
    performance.measure(name, markName, endMarkName);

    // Create metric object
    const metric: PerformanceMetric = {
      type,
      name,
      duration,
      timestamp: Date.now(),
      metadata,
    };

    // Add to metrics array
    addMetric(metric);

    return metric;
  };
};

/**
 * Measure a function execution time
 * @param type The type of metric
 * @param name The name of the metric
 * @param fn The function to measure
 * @param metadata Additional metadata
 * @returns The result of the function
 */
export const measureFunction = async <T>(
  type: PerformanceMetricType,
  name: string,
  fn: () => Promise<T> | T,
  metadata?: Record<string, any>
): Promise<T> => {
  const stopMeasure = startMeasure(type, name, metadata);

  try {
    const result = await fn();
    stopMeasure();
    return result;
  } catch (error) {
    stopMeasure();
    throw error;
  }
};

/**
 * Add a metric to the metrics array
 * @param metric The metric to add
 */
const addMetric = (metric: PerformanceMetric): void => {
  metrics.push(metric);

  // Remove oldest metrics if we exceed the maximum
  if (metrics.length > MAX_METRICS) {
    metrics.shift();
  }

  // Report metric if in production
  // Check if we're in production environment
  // Use a try-catch to handle Jest environment where import.meta is not available
  try {
    // @ts-ignore - This is needed for Jest tests
    if (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.PROD) {
      reportMetric(metric);
    }
  } catch (error) {
    // In test environment, do nothing
  }
};

/**
 * Get all recorded metrics
 * @returns Array of performance metrics
 */
export const getMetrics = (): PerformanceMetric[] => {
  return [...metrics];
};

/**
 * Clear all recorded metrics
 */
export const clearMetrics = (): void => {
  metrics.length = 0;
};

/**
 * Report a metric to an analytics service
 * @param metric The metric to report
 */
const reportMetric = (metric: PerformanceMetric): void => {
  // In a real application, this would send the metric to an analytics service
  // For now, we'll just log it to the console in development
  // Check if we're in development environment
  // Use a try-catch to handle Jest environment where import.meta is not available
  try {
    // @ts-ignore - This is needed for Jest tests
    if (typeof import.meta !== 'undefined' && import.meta.env && import.meta.env.DEV) {
      console.log('Performance metric:', metric);
    }
  } catch (error) {
    // In test environment, do nothing
  }

  // Example of sending to an analytics service
  // This is commented out as it's just an example
  /*
  fetch('/api/metrics', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(metric),
  }).catch(error => {
    console.error('Failed to report metric:', error);
  });
  */
};

/**
 * Track web vitals metrics
 */
export const trackWebVitals = (): void => {
  // Import web-vitals dynamically
  try {
    import('web-vitals').then(({ onCLS, onFID, onLCP, onTTFB, onINP }) => {
      onCLS(metric => {
        addMetric({
          type: 'page-load',
          name: 'CLS',
          duration: metric.value,
          timestamp: Date.now(),
          metadata: {
            rating: metric.rating,
            entries: metric.entries,
          },
        });
      });

      onFID(metric => {
        addMetric({
          type: 'interaction',
          name: 'FID',
          duration: metric.value,
          timestamp: Date.now(),
          metadata: {
            rating: metric.rating,
            entries: metric.entries,
          },
        });
      });

      onLCP(metric => {
        addMetric({
          type: 'page-load',
          name: 'LCP',
          duration: metric.value,
          timestamp: Date.now(),
          metadata: {
            rating: metric.rating,
            entries: metric.entries,
          },
        });
      });

      onTTFB(metric => {
        addMetric({
          type: 'page-load',
          name: 'TTFB',
          duration: metric.value,
          timestamp: Date.now(),
          metadata: {
            rating: metric.rating,
            entries: metric.entries,
          },
        });
      });

      onINP(metric => {
        addMetric({
          type: 'interaction',
          name: 'INP',
          duration: metric.value,
          timestamp: Date.now(),
          metadata: {
            rating: metric.rating,
            entries: metric.entries,
          },
        });
      });
    });
  } catch (error) {
    console.error('Failed to load web-vitals:', error);
  }
};

/**
 * Initialize performance monitoring
 */
export const initPerformanceMonitoring = (): void => {
  // Track web vitals
  trackWebVitals();

  // Track resource timing
  if (window.PerformanceObserver) {
    const resourceObserver = new PerformanceObserver(list => {
      list.getEntries().forEach(entry => {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming;

          // Only track resources from our domain
          if (resourceEntry.name.includes(window.location.hostname)) {
            addMetric({
              type: 'resource-load',
              name: resourceEntry.name,
              duration: resourceEntry.duration,
              timestamp: Date.now(),
              metadata: {
                initiatorType: resourceEntry.initiatorType,
                transferSize: resourceEntry.transferSize,
                encodedBodySize: resourceEntry.encodedBodySize,
                decodedBodySize: resourceEntry.decodedBodySize,
              },
            });
          }
        }
      });
    });

    resourceObserver.observe({ entryTypes: ['resource'] });
  }
};
