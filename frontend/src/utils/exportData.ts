/**
 * Utility functions for exporting data in various formats
 * Supports CSV, JSON, and other common formats
 * Includes security measures to prevent XSS and other vulnerabilities
 */

/**
 * Convert an array of objects to CSV format
 * @param data - Array of objects to convert
 * @param headers - Optional custom headers (if not provided, will use object keys)
 * @returns CSV formatted string
 */
export const convertToCSV = <T extends Record<string, any>>(
  data: T[],
  headers?: { key: keyof T; label: string }[]
): string => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return '';
  }

  // If headers are not provided, use the keys from the first object
  const headerKeys = headers ? headers.map(h => h.key) : Object.keys(data[0]) as (keyof T)[];
  const headerLabels = headers ? headers.map(h => h.label) : headerKeys as string[];

  // Sanitize header labels to prevent CSV injection
  const sanitizedHeaders = headerLabels.map(header => 
    // Escape quotes and wrap in quotes if contains comma, newline, or quotes
    typeof header === 'string' 
      ? `"${header.replace(/"/g, '""')}"`
      : `"${String(header).replace(/"/g, '""')}"`
  );

  // Create CSV header row
  const csvContent = [sanitizedHeaders.join(',')];

  // Add data rows
  data.forEach(item => {
    const row = headerKeys.map(key => {
      const value = item[key];
      
      // Handle different data types
      if (value === null || value === undefined) {
        return '';
      } else if (typeof value === 'string') {
        // Escape quotes and wrap in quotes if contains comma, newline, or quotes
        return `"${value.replace(/"/g, '""')}"`;
      } else if (typeof value === 'object') {
        // For objects, convert to JSON string and escape
        return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
      } else {
        // For numbers, booleans, etc.
        return String(value);
      }
    });
    
    csvContent.push(row.join(','));
  });

  return csvContent.join('\n');
};

/**
 * Download data as a CSV file
 * @param data - Array of objects to download
 * @param filename - Name of the file to download
 * @param headers - Optional custom headers
 */
export const downloadCSV = <T extends Record<string, any>>(
  data: T[],
  filename: string,
  headers?: { key: keyof T; label: string }[]
): void => {
  // Convert data to CSV
  const csv = convertToCSV(data, headers);
  
  // Create a Blob with the CSV data
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  
  // Create a download link
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  
  // Add to document, click to download, and remove
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL object
  URL.revokeObjectURL(url);
};

/**
 * Download data as a JSON file
 * @param data - Data to download
 * @param filename - Name of the file to download
 */
export const downloadJSON = <T>(data: T, filename: string): void => {
  // Convert data to JSON string
  const json = JSON.stringify(data, null, 2);
  
  // Create a Blob with the JSON data
  const blob = new Blob([json], { type: 'application/json;charset=utf-8;' });
  
  // Create a download link
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.json`);
  link.style.visibility = 'hidden';
  
  // Add to document, click to download, and remove
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up the URL object
  URL.revokeObjectURL(url);
};

/**
 * Share data via the Web Share API if available
 * Falls back to copying to clipboard if Web Share API is not available
 * @param data - Data to share
 * @param title - Title for the share
 * @param text - Text description for the share
 * @returns Promise that resolves to true if sharing was successful
 */
export const shareData = async (
  data: any,
  title: string,
  text: string
): Promise<boolean> => {
  try {
    // Check if Web Share API is available
    if (navigator.share) {
      // Use Web Share API
      await navigator.share({
        title,
        text,
        // If data is a string, use it as URL, otherwise create a Blob URL
        url: typeof data === 'string' ? data : URL.createObjectURL(
          new Blob([JSON.stringify(data)], { type: 'application/json' })
        )
      });
      return true;
    } else {
      // Fallback to clipboard
      const dataStr = typeof data === 'string' ? data : JSON.stringify(data, null, 2);
      await navigator.clipboard.writeText(`${title}\n${text}\n\n${dataStr}`);
      return true;
    }
  } catch (error) {
    console.error('Error sharing data:', error);
    return false;
  }
};

export default {
  convertToCSV,
  downloadCSV,
  downloadJSON,
  shareData
};
