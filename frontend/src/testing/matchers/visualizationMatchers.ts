import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';

/**
 * Custom matchers for testing visualization components
 * These matchers make tests more readable and expressive
 */

// Extend Jest's expect
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInLoadingState(): R;
      toBeInErrorState(): R;
      toBeInEmptyState(): R;
      toBeInDataState(): R;
      toHavePoint(pointId: string): R;
      toHaveCategory(category: string): R;
      toHaveZoomControls(): R;
      toHaveLegend(): R;
    }
  }
}

/**
 * Check if a component is in the loading state
 * @param testId The test ID of the component
 * @returns Whether the component is in the loading state
 */
export const toBeInLoadingState = function(this: any, testId: string) {
  const loadingElement = screen.queryByTestId(`${testId}-loading`);
  const pass = !!loadingElement;
  
  return {
    pass,
    message: () => 
      pass
        ? `Expected component with testId "${testId}" not to be in loading state`
        : `Expected component with testId "${testId}" to be in loading state`,
  };
};

/**
 * Check if a component is in the error state
 * @param testId The test ID of the component
 * @returns Whether the component is in the error state
 */
export const toBeInErrorState = function(this: any, testId: string) {
  const errorElement = screen.queryByTestId(`${testId}-error`);
  const pass = !!errorElement;
  
  return {
    pass,
    message: () => 
      pass
        ? `Expected component with testId "${testId}" not to be in error state`
        : `Expected component with testId "${testId}" to be in error state`,
  };
};

/**
 * Check if a component is in the empty state
 * @param testId The test ID of the component
 * @returns Whether the component is in the empty state
 */
export const toBeInEmptyState = function(this: any, testId: string) {
  const emptyElement = screen.queryByTestId(`${testId}-empty`);
  const pass = !!emptyElement;
  
  return {
    pass,
    message: () => 
      pass
        ? `Expected component with testId "${testId}" not to be in empty state`
        : `Expected component with testId "${testId}" to be in empty state`,
  };
};

/**
 * Check if a component is in the data state
 * @param testId The test ID of the component
 * @returns Whether the component is in the data state
 */
export const toBeInDataState = function(this: any, testId: string) {
  const containerElement = screen.queryByTestId(`${testId}-container`);
  const pass = !!containerElement;
  
  return {
    pass,
    message: () => 
      pass
        ? `Expected component with testId "${testId}" not to be in data state`
        : `Expected component with testId "${testId}" to be in data state`,
  };
};

/**
 * Check if a component has a point with the given ID
 * @param testId The test ID of the component
 * @param pointId The ID of the point
 * @returns Whether the component has a point with the given ID
 */
export const toHavePoint = function(this: any, testId: string, pointId: string) {
  const pointElement = screen.queryByTestId(`${testId}-point-${pointId}`);
  const pass = !!pointElement;
  
  return {
    pass,
    message: () => 
      pass
        ? `Expected component with testId "${testId}" not to have point with ID "${pointId}"`
        : `Expected component with testId "${testId}" to have point with ID "${pointId}"`,
  };
};

/**
 * Check if a component has a category with the given name
 * @param testId The test ID of the component
 * @param category The name of the category
 * @returns Whether the component has a category with the given name
 */
export const toHaveCategory = function(this: any, testId: string, category: string) {
  const categoryElement = screen.queryByTestId(`${testId}-category-${category}`);
  const pass = !!categoryElement;
  
  return {
    pass,
    message: () => 
      pass
        ? `Expected component with testId "${testId}" not to have category "${category}"`
        : `Expected component with testId "${testId}" to have category "${category}"`,
  };
};

/**
 * Check if a component has zoom controls
 * @param testId The test ID of the component
 * @returns Whether the component has zoom controls
 */
export const toHaveZoomControls = function(this: any, testId: string) {
  const zoomInElement = screen.queryByTestId(`${testId}-zoom-in`);
  const zoomOutElement = screen.queryByTestId(`${testId}-zoom-out`);
  const pass = !!zoomInElement && !!zoomOutElement;
  
  return {
    pass,
    message: () => 
      pass
        ? `Expected component with testId "${testId}" not to have zoom controls`
        : `Expected component with testId "${testId}" to have zoom controls`,
  };
};

/**
 * Check if a component has a legend
 * @param testId The test ID of the component
 * @returns Whether the component has a legend
 */
export const toHaveLegend = function(this: any, testId: string) {
  const legendElement = screen.queryByTestId(`${testId}-legend`);
  const pass = !!legendElement;
  
  return {
    pass,
    message: () => 
      pass
        ? `Expected component with testId "${testId}" not to have a legend`
        : `Expected component with testId "${testId}" to have a legend`,
  };
};

// Add all matchers to Jest
beforeEach(() => {
  expect.extend({
    toBeInLoadingState,
    toBeInErrorState,
    toBeInEmptyState,
    toBeInDataState,
    toHavePoint,
    toHaveCategory,
    toHaveZoomControls,
    toHaveLegend,
  });
});
