import React from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import '@testing-library/jest-dom';
import mockRecommendationService from '../../services/testing/mockRecommendationService';

// Types for test configuration
export interface TestConfig {
  // Media query mocks
  isMobile?: boolean;
  isTablet?: boolean;
  isDesktop?: boolean;

  // Service mocks
  mockServiceError?: boolean;
  mockServiceEmpty?: boolean;

  // Fetch mock
  mockFetchResponse?: any;
  mockFetchError?: boolean;

  // Component props
  componentProps?: Record<string, any>;
}

// Default test configuration
const defaultConfig: TestConfig = {
  isMobile: false,
  isTablet: false,
  isDesktop: true,
  mockServiceError: false,
  mockServiceEmpty: false,
  mockFetchError: false,
};

/**
 * Set up a test for a visualization component
 * This function sets up all the necessary mocks and renders the component
 *
 * @param Component The component to render
 * @param config Test configuration
 * @param renderOptions Options for the render function
 * @returns The render result
 */
export const setupVisualizationTest = <P extends object>(
  Component: React.ComponentType<P>,
  config: TestConfig = defaultConfig,
  renderOptions?: Omit<RenderOptions, 'wrapper'>
): RenderResult => {
  // Merge config with defaults
  const testConfig = { ...defaultConfig, ...config };

  // Mock the useMediaQuery hook
  jest.mock('../../hooks/useMediaQuery', () => ({
    useIsMobile: jest.fn(() => testConfig.isMobile),
    useIsTablet: jest.fn(() => testConfig.isTablet),
    useIsDesktop: jest.fn(() => testConfig.isDesktop),
    useMediaQuery: jest.fn(() => false),
  }));

  // Mock the recommendationService
  jest.mock('../../services/recommendationService', () => ({
    __esModule: true,
    default: mockRecommendationService,
    getApiUrl: jest.fn(() => 'http://localhost:8000'),
  }));

  // Configure the mock service
  if (testConfig.mockServiceError) {
    mockRecommendationService.mockError(
      'getEmbeddingVisualization',
      new Error('Test error')
    );
  } else if (testConfig.mockServiceEmpty) {
    mockRecommendationService.mockEmpty('getEmbeddingVisualization');
  } else {
    mockRecommendationService.reset();
  }

  // Mock the useErrorHandler hook
  jest.mock('../../hooks/useErrorHandler', () => ({
    useErrorHandler: () => ({
      handleError: jest.fn((err, options) => {
        if (options?.setError) {
          options.setError('Error message');
        }
      }),
    }),
  }));

  // Mock fetch
  const mockFetch = jest.fn();
  global.fetch = mockFetch;

  if (testConfig.mockFetchError) {
    mockFetch.mockImplementation(() =>
      Promise.reject(new Error('Test error'))
    );
  } else if (testConfig.mockFetchResponse) {
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(testConfig.mockFetchResponse),
      })
    );
  } else {
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve([]),
      })
    );
  }

  // Mock framer-motion
  jest.mock('framer-motion', () => require('../../testing/mocks/framerMotionMock'));

  // Render the component with the provided props
  return render(<Component {...(testConfig.componentProps as P)} />, renderOptions);
};

/**
 * Reset all mocks after a test
 * Call this function in afterEach to reset all mocks
 */
export const resetVisualizationTest = () => {
  jest.clearAllMocks();
  mockRecommendationService.reset();
};
