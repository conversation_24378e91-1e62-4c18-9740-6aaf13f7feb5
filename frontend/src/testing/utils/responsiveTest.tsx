import React from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';

/**
 * Breakpoints for responsive design testing
 */
export const breakpoints = {
  xs: 375,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

/**
 * Type for breakpoint keys
 */
export type BreakpointKey = keyof typeof breakpoints;

/**
 * Options for responsive design testing
 */
interface ResponsiveTestOptions extends RenderOptions {
  /**
   * The component to test
   */
  component: React.ReactElement;

  /**
   * The breakpoints to test
   * If not provided, all breakpoints will be tested
   */
  breakpoints?: BreakpointKey[];

  /**
   * A callback to run for each breakpoint
   * This is where you should put your assertions
   */
  callback: (result: RenderResult, breakpoint: BreakpointKey, width: number) => void | Promise<void>;

  /**
   * Whether to test in landscape orientation
   */
  landscape?: boolean;

  /**
   * Whether to test both portrait and landscape orientations
   */
  testBothOrientations?: boolean;
}

/**
 * Mock window.matchMedia for a specific width and orientation
 *
 * @param width The width to mock
 * @param height The height to mock
 * @param landscape Whether to mock landscape orientation
 */
const mockMatchMedia = (width: number, height: number = 800, landscape: boolean = false) => {
  // If in landscape mode, swap width and height
  const finalWidth = landscape ? Math.max(width, height) : width;
  const finalHeight = landscape ? width : height;

  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => {
      // Handle width queries
      const widthMatches = query.includes(`(min-width: ${finalWidth}px)`) ||
                           query.includes(`(max-width: ${finalWidth}px)`);

      // Handle height queries
      const heightMatches = query.includes(`(min-height: ${finalHeight}px)`) ||
                            query.includes(`(max-height: ${finalHeight}px)`);

      // Handle orientation queries
      const orientationMatches =
        (landscape && query.includes('(orientation: landscape)')) ||
        (!landscape && query.includes('(orientation: portrait)'));

      // Determine if the query matches
      const matches = widthMatches || heightMatches || orientationMatches;

      return {
        matches,
        media: query,
        onchange: null,
        addListener: jest.fn(), // Deprecated
        removeListener: jest.fn(), // Deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      };
    }),
  });

  // Mock window dimensions
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    value: finalWidth,
  });

  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    value: finalHeight,
  });

  // Mock screen dimensions
  Object.defineProperty(window.screen, 'width', {
    writable: true,
    value: finalWidth,
  });

  Object.defineProperty(window.screen, 'height', {
    writable: true,
    value: finalHeight,
  });

  // Mock screen orientation
  Object.defineProperty(window.screen, 'orientation', {
    writable: true,
    value: {
      type: landscape ? 'landscape-primary' : 'portrait-primary',
      angle: landscape ? 90 : 0,
    },
  });
};

/**
 * Test a component at different breakpoints
 *
 * @param options Options for the test
 */
export const testResponsive = async (options: ResponsiveTestOptions) => {
  const {
    component,
    breakpoints: breakpointsToTest = Object.keys(breakpoints) as BreakpointKey[],
    callback,
    landscape = false,
    testBothOrientations = false,
    ...renderOptions
  } = options;

  // Test each breakpoint
  for (const breakpoint of breakpointsToTest) {
    const width = breakpoints[breakpoint];

    // If testing both orientations, test portrait first
    if (testBothOrientations) {
      // Mock matchMedia for portrait orientation
      mockMatchMedia(width, 800, false);

      // Render the component
      const portraitResult = render(component, renderOptions);

      // Run the callback
      await callback(portraitResult, breakpoint, width);

      // Clean up
      portraitResult.unmount();

      // Mock matchMedia for landscape orientation
      mockMatchMedia(width, 800, true);

      // Render the component
      const landscapeResult = render(component, renderOptions);

      // Run the callback
      await callback(landscapeResult, breakpoint, width);

      // Clean up
      landscapeResult.unmount();
    } else {
      // Mock matchMedia for this width and orientation
      mockMatchMedia(width, 800, landscape);

      // Render the component
      const result = render(component, renderOptions);

      // Run the callback
      await callback(result, breakpoint, width);

      // Clean up
      result.unmount();
    }
  }
};

/**
 * Test a component at a specific breakpoint
 *
 * @param breakpoint The breakpoint to test
 * @param component The component to test
 * @param renderOptions Options for rendering
 * @param landscape Whether to test in landscape orientation
 * @param height The height to use for the test
 * @returns The render result
 */
export const renderAtBreakpoint = (
  breakpoint: BreakpointKey,
  component: React.ReactElement,
  renderOptions?: RenderOptions,
  landscape: boolean = false,
  height: number = 800
): RenderResult => {
  const width = breakpoints[breakpoint];

  // Mock matchMedia for this width and orientation
  mockMatchMedia(width, height, landscape);

  // Render the component
  return render(component, renderOptions);
};

/**
 * Test a component in both portrait and landscape orientations at a specific breakpoint
 *
 * @param breakpoint The breakpoint to test
 * @param component The component to test
 * @param callback A callback to run for each orientation
 * @param renderOptions Options for rendering
 */
export const testOrientations = async (
  breakpoint: BreakpointKey,
  component: React.ReactElement,
  callback: (result: RenderResult, orientation: 'portrait' | 'landscape') => void | Promise<void>,
  renderOptions?: RenderOptions
): Promise<void> => {
  // Test in portrait orientation
  const portraitResult = renderAtBreakpoint(breakpoint, component, renderOptions, false);
  await callback(portraitResult, 'portrait');
  portraitResult.unmount();

  // Test in landscape orientation
  const landscapeResult = renderAtBreakpoint(breakpoint, component, renderOptions, true);
  await callback(landscapeResult, 'landscape');
  landscapeResult.unmount();
};

/**
 * Example usage:
 *
 * ```typescript
 * // Test at all breakpoints
 * it('is responsive', async () => {
 *   await testResponsive({
 *     component: <MyComponent />,
 *     callback: (result, breakpoint, width) => {
 *       // Test that the component renders correctly at this breakpoint
 *       if (breakpoint === 'xs') {
 *         expect(result.getByTestId('mobile-view')).toBeInTheDocument();
 *       } else {
 *         expect(result.getByTestId('desktop-view')).toBeInTheDocument();
 *       }
 *     },
 *   });
 * });
 *
 * // Test at a specific breakpoint
 * it('renders correctly on mobile', () => {
 *   const { getByTestId } = renderAtBreakpoint('xs', <MyComponent />);
 *   expect(getByTestId('mobile-view')).toBeInTheDocument();
 * });
 *
 * // Test at a specific breakpoint in landscape mode
 * it('renders correctly on mobile in landscape', () => {
 *   const { getByTestId } = renderAtBreakpoint('xs', <MyComponent />, {}, true);
 *   expect(getByTestId('mobile-landscape-view')).toBeInTheDocument();
 * });
 *
 * // Test at a specific breakpoint in both orientations
 * it('handles orientation changes', async () => {
 *   await testOrientations('md', <MyComponent />, (result, orientation) => {
 *     if (orientation === 'portrait') {
 *       expect(result.getByTestId('portrait-view')).toBeInTheDocument();
 *     } else {
 *       expect(result.getByTestId('landscape-view')).toBeInTheDocument();
 *     }
 *   });
 * });
 *
 * // Test at all breakpoints in both orientations
 * it('is fully responsive', async () => {
 *   await testResponsive({
 *     component: <MyComponent />,
 *     testBothOrientations: true,
 *     callback: (result, breakpoint, width) => {
 *       // The component will be tested in both portrait and landscape
 *       // at each breakpoint
 *       expect(result.getByTestId('responsive-view')).toBeInTheDocument();
 *     },
 *   });
 * });
 * ```
 */
