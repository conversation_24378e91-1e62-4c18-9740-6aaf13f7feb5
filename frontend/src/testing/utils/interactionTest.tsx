import React from 'react';
import { render, RenderOptions, RenderResult, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

/**
 * A user interaction to test
 */
export interface UserInteraction {
  /**
   * The type of interaction
   */
  type: 'click' | 'hover' | 'type' | 'select' | 'check' | 'clear' | 'tab' | 'press' | 'drag' | 'custom';

  /**
   * The test ID of the element to interact with
   */
  testId: string;

  /**
   * The value to use for the interaction (e.g. text to type)
   */
  value?: string;

  /**
   * A custom interaction function
   * Only used when type is 'custom'
   */
  customInteraction?: (result: RenderResult) => void | Promise<void>;

  /**
   * A description of the interaction (for logging)
   */
  description?: string;

  /**
   * A callback to run after the interaction
   * This is where you should put your assertions
   */
  callback?: (result: RenderResult) => void | Promise<void>;

  /**
   * The timeout for waitFor after the interaction
   */
  timeout?: number;
}

/**
 * Options for user interaction testing
 */
interface InteractionTestOptions extends RenderOptions {
  /**
   * The component to test
   */
  component: React.ReactElement;

  /**
   * The interactions to test
   */
  interactions: UserInteraction[];

  /**
   * Whether to log the interactions
   */
  log?: boolean;
}

/**
 * Test a series of user interactions
 *
 * @param options Options for the test
 */
export const testInteractions = async (options: InteractionTestOptions) => {
  const {
    component,
    interactions,
    log = false,
    ...renderOptions
  } = options;

  // Render the component
  const result = render(component, renderOptions);

  // Run each interaction
  for (const interaction of interactions) {
    const {
      type,
      testId,
      value,
      customInteraction,
      description,
      callback,
      timeout = 1000,
    } = interaction;

    // Log the interaction if requested
    if (log) {
      console.log(`Interaction: ${description || type} on ${testId}`);
    }

    // Get the element
    const element = result.getByTestId(testId);

    // Perform the interaction
    switch (type) {
      case 'click':
        fireEvent.click(element);
        break;
      case 'hover':
        fireEvent.mouseOver(element);
        break;
      case 'type':
        if (value) {
          userEvent.type(element, value);
        }
        break;
      case 'select':
        if (value) {
          userEvent.selectOptions(element, value);
        }
        break;
      case 'check':
        fireEvent.click(element);
        break;
      case 'clear':
        userEvent.clear(element);
        break;
      case 'tab':
        userEvent.tab();
        break;
      case 'press':
        if (value) {
          userEvent.keyboard(value);
        }
        break;
      case 'drag':
        // Simple drag simulation
        fireEvent.mouseDown(element);
        fireEvent.mouseMove(element);
        fireEvent.mouseUp(element);
        break;
      case 'custom':
        if (customInteraction) {
          await customInteraction(result);
        }
        break;
    }

    // Wait for any updates
    await waitFor(() => {}, { timeout });

    // Run the callback if provided
    if (callback) {
      await callback(result);
    }
  }

  // Return the render result
  return result;
};

/**
 * Example usage:
 *
 * ```typescript
 * it('handles user interactions correctly', async () => {
 *   await testInteractions({
 *     component: <MyComponent />,
 *     interactions: [
 *       {
 *         type: 'click',
 *         testId: 'button-1',
 *         description: 'Click the first button',
 *         callback: (result) => {
 *           expect(result.getByTestId('panel-1')).toBeVisible();
 *         },
 *       },
 *       {
 *         type: 'type',
 *         testId: 'input-1',
 *         value: 'Hello, world!',
 *         description: 'Type in the input',
 *         callback: (result) => {
 *           expect(result.getByTestId('input-1')).toHaveValue('Hello, world!');
 *         },
 *       },
 *       {
 *         type: 'click',
 *         testId: 'submit-button',
 *         description: 'Submit the form',
 *         callback: (result) => {
 *           expect(result.getByTestId('success-message')).toBeInTheDocument();
 *         },
 *       },
 *     ],
 *     log: true,
 *   });
 * });
 * ```
 */
