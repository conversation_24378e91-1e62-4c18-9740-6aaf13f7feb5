import React from 'react';
import { render, RenderOptions } from '@testing-library/react';

/**
 * Options for performance testing
 */
interface PerformanceTestOptions extends RenderOptions {
  /**
   * The component to test
   */
  component: React.ReactElement;

  /**
   * The number of times to render the component
   */
  iterations?: number;

  /**
   * The maximum acceptable average render time in milliseconds
   */
  maxAverageRenderTime?: number;

  /**
   * Whether to log the results
   */
  log?: boolean;
}

/**
 * Performance test results
 */
export interface PerformanceTestResults {
  /**
   * The average render time in milliseconds
   */
  averageRenderTime: number;

  /**
   * The minimum render time in milliseconds
   */
  minRenderTime: number;

  /**
   * The maximum render time in milliseconds
   */
  maxRenderTime: number;

  /**
   * All render times in milliseconds
   */
  renderTimes: number[];

  /**
   * Whether the test passed
   */
  passed: boolean;
}

/**
 * Test the rendering performance of a component
 *
 * @param options Options for the test
 * @returns The test results
 */
export const testPerformance = (options: PerformanceTestOptions): PerformanceTestResults => {
  const {
    component,
    iterations = 100,
    maxAverageRenderTime = 16, // 60fps = 16.67ms per frame
    log = false,
    ...renderOptions
  } = options;

  // Array to store render times
  const renderTimes: number[] = [];

  // Run the test
  for (let i = 0; i < iterations; i++) {
    // Measure render time
    const start = performance.now();
    const { unmount } = render(component, renderOptions);
    const end = performance.now();

    // Calculate render time
    const renderTime = end - start;
    renderTimes.push(renderTime);

    // Clean up
    unmount();
  }

  // Calculate statistics
  const averageRenderTime = renderTimes.reduce((sum, time) => sum + time, 0) / renderTimes.length;
  const minRenderTime = Math.min(...renderTimes);
  const maxRenderTime = Math.max(...renderTimes);
  const passed = averageRenderTime <= maxAverageRenderTime;

  // Create results
  const results: PerformanceTestResults = {
    averageRenderTime,
    minRenderTime,
    maxRenderTime,
    renderTimes,
    passed,
  };

  // Log results if requested
  if (log) {
    console.log('Performance Test Results:');
    console.log(`  Average Render Time: ${averageRenderTime.toFixed(2)}ms`);
    console.log(`  Min Render Time: ${minRenderTime.toFixed(2)}ms`);
    console.log(`  Max Render Time: ${maxRenderTime.toFixed(2)}ms`);
    console.log(`  Iterations: ${iterations}`);
    console.log(`  Passed: ${passed ? 'Yes' : 'No'}`);
  }

  // Return results
  return results;
};

/**
 * Test the rendering performance of a component with different props
 *
 * @param Component The component to test
 * @param propsVariations Different props to test
 * @param options Options for the test
 * @returns The test results for each props variation
 */
export const testPerformanceWithProps = <P extends object>(
  Component: React.ComponentType<P>,
  propsVariations: Record<string, P>,
  options?: Omit<PerformanceTestOptions, 'component'>
): Record<string, PerformanceTestResults> => {
  // Run the test for each props variation
  const results: Record<string, PerformanceTestResults> = {};

  for (const [name, props] of Object.entries(propsVariations)) {
    results[name] = testPerformance({
      component: <Component {...props} />,
      ...options,
    });
  }

  // Return results
  return results;
};

/**
 * Example usage:
 *
 * ```typescript
 * it('renders efficiently', () => {
 *   const results = testPerformance({
 *     component: <MyComponent />,
 *     iterations: 50,
 *     maxAverageRenderTime: 10,
 *     log: true,
 *   });
 *
 *   expect(results.passed).toBe(true);
 * });
 *
 * it('renders efficiently with different props', () => {
 *   const results = testPerformanceWithProps(
 *     MyComponent,
 *     {
 *       empty: { data: [] },
 *       small: { data: mockSmallData },
 *       large: { data: mockLargeData },
 *     },
 *     { log: true }
 *   );
 *
 *   expect(results.empty.passed).toBe(true);
 *   expect(results.small.passed).toBe(true);
 *   expect(results.large.passed).toBe(true);
 * });
 * ```
 */
