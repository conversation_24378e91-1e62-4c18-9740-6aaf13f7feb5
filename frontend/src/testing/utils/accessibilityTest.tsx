import React from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';

// Extend Jest's expect
expect.extend(toHaveNoViolations);

/**
 * Options for accessibility testing
 */
export interface AccessibilityTestOptions extends RenderOptions {
  /**
   * The component to test
   */
  component: React.ReactElement;

  /**
   * The name of the component (for test output)
   */
  componentName?: string;

  /**
   * Whether to run the test immediately
   * If false, the test will be returned and can be run later
   */
  runImmediately?: boolean;

  /**
   * Options for axe
   */
  axeOptions?: axe.RunOptions;

  /**
   * Whether to test keyboard navigation
   */
  testKeyboardNavigation?: boolean;

  /**
   * Whether to test color contrast
   */
  testColorContrast?: boolean;

  /**
   * Whether to test screen reader announcements
   */
  testScreenReader?: boolean;

  /**
   * WCAG level to test (A, AA, or AAA)
   */
  wcagLevel?: 'A' | 'AA' | 'AAA';
}

/**
 * Test a component for accessibility issues
 *
 * @param options Options for the test
 * @returns A function that runs the test
 */
export const testAccessibility = async (options: AccessibilityTestOptions) => {
  const {
    component,
    componentName = 'Component',
    runImmediately = true,
    axeOptions,
    testKeyboardNavigation = false,
    testColorContrast = true,
    testScreenReader = false,
    wcagLevel,
    ...renderOptions
  } = options;

  // Render the component
  const result = render(component, renderOptions);
  const { container } = result;

  // Configure axe options based on WCAG level
  let finalAxeOptions = { ...axeOptions };

  if (wcagLevel) {
    finalAxeOptions = {
      ...finalAxeOptions,
      ...getWCAGOptions(wcagLevel),
    };
  }

  // Ensure color contrast testing is enabled/disabled as requested
  if (finalAxeOptions?.rules && testColorContrast) {
    finalAxeOptions.rules['color-contrast'] = { enabled: true };
  } else if (finalAxeOptions?.rules && !testColorContrast) {
    finalAxeOptions.rules['color-contrast'] = { enabled: false };
  }

  // Create the test function
  const runTest = async () => {
    // Run axe on the container
    const results = await axe(container, finalAxeOptions);

    // Check for violations
    expect(results).toHaveNoViolations();

    // Test keyboard navigation if requested
    if (testKeyboardNavigation) {
      await testKeyboardAccessibility(result);
    }

    // Test screen reader announcements if requested
    if (testScreenReader) {
      await testScreenReaderAnnouncements(result);
    }
  };

  // Run the test immediately if requested
  if (runImmediately) {
    await runTest();
  }

  // Return the test function
  return runTest;
};

/**
 * Test a component for accessibility issues in different states
 *
 * @param options Options for the test
 * @returns A function that runs the test
 */
export const testAccessibilityInStates = async <P extends object>(
  Component: React.ComponentType<P>,
  states: Record<string, P>,
  options?: Omit<AccessibilityTestOptions, 'component' | 'componentName'>
) => {
  // Create a test for each state
  const tests = Object.entries(states).map(([stateName, props]) => {
    return testAccessibility({
      component: <Component {...props} />,
      componentName: `${Component.displayName || Component.name} (${stateName})`,
      runImmediately: false,
      ...options,
    });
  });

  // Return a function that runs all tests
  return async () => {
    for (const test of tests) {
      await test();
    }
  };
};

/**
 * Test keyboard navigation for a component
 *
 * @param result The render result
 */
const testKeyboardAccessibility = async (result: RenderResult) => {
  const { container } = result;

  // Find all focusable elements
  const focusableElements = container.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  );

  // Check that there are focusable elements
  if (focusableElements.length === 0) {
    // If there are no focusable elements, this test is not applicable
    return;
  }

  // Check that each element can be focused
  for (let i = 0; i < focusableElements.length; i++) {
    const element = focusableElements[i] as HTMLElement;
    element.focus();
    expect(document.activeElement).toBe(element);
  }
};

/**
 * Test screen reader announcements for a component
 *
 * @param result The render result
 */
const testScreenReaderAnnouncements = async (result: RenderResult) => {
  const { container } = result;

  // Find all live regions
  const liveRegions = container.querySelectorAll('[aria-live]');

  // If there are no live regions, this test is not applicable
  if (liveRegions.length === 0) {
    return;
  }

  // Check that each live region has the correct attributes
  for (let i = 0; i < liveRegions.length; i++) {
    const region = liveRegions[i] as HTMLElement;
    expect(region.getAttribute('aria-live')).toMatch(/polite|assertive/);
  }
};

/**
 * Get axe options for a specific WCAG level
 *
 * @param wcagLevel The WCAG level to test (A, AA, or AAA)
 * @returns Axe options for the specified WCAG level
 */
const getWCAGOptions = (wcagLevel: 'A' | 'AA' | 'AAA'): axe.RunOptions => {
  // Define rules for each WCAG level
  const wcagRules: Record<string, string[]> = {
    'A': [
      'aria-allowed-attr',
      'aria-hidden-body',
      'aria-required-attr',
      'aria-required-children',
      'aria-required-parent',
      'aria-roles',
      'aria-valid-attr-value',
      'aria-valid-attr',
      'button-name',
      'document-title',
      'duplicate-id-active',
      'duplicate-id',
      'html-has-lang',
      'html-lang-valid',
      'image-alt',
      'input-button-name',
      'input-image-alt',
      'link-name',
      'list',
      'listitem',
      'meta-refresh',
      'meta-viewport',
      'nested-interactive',
      'no-autoplay-audio',
      'role-img-alt',
      'scrollable-region-focusable',
      'valid-lang',
    ],
    'AA': [
      'color-contrast',
      'dlitem',
      'form-field-multiple-labels',
      'frame-title',
      'heading-order',
      'label',
      'landmark-banner-is-top-level',
      'landmark-complementary-is-top-level',
      'landmark-contentinfo-is-top-level',
      'landmark-main-is-top-level',
      'landmark-no-duplicate-banner',
      'landmark-no-duplicate-contentinfo',
      'landmark-one-main',
      'meta-viewport-large',
      'region',
      'skip-link',
      'table-duplicate-name',
      'table-fake-caption',
      'td-has-header',
    ],
    'AAA': [
      'accesskeys',
      'focus-order-semantics',
      'frame-tested',
      'landmark-unique',
      'p-as-heading',
      'tabindex',
    ],
  };

  // Determine which rules to enable based on the WCAG level
  const rulesToEnable: string[] = [];

  if (wcagLevel === 'A' || wcagLevel === 'AA' || wcagLevel === 'AAA') {
    rulesToEnable.push(...wcagRules['A']);
  }

  if (wcagLevel === 'AA' || wcagLevel === 'AAA') {
    rulesToEnable.push(...wcagRules['AA']);
  }

  if (wcagLevel === 'AAA') {
    rulesToEnable.push(...wcagRules['AAA']);
  }

  // Create axe options with the appropriate rules
  const options: axe.RunOptions = {
    rules: {},
  };

  // Enable the appropriate rules
  rulesToEnable.forEach(rule => {
    options.rules![rule] = { enabled: true };
  });

  return options;
};

/**
 * Test a component for WCAG compliance
 *
 * @param component The component to test
 * @param wcagLevel The WCAG level to test (A, AA, or AAA)
 * @param options Additional options for the test
 * @returns A function that runs the test
 */
export const testWCAG = async (
  component: React.ReactElement,
  wcagLevel: 'A' | 'AA' | 'AAA' = 'AA',
  options?: Omit<AccessibilityTestOptions, 'component' | 'wcagLevel'>
) => {
  return testAccessibility({
    component,
    wcagLevel,
    testKeyboardNavigation: true,
    testColorContrast: true,
    testScreenReader: true,
    ...options,
  });
};

/**
 * Example usage:
 *
 * ```typescript
 * it('passes accessibility tests', async () => {
 *   await testAccessibility({
 *     component: <MyComponent />,
 *     componentName: 'MyComponent',
 *   });
 * });
 *
 * it('passes accessibility tests in all states', async () => {
 *   await testAccessibilityInStates(
 *     MyComponent,
 *     {
 *       loading: { loading: true },
 *       error: { error: 'Error message' },
 *       empty: { data: [] },
 *       data: { data: mockData },
 *     }
 *   );
 * });
 *
 * it('meets WCAG AA standards', async () => {
 *   await testWCAG(<MyComponent />, 'AA');
 * });
 * ```
 */

export default {
  testAccessibility,
  testAccessibilityInStates,
  testWCAG,
};
