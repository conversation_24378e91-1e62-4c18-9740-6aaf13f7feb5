import React from 'react';

/**
 * Comprehensive mock for Framer Motion
 * This mock handles all the animation props used in the project
 */

// Helper to filter out animation props
const filterAnimationProps = (props: any) => {
  const {
    // Animation props to filter out
    animate,
    initial,
    exit,
    transition,
    variants,
    whileHover,
    whileTap,
    whileFocus,
    whileDrag,
    whileInView,
    viewport,
    drag,
    dragConstraints,
    dragElastic,
    dragMomentum,
    dragTransition,
    dragControls,
    dragListener,
    dragSnapToOrigin,
    // Layout props
    layout,
    layoutId,
    layoutDependency,
    layoutScroll,
    // Other props
    onAnimationStart,
    onAnimationComplete,
    onLayoutAnimationStart,
    onLayoutAnimationComplete,
    onDragStart,
    onDragEnd,
    onDrag,
    onDirectionLock,
    onDragTransitionEnd,
    ...rest
  } = props;
  
  return rest;
};

// Create mock components for each HTML element
const createMockComponent = (Element: any) => {
  return React.forwardRef(({ children, ...props }: any, ref: any) => {
    const filteredProps = filterAnimationProps(props);
    return <Element ref={ref} {...filteredProps}>{children}</Element>;
  });
};

// Create mock for AnimatePresence
const AnimatePresence = ({ children, onExitComplete, initial, mode, custom }: any) => {
  return <>{children}</>;
};

// Create mock for motion components
const motion = {
  div: createMockComponent('div'),
  span: createMockComponent('span'),
  button: createMockComponent('button'),
  a: createMockComponent('a'),
  ul: createMockComponent('ul'),
  li: createMockComponent('li'),
  p: createMockComponent('p'),
  h1: createMockComponent('h1'),
  h2: createMockComponent('h2'),
  h3: createMockComponent('h3'),
  h4: createMockComponent('h4'),
  h5: createMockComponent('h5'),
  h6: createMockComponent('h6'),
  img: createMockComponent('img'),
  svg: createMockComponent('svg'),
  path: createMockComponent('path'),
  circle: createMockComponent('circle'),
  rect: createMockComponent('rect'),
  line: createMockComponent('line'),
  input: createMockComponent('input'),
  textarea: createMockComponent('textarea'),
  select: createMockComponent('select'),
  option: createMockComponent('option'),
  form: createMockComponent('form'),
  label: createMockComponent('label'),
  nav: createMockComponent('nav'),
  header: createMockComponent('header'),
  footer: createMockComponent('footer'),
  main: createMockComponent('main'),
  section: createMockComponent('section'),
  article: createMockComponent('article'),
  aside: createMockComponent('aside'),
  // Add more as needed
};

// Mock for useAnimation hook
const useAnimation = () => {
  return {
    start: jest.fn(),
    stop: jest.fn(),
    set: jest.fn(),
    mount: jest.fn(),
    unmount: jest.fn(),
  };
};

// Mock for useMotionValue hook
const useMotionValue = (initial: any) => {
  return {
    get: () => initial,
    set: jest.fn(),
    onChange: jest.fn(),
    clearListeners: jest.fn(),
    getVelocity: () => 0,
    getAnimationState: () => ({ isAnimating: false }),
    stop: jest.fn(),
    isAnimating: () => false,
  };
};

// Mock for useTransform hook
const useTransform = (value: any, inputRange: any, outputRange: any) => {
  return useMotionValue(outputRange[0]);
};

// Mock for useSpring hook
const useSpring = (initial: any, config: any) => {
  return useMotionValue(initial);
};

// Mock for useCycle hook
const useCycle = (...items: any[]) => {
  const [index, setIndex] = React.useState(0);
  const cycle = (newIndex?: number) => {
    if (typeof newIndex === 'number') {
      setIndex(newIndex);
    } else {
      setIndex((index + 1) % items.length);
    }
  };
  return [items[index], cycle];
};

// Mock for useInView hook
const useInView = (ref: any, options: any = {}) => {
  return { inView: true, entry: null };
};

// Mock for useScroll hook
const useScroll = (options: any = {}) => {
  return {
    scrollX: useMotionValue(0),
    scrollY: useMotionValue(0),
    scrollXProgress: useMotionValue(0),
    scrollYProgress: useMotionValue(0),
  };
};

// Mock for useViewportScroll hook (deprecated but still used in some projects)
const useViewportScroll = () => {
  return {
    scrollX: useMotionValue(0),
    scrollY: useMotionValue(0),
    scrollXProgress: useMotionValue(0),
    scrollYProgress: useMotionValue(0),
  };
};

// Export all mocks
export {
  motion,
  AnimatePresence,
  useAnimation,
  useMotionValue,
  useTransform,
  useSpring,
  useCycle,
  useInView,
  useScroll,
  useViewportScroll,
};

// Default export for jest.mock
export default {
  motion,
  AnimatePresence,
  useAnimation,
  useMotionValue,
  useTransform,
  useSpring,
  useCycle,
  useInView,
  useScroll,
  useViewportScroll,
};
