import React, { ReactNode } from 'react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { ToastProvider } from '../contexts/ToastContext';

interface TestProvidersProps {
  children: ReactNode;
}

/**
 * A wrapper component that provides all necessary context providers for testing
 * This includes:
 * - ToastProvider for toast notifications
 * - BrowserRouter for routing
 */
export const TestProviders: React.FC<TestProvidersProps> = ({ children }) => {
  return (
    <BrowserRouter>
      <ToastProvider>
        {children}
      </ToastProvider>
    </BrowserRouter>
  );
};

export default TestProviders;
