// Import Jest DOM matchers
import '@testing-library/jest-dom';

// Import custom matchers
import './matchers/visualizationMatchers';

// Polyfill for TextEncoder and TextDecoder
class TextEncoderPolyfill {
  encode(text: string): Uint8Array {
    const encoded = new Uint8Array(text.length);
    for (let i = 0; i < text.length; i++) {
      encoded[i] = text.charCodeAt(i);
    }
    return encoded;
  }
}

class TextDecoderPolyfill {
  decode(buffer: Uint8Array): string {
    return String.fromCharCode.apply(null, Array.from(buffer));
  }
}

global.TextEncoder = TextEncoderPolyfill as any;
global.TextDecoder = TextDecoderPolyfill as any;

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock window.ResizeObserver
class ResizeObserverMock {
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
}

Object.defineProperty(window, 'ResizeObserver', {
  writable: true,
  value: ResizeObserverMock,
});

// Mock window.IntersectionObserver
class IntersectionObserverMock {
  constructor(callback: IntersectionObserverCallback) {
    this.callback = callback;
  }

  callback: IntersectionObserverCallback;
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();

  // Helper method to simulate intersection
  simulateIntersection(entries: IntersectionObserverEntry[]) {
    this.callback(entries, this);
  }
}

Object.defineProperty(window, 'IntersectionObserver', {
  writable: true,
  value: IntersectionObserverMock,
});

// Mock console methods to catch warnings and errors
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

beforeAll(() => {
  console.error = jest.fn((...args) => {
    // Check if this is a React warning about act()
    const message = args.join(' ');
    if (
      message.includes('Warning: An update to') &&
      message.includes('inside a test was not wrapped in act')
    ) {
      // Ignore act warnings
      return;
    }

    // Pass through to original console.error
    originalConsoleError(...args);
  });

  console.warn = jest.fn((...args) => {
    // Check if this is a React warning about deprecated features
    const message = args.join(' ');
    if (
      message.includes('Warning: ReactDOM.render is no longer supported') ||
      message.includes('Warning: findDOMNode is deprecated')
    ) {
      // Ignore deprecation warnings
      return;
    }

    // Pass through to original console.warn
    originalConsoleWarn(...args);
  });

  // Silence console.log in tests
  console.log = jest.fn();
});

afterAll(() => {
  // Restore console methods
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  console.log = originalConsoleLog;
});

// Set up fake timers
jest.useFakeTimers();

// Clean up after each test
afterEach(() => {
  // Clear all mocks
  jest.clearAllMocks();

  // Reset fake timers
  jest.runOnlyPendingTimers();
  jest.clearAllTimers();
});

// Mock import.meta.env for Vite environment variables in Jest
// This is needed because Jest doesn't support import.meta
if (typeof global.import === 'undefined') {
  // @ts-ignore - intentionally adding this to the global scope for tests
  global.import = {
    meta: {
      env: {
        MODE: 'test',
        DEV: true,
        PROD: false,
        SSR: false,
        BASE_URL: '/',
        VITE_API_URL: 'http://localhost:8000',
      },
    },
  };
}
