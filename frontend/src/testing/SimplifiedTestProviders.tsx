import React, { createContext, ReactNode, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { ToastType } from '../contexts/ToastContext';

// Create a simplified version of the ToastContext
interface ToastMessage {
  id?: string;
  type: ToastType;
  title: string;
  message: string;
  duration?: number;
}

interface ToastContextType {
  toasts: ToastMessage[];
  showToast: (toast: Omit<ToastMessage, 'id'>) => void;
  hideToast: (id: string) => void;
}

const SimplifiedToastContext = createContext<ToastContextType | undefined>(undefined);

// Create a simplified version of the ToastProvider
const SimplifiedToastProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastMessage[]>([]);

  const showToast = (toast: Omit<ToastMessage, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast = { ...toast, id, duration: toast.duration || 5000 };
    setToasts((prevToasts) => [...prevToasts, newToast]);
  };

  const hideToast = (id: string) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
  };

  return (
    <SimplifiedToastContext.Provider value={{ toasts, showToast, hideToast }}>
      {children}
      <div className="fixed top-4 right-4 z-50 flex flex-col gap-2">
        {toasts.map((toast) => (
          <div 
            key={toast.id}
            data-testid={`toast-${toast.type}`}
            className="bg-white border-l-4 shadow-md rounded-md overflow-hidden"
          >
            <div className="p-4">
              <div className="flex items-start">
                <div className="ml-3 w-0 flex-1 pt-0.5">
                  <p className="text-sm font-medium">{toast.title}</p>
                  <p className="mt-1 text-sm">{toast.message}</p>
                </div>
                <button
                  data-testid={`toast-close-${toast.id}`}
                  onClick={() => hideToast(toast.id!)}
                >
                  <span className="sr-only">Close</span>
                  X
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </SimplifiedToastContext.Provider>
  );
};

// Create a simplified version of the TestProviders component
interface SimplifiedTestProvidersProps {
  children: ReactNode;
}

export const SimplifiedTestProviders: React.FC<SimplifiedTestProvidersProps> = ({ children }) => {
  return (
    <BrowserRouter>
      <SimplifiedToastProvider>
        {children}
      </SimplifiedToastProvider>
    </BrowserRouter>
  );
};

export default SimplifiedTestProviders;
