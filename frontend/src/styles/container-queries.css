/*
 * RentUp Container Queries
 * A comprehensive container query system for component-level responsiveness
 * Based on the latest CSS Container Queries specification
 */

/*
 * Container Query Breakpoints
 * These breakpoints are used for container queries, not viewport queries
 */
:root {
  /* Container breakpoint values */
  --container-xs: 20rem;   /* 320px - Small containers */
  --container-sm: 30rem;   /* 480px - Medium containers */
  --container-md: 40rem;   /* 640px - Large containers */
  --container-lg: 50rem;   /* 800px - Extra large containers */
  --container-xl: 60rem;   /* 960px - Super large containers */
}

/*
 * Container Query Context
 * Apply these classes to create a container query context
 */
.cq-container {
  container-type: inline-size;
  container-name: component;
}

.cq-container-strict {
  container-type: size;
  container-name: component-strict;
}

/*
 * Container Query Utilities
 * Apply these classes to elements that should respond to container size
 */

/* Container Query Display Classes */
@container component (max-width: 20rem) {
  .cq-block-xs {
    display: block;
  }
  
  .cq-hidden-xs {
    display: none;
  }
  
  .cq-flex-col-xs {
    flex-direction: column;
  }
}

@container component (min-width: 20.0625rem) and (max-width: 30rem) {
  .cq-block-sm {
    display: block;
  }
  
  .cq-hidden-sm {
    display: none;
  }
  
  .cq-flex-col-sm {
    flex-direction: column;
  }
}

@container component (min-width: 30.0625rem) and (max-width: 40rem) {
  .cq-block-md {
    display: block;
  }
  
  .cq-hidden-md {
    display: none;
  }
  
  .cq-flex-col-md {
    flex-direction: column;
  }
}

@container component (min-width: 40.0625rem) and (max-width: 50rem) {
  .cq-block-lg {
    display: block;
  }
  
  .cq-hidden-lg {
    display: none;
  }
  
  .cq-flex-col-lg {
    flex-direction: column;
  }
}

@container component (min-width: 50.0625rem) {
  .cq-block-xl {
    display: block;
  }
  
  .cq-hidden-xl {
    display: none;
  }
  
  .cq-flex-col-xl {
    flex-direction: column;
  }
}

/* Container Query Grid Classes */
@container component (max-width: 20rem) {
  .cq-grid-cols-1-xs {
    grid-template-columns: 1fr;
  }
  
  .cq-grid-cols-2-xs {
    grid-template-columns: repeat(2, 1fr);
  }
}

@container component (min-width: 20.0625rem) and (max-width: 30rem) {
  .cq-grid-cols-1-sm {
    grid-template-columns: 1fr;
  }
  
  .cq-grid-cols-2-sm {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .cq-grid-cols-3-sm {
    grid-template-columns: repeat(3, 1fr);
  }
}

@container component (min-width: 30.0625rem) and (max-width: 40rem) {
  .cq-grid-cols-2-md {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .cq-grid-cols-3-md {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .cq-grid-cols-4-md {
    grid-template-columns: repeat(4, 1fr);
  }
}

@container component (min-width: 40.0625rem) {
  .cq-grid-cols-3-lg {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .cq-grid-cols-4-lg {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .cq-grid-cols-5-lg {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* Container Query Spacing Classes */
@container component (max-width: 20rem) {
  .cq-p-2-xs {
    padding: 0.5rem;
  }
  
  .cq-gap-2-xs {
    gap: 0.5rem;
  }
}

@container component (min-width: 20.0625rem) and (max-width: 30rem) {
  .cq-p-3-sm {
    padding: 0.75rem;
  }
  
  .cq-gap-3-sm {
    gap: 0.75rem;
  }
}

@container component (min-width: 30.0625rem) and (max-width: 40rem) {
  .cq-p-4-md {
    padding: 1rem;
  }
  
  .cq-gap-4-md {
    gap: 1rem;
  }
}

@container component (min-width: 40.0625rem) {
  .cq-p-6-lg {
    padding: 1.5rem;
  }
  
  .cq-gap-6-lg {
    gap: 1.5rem;
  }
}

/* Container Query Typography Classes */
@container component (max-width: 20rem) {
  .cq-text-sm-xs {
    font-size: 0.875rem;
  }
}

@container component (min-width: 20.0625rem) and (max-width: 30rem) {
  .cq-text-base-sm {
    font-size: 1rem;
  }
}

@container component (min-width: 30.0625rem) {
  .cq-text-lg-md {
    font-size: 1.125rem;
  }
}
