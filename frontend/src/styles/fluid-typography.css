/*
 * RentUp Fluid Typography System
 * A comprehensive fluid typography system using CSS clamp for smooth scaling
 * Based on the latest CSS techniques for responsive typography
 */

:root {
  /* Base font sizes */
  --font-size-xs: 0.75rem;   /* 12px */
  --font-size-sm: 0.875rem;  /* 14px */
  --font-size-base: 1rem;    /* 16px */
  --font-size-lg: 1.125rem;  /* 18px */
  --font-size-xl: 1.25rem;   /* 20px */
  --font-size-2xl: 1.5rem;   /* 24px */
  --font-size-3xl: 1.875rem; /* 30px */
  --font-size-4xl: 2.25rem;  /* 36px */
  --font-size-5xl: 3rem;     /* 48px */
  --font-size-6xl: 3.75rem;  /* 60px */
  --font-size-7xl: 4.5rem;   /* 72px */
  --font-size-8xl: 6rem;     /* 96px */
  --font-size-9xl: 8rem;     /* 128px */
  
  /* Fluid typography using clamp */
  --fluid-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
  --fluid-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
  --fluid-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
  --fluid-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
  --fluid-xl: clamp(1.25rem, 1.125rem + 0.75vw, 1.5rem);
  --fluid-2xl: clamp(1.5rem, 1.25rem + 1.25vw, 1.875rem);
  --fluid-3xl: clamp(1.875rem, 1.5rem + 1.875vw, 2.25rem);
  --fluid-4xl: clamp(2.25rem, 1.75rem + 2.5vw, 3rem);
  --fluid-5xl: clamp(3rem, 2.25rem + 3.75vw, 3.75rem);
  --fluid-6xl: clamp(3.75rem, 2.75rem + 5vw, 4.5rem);
  
  /* Fluid heading sizes */
  --fluid-h1: clamp(2rem, 1.5rem + 2.5vw, 3rem);
  --fluid-h2: clamp(1.5rem, 1.25rem + 1.25vw, 2.25rem);
  --fluid-h3: clamp(1.25rem, 1.125rem + 0.625vw, 1.5rem);
  --fluid-h4: clamp(1.125rem, 1rem + 0.5vw, 1.25rem);
  --fluid-h5: clamp(1rem, 0.9rem + 0.375vw, 1.125rem);
  --fluid-h6: clamp(0.875rem, 0.8rem + 0.25vw, 1rem);
  
  /* Fluid line heights */
  --fluid-line-height-tight: clamp(1.1, 1.1 + 0.2vw, 1.2);
  --fluid-line-height-normal: clamp(1.4, 1.4 + 0.2vw, 1.5);
  --fluid-line-height-loose: clamp(1.6, 1.6 + 0.2vw, 1.8);
  
  /* Fluid letter spacing */
  --fluid-tracking-tighter: clamp(-0.05em, -0.05em + 0.01vw, -0.025em);
  --fluid-tracking-tight: clamp(-0.025em, -0.025em + 0.01vw, -0.01em);
  --fluid-tracking-normal: 0;
  --fluid-tracking-wide: clamp(0.01em, 0.01em + 0.01vw, 0.025em);
  --fluid-tracking-wider: clamp(0.025em, 0.025em + 0.01vw, 0.05em);
  --fluid-tracking-widest: clamp(0.05em, 0.05em + 0.02vw, 0.1em);
}

/* Fluid Typography Classes */
.text-fluid-xs {
  font-size: var(--fluid-xs);
}

.text-fluid-sm {
  font-size: var(--fluid-sm);
}

.text-fluid-base {
  font-size: var(--fluid-base);
}

.text-fluid-lg {
  font-size: var(--fluid-lg);
}

.text-fluid-xl {
  font-size: var(--fluid-xl);
}

.text-fluid-2xl {
  font-size: var(--fluid-2xl);
}

.text-fluid-3xl {
  font-size: var(--fluid-3xl);
}

.text-fluid-4xl {
  font-size: var(--fluid-4xl);
}

.text-fluid-5xl {
  font-size: var(--fluid-5xl);
}

.text-fluid-6xl {
  font-size: var(--fluid-6xl);
}

/* Fluid Heading Classes */
h1, .h1 {
  font-size: var(--fluid-h1);
  line-height: var(--fluid-line-height-tight);
}

h2, .h2 {
  font-size: var(--fluid-h2);
  line-height: var(--fluid-line-height-tight);
}

h3, .h3 {
  font-size: var(--fluid-h3);
  line-height: var(--fluid-line-height-tight);
}

h4, .h4 {
  font-size: var(--fluid-h4);
  line-height: var(--fluid-line-height-normal);
}

h5, .h5 {
  font-size: var(--fluid-h5);
  line-height: var(--fluid-line-height-normal);
}

h6, .h6 {
  font-size: var(--fluid-h6);
  line-height: var(--fluid-line-height-normal);
}

/* Fluid Line Height Classes */
.leading-fluid-tight {
  line-height: var(--fluid-line-height-tight);
}

.leading-fluid-normal {
  line-height: var(--fluid-line-height-normal);
}

.leading-fluid-loose {
  line-height: var(--fluid-line-height-loose);
}

/* Fluid Tracking Classes */
.tracking-fluid-tighter {
  letter-spacing: var(--fluid-tracking-tighter);
}

.tracking-fluid-tight {
  letter-spacing: var(--fluid-tracking-tight);
}

.tracking-fluid-normal {
  letter-spacing: var(--fluid-tracking-normal);
}

.tracking-fluid-wide {
  letter-spacing: var(--fluid-tracking-wide);
}

.tracking-fluid-wider {
  letter-spacing: var(--fluid-tracking-wider);
}

.tracking-fluid-widest {
  letter-spacing: var(--fluid-tracking-widest);
}
