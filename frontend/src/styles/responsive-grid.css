/*
 * RentUp Responsive Grid System
 * A comprehensive responsive grid system using CSS Grid with auto-fit and minmax
 * Based on the latest CSS techniques for responsive layouts
 */

:root {
  /* Grid gap sizes */
  --grid-gap-xs: 0.5rem;   /* 8px */
  --grid-gap-sm: 0.75rem;  /* 12px */
  --grid-gap-md: 1rem;     /* 16px */
  --grid-gap-lg: 1.5rem;   /* 24px */
  --grid-gap-xl: 2rem;     /* 32px */
  --grid-gap-2xl: 3rem;    /* 48px */
  
  /* Grid item min widths */
  --grid-item-xs: 16rem;   /* 256px */
  --grid-item-sm: 20rem;   /* 320px */
  --grid-item-md: 24rem;   /* 384px */
  --grid-item-lg: 28rem;   /* 448px */
  --grid-item-xl: 32rem;   /* 512px */
}

/*
 * Auto-Fit Grid
 * Creates a responsive grid that automatically adjusts the number of columns
 * based on the available space and the minimum width of each item.
 */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, var(--grid-item-sm)), 1fr));
  gap: var(--grid-gap-md);
}

.grid-auto-fit-xs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, var(--grid-item-xs)), 1fr));
  gap: var(--grid-gap-sm);
}

.grid-auto-fit-sm {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, var(--grid-item-sm)), 1fr));
  gap: var(--grid-gap-md);
}

.grid-auto-fit-md {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, var(--grid-item-md)), 1fr));
  gap: var(--grid-gap-lg);
}

.grid-auto-fit-lg {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, var(--grid-item-lg)), 1fr));
  gap: var(--grid-gap-xl);
}

.grid-auto-fit-xl {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, var(--grid-item-xl)), 1fr));
  gap: var(--grid-gap-2xl);
}

/*
 * Auto-Fill Grid
 * Similar to auto-fit, but leaves empty tracks when there's not enough content
 * to fill the row. This is useful for maintaining consistent column widths.
 */
.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(min(100%, var(--grid-item-sm)), 1fr));
  gap: var(--grid-gap-md);
}

.grid-auto-fill-xs {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(min(100%, var(--grid-item-xs)), 1fr));
  gap: var(--grid-gap-sm);
}

.grid-auto-fill-sm {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(min(100%, var(--grid-item-sm)), 1fr));
  gap: var(--grid-gap-md);
}

.grid-auto-fill-md {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(min(100%, var(--grid-item-md)), 1fr));
  gap: var(--grid-gap-lg);
}

.grid-auto-fill-lg {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(min(100%, var(--grid-item-lg)), 1fr));
  gap: var(--grid-gap-xl);
}

.grid-auto-fill-xl {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(min(100%, var(--grid-item-xl)), 1fr));
  gap: var(--grid-gap-2xl);
}

/*
 * Grid Gap Utilities
 * Classes to control the gap between grid items
 */
.grid-gap-xs {
  gap: var(--grid-gap-xs);
}

.grid-gap-sm {
  gap: var(--grid-gap-sm);
}

.grid-gap-md {
  gap: var(--grid-gap-md);
}

.grid-gap-lg {
  gap: var(--grid-gap-lg);
}

.grid-gap-xl {
  gap: var(--grid-gap-xl);
}

.grid-gap-2xl {
  gap: var(--grid-gap-2xl);
}

/*
 * Grid Column Span Utilities
 * Classes to control how many columns an item spans
 */
.col-span-full {
  grid-column: 1 / -1;
}

.col-span-half {
  grid-column: span 2;
}

/* Responsive column spans */
@media (min-width: 640px) {
  .sm:col-span-full {
    grid-column: 1 / -1;
  }
  
  .sm:col-span-half {
    grid-column: span 2;
  }
}

@media (min-width: 768px) {
  .md:col-span-full {
    grid-column: 1 / -1;
  }
  
  .md:col-span-half {
    grid-column: span 2;
  }
}

@media (min-width: 1024px) {
  .lg:col-span-full {
    grid-column: 1 / -1;
  }
  
  .lg:col-span-half {
    grid-column: span 2;
  }
}

@media (min-width: 1280px) {
  .xl:col-span-full {
    grid-column: 1 / -1;
  }
  
  .xl:col-span-half {
    grid-column: span 2;
  }
}

/*
 * Grid Row Span Utilities
 * Classes to control how many rows an item spans
 */
.row-span-2 {
  grid-row: span 2;
}

.row-span-3 {
  grid-row: span 3;
}

/* Responsive row spans */
@media (min-width: 640px) {
  .sm:row-span-2 {
    grid-row: span 2;
  }
  
  .sm:row-span-3 {
    grid-row: span 3;
  }
}

@media (min-width: 768px) {
  .md:row-span-2 {
    grid-row: span 2;
  }
  
  .md:row-span-3 {
    grid-row: span 3;
  }
}

@media (min-width: 1024px) {
  .lg:row-span-2 {
    grid-row: span 2;
  }
  
  .lg:row-span-3 {
    grid-row: span 3;
  }
}

@media (min-width: 1280px) {
  .xl:row-span-2 {
    grid-row: span 2;
  }
  
  .xl:row-span-3 {
    grid-row: span 3;
  }
}
