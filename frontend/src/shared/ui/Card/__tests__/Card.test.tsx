import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON>, CardHeader, CardContent, CardFooter, CardMedia } from '..';

describe('Card Components', () => {
  describe('Card Component', () => {
    test('renders correctly with default props', () => {
      render(<Card testId="default-card">Card Content</Card>);
      expect(screen.getByText('Card Content')).toBeInTheDocument();
      expect(screen.getByTestId('default-card')).toBeInTheDocument();
    });

    test('renders with different variants', () => {
      const { rerender } = render(<Card variant="default" testId="default-variant">Default Card</Card>);
      expect(screen.getByTestId('default-variant')).toBeInTheDocument();

      rerender(<Card variant="outlined" testId="outlined-variant">Outlined Card</Card>);
      expect(screen.getByTestId('outlined-variant')).toBeInTheDocument();

      rerender(<Card variant="elevated" testId="elevated-variant">Elevated Card</Card>);
      expect(screen.getByTestId('elevated-variant')).toBeInTheDocument();

      rerender(<Card variant="flat" testId="flat-variant">Flat Card</Card>);
      expect(screen.getByTestId('flat-variant')).toBeInTheDocument();
    });

    test('renders as interactive when interactive is true', () => {
      render(<Card interactive testId="interactive-card">Interactive Card</Card>);
      expect(screen.getByTestId('interactive-card')).toBeInTheDocument();
    });

    test('renders as full width when fullWidth is true', () => {
      render(<Card fullWidth testId="full-width-card">Full Width Card</Card>);
      expect(screen.getByTestId('full-width-card')).toBeInTheDocument();
    });

    test('applies different padding sizes', () => {
      const { rerender } = render(<Card padding="none" testId="no-padding">No Padding</Card>);
      expect(screen.getByTestId('no-padding')).toBeInTheDocument();

      rerender(<Card padding="sm" testId="small-padding">Small Padding</Card>);
      expect(screen.getByTestId('small-padding')).toBeInTheDocument();

      rerender(<Card padding="md" testId="medium-padding">Medium Padding</Card>);
      expect(screen.getByTestId('medium-padding')).toBeInTheDocument();

      rerender(<Card padding="lg" testId="large-padding">Large Padding</Card>);
      expect(screen.getByTestId('large-padding')).toBeInTheDocument();
    });

    test('applies responsive padding when responsivePadding is true', () => {
      render(<Card padding="md" responsivePadding testId="responsive-padding">Responsive Padding</Card>);
      expect(screen.getByTestId('responsive-padding')).toBeInTheDocument();
    });

    test('applies stack on mobile class when stackOnMobile is true', () => {
      render(<Card stackOnMobile testId="stack-on-mobile">Stack On Mobile</Card>);
      expect(screen.getByTestId('stack-on-mobile')).toBeInTheDocument();
    });

    test('applies custom className', () => {
      render(<Card className="custom-class" testId="custom-class">Custom Class</Card>);
      expect(screen.getByTestId('custom-class')).toBeInTheDocument();
    });

    test('applies data-testid attribute', () => {
      render(<Card testId="card-test">Test ID</Card>);
      expect(screen.getByTestId('card-test')).toBeInTheDocument();
    });
  });

  describe('CardHeader Component', () => {
    test('renders correctly with children', () => {
      render(
        <CardHeader testId="header-with-children">
          <h2>Header Content</h2>
        </CardHeader>
      );
      expect(screen.getByText('Header Content')).toBeInTheDocument();
      expect(screen.getByTestId('header-with-children')).toBeInTheDocument();
    });

    test('renders with title and subtitle', () => {
      render(<CardHeader title="Card Title" subtitle="Card Subtitle" testId="header-with-title-subtitle" />);
      expect(screen.getByText('Card Title')).toBeInTheDocument();
      expect(screen.getByText('Card Subtitle')).toBeInTheDocument();
      expect(screen.getByTestId('header-with-title-subtitle')).toBeInTheDocument();
    });

    test('renders with icon', () => {
      render(
        <CardHeader
          title="Card Title"
          icon={<span data-testid="header-icon">🏠</span>}
          testId="header-with-icon"
        />
      );
      expect(screen.getByTestId('header-icon')).toBeInTheDocument();
      expect(screen.getByTestId('header-with-icon')).toBeInTheDocument();
    });

    test('renders with action', () => {
      render(
        <CardHeader
          title="Card Title"
          action={<button data-testid="header-action">Action</button>}
          testId="header-with-action"
        />
      );
      expect(screen.getByTestId('header-action')).toBeInTheDocument();
      expect(screen.getByTestId('header-with-action')).toBeInTheDocument();
    });

    test('renders centered when centered is true', () => {
      render(<CardHeader title="Centered Title" centered testId="centered-header" />);
      expect(screen.getByTestId('centered-header')).toBeInTheDocument();
    });

    test('renders with border when bordered is true', () => {
      render(<CardHeader title="Bordered Title" bordered testId="bordered-header" />);
      expect(screen.getByTestId('bordered-header')).toBeInTheDocument();
    });
  });

  describe('CardContent Component', () => {
    test('renders correctly with children', () => {
      render(<CardContent testId="content-with-children">Content Text</CardContent>);
      expect(screen.getByText('Content Text')).toBeInTheDocument();
      expect(screen.getByTestId('content-with-children')).toBeInTheDocument();
    });

    test('applies padding when padded is true', () => {
      render(<CardContent padded testId="padded-content">Padded Content</CardContent>);
      expect(screen.getByTestId('padded-content')).toBeInTheDocument();
    });

    test('applies flex column layout when flexCol is true', () => {
      render(<CardContent flexCol testId="flex-col-content">Flex Column</CardContent>);
      expect(screen.getByTestId('flex-col-content')).toBeInTheDocument();
    });

    test('applies full height when fullHeight is true', () => {
      render(<CardContent fullHeight testId="full-height-content">Full Height</CardContent>);
      expect(screen.getByTestId('full-height-content')).toBeInTheDocument();
    });
  });

  describe('CardFooter Component', () => {
    test('renders correctly with children', () => {
      render(<CardFooter testId="footer-with-children">Footer Content</CardFooter>);
      expect(screen.getByText('Footer Content')).toBeInTheDocument();
      expect(screen.getByTestId('footer-with-children')).toBeInTheDocument();
    });

    test('applies border when border is true', () => {
      render(<CardFooter border testId="bordered-footer">Bordered Footer</CardFooter>);
      expect(screen.getByTestId('bordered-footer')).toBeInTheDocument();
    });

    test('applies margin top when marginTop is true', () => {
      render(<CardFooter marginTop testId="margin-top-footer">Margin Top</CardFooter>);
      expect(screen.getByTestId('margin-top-footer')).toBeInTheDocument();
    });

    test('applies flex layout when flex is true', () => {
      render(<CardFooter flex testId="flex-footer">Flex Layout</CardFooter>);
      expect(screen.getByTestId('flex-footer')).toBeInTheDocument();
    });

    test('applies stack on mobile when stackOnMobile is true', () => {
      render(<CardFooter stackOnMobile testId="stack-on-mobile-footer">Stack On Mobile</CardFooter>);
      expect(screen.getByTestId('stack-on-mobile-footer')).toBeInTheDocument();
    });
  });
});
