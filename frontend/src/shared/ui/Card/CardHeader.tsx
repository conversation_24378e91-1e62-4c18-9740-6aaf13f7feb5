import React, { forwardRef, HTMLAttributes, ReactNode } from 'react';
import { cn } from '../../../utils/classNames';

export interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  /**
   * The content of the card header
   */
  children?: ReactNode;

  /**
   * The title of the card header
   */
  title?: ReactNode;

  /**
   * The subtitle of the card header
   */
  subtitle?: ReactNode;

  /**
   * The icon to display in the card header
   */
  icon?: ReactNode;

  /**
   * The action to display in the card header
   */
  action?: ReactNode;

  /**
   * Whether to center the content of the card header
   * @default false
   */
  centered?: boolean;

  /**
   * Whether to add a border to the bottom of the card header
   * @default false
   */
  bordered?: boolean;

  /**
   * The data-testid attribute for testing
   */
  testId?: string;

  /**
   * Additional CSS classes to apply to the card header
   */
  className?: string;
}

/**
 * Card header component for displaying a title, subtitle, icon, and action
 */
export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  (
    {
      children,
      title,
      subtitle,
      icon,
      action,
      centered = false,
      bordered = false,
      testId,
      className,
      ...props
    },
    ref
  ) => {
    const headerClasses = cn(
      'flex',
      centered ? 'flex-col items-center text-center' : 'flex-row items-start justify-between',
      bordered && 'border-b border-gray-200 pb-3 mb-3',
      className
    );

    // If children are provided, render them directly
    if (children) {
      return (
        <div
          ref={ref}
          className={headerClasses}
          data-testid={testId}
          {...props}
        >
          {children}
        </div>
      );
    }

    // Otherwise, render the structured header
    return (
      <div
        ref={ref}
        className={headerClasses}
        data-testid={testId}
        {...props}
      >
        <div className={`flex ${centered ? 'flex-col items-center' : 'items-center'}`}>
          {icon && (
            <div className={`${centered ? 'mb-2' : 'mr-3'} text-primary-500 flex-shrink-0`}>
              {icon}
            </div>
          )}
          <div>
            {title && (
              <h3 className="text-lg font-semibold text-gray-900">
                {title}
              </h3>
            )}
            {subtitle && (
              <p className="text-sm text-gray-700 mt-1 font-medium">
                {subtitle}
              </p>
            )}
          </div>
        </div>
        {action && (
          <div className={centered ? 'mt-3' : 'ml-4'}>
            {action}
          </div>
        )}
      </div>
    );
  }
);

CardHeader.displayName = 'CardHeader';
