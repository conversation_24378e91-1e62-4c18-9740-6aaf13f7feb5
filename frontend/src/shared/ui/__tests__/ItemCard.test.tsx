import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ItemCard } from '../ItemCard';

// Mock item data
const mockItem = {
  id: '123',
  name: 'Test Item',
  imageUrl: 'https://example.com/image.jpg',
  price: 25,
  pricePerDay: 5,
  originalPrice: 30,
  rating: 4.5,
  reviewCount: 10,
  location: 'New York',
  distance: 2.5,
  category: 'Electronics',
  subCategory: 'Cameras',
  condition: {
    status: 'Like New'
  },
  reliability: {
    reliabilityScore: 85,
    ageInDays: 90,
    rentalHistory: {
      totalRentals: 5
    }
  },
  listingType: 'rent-to-buy',
  isRentToBuy: true,
  hasBackupAvailable: true
};

// Wrap component with BrowserRouter for Link components
const renderWithRouter = (ui: React.ReactElement) => {
  return render(<BrowserRouter>{ui}</BrowserRouter>);
};

describe('ItemCard Component', () => {
  test('renders correctly with default props', () => {
    renderWithRouter(<ItemCard item={mockItem} />);
    expect(screen.getByText('Test Item')).toBeInTheDocument();
    expect(screen.getByText('$5.00/day')).toBeInTheDocument();
  });

  test('renders with rent-to-buy badge', () => {
    renderWithRouter(<ItemCard item={mockItem} />);
    expect(screen.getByText('Rent-to-Buy')).toBeInTheDocument();
  });

  test('renders with discount badge', () => {
    renderWithRouter(<ItemCard item={mockItem} />);
    expect(screen.getByText('17% OFF')).toBeInTheDocument();
  });

  test('renders location when showLocation is true', () => {
    renderWithRouter(<ItemCard item={mockItem} showLocation />);
    expect(screen.getByText('New York')).toBeInTheDocument();
    expect(screen.getByText('(2.5 mi)')).toBeInTheDocument();
  });

  test('renders category information', () => {
    renderWithRouter(<ItemCard item={mockItem} />);
    expect(screen.getByText('Electronics')).toBeInTheDocument();
    expect(screen.getByText('• Cameras')).toBeInTheDocument();
  });

  test('renders reliability score', () => {
    renderWithRouter(<ItemCard item={mockItem} />);
    expect(screen.getByText('85%')).toBeInTheDocument();
    expect(screen.getByText('Owner has had for:')).toBeInTheDocument();
    expect(screen.getByText(/3.*months/)).toBeInTheDocument();
    expect(screen.getByText('Rented:')).toBeInTheDocument();
    expect(screen.getByText(/5.*times/)).toBeInTheDocument();
  });

  test('renders action buttons when showActions is true', () => {
    renderWithRouter(<ItemCard item={mockItem} showActions />);
    expect(screen.getByText('View Details')).toBeInTheDocument();
    expect(screen.getByText('Book Now')).toBeInTheDocument();
  });

  test('does not render action buttons when showActions is false', () => {
    renderWithRouter(<ItemCard item={mockItem} showActions={false} />);
    expect(screen.queryByText('Book Now')).not.toBeInTheDocument();
  });

  test('renders with enhanced visual effects when enhanced is true', () => {
    renderWithRouter(<ItemCard item={mockItem} enhanced />);
    const card = screen.getByText('Test Item').closest('.group');
    expect(card).toHaveClass('hover:shadow-lg');
  });

  test('handles image error correctly', () => {
    renderWithRouter(<ItemCard item={mockItem} />);
    const image = document.querySelector('img');
    if (image) {
      fireEvent.error(image);
      expect(image).toHaveAttribute('src', 'https://placehold.jp/300x200.png?text=Image+Not+Available');
    }
  });

  test('renders rating correctly', () => {
    renderWithRouter(<ItemCard item={mockItem} />);
    expect(screen.getByText('4.5')).toBeInTheDocument();
  });

  test('applies custom className', () => {
    renderWithRouter(<ItemCard item={mockItem} className="custom-class" />);
    const card = screen.getByText('Test Item').closest('.group');
    expect(card).toHaveClass('custom-class');
  });

  test('handles mouse enter and leave events', () => {
    renderWithRouter(<ItemCard item={{...mockItem, secondaryImage: 'https://example.com/secondary.jpg'}} enhanced />);
    const card = screen.getByText('Test Item').closest('.group');
    if (card) {
      fireEvent.mouseEnter(card);
      fireEvent.mouseLeave(card);
      // Just testing that the events don't cause errors
      expect(card).toBeInTheDocument();
    }
  });

  test('renders with edit and delete buttons when callbacks are provided', () => {
    const onEdit = jest.fn();
    const onDelete = jest.fn();
    renderWithRouter(
      <ItemCard
        item={mockItem}
        onEdit={onEdit}
        onDelete={onDelete}
      />
    );
    // Note: These buttons are not implemented in the current version
    // This test is for future implementation
  });
});
