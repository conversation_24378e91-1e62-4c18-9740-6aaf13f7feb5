import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardFooter, CardMedia } from './Card';
import { Button } from './Button';
import { cn } from '../../utils/classNames';

export interface ItemCardProps {
  /**
   * The item data to display
   */
  item: {
    id: string;
    name: string;
    imageUrl?: string;
    image?: string;
    secondaryImage?: string;
    pricePerDay?: number;
    price?: number;
    originalPrice?: number;
    rating?: number;
    reviewCount?: number;
    location?: string;
    distance?: number;
    category?: string;
    subCategory?: string;
    condition?: {
      status: string;
    };
    reliability?: {
      reliabilityScore: number;
      ageInDays?: number;
      rentalHistory?: {
        totalRentals: number;
      };
    };
    listingType?: string;
    isRentToBuy?: boolean;
    hasBackupAvailable?: boolean;
  };
  
  /**
   * Callback for editing the item
   */
  onEdit?: (id: string) => void;
  
  /**
   * Callback for deleting the item
   */
  onDelete?: (id: string) => void;
  
  /**
   * Whether to show the owner information
   * @default false
   */
  showOwner?: boolean;
  
  /**
   * Whether to show the location information
   * @default false
   */
  showLocation?: boolean;
  
  /**
   * Whether to show action buttons
   * @default true
   */
  showActions?: boolean;
  
  /**
   * Whether to show enhanced visual effects
   * @default false
   */
  enhanced?: boolean;
  
  /**
   * Additional CSS classes to apply to the card
   */
  className?: string;
}

/**
 * A specialized card component for displaying item information
 */
export const ItemCard: React.FC<ItemCardProps> = ({
  item,
  onEdit,
  onDelete,
  showOwner = false,
  showLocation = false,
  showActions = true,
  enhanced = false,
  className,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Handle image error
  const handleImageError = () => {
    setImageError(true);
  };

  // Calculate discount percentage if applicable
  const discountPercentage = item.originalPrice && item.price && item.price < item.originalPrice
    ? Math.round(((item.originalPrice - item.price) / item.originalPrice) * 100)
    : null;

  // Format price with currency symbol
  const formatPrice = (price: number): string => {
    return `$${price.toFixed(2)}`;
  };

  // Render badges for the item
  const renderBadges = () => {
    if (!item.listingType && !discountPercentage) return null;
    
    return (
      <div className="flex flex-col gap-1">
        {item.listingType === 'rent-to-buy' && (
          <span className="bg-accent-500 text-white text-xs px-2 py-1 rounded-md">
            Rent-to-Buy
          </span>
        )}
        {discountPercentage && (
          <span className="bg-success-500 text-white text-xs px-2 py-1 rounded-md">
            {discountPercentage}% OFF
          </span>
        )}
      </div>
    );
  };

  // Render overlay actions for enhanced mode
  const renderOverlayActions = () => {
    if (!enhanced) return null;
    
    return (
      <div className="flex gap-2">
        <Button
          variant="primary"
          size="sm"
          href={`/items/${item.id}`}
        >
          View Details
        </Button>
        <Button
          variant="secondary"
          size="sm"
          href={`/book/${item.id}`}
        >
          Book Now
        </Button>
      </div>
    );
  };

  return (
    <Card
      className={cn(
        'group',
        enhanced ? 'hover:shadow-lg' : '',
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      interactive={enhanced}
    >
      <CardMedia
        src={isHovered && item.secondaryImage && enhanced 
          ? item.secondaryImage 
          : (imageError 
            ? "https://placehold.jp/300x200.png?text=Image+Not+Available" 
            : (item.imageUrl || item.image || "https://placehold.jp/300x200.png?text=No+Image"))}
        alt={item.name}
        aspectRatio={enhanced ? "aspect-w-1 aspect-h-1" : "aspect-w-16 aspect-h-9"}
        badges={renderBadges()}
        overlay={enhanced ? renderOverlayActions() : undefined}
        onError={handleImageError}
      >
        {/* Price Badge */}
        <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-sm font-semibold text-primary-700 shadow-sm">
          ${(item.pricePerDay || item.price || 0).toFixed(2)}/day
        </div>
      </CardMedia>

      <CardContent>
        {/* Item Name and Rating */}
        <div className="flex justify-between items-start mb-2">
          <Link to={`/items/${item.id}`} className="hover:underline focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded">
            <h3 className={cn(
              "text-lg font-semibold text-gray-900 truncate",
              enhanced && "group-hover:text-primary-600 transition-colors"
            )}>
              {item.name}
            </h3>
          </Link>
          {item.rating !== undefined && (
            <div className="flex items-center bg-yellow-50 px-2 py-0.5 rounded-md border border-yellow-200">
              <span className="text-yellow-600 mr-1 font-bold">★</span>
              <span className="text-sm font-bold text-gray-800">{item.rating.toFixed(1)}</span>
            </div>
          )}
        </div>

        {/* Location and Category */}
        {(showLocation || item.category) && (
          <div className="text-sm text-gray-700 mb-3">
            {showLocation && item.location && (
              <div className="flex items-center mb-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {item.location}
                {item.distance && <span className="ml-1">({item.distance} mi)</span>}
              </div>
            )}
            
            {item.category && (
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                {item.category}
                {item.subCategory && <span className="ml-1">• {item.subCategory}</span>}
              </div>
            )}
          </div>
        )}

        {/* Reliability Score */}
        {item.reliability && (
          <div className="mb-3">
            <div className="flex items-center">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={cn(
                    "h-2 rounded-full",
                    item.reliability.reliabilityScore >= 80 ? "bg-green-500" : 
                    item.reliability.reliabilityScore >= 60 ? "bg-yellow-500" : "bg-red-500"
                  )}
                  style={{ width: `${item.reliability.reliabilityScore}%` }}
                ></div>
              </div>
              <span className="ml-2 text-xs font-medium">
                {item.reliability.reliabilityScore}%
              </span>
            </div>
            
            {item.reliability.rentalHistory && (
              <div className="mt-2 text-sm text-gray-700 bg-gray-50 px-2 py-1 rounded-md border border-gray-200">
                <span className="font-bold">Owner has had for:</span> {Math.floor((item.reliability.ageInDays || 0) / 30)} months
                <span className="mx-1 text-gray-500">•</span>
                <span className="font-bold">Rented:</span> {item.reliability.rentalHistory.totalRentals} times
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Actions */}
      {showActions && (
        <CardFooter>
          <Link
            to={`/items/${item.id}`}
            className="text-sm text-primary-700 hover:text-primary-800 font-semibold"
          >
            View Details
          </Link>
          <Link
            to={`/book/${item.id}`}
            className="px-3 py-1.5 bg-accent-600 text-white text-sm font-semibold rounded-md hover:bg-accent-700 transition-colors"
          >
            Book Now
          </Link>
        </CardFooter>
      )}
    </Card>
  );
};
