import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../hooks/useAuth';
import { useIsMobile } from '../../hooks/useMediaQuery';
import { Link } from 'react-router-dom';
import { EnhancedPreferenceVisualization } from '../Visualization';
import { InteractiveEmbeddingVisualization } from '../Visualization';
import recommendationService from '../../services/recommendationService';

interface UserDashboardAIInsightsProps {
  className?: string;
  testId?: string;
}

/**
 * A component that displays AI insights for the user dashboard
 * Integrates preference visualization and embedding visualization
 * Provides personalized insights based on user activity
 */
const UserDashboardAIInsights: React.FC<UserDashboardAIInsightsProps> = ({
  className = '',
  testId = 'user-dashboard-ai-insights',
}) => {
  const { user } = useAuth();
  const isMobile = useIsMobile();

  // State for active tab
  const [activeTab, setActiveTab] = useState<'preferences' | 'relationships' | 'recommendations'>('preferences');

  // Loading state
  const [loading, setLoading] = useState<boolean>(false);

  // Calculate recommendation strength (placeholder for now)
  const recommendationStrength = 85;

  return (
    <div
      className={`bg-white rounded-lg shadow-md p-6 ${className}`}
      data-testid={testId}
    >
      <h2 className="text-xl font-semibold mb-6">Your AI Insights</h2>

      {/* Recommendation strength indicator */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-lg font-medium">Recommendation Strength</h3>
          <span
            className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium"
            aria-label={`${recommendationStrength}% recommendation strength`}
          >
            {recommendationStrength}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-2">
          <div
            className="bg-blue-600 h-2.5 rounded-full"
            style={{ width: `${recommendationStrength}%` }}
            role="progressbar"
            aria-valuenow={recommendationStrength}
            aria-valuemin={0}
            aria-valuemax={100}
          ></div>
        </div>
        <p className="text-sm text-gray-600">
          Based on your activity, our AI can provide highly personalized recommendations.
          {recommendationStrength < 70 && " Add more items to your favorites to improve recommendations."}
        </p>
      </div>

      {/* Tab navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8" aria-label="AI insights tabs">
          <button
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'preferences'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('preferences')}
            aria-current={activeTab === 'preferences' ? 'page' : undefined}
            data-testid={`${testId}-tab-preferences`}
          >
            Your Preferences
          </button>
          <button
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'relationships'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('relationships')}
            aria-current={activeTab === 'relationships' ? 'page' : undefined}
            data-testid={`${testId}-tab-relationships`}
          >
            Item Relationships
          </button>
          <button
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'recommendations'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
            onClick={() => setActiveTab('recommendations')}
            aria-current={activeTab === 'recommendations' ? 'page' : undefined}
            data-testid={`${testId}-tab-recommendations`}
          >
            Recommendations
          </button>
        </nav>
      </div>

      {/* Tab content */}
      <div className="mt-6">
        {activeTab === 'preferences' && (
          <div data-testid={`${testId}-content-preferences`}>
            <p className="text-sm text-gray-600 mb-4">
              This visualization shows how your preferences are tracked based on your interactions.
              Items you've liked, favorited, or rented have higher weights and influence your recommendations more.
            </p>
            <EnhancedPreferenceVisualization
              userId={user?.id}
              testId={`${testId}-preference-viz`}
              compact={isMobile}
              interactive={true}
            />
          </div>
        )}

        {activeTab === 'relationships' && (
          <div data-testid={`${testId}-content-relationships`}>
            <p className="text-sm text-gray-600 mb-4">
              This visualization shows how items are related to each other based on their features.
              Items that are closer together are more similar. The colors represent different categories.
            </p>
            <InteractiveEmbeddingVisualization
              testId={`${testId}-embedding-viz`}
              compact={isMobile}
              limit={30}
              interactive={true}
            />
          </div>
        )}

        {activeTab === 'recommendations' && (
          <div data-testid={`${testId}-content-recommendations`}>
            <p className="text-sm text-gray-600 mb-4">
              Based on your preferences and activity, we recommend these items for you.
              Click on an item to see why it was recommended.
            </p>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-center text-gray-500 py-8">
                Recommendation insights coming soon
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="mt-8 pt-4 border-t border-gray-100">
        <Link
          to="/visualization"
          className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          data-testid={`${testId}-view-more-link`}
        >
          View detailed AI insights →
        </Link>
      </div>
    </div>
  );
};

export default UserDashboardAIInsights;
