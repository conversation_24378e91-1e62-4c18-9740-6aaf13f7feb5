import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import UserDashboardAIInsights from '../UserDashboardAIInsights';
import { AuthProvider } from '../../../contexts/AuthContext';
import recommendationService from '../../../services/recommendationService';

// Mock the recommendation service
jest.mock('../../../services/recommendationService', () => ({
  getApiUrl: jest.fn().mockReturnValue('http://localhost:8000'),
  getPreferenceVisualization: jest.fn(),
  getEmbeddingVisualization: jest.fn(),
}));

// Mock the visualization components
jest.mock('../../Visualization/EnhancedPreferenceVisualization', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-enhanced-preference-visualization">Mock Enhanced Preference Visualization</div>,
}));

jest.mock('../../Visualization/InteractiveEmbeddingVisualization', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-interactive-embedding-visualization">Mock Interactive Embedding Visualization</div>,
}));

// Mock the auth hook
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id', name: 'Test User' },
    isAuthenticated: true,
  }),
}));

// Mock the media query hook
jest.mock('../../../hooks/useMediaQuery', () => ({
  __esModule: true,
  useIsMobile: jest.fn().mockReturnValue(false),
}));

describe('UserDashboardAIInsights', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the recommendation service functions
    (recommendationService.getPreferenceVisualization as jest.Mock).mockResolvedValue([
      { preferenceType: 'liked', weight: 0.8, count: 10 },
      { preferenceType: 'viewed', weight: 0.5, count: 20 },
    ]);

    (recommendationService.getEmbeddingVisualization as jest.Mock).mockResolvedValue([
      { id: 'item1', name: 'Item 1', x: 0.1, y: 0.2, category: 'electronics' },
      { id: 'item2', name: 'Item 2', x: 0.3, y: 0.4, category: 'furniture' },
    ]);
  });

  it('renders the component with recommendation strength indicator', () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <UserDashboardAIInsights />
        </AuthProvider>
      </BrowserRouter>
    );

    // Check that the component renders
    expect(screen.getByText('Your AI Insights')).toBeInTheDocument();

    // Check that the recommendation strength indicator is rendered
    expect(screen.getByText(/Recommendation Strength/i)).toBeInTheDocument();

    // Check that the tabs are rendered
    expect(screen.getByText('Your Preferences')).toBeInTheDocument();
    expect(screen.getByText('Item Relationships')).toBeInTheDocument();
    expect(screen.getByText('Recommendations')).toBeInTheDocument();
  });

  it('switches between tabs when clicked', async () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <UserDashboardAIInsights />
        </AuthProvider>
      </BrowserRouter>
    );

    // Initially, the preferences tab should be active and the preference visualization should be visible
    expect(screen.getByTestId('mock-enhanced-preference-visualization')).toBeInTheDocument();

    // Click on the relationships tab
    fireEvent.click(screen.getByText('Item Relationships'));

    // The embedding visualization should now be visible
    await waitFor(() => {
      expect(screen.getByTestId('mock-interactive-embedding-visualization')).toBeInTheDocument();
    });

    // Click on the recommendations tab
    fireEvent.click(screen.getByText('Recommendations'));

    // The recommendations content should now be visible
    await waitFor(() => {
      expect(screen.getByText('Recommendation insights coming soon')).toBeInTheDocument();
    });
  });

  it('renders the "View detailed AI insights" link', () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <UserDashboardAIInsights />
        </AuthProvider>
      </BrowserRouter>
    );

    // Check that the link is rendered
    const link = screen.getByText('View detailed AI insights →');
    expect(link).toBeInTheDocument();
    expect(link.getAttribute('href')).toBe('/visualization');
  });
});
