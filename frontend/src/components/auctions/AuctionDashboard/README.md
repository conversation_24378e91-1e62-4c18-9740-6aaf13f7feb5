# AuctionDashboard Component

A comprehensive auction management dashboard for both sellers and buyers. This component provides auction listing, filtering, statistics, and management capabilities with a responsive design and intuitive interface.

## Overview

The AuctionDashboard component has been refactored from a single 662-line file into a modular architecture following the May 2025 code optimization guidelines. Each file is now focused on a specific responsibility and maintains 50-200 lines for optimal maintainability.

## Architecture

```
AuctionDashboard/
├── index.tsx                           # Main component (100 lines)
├── components/                         # UI Components
│   ├── StatsCards.tsx                  # Statistics display (100 lines)
│   ├── TabsAndFilters.tsx              # Navigation and search (80 lines)
│   ├── AuctionsTable.tsx               # Auction listing table (120 lines)
│   ├── ActionMenu.tsx                  # Context menu actions (60 lines)
│   ├── DeleteConfirmationDialog.tsx    # Deletion confirmation (40 lines)
│   └── CreateAuctionButton.tsx         # Create auction CTA (30 lines)
├── hooks/                              # Custom Hooks
│   ├── useAuctionData.ts               # Data fetching and management (140 lines)
│   ├── useAuctionFiltering.ts          # Filtering and sorting logic (120 lines)
│   ├── useAuctionActions.ts            # Action handling (100 lines)
│   └── useAuctionStats.ts              # Statistics calculation (80 lines)
├── utils/                              # Utility Functions
│   ├── types.ts                        # TypeScript interfaces (180 lines)
│   ├── constants.ts                    # Configuration constants (150 lines)
│   └── dateUtils.ts                    # Date formatting utilities (120 lines)
└── README.md                           # Component documentation (300 lines)
```

## Features

### Core Functionality
- **Auction Management**: View, edit, delete, and duplicate auctions
- **Real-time Statistics**: Active auctions, bid counts, revenue tracking
- **Advanced Filtering**: Search, sort, and filter by multiple criteria
- **Role-based Views**: Different interfaces for sellers and buyers
- **Responsive Design**: Works seamlessly on all device sizes

### Statistics Dashboard
- **Active Auctions**: Count of currently running auctions
- **Total Bids**: Aggregate bid count across all auctions
- **Highest Bid**: Maximum bid amount achieved
- **Completed Auctions**: Count of finished auctions
- **Performance Metrics**: Success rate and average duration

### Filtering and Search
- **Tab Navigation**: Filter by auction status (Active, Pending, Draft, Ended)
- **Text Search**: Search by auction title and category
- **Sortable Columns**: Sort by price, bids, end date, status
- **Real-time Updates**: Instant filtering without page reload

### Action Management
- **Context Menus**: Right-click actions based on auction status
- **Bulk Operations**: Select and perform actions on multiple auctions
- **Status Updates**: Change auction status with confirmation
- **Safe Deletion**: Confirmation dialogs for destructive actions

## Components

### Main Component (`index.tsx`)
The orchestrating component that brings together all sub-components and hooks.

**Features:**
- Centralized state management
- Error boundary handling
- Role-based rendering
- Loading state management

### StatsCards Component
Displays key auction statistics in an attractive card layout.

**Features:**
- Real-time statistics calculation
- Role-specific metrics
- Visual indicators and icons
- Responsive grid layout

### TabsAndFilters Component
Provides navigation tabs and search/filter functionality.

**Features:**
- Dynamic tab generation based on user role
- Real-time search with debouncing
- Filter controls integration
- Responsive design

### AuctionsTable Component
Displays auctions in a sortable, filterable table format.

**Features:**
- Sortable columns with visual indicators
- Status chips with color coding
- Action buttons and context menus
- Empty state handling
- Responsive table design

### ActionMenu Component
Context menu for auction-specific actions.

**Features:**
- Status-based action availability
- Navigation integration
- Confirmation dialogs
- Keyboard accessibility

### DeleteConfirmationDialog Component
Safety dialog for auction deletion.

**Features:**
- Clear warning message
- Confirmation workflow
- Keyboard navigation
- Accessible design

## Custom Hooks

### useAuctionData
Manages auction data fetching and state management.

**Returns:**
- `auctions`: Array of auction objects
- `loading`: Loading state
- `error`: Error state
- `refreshAuctions`: Refresh function
- `updateAuction`: Update single auction
- `removeAuction`: Remove auction from state

### useAuctionFiltering
Handles filtering, searching, and sorting functionality.

**Returns:**
- Filter state variables
- `filteredAuctions`: Filtered and sorted results
- `handleSort`: Sorting function
- `tabs`: Dynamic tab configuration

### useAuctionActions
Manages auction actions and menu interactions.

**Returns:**
- Menu state variables
- Action handlers for CRUD operations
- Dialog state management
- Confirmation workflows

### useAuctionStats
Calculates real-time auction statistics.

**Returns:**
- `stats`: Comprehensive statistics object
- Automatically updates when auction data changes
- Performance metrics calculation

## Utilities

### types.ts
Comprehensive TypeScript interfaces for type safety.

**Includes:**
- `Auction`: Complete auction data structure
- `AuctionStats`: Statistics interface
- `FilterState`: Filter configuration
- Component prop interfaces

### constants.ts
Configuration constants and mappings.

**Includes:**
- Status color mappings
- Tab configurations
- Sort field options
- API endpoints
- UI configuration

### dateUtils.ts
Date formatting and calculation utilities.

**Functions:**
- `formatDate`: Standard date formatting
- `formatTimeRemaining`: Time until auction end
- `isEndingSoon`: Urgency detection
- `getAuctionDuration`: Duration calculation

## Usage

```tsx
import AuctionDashboard from './components/auctions/AuctionDashboard';

function SellerDashboard() {
  return (
    <div>
      <h1>Seller Dashboard</h1>
      <AuctionDashboard userRole="seller" />
    </div>
  );
}

function BuyerDashboard() {
  return (
    <div>
      <h1>My Bids</h1>
      <AuctionDashboard userRole="buyer" />
    </div>
  );
}
```

## Development

### Testing
Each component and hook should be tested individually:
- Unit tests for utility functions
- Component tests for UI interactions
- Integration tests for hook behavior
- E2E tests for complete workflows

### Performance Considerations
- Efficient data fetching with caching
- Optimized table rendering for large datasets
- Debounced search functionality
- Memoized calculations for statistics

### Accessibility
- All interactive elements have proper ARIA labels
- Keyboard navigation support
- Screen reader announcements
- High contrast mode compatibility

## Migration Notes

This refactored version maintains full backward compatibility with the original AuctionDashboard component. The API remains the same, but the internal structure is now modular and maintainable.

### Benefits of Refactoring
1. **Improved Maintainability**: Smaller, focused files are easier to understand and modify
2. **Better Testing**: Individual components and hooks can be tested in isolation
3. **Enhanced Reusability**: Components can be reused in other auction features
4. **Improved Performance**: Better code splitting and optimized rendering
5. **Developer Experience**: Faster IDE loading and better IntelliSense support

### Breaking Changes
None. The component maintains the same public API as the original implementation.
