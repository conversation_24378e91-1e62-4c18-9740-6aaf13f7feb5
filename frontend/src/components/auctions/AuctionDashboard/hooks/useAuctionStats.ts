import { useMemo } from 'react';
import { Auction, AuctionStats } from '../utils/types';
import { isAfter, isBefore, addDays } from 'date-fns';

/**
 * useAuctionStats Hook
 * 
 * Calculates and provides auction statistics including counts,
 * bid information, and performance metrics.
 */
export const useAuctionStats = (auctions: Auction[]): AuctionStats => {
  const stats = useMemo(() => {
    const now = new Date();
    const soonThreshold = addDays(now, 1); // Ending within 24 hours
    
    // Count auctions by status
    const active = auctions.filter(a => a.status === 'active').length;
    const pending = auctions.filter(a => a.status === 'pending').length;
    const draft = auctions.filter(a => a.status === 'draft').length;
    const ended = auctions.filter(a => a.status === 'ended').length;
    
    // Count auctions ending soon
    const ending = auctions.filter(a => 
      a.status === 'active' && 
      a.endDate && 
      isAfter(new Date(a.endDate), now) && 
      isBefore(new Date(a.endDate), soonThreshold)
    ).length;
    
    // Calculate bid statistics
    const totalBids = auctions.reduce((sum, auction) => sum + auction.bidCount, 0);
    
    const auctionsWithBids = auctions.filter(a => a.bidCount > 0);
    const highestBid = auctionsWithBids.length > 0 
      ? Math.max(...auctionsWithBids.map(a => a.currentPrice))
      : 0;
    
    const averageBid = auctionsWithBids.length > 0
      ? auctionsWithBids.reduce((sum, a) => sum + a.currentPrice, 0) / auctionsWithBids.length
      : 0;
    
    // Calculate revenue (for ended auctions)
    const totalRevenue = auctions
      .filter(a => a.status === 'ended' && a.bidCount > 0)
      .reduce((sum, a) => sum + a.currentPrice, 0);
    
    // Calculate success rate (auctions with bids vs total active/ended)
    const completedAuctions = auctions.filter(a => a.status === 'active' || a.status === 'ended');
    const successfulAuctions = completedAuctions.filter(a => a.bidCount > 0);
    const successRate = completedAuctions.length > 0 
      ? (successfulAuctions.length / completedAuctions.length) * 100 
      : 0;
    
    // Calculate average auction duration (for ended auctions)
    const endedAuctions = auctions.filter(a => 
      a.status === 'ended' && a.startDate && a.endDate
    );
    const averageDuration = endedAuctions.length > 0
      ? endedAuctions.reduce((sum, a) => {
          const start = new Date(a.startDate!).getTime();
          const end = new Date(a.endDate!).getTime();
          return sum + (end - start);
        }, 0) / endedAuctions.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0;
    
    return {
      active,
      pending,
      draft,
      ended,
      ending,
      totalBids,
      highestBid,
      averageBid,
      totalRevenue,
      successRate,
      averageDuration
    };
  }, [auctions]);
  
  return stats;
};
