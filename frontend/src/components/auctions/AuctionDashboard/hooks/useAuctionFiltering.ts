import { useState, useMemo } from 'react';
import { Auction } from '../utils/types';

/**
 * useAuctionFiltering Hook
 * 
 * Manages filtering, searching, and sorting functionality for auctions.
 * Provides filtered results based on tab selection, search query, and sorting.
 */
export const useAuctionFiltering = (auctions: Auction[], userRole: 'seller' | 'buyer') => {
  const [activeTab, setActiveTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState('endDate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  
  // Define tabs based on user role
  const tabs = useMemo(() => {
    if (userRole === 'seller') {
      return ['All', 'Active', 'Pending', 'Draft', 'Ended'];
    } else {
      return ['All', 'Active Bids', 'Won', 'Lost', 'Watching'];
    }
  }, [userRole]);
  
  // Filter auctions by tab
  const filterByTab = (auctions: Auction[], tabIndex: number): Auction[] => {
    if (userRole === 'seller') {
      switch (tabIndex) {
        case 0: return auctions; // All
        case 1: return auctions.filter(a => a.status === 'active');
        case 2: return auctions.filter(a => a.status === 'pending');
        case 3: return auctions.filter(a => a.status === 'draft');
        case 4: return auctions.filter(a => a.status === 'ended');
        default: return auctions;
      }
    } else {
      // For buyers, we would filter based on their bid status
      // This would require additional data about user's bids
      switch (tabIndex) {
        case 0: return auctions; // All
        case 1: return auctions.filter(a => a.status === 'active'); // Active Bids (mock)
        case 2: return auctions.filter(a => a.status === 'ended'); // Won (mock)
        case 3: return []; // Lost (mock)
        case 4: return []; // Watching (mock)
        default: return auctions;
      }
    }
  };
  
  // Filter auctions by search query
  const filterBySearch = (auctions: Auction[], query: string): Auction[] => {
    if (!query.trim()) return auctions;
    
    const lowercaseQuery = query.toLowerCase();
    return auctions.filter(auction => 
      auction.title.toLowerCase().includes(lowercaseQuery) ||
      auction.category.toLowerCase().includes(lowercaseQuery)
    );
  };
  
  // Sort auctions
  const sortAuctions = (auctions: Auction[], field: string, direction: 'asc' | 'desc'): Auction[] => {
    return [...auctions].sort((a, b) => {
      let aValue: any;
      let bValue: any;
      
      switch (field) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'category':
          aValue = a.category.toLowerCase();
          bValue = b.category.toLowerCase();
          break;
        case 'currentPrice':
          aValue = a.currentPrice;
          bValue = b.currentPrice;
          break;
        case 'bidCount':
          aValue = a.bidCount;
          bValue = b.bidCount;
          break;
        case 'endDate':
          aValue = a.endDate ? new Date(a.endDate).getTime() : 0;
          bValue = b.endDate ? new Date(b.endDate).getTime() : 0;
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        default:
          return 0;
      }
      
      if (aValue < bValue) return direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return direction === 'asc' ? 1 : -1;
      return 0;
    });
  };
  
  // Handle sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  
  // Get filtered and sorted auctions
  const filteredAuctions = useMemo(() => {
    let result = filterByTab(auctions, activeTab);
    result = filterBySearch(result, searchQuery);
    result = sortAuctions(result, sortField, sortDirection);
    return result;
  }, [auctions, activeTab, searchQuery, sortField, sortDirection, userRole]);
  
  return {
    activeTab,
    setActiveTab,
    searchQuery,
    setSearchQuery,
    sortField,
    sortDirection,
    filteredAuctions,
    handleSort,
    tabs
  };
};
