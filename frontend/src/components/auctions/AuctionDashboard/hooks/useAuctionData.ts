import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '../../../../config';
import { Auction } from '../utils/types';
import { addDays } from 'date-fns';

/**
 * Mock auction data for development
 */
const MOCK_AUCTIONS: Auction[] = [
  {
    id: 1,
    title: 'Mountain Bike',
    itemId: 1,
    itemTitle: 'Mountain Bike',
    category: 'Sports & Recreation',
    startingPrice: 25.00,
    currentPrice: 45.00,
    bidCount: 8,
    startDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
    endDate: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000), // 4 days from now
    status: 'active'
  },
  {
    id: 2,
    title: 'Professional Camera',
    itemId: 2,
    itemTitle: 'Professional Camera',
    category: 'Electronics & Technology',
    startingPrice: 75.00,
    currentPrice: 125.00,
    bidCount: 12,
    startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
    status: 'active'
  },
  {
    id: 3,
    title: 'Apartment in Downtown',
    itemId: 3,
    itemTitle: 'Apartment in Downtown',
    category: 'Real Estate & Spaces',
    startingPrice: 150.00,
    currentPrice: 150.00,
    bidCount: 0,
    startDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000), // 1 day from now
    endDate: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000), // 8 days from now
    status: 'pending'
  },
  {
    id: 4,
    title: 'Power Drill',
    itemId: 4,
    itemTitle: 'Power Drill',
    category: 'Tools & Equipment',
    startingPrice: 15.00,
    currentPrice: 35.00,
    bidCount: 6,
    startDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
    endDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    status: 'ended'
  },
  {
    id: 5,
    title: 'Designer Dress',
    itemId: 5,
    itemTitle: 'Designer Dress',
    category: 'Fashion & Accessories',
    startingPrice: 45.00,
    currentPrice: 0.00,
    bidCount: 0,
    startDate: null,
    endDate: null,
    status: 'draft'
  }
];

/**
 * useAuctionData Hook
 * 
 * Manages auction data fetching and state management.
 * Provides loading states, error handling, and refresh functionality.
 */
export const useAuctionData = (userRole: 'seller' | 'buyer') => {
  const [auctions, setAuctions] = useState<Auction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch auctions from API
  const fetchAuctions = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would fetch from the API
      // const endpoint = userRole === 'seller' 
      //   ? `${API_BASE_URL}/api/v1/auctions/my-auctions`
      //   : `${API_BASE_URL}/api/v1/auctions/my-bids`;
      // const response = await axios.get(endpoint);
      // setAuctions(response.data);
      
      // Mock data for development
      setTimeout(() => {
        setAuctions(MOCK_AUCTIONS);
        setLoading(false);
      }, 1000);
    } catch (err) {
      console.error('Error fetching auctions:', err);
      setError('Failed to load auctions. Please try again later.');
      setLoading(false);
    }
  };
  
  // Refresh auctions
  const refreshAuctions = () => {
    fetchAuctions();
  };
  
  // Update auction in local state
  const updateAuction = (auctionId: number, updates: Partial<Auction>) => {
    setAuctions(prevAuctions => 
      prevAuctions.map(auction => 
        auction.id === auctionId 
          ? { ...auction, ...updates }
          : auction
      )
    );
  };
  
  // Remove auction from local state
  const removeAuction = (auctionId: number) => {
    setAuctions(prevAuctions => 
      prevAuctions.filter(auction => auction.id !== auctionId)
    );
  };
  
  // Fetch auctions on mount and when userRole changes
  useEffect(() => {
    fetchAuctions();
  }, [userRole]);
  
  return {
    auctions,
    loading,
    error,
    refreshAuctions,
    updateAuction,
    removeAuction
  };
};
