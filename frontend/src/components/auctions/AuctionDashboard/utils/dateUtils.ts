import { format, formatDistanceToNow, isPast, isFuture, differenceInDays, differenceInHours } from 'date-fns';

/**
 * Date utility functions for the Auction Dashboard
 */

/**
 * Formats a date for display in the auction table
 */
export const formatDate = (date: Date | null): string => {
  if (!date) return 'Not set';
  
  const dateObj = new Date(date);
  
  // Check if date is valid
  if (isNaN(dateObj.getTime())) return 'Invalid date';
  
  return format(dateObj, 'MMM dd, yyyy HH:mm');
};

/**
 * Formats a date as a short string (e.g., "Mar 15, 2024")
 */
export const formatDateShort = (date: Date | null): string => {
  if (!date) return 'Not set';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return 'Invalid date';
  
  return format(dateObj, 'MMM dd, yyyy');
};

/**
 * Formats time remaining until auction end
 */
export const formatTimeRemaining = (endDate: Date | null): string => {
  if (!endDate) return 'No end date';
  
  const dateObj = new Date(endDate);
  if (isNaN(dateObj.getTime())) return 'Invalid date';
  
  if (isPast(dateObj)) {
    return 'Ended';
  }
  
  return formatDistanceToNow(dateObj, { addSuffix: true });
};

/**
 * Gets the status of an auction based on its dates
 */
export const getAuctionTimeStatus = (startDate: Date | null, endDate: Date | null): 'not-started' | 'active' | 'ended' => {
  const now = new Date();
  
  if (!startDate || !endDate) return 'not-started';
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isFuture(start)) return 'not-started';
  if (isPast(end)) return 'ended';
  return 'active';
};

/**
 * Checks if an auction is ending soon (within 24 hours)
 */
export const isEndingSoon = (endDate: Date | null): boolean => {
  if (!endDate) return false;
  
  const dateObj = new Date(endDate);
  if (isNaN(dateObj.getTime())) return false;
  
  const hoursRemaining = differenceInHours(dateObj, new Date());
  return hoursRemaining > 0 && hoursRemaining <= 24;
};

/**
 * Checks if an auction is ending very soon (within 1 hour)
 */
export const isEndingVerySoon = (endDate: Date | null): boolean => {
  if (!endDate) return false;
  
  const dateObj = new Date(endDate);
  if (isNaN(dateObj.getTime())) return false;
  
  const hoursRemaining = differenceInHours(dateObj, new Date());
  return hoursRemaining > 0 && hoursRemaining <= 1;
};

/**
 * Gets the urgency level of an auction based on time remaining
 */
export const getAuctionUrgency = (endDate: Date | null): 'low' | 'medium' | 'high' | 'ended' => {
  if (!endDate) return 'low';
  
  const dateObj = new Date(endDate);
  if (isNaN(dateObj.getTime())) return 'low';
  
  if (isPast(dateObj)) return 'ended';
  
  const hoursRemaining = differenceInHours(dateObj, new Date());
  
  if (hoursRemaining <= 1) return 'high';
  if (hoursRemaining <= 24) return 'medium';
  return 'low';
};

/**
 * Calculates auction duration in days
 */
export const getAuctionDuration = (startDate: Date | null, endDate: Date | null): number => {
  if (!startDate || !endDate) return 0;
  
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0;
  
  return differenceInDays(end, start);
};

/**
 * Formats auction duration for display
 */
export const formatAuctionDuration = (startDate: Date | null, endDate: Date | null): string => {
  const duration = getAuctionDuration(startDate, endDate);
  
  if (duration === 0) return 'Unknown duration';
  if (duration === 1) return '1 day';
  return `${duration} days`;
};

/**
 * Gets a relative time string (e.g., "2 hours ago", "in 3 days")
 */
export const getRelativeTime = (date: Date | null): string => {
  if (!date) return 'Unknown';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return 'Invalid date';
  
  return formatDistanceToNow(dateObj, { addSuffix: true });
};

/**
 * Checks if a date is today
 */
export const isToday = (date: Date | null): boolean => {
  if (!date) return false;
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return false;
  
  const today = new Date();
  return (
    dateObj.getDate() === today.getDate() &&
    dateObj.getMonth() === today.getMonth() &&
    dateObj.getFullYear() === today.getFullYear()
  );
};

/**
 * Formats a date for sorting purposes (ISO string)
 */
export const formatDateForSorting = (date: Date | null): string => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  return dateObj.toISOString();
};
