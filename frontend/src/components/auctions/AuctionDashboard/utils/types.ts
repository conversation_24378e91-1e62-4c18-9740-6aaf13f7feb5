/**
 * TypeScript interfaces and types for the AuctionDashboard component
 */

/**
 * Auction interface
 */
export interface Auction {
  id: number;
  title: string;
  itemId: number;
  itemTitle: string;
  category: string;
  startingPrice: number;
  currentPrice: number;
  bidCount: number;
  startDate: Date | null;
  endDate: Date | null;
  status: 'draft' | 'pending' | 'active' | 'ended' | 'cancelled';
  description?: string;
  images?: string[];
  sellerId?: number;
  sellerName?: string;
  winningBidderId?: number;
  winningBidderName?: string;
  incrementAmount?: number;
  reservePrice?: number;
  buyNowPrice?: number;
  watchers?: number;
  views?: number;
  featured?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Auction statistics interface
 */
export interface AuctionStats {
  active: number;
  pending: number;
  draft: number;
  ended: number;
  ending: number; // Ending soon (within 24 hours)
  totalBids: number;
  highestBid: number;
  averageBid: number;
  totalRevenue: number;
  successRate: number; // Percentage of auctions with bids
  averageDuration: number; // Average auction duration in days
}

/**
 * Filter state interface
 */
export interface FilterState {
  activeTab: number;
  searchQuery: string;
  sortField: string;
  sortDirection: 'asc' | 'desc';
  categoryFilter?: string;
  statusFilter?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

/**
 * Sort field options
 */
export type SortField = 
  | 'title'
  | 'category'
  | 'currentPrice'
  | 'bidCount'
  | 'endDate'
  | 'status'
  | 'startDate'
  | 'createdAt';

/**
 * Auction action types
 */
export type AuctionAction = 
  | 'view'
  | 'edit'
  | 'delete'
  | 'duplicate'
  | 'analytics'
  | 'promote'
  | 'pause'
  | 'resume';

/**
 * User role types
 */
export type UserRole = 'seller' | 'buyer' | 'admin';

/**
 * Auction status colors mapping
 */
export interface StatusColors {
  draft: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  pending: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  active: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  ended: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  cancelled: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
}

/**
 * Component props interfaces
 */
export interface AuctionDashboardProps {
  userRole?: UserRole;
  initialTab?: number;
  showStats?: boolean;
  showFilters?: boolean;
}

export interface StatsCardsProps {
  stats: AuctionStats;
  userRole: UserRole;
}

export interface TabsAndFiltersProps {
  activeTab: number;
  setActiveTab: (tab: number) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  tabs: string[];
}

export interface AuctionsTableProps {
  auctions: Auction[];
  sortField: string;
  sortDirection: 'asc' | 'desc';
  onSort: (field: string) => void;
  onMenuOpen: (event: React.MouseEvent<HTMLElement>, auction: Auction) => void;
  userRole: UserRole;
}

export interface ActionMenuProps {
  anchorEl: HTMLElement | null;
  selectedAuction: Auction | null;
  onClose: () => void;
  onDeleteDialogOpen: () => void;
}

export interface DeleteConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

/**
 * API response interfaces
 */
export interface AuctionListResponse {
  auctions: Auction[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface AuctionStatsResponse {
  stats: AuctionStats;
  trends: {
    bidsThisWeek: number;
    revenueThisWeek: number;
    newAuctionsThisWeek: number;
  };
}

/**
 * Pagination interface
 */
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

/**
 * Search and filter options
 */
export interface SearchFilters {
  query?: string;
  category?: string;
  status?: string;
  minPrice?: number;
  maxPrice?: number;
  startDate?: Date;
  endDate?: Date;
  sortBy?: SortField;
  sortOrder?: 'asc' | 'desc';
}
