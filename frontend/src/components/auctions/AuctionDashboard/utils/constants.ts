import { StatusColors } from './types';

/**
 * Constants for the Auction Dashboard
 */

/**
 * Auction status options
 */
export const AUCTION_STATUSES = [
  'draft',
  'pending',
  'active',
  'ended',
  'cancelled'
] as const;

/**
 * Status color mappings for chips
 */
export const STATUS_COLORS: StatusColors = {
  draft: 'default',
  pending: 'warning',
  active: 'success',
  ended: 'info',
  cancelled: 'error'
};

/**
 * Sort field options
 */
export const SORT_FIELDS = [
  'title',
  'category',
  'currentPrice',
  'bidCount',
  'endDate',
  'status',
  'startDate',
  'createdAt'
] as const;

/**
 * Default sort configuration
 */
export const DEFAULT_SORT = {
  field: 'endDate',
  direction: 'asc'
} as const;

/**
 * Tab configurations for different user roles
 */
export const SELLER_TABS = [
  'All',
  'Active',
  'Pending',
  'Draft',
  'Ended'
] as const;

export const BUYER_TABS = [
  'All',
  'Active Bids',
  'Won',
  'Lost',
  'Watching'
] as const;

/**
 * Table column configurations
 */
export const TABLE_COLUMNS = [
  { id: 'title', label: 'Title', sortable: true, minWidth: 200 },
  { id: 'category', label: 'Category', sortable: true, minWidth: 150 },
  { id: 'currentPrice', label: 'Current Bid', sortable: true, minWidth: 120, align: 'right' as const },
  { id: 'bidCount', label: 'Bids', sortable: true, minWidth: 80, align: 'right' as const },
  { id: 'endDate', label: 'End Date', sortable: true, minWidth: 150 },
  { id: 'status', label: 'Status', sortable: true, minWidth: 100 },
  { id: 'actions', label: 'Actions', sortable: false, minWidth: 120, align: 'center' as const }
] as const;

/**
 * Action menu items based on auction status
 */
export const ACTION_MENU_ITEMS = {
  draft: ['edit', 'delete', 'duplicate'],
  pending: ['view', 'delete', 'duplicate'],
  active: ['view', 'analytics', 'duplicate'],
  ended: ['view', 'analytics', 'duplicate'],
  cancelled: ['view', 'duplicate']
} as const;

/**
 * Stats card configurations
 */
export const STATS_CARDS = [
  {
    id: 'active',
    title: 'Active Auctions',
    icon: 'Gavel',
    color: 'primary'
  },
  {
    id: 'totalBids',
    title: 'Total Bids',
    icon: 'People',
    color: 'primary'
  },
  {
    id: 'highestBid',
    title: 'Highest Bid',
    icon: 'AttachMoney',
    color: 'primary'
  },
  {
    id: 'ended',
    title: 'Completed Auctions',
    icon: 'CalendarToday',
    color: 'primary'
  }
] as const;

/**
 * Pagination defaults
 */
export const PAGINATION_DEFAULTS = {
  PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100
} as const;

/**
 * Search and filter defaults
 */
export const SEARCH_DEFAULTS = {
  DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 2
} as const;

/**
 * Date format options
 */
export const DATE_FORMATS = {
  SHORT: 'MMM dd, yyyy',
  LONG: 'MMMM dd, yyyy HH:mm',
  TIME_ONLY: 'HH:mm',
  RELATIVE: 'relative'
} as const;

/**
 * API endpoints
 */
export const API_ENDPOINTS = {
  AUCTIONS: '/api/v1/auctions',
  MY_AUCTIONS: '/api/v1/auctions/my-auctions',
  MY_BIDS: '/api/v1/auctions/my-bids',
  AUCTION_STATS: '/api/v1/auctions/stats',
  DELETE_AUCTION: '/api/v1/auctions/:id',
  UPDATE_AUCTION: '/api/v1/auctions/:id'
} as const;

/**
 * UI configuration
 */
export const UI_CONFIG = {
  CARD_ELEVATION: 1,
  TABLE_ROW_HEIGHT: 64,
  STATS_CARD_HEIGHT: 140,
  MOBILE_BREAKPOINT: 'sm',
  TABLET_BREAKPOINT: 'md'
} as const;

/**
 * Animation durations
 */
export const ANIMATION_DURATIONS = {
  SHORT: 200,
  MEDIUM: 300,
  LONG: 500
} as const;

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  FETCH_AUCTIONS: 'Failed to load auctions. Please try again later.',
  DELETE_AUCTION: 'Failed to delete auction. Please try again.',
  UPDATE_AUCTION: 'Failed to update auction. Please try again.',
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'Auction not found.'
} as const;

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  AUCTION_DELETED: 'Auction deleted successfully.',
  AUCTION_UPDATED: 'Auction updated successfully.',
  AUCTION_CREATED: 'Auction created successfully.'
} as const;
