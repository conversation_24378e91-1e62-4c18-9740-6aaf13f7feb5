import React from 'react';
import { Box, Typography, Alert, CircularProgress } from '@mui/material';
import { StatsCards } from './components/StatsCards';
import { TabsAndFilters } from './components/TabsAndFilters';
import { AuctionsTable } from './components/AuctionsTable';
import { ActionMenu } from './components/ActionMenu';
import { DeleteConfirmationDialog } from './components/DeleteConfirmationDialog';
import { CreateAuctionButton } from './components/CreateAuctionButton';
import { useAuctionData } from './hooks/useAuctionData';
import { useAuctionFiltering } from './hooks/useAuctionFiltering';
import { useAuctionActions } from './hooks/useAuctionActions';
import { useAuctionStats } from './hooks/useAuctionStats';

/**
 * AuctionDashboard Component
 * 
 * Main component that orchestrates the auction dashboard interface.
 * Provides auction management, filtering, statistics, and actions.
 * 
 * @param userRole - The role of the user ('seller' or 'buyer')
 */
interface AuctionDashboardProps {
  userRole?: 'seller' | 'buyer';
}

const AuctionDashboard: React.FC<AuctionDashboardProps> = ({ 
  userRole = 'seller' 
}) => {
  // Custom hooks for data and logic
  const { auctions, loading, error, refreshAuctions } = useAuctionData(userRole);
  
  const {
    activeTab,
    setActiveTab,
    searchQuery,
    setSearchQuery,
    sortField,
    sortDirection,
    filteredAuctions,
    handleSort,
    tabs
  } = useAuctionFiltering(auctions, userRole);
  
  const {
    anchorEl,
    selectedAuction,
    deleteDialogOpen,
    handleMenuOpen,
    handleMenuClose,
    handleDeleteDialogOpen,
    handleDeleteDialogClose,
    handleDeleteAuction
  } = useAuctionActions(auctions, refreshAuctions);
  
  const stats = useAuctionStats(auctions);
  
  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }
  
  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        {userRole === 'seller' ? 'My Auctions' : 'Auctions Dashboard'}
      </Typography>
      
      {/* Stats Cards */}
      <StatsCards stats={stats} userRole={userRole} />
      
      {/* Tabs and Filters */}
      <TabsAndFilters
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        tabs={tabs}
      />
      
      {/* Create Auction Button (for sellers) */}
      {userRole === 'seller' && <CreateAuctionButton />}
      
      {/* Auctions Table */}
      <AuctionsTable
        auctions={filteredAuctions}
        sortField={sortField}
        sortDirection={sortDirection}
        onSort={handleSort}
        onMenuOpen={handleMenuOpen}
        userRole={userRole}
      />
      
      {/* Action Menu */}
      <ActionMenu
        anchorEl={anchorEl}
        selectedAuction={selectedAuction}
        onClose={handleMenuClose}
        onDeleteDialogOpen={handleDeleteDialogOpen}
      />
      
      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onClose={handleDeleteDialogClose}
        onConfirm={handleDeleteAuction}
      />
    </Box>
  );
};

export default AuctionDashboard;
