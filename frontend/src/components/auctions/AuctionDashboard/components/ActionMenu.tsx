import React from 'react';
import {
  Menu,
  MenuItem
} from '@mui/material';
import {
  Edit,
  Delete,
  BarChart
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { Auction } from '../utils/types';

/**
 * ActionMenu Component
 * 
 * Displays context menu with actions for auction management.
 * Actions vary based on auction status (edit for drafts, analytics for active, delete for drafts/pending).
 */
interface ActionMenuProps {
  anchorEl: HTMLElement | null;
  selectedAuction: Auction | null;
  onClose: () => void;
  onDeleteDialogOpen: () => void;
}

export const ActionMenu: React.FC<ActionMenuProps> = ({
  anchorEl,
  selectedAuction,
  onClose,
  onDeleteDialogOpen
}) => {
  return (
    <Menu
      anchorEl={anchorEl}
      open={Boolean(anchorEl)}
      onClose={onClose}
    >
      {/* Edit option for draft auctions */}
      {selectedAuction && selectedAuction.status === 'draft' && (
        <MenuItem 
          component={Link} 
          to={`/auctions/${selectedAuction.id}/edit`}
          onClick={onClose}
        >
          <Edit fontSize="small" sx={{ mr: 1 }} /> Edit
        </MenuItem>
      )}
      
      {/* Analytics option for active auctions */}
      {selectedAuction && selectedAuction.status === 'active' && (
        <MenuItem 
          component={Link} 
          to={`/auctions/${selectedAuction.id}/analytics`}
          onClick={onClose}
        >
          <BarChart fontSize="small" sx={{ mr: 1 }} /> Analytics
        </MenuItem>
      )}
      
      {/* Delete option for draft and pending auctions */}
      {selectedAuction && (selectedAuction.status === 'draft' || selectedAuction.status === 'pending') && (
        <MenuItem onClick={onDeleteDialogOpen}>
          <Delete fontSize="small" sx={{ mr: 1 }} /> Delete
        </MenuItem>
      )}
    </Menu>
  );
};
