import React from 'react';
import {
  Box,
  Grid,
  Tabs,
  Tab,
  TextField,
  InputAdornment,
  IconButton
} from '@mui/material';
import {
  Search,
  FilterList
} from '@mui/icons-material';

/**
 * TabsAndFilters Component
 * 
 * Provides tab navigation and search/filter functionality
 * for the auction dashboard.
 */
interface TabsAndFiltersProps {
  activeTab: number;
  setActiveTab: (tab: number) => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  tabs: string[];
}

export const TabsAndFilters: React.FC<TabsAndFiltersProps> = ({
  activeTab,
  setActiveTab,
  searchQuery,
  setSearchQuery,
  tabs
}) => {
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };
  
  return (
    <Box sx={{ mb: 3 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} md={8}>
          <Tabs 
            value={activeTab} 
            onChange={handleTabChange} 
            variant="scrollable" 
            scrollButtons="auto"
          >
            {tabs.map((tab, index) => (
              <Tab key={index} label={tab} />
            ))}
          </Tabs>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <TextField
            fullWidth
            placeholder="Search auctions..."
            value={searchQuery}
            onChange={handleSearchChange}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Search />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton size="small">
                    <FilterList />
                  </IconButton>
                </InputAdornment>
              )
            }}
            size="small"
          />
        </Grid>
      </Grid>
    </Box>
  );
};
