import React from 'react';
import {
  Box,
  Button
} from '@mui/material';
import { Gavel } from '@mui/icons-material';
import { Link } from 'react-router-dom';

/**
 * CreateAuctionButton Component
 * 
 * Displays a button for sellers to create new auctions.
 * Positioned prominently in the dashboard for easy access.
 */
export const CreateAuctionButton: React.FC = () => {
  return (
    <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
      <Button
        variant="contained"
        color="primary"
        startIcon={<Gavel />}
        component={Link}
        to="/auctions/create"
        size="large"
      >
        Create New Auction
      </Button>
    </Box>
  );
};
