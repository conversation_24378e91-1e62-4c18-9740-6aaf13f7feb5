import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box
} from '@mui/material';
import {
  Gavel,
  People,
  AttachMoney,
  CalendarToday
} from '@mui/icons-material';
import { AuctionStats } from '../utils/types';

/**
 * StatsCards Component
 * 
 * Displays auction statistics in card format including
 * active auctions, total bids, highest bid, and completed auctions.
 */
interface StatsCardsProps {
  stats: AuctionStats;
  userRole: 'seller' | 'buyer';
}

export const StatsCards: React.FC<StatsCardsProps> = ({ stats, userRole }) => {
  return (
    <Grid container spacing={3} sx={{ mb: 4 }}>
      {/* Active Auctions */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                  Active Auctions
                </Typography>
                <Typography variant="h4">
                  {stats.active}
                </Typography>
              </Box>
              <Gavel color="primary" />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {stats.ending} ending soon
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Total Bids */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                  Total Bids
                </Typography>
                <Typography variant="h4">
                  {stats.totalBids}
                </Typography>
              </Box>
              <People color="primary" />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Across all auctions
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Highest Bid */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                  Highest Bid
                </Typography>
                <Typography variant="h4">
                  ${stats.highestBid.toFixed(2)}
                </Typography>
              </Box>
              <AttachMoney color="primary" />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Average: ${stats.averageBid.toFixed(2)}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
      
      {/* Completed Auctions */}
      <Grid item xs={12} sm={6} md={3}>
        <Card>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
              <Box>
                <Typography color="text.secondary" variant="subtitle2" gutterBottom>
                  Completed Auctions
                </Typography>
                <Typography variant="h4">
                  {stats.ended}
                </Typography>
              </Box>
              <CalendarToday color="primary" />
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {userRole === 'seller' ? 'View performance analytics' : 'View your wins'}
            </Typography>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};
