import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Chip,
  IconButton
} from '@mui/material';
import {
  ArrowUpward,
  ArrowDownward,
  Visibility,
  MoreVert
} from '@mui/icons-material';
import { Link } from 'react-router-dom';
import { Auction } from '../utils/types';
import { STATUS_COLORS } from '../utils/constants';
import { formatDate } from '../utils/dateUtils';

/**
 * AuctionsTable Component
 * 
 * Displays auctions in a sortable table format with action buttons.
 * Supports sorting by different fields and provides action menus for sellers.
 */
interface AuctionsTableProps {
  auctions: Auction[];
  sortField: string;
  sortDirection: 'asc' | 'desc';
  onSort: (field: string) => void;
  onMenuOpen: (event: React.MouseEvent<HTMLElement>, auction: Auction) => void;
  userRole: 'seller' | 'buyer';
}

export const AuctionsTable: React.FC<AuctionsTableProps> = ({
  auctions,
  sortField,
  sortDirection,
  onSort,
  onMenuOpen,
  userRole
}) => {
  const renderSortableHeader = (field: string, label: string, align: 'left' | 'right' | 'center' = 'left') => (
    <TableCell align={align}>
      <Box 
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          cursor: 'pointer',
          justifyContent: align === 'right' ? 'flex-end' : align === 'center' ? 'center' : 'flex-start'
        }} 
        onClick={() => onSort(field)}
      >
        {label}
        {sortField === field && (
          sortDirection === 'asc' ? <ArrowUpward fontSize="small" /> : <ArrowDownward fontSize="small" />
        )}
      </Box>
    </TableCell>
  );
  
  return (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            {renderSortableHeader('title', 'Title')}
            {renderSortableHeader('category', 'Category')}
            {renderSortableHeader('currentPrice', 'Current Bid', 'right')}
            {renderSortableHeader('bidCount', 'Bids', 'right')}
            {renderSortableHeader('endDate', 'End Date')}
            {renderSortableHeader('status', 'Status')}
            <TableCell align="center">Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {auctions.length > 0 ? (
            auctions.map((auction) => (
              <TableRow key={auction.id} hover>
                <TableCell>
                  <Box sx={{ fontWeight: 'medium' }}>
                    {auction.title}
                  </Box>
                </TableCell>
                <TableCell>{auction.category}</TableCell>
                <TableCell align="right">
                  {auction.currentPrice > 0 ? `$${auction.currentPrice.toFixed(2)}` : 'No bids'}
                </TableCell>
                <TableCell align="right">{auction.bidCount}</TableCell>
                <TableCell>{formatDate(auction.endDate)}</TableCell>
                <TableCell>
                  <Chip 
                    label={auction.status} 
                    color={STATUS_COLORS[auction.status]} 
                    size="small"
                    sx={{ textTransform: 'capitalize' }}
                  />
                </TableCell>
                <TableCell align="center">
                  <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5 }}>
                    <IconButton 
                      component={Link} 
                      to={`/auctions/${auction.id}`}
                      size="small"
                      title="View auction"
                    >
                      <Visibility fontSize="small" />
                    </IconButton>
                    
                    {userRole === 'seller' && auction.status !== 'ended' && (
                      <IconButton 
                        size="small"
                        onClick={(event) => onMenuOpen(event, auction)}
                        title="More actions"
                      >
                        <MoreVert fontSize="small" />
                      </IconButton>
                    )}
                  </Box>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={7} align="center" sx={{ py: 4 }}>
                No auctions found matching your criteria.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
