import React, { useState } from 'react';
import {
  Box,
  Button,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  History,
  Notifications,
  NotificationsActive,
  Person
} from '@mui/icons-material';
import { format } from 'date-fns';

/**
 * BidHistory Component
 * 
 * Displays bid history and manages notification settings.
 * Includes toggle for showing/hiding bid history and notification controls.
 */
interface Bid {
  id: number;
  userId: number;
  userName: string;
  amount: number;
  timestamp: Date;
}

interface BidHistoryProps {
  bids: Bid[];
  notifications?: boolean;
  onToggleNotifications?: () => void;
  showHistory?: boolean;
  onToggleHistory?: () => void;
  auctionEnded?: boolean;
}

export const BidHistory: React.FC<BidHistoryProps> = ({
  bids,
  notifications = false,
  onToggleNotifications,
  showHistory: controlledShowHistory,
  onToggleHistory,
  auctionEnded = false
}) => {
  const [internalShowHistory, setInternalShowHistory] = useState(false);
  
  // Use controlled state if provided, otherwise use internal state
  const showHistory = controlledShowHistory !== undefined 
    ? controlledShowHistory 
    : internalShowHistory;
  
  const handleToggleHistory = () => {
    if (onToggleHistory) {
      onToggleHistory();
    } else {
      setInternalShowHistory(prev => !prev);
    }
  };
  
  return (
    <>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Button
          variant="outlined"
          startIcon={<History />}
          onClick={handleToggleHistory}
        >
          {showHistory ? 'Hide Bid History' : 'Show Bid History'}
        </Button>
        
        {!auctionEnded && onToggleNotifications && (
          <Tooltip title={notifications ? "Disable bid notifications" : "Enable bid notifications"}>
            <Button
              variant="outlined"
              color={notifications ? "primary" : "inherit"}
              startIcon={notifications ? <NotificationsActive /> : <Notifications />}
              onClick={onToggleNotifications}
            >
              {notifications ? "Notifications On" : "Notifications Off"}
            </Button>
          </Tooltip>
        )}
      </Box>
      
      {showHistory && (
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            Bid History
          </Typography>
          
          <List>
            {bids.map((bid) => (
              <ListItem key={bid.id} divider>
                <ListItemAvatar>
                  <Avatar>
                    <Person />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={`$${bid.amount.toFixed(2)} by ${bid.userName}`}
                  secondary={format(new Date(bid.timestamp), 'PPpp')}
                />
              </ListItem>
            ))}
            
            {bids.length === 0 && (
              <ListItem>
                <ListItemText 
                  primary={auctionEnded 
                    ? "No bids were placed on this auction." 
                    : "No bids have been placed yet. Be the first to bid!"
                  } 
                />
              </ListItem>
            )}
          </List>
        </Paper>
      )}
    </>
  );
};
