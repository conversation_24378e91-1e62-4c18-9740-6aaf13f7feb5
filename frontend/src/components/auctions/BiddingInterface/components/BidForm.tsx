import React from 'react';
import {
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  InputAdornment,
  IconButton,
  Alert
} from '@mui/material';
import {
  Gavel,
  Add,
  Remove
} from '@mui/icons-material';

/**
 * BidForm Component
 * 
 * Handles bid input and placement functionality.
 * Includes bid amount input with increment/decrement controls
 * and bid placement button.
 */
interface BidFormProps {
  auction: {
    currentPrice: number;
    incrementAmount: number;
  };
  bidAmount: string;
  setBidAmount: (amount: string) => void;
  onPlaceBid: () => void;
  onIncrementBid: () => void;
  onDecrementBid: () => void;
}

export const BidForm: React.FC<BidFormProps> = ({
  auction,
  bidAmount,
  setBidAmount,
  onPlaceBid,
  onIncrementBid,
  onDecrementBid
}) => {
  const handleBidAmountChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setBidAmount(event.target.value);
  };
  
  const minBid = auction.currentPrice + auction.incrementAmount;
  
  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        Place Your Bid
      </Typography>
      
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={8}>
          <TextField
            fullWidth
            label="Your Bid"
            type="number"
            value={bidAmount}
            onChange={handleBidAmountChange}
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>,
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton 
                    onClick={onDecrementBid}
                    size="small"
                    aria-label="Decrease bid amount"
                  >
                    <Remove />
                  </IconButton>
                  <IconButton 
                    onClick={onIncrementBid}
                    size="small"
                    aria-label="Increase bid amount"
                  >
                    <Add />
                  </IconButton>
                </InputAdornment>
              )
            }}
            inputProps={{
              min: minBid,
              step: auction.incrementAmount
            }}
          />
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <Button
            fullWidth
            variant="contained"
            color="primary"
            startIcon={<Gavel />}
            onClick={onPlaceBid}
            size="large"
          >
            Place Bid
          </Button>
        </Grid>
        
        <Grid item xs={12}>
          <Alert severity="info">
            Minimum bid is ${minBid.toFixed(2)}
          </Alert>
        </Grid>
      </Grid>
    </Paper>
  );
};
