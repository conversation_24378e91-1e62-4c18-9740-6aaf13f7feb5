import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  CircularProgress
} from '@mui/material';

/**
 * BidConfirmationDialog Component
 * 
 * Displays a confirmation dialog when placing a bid.
 * Includes loading state during bid submission.
 */
interface BidConfirmationDialogProps {
  open: boolean;
  amount: number;
  loading: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export const BidConfirmationDialog: React.FC<BidConfirmationDialogProps> = ({
  open,
  amount,
  loading,
  onConfirm,
  onCancel
}) => {
  return (
    <Dialog
      open={open}
      onClose={loading ? undefined : onCancel}
      disableEscapeKeyDown={loading}
    >
      <DialogTitle>Confirm Your Bid</DialogTitle>
      <DialogContent>
        <DialogContentText>
          Are you sure you want to place a bid of ${amount.toFixed(2)} on this auction?
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button 
          onClick={onCancel}
          disabled={loading}
        >
          Cancel
        </Button>
        <Button 
          onClick={onConfirm}
          color="primary"
          disabled={loading}
          startIcon={loading && <CircularProgress size={20} />}
        >
          {loading ? 'Placing Bid...' : 'Confirm Bid'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
