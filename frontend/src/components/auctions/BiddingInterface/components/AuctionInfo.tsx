import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  Chip
} from '@mui/material';
import { AccessTime } from '@mui/icons-material';
import { formatDistanceToNow, isPast } from 'date-fns';

/**
 * AuctionInfo Component
 * 
 * Displays auction details including current price, time remaining,
 * starting price, bid increment, total bids, and seller information.
 */
interface AuctionInfoProps {
  auction: {
    id: string | number;
    title: string;
    description: string;
    seller: {
      id: number;
      name: string;
      rating: number;
    };
    item: {
      id: number;
      title: string;
      category: string;
      images: string[];
    };
    auctionType: string;
    startingPrice: number;
    currentPrice: number;
    incrementAmount: number;
    startDate: Date;
    endDate: Date;
    status: string;
    bidCount: number;
  };
}

export const AuctionInfo: React.FC<AuctionInfoProps> = ({ auction }) => {
  const [timeLeft, setTimeLeft] = useState<string>('');
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Update time left
  useEffect(() => {
    if (!auction) return;
    
    const updateTimeLeft = () => {
      const now = new Date();
      const end = new Date(auction.endDate);
      
      if (isPast(end)) {
        setTimeLeft('Auction ended');
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }
        return;
      }
      
      setTimeLeft(formatDistanceToNow(end, { addSuffix: true }));
    };
    
    updateTimeLeft();
    timerRef.current = setInterval(updateTimeLeft, 1000);
    
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [auction]);
  
  if (!auction) {
    return null;
  }
  
  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Current Bid: ${auction.currentPrice.toFixed(2)}
            </Typography>
            
            <Chip
              icon={<AccessTime />}
              label={timeLeft}
              color={isPast(new Date(auction.endDate)) ? "default" : "primary"}
              variant="outlined"
            />
          </Box>
        </Grid>
        
        <Grid item xs={12}>
          <Divider />
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Typography variant="body2" color="text.secondary">
            Starting Price: ${auction.startingPrice.toFixed(2)}
          </Typography>
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Typography variant="body2" color="text.secondary">
            Bid Increment: ${auction.incrementAmount.toFixed(2)}
          </Typography>
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Typography variant="body2" color="text.secondary">
            Total Bids: {auction.bidCount}
          </Typography>
        </Grid>
        
        <Grid item xs={12} sm={6}>
          <Typography variant="body2" color="text.secondary">
            Seller: {auction.seller.name} ({auction.seller.rating} ★)
          </Typography>
        </Grid>
      </Grid>
    </Paper>
  );
};
