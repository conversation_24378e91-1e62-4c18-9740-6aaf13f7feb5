import React from 'react';
import {
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  InputAdornment
} from '@mui/material';

/**
 * AutoBidSettings Component
 * 
 * Manages auto-bidding functionality including enabling/disabling
 * auto-bid and setting maximum bid amounts.
 */
interface AutoBidSettingsProps {
  auction: {
    currentPrice: number;
    incrementAmount: number;
  };
  autoBidEnabled: boolean;
  maxAutoBid: string;
  setMaxAutoBid: (amount: string) => void;
  onToggleAutoBid: () => void;
  onSetAutoBid: () => void;
}

export const AutoBidSettings: React.FC<AutoBidSettingsProps> = ({
  auction,
  autoBidEnabled,
  maxAutoBid,
  setMaxAutoBid,
  onToggleAutoBid,
  onSetAutoBid
}) => {
  const handleMaxAutoBidChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setMaxAutoBid(event.target.value);
  };
  
  const minBid = auction.currentPrice + auction.incrementAmount;
  
  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Typography variant="h6" gutterBottom>
        Auto-Bidding
      </Typography>
      
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={8}>
          <TextField
            fullWidth
            label="Maximum Bid Amount"
            type="number"
            value={maxAutoBid}
            onChange={handleMaxAutoBidChange}
            disabled={!autoBidEnabled}
            InputProps={{
              startAdornment: <InputAdornment position="start">$</InputAdornment>
            }}
            inputProps={{
              min: minBid,
              step: auction.incrementAmount
            }}
          />
        </Grid>
        
        <Grid item xs={12} sm={4}>
          <Button
            fullWidth
            variant="outlined"
            color={autoBidEnabled ? "primary" : "inherit"}
            onClick={onToggleAutoBid}
          >
            {autoBidEnabled ? "Auto-Bid Enabled" : "Enable Auto-Bid"}
          </Button>
        </Grid>
        
        {autoBidEnabled && (
          <Grid item xs={12}>
            <Button
              fullWidth
              variant="contained"
              color="secondary"
              onClick={onSetAutoBid}
              disabled={!maxAutoBid}
            >
              Set Maximum Bid
            </Button>
            
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Auto-bidding will automatically place bids on your behalf up to your maximum amount.
            </Typography>
          </Grid>
        )}
      </Grid>
    </Paper>
  );
};
