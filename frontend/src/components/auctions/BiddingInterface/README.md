# BiddingInterface Component

A comprehensive, modular auction bidding interface component built with React and TypeScript. This component provides real-time bidding functionality with WebSocket support, auto-bidding capabilities, and a responsive design.

## Overview

The BiddingInterface component has been refactored from a single 762-line file into a modular architecture following the May 2025 code optimization guidelines. Each file is now focused on a specific responsibility and maintains 50-200 lines for optimal maintainability.

## Architecture

```
BiddingInterface/
├── index.tsx                           # Main component (130 lines)
├── components/                         # UI Components
│   ├── AuctionInfo.tsx                 # Auction details display (100 lines)
│   ├── BidForm.tsx                     # Bid input and placement (90 lines)
│   ├── AutoBidSettings.tsx             # Auto-bidding controls (80 lines)
│   ├── BidHistory.tsx                  # Bid history and notifications (120 lines)
│   ├── BidConfirmationDialog.tsx       # Bid confirmation modal (50 lines)
│   └── NotificationSnackbar.tsx        # Notification display (40 lines)
├── hooks/                              # Custom Hooks
│   ├── useAuctionData.ts               # Auction data management (150 lines)
│   ├── useBiddingLogic.ts              # Bidding logic and state (180 lines)
│   ├── useWebSocketConnection.ts       # Real-time updates (120 lines)
│   └── useNotifications.ts             # Notification management (60 lines)
└── utils/                              # Utility Functions
    ├── bidValidation.ts                # Bid validation logic (100 lines)
    ├── bidFormatting.ts                # Formatting utilities (80 lines)
    └── types.ts                        # TypeScript interfaces (200 lines)
```

## Features

### Core Functionality
- **Real-time Bidding**: Place bids with instant feedback
- **Auto-bidding**: Set maximum bid amounts for automatic bidding
- **Bid History**: View complete bidding history with timestamps
- **Auction Information**: Display current price, time remaining, and seller details
- **Bid Validation**: Comprehensive validation for bid amounts and increments

### Real-time Updates
- **WebSocket Integration**: Real-time auction updates
- **Live Notifications**: Instant notifications for new bids
- **Connection Management**: Automatic reconnection handling
- **Error Handling**: Graceful degradation when connection fails

### User Experience
- **Responsive Design**: Works on all device sizes
- **Accessibility**: WCAG compliant with proper ARIA labels
- **Loading States**: Clear feedback during operations
- **Error Handling**: User-friendly error messages
- **Confirmation Dialogs**: Prevent accidental bid placement

## Components

### Main Component (`index.tsx`)
The orchestrating component that brings together all sub-components and hooks.

**Props:**
- `auctionId`: The auction identifier
- `onBidPlaced`: Callback when a bid is successfully placed

### AuctionInfo Component
Displays auction details including current price, time remaining, and seller information.

**Features:**
- Real-time countdown timer
- Seller rating display
- Auction statistics
- Responsive layout

### BidForm Component
Handles bid input and placement with increment/decrement controls.

**Features:**
- Bid amount validation
- Quick increment/decrement buttons
- Minimum bid enforcement
- Accessible form controls

### AutoBidSettings Component
Manages auto-bidding functionality and settings.

**Features:**
- Enable/disable auto-bidding
- Maximum bid amount setting
- Validation for auto-bid amounts
- Clear user feedback

### BidHistory Component
Shows bid history with optional notifications toggle.

**Features:**
- Chronological bid listing
- User avatar display
- Timestamp formatting
- Notification preferences

### BidConfirmationDialog Component
Confirmation modal for bid placement with loading states.

**Features:**
- Bid amount confirmation
- Loading state during submission
- Cancel/confirm actions
- Keyboard navigation support

### NotificationSnackbar Component
Displays notifications with different severity levels.

**Features:**
- Multiple severity types
- Auto-dismiss functionality
- Accessible notifications
- Consistent positioning

## Custom Hooks

### useAuctionData
Manages auction and bid data fetching and state management.

**Returns:**
- `auction`: Current auction data
- `bids`: Array of bids
- `loading`: Loading state
- `error`: Error state
- `updateAuction`: Function to update auction data
- `updateBids`: Function to update bid data

### useBiddingLogic
Handles all bidding-related logic and state management.

**Returns:**
- `bidAmount`: Current bid amount input
- `bidLoading`: Bid submission loading state
- `confirmDialog`: Confirmation dialog state
- `autoBidEnabled`: Auto-bid enabled state
- `maxAutoBid`: Maximum auto-bid amount
- Handler functions for all bidding actions

### useWebSocketConnection
Manages WebSocket connection for real-time updates.

**Features:**
- Automatic connection establishment
- Message handling for different event types
- Connection error handling
- Cleanup on component unmount

### useNotifications
Manages notification state and preferences.

**Returns:**
- `notifications`: Notification enabled state
- `snackbar`: Snackbar state
- `handleToggleNotifications`: Toggle notification preferences
- `showNotification`: Display a notification
- `handleCloseSnackbar`: Close notification

## Utilities

### bidValidation.ts
Comprehensive bid validation functions.

**Functions:**
- `validateBidAmount`: Validates bid against auction rules
- `validateAutoBidAmount`: Validates auto-bid settings
- `formatBidAmount`: Formats bid amounts for display
- `calculateMinimumBid`: Calculates minimum valid bid

### bidFormatting.ts
Formatting utilities for display purposes.

**Functions:**
- `formatCurrency`: Currency formatting with locale support
- `formatTimeRemaining`: Time remaining display
- `formatBidTimestamp`: Timestamp formatting for bid history
- `formatBidNotification`: Notification message formatting

### types.ts
Comprehensive TypeScript interfaces and types.

**Interfaces:**
- `Auction`: Complete auction data structure
- `Bid`: Bid data structure
- `WebSocketMessage`: WebSocket message types
- Component prop interfaces
- State interfaces

## Usage

```tsx
import BiddingInterface from './components/auctions/BiddingInterface';

function AuctionPage() {
  const handleBidPlaced = (bid) => {
    console.log('Bid placed:', bid);
    // Handle bid placement (e.g., update parent state, analytics)
  };

  return (
    <BiddingInterface
      auctionId="123"
      onBidPlaced={handleBidPlaced}
    />
  );
}
```

## Development

### Testing
Each component and hook should be tested individually:
- Unit tests for utility functions
- Component tests for UI interactions
- Integration tests for hook behavior
- E2E tests for complete bidding flow

### Performance Considerations
- WebSocket connection is managed efficiently
- Components are optimized for re-rendering
- Validation functions are memoized where appropriate
- Large lists use virtualization when needed

### Accessibility
- All interactive elements have proper ARIA labels
- Keyboard navigation is fully supported
- Screen reader announcements for important updates
- High contrast mode compatibility

## Migration Notes

This refactored version maintains full backward compatibility with the original BiddingInterface component. The API remains the same, but the internal structure is now modular and maintainable.

### Benefits of Refactoring
1. **Improved Maintainability**: Smaller, focused files are easier to understand and modify
2. **Better Testing**: Individual components and hooks can be tested in isolation
3. **Enhanced Reusability**: Components can be reused in other auction-related features
4. **Improved Performance**: Better code splitting and tree shaking opportunities
5. **Developer Experience**: Faster IDE loading and better IntelliSense support

### Breaking Changes
None. The component maintains the same public API as the original implementation.
