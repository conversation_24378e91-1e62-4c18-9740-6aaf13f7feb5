import { format, formatDistanceToNow, isPast } from 'date-fns';

/**
 * Formats a currency amount for display
 * 
 * @param amount - The amount to format
 * @param currency - The currency code (default: 'USD')
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, currency: string = 'USD'): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

/**
 * Formats a bid amount with currency symbol
 * 
 * @param amount - The bid amount
 * @returns Formatted bid amount string
 */
export const formatBidAmount = (amount: number): string => {
  return formatCurrency(amount);
};

/**
 * Formats time remaining for auction display
 * 
 * @param endDate - The auction end date
 * @returns Formatted time remaining string
 */
export const formatTimeRemaining = (endDate: Date): string => {
  const end = new Date(endDate);
  
  if (isPast(end)) {
    return 'Auction ended';
  }
  
  return formatDistanceToNow(end, { addSuffix: true });
};

/**
 * Formats a timestamp for bid history display
 * 
 * @param timestamp - The timestamp to format
 * @returns Formatted timestamp string
 */
export const formatBidTimestamp = (timestamp: Date): string => {
  return format(new Date(timestamp), 'PPpp');
};

/**
 * Formats a bid for display in notifications
 * 
 * @param amount - The bid amount
 * @param userName - The bidder's name
 * @returns Formatted bid notification string
 */
export const formatBidNotification = (amount: number, userName: string): string => {
  return `New bid: ${formatBidAmount(amount)} by ${userName}`;
};

/**
 * Formats auction status for display
 * 
 * @param status - The auction status
 * @param endDate - The auction end date
 * @returns Formatted status string
 */
export const formatAuctionStatus = (status: string, endDate: Date): string => {
  if (isPast(new Date(endDate))) {
    return 'Ended';
  }
  
  switch (status.toLowerCase()) {
    case 'active':
      return 'Active';
    case 'pending':
      return 'Starting Soon';
    case 'cancelled':
      return 'Cancelled';
    case 'completed':
      return 'Completed';
    default:
      return status;
  }
};

/**
 * Formats seller rating for display
 * 
 * @param rating - The seller rating (0-5)
 * @returns Formatted rating string with stars
 */
export const formatSellerRating = (rating: number): string => {
  const stars = '★'.repeat(Math.floor(rating));
  const halfStar = rating % 1 >= 0.5 ? '☆' : '';
  return `${rating.toFixed(1)} ${stars}${halfStar}`;
};

/**
 * Formats bid increment for display
 * 
 * @param increment - The bid increment amount
 * @returns Formatted increment string
 */
export const formatBidIncrement = (increment: number): string => {
  return `${formatBidAmount(increment)} increments`;
};
