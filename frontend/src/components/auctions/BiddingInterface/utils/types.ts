/**
 * TypeScript interfaces and types for the BiddingInterface component
 */

/**
 * Seller information interface
 */
export interface Seller {
  id: number;
  name: string;
  rating: number;
  verified?: boolean;
  joinDate?: Date;
}

/**
 * Item information interface
 */
export interface AuctionItem {
  id: number;
  title: string;
  category: string;
  images: string[];
  description?: string;
  condition?: string;
  brand?: string;
  model?: string;
}

/**
 * Auction interface
 */
export interface Auction {
  id: string | number;
  title: string;
  description: string;
  seller: Seller;
  item: AuctionItem;
  auctionType: 'standard' | 'reserve' | 'dutch' | 'sealed';
  startingPrice: number;
  currentPrice: number;
  incrementAmount: number;
  startDate: Date;
  endDate: Date;
  status: 'pending' | 'active' | 'ended' | 'cancelled' | 'completed';
  bidCount: number;
  reservePrice?: number;
  buyNowPrice?: number;
  watchers?: number;
  views?: number;
}

/**
 * Bid interface
 */
export interface Bid {
  id: number;
  userId: number;
  userName: string;
  amount: number;
  timestamp: Date;
  isAutoBid?: boolean;
  isWinning?: boolean;
}

/**
 * Bidder information interface
 */
export interface Bidder {
  id: number;
  userName: string;
  rating?: number;
  bidCount?: number;
  isVerified?: boolean;
}

/**
 * Auto-bid settings interface
 */
export interface AutoBidSettings {
  enabled: boolean;
  maxAmount: number;
  incrementStrategy: 'minimum' | 'aggressive' | 'conservative';
  stopOnOutbid?: boolean;
}

/**
 * Notification preferences interface
 */
export interface NotificationPreferences {
  bidUpdates: boolean;
  outbidAlerts: boolean;
  auctionEnding: boolean;
  auctionWon: boolean;
  auctionLost: boolean;
}

/**
 * Bid validation result interface
 */
export interface BidValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

/**
 * Snackbar state interface
 */
export interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

/**
 * Confirmation dialog state interface
 */
export interface ConfirmDialogState {
  open: boolean;
  amount: number;
  type?: 'bid' | 'autoBid';
}

/**
 * WebSocket message types
 */
export type WebSocketMessageType = 
  | 'new_bid'
  | 'auction_updated'
  | 'auction_ended'
  | 'auction_cancelled'
  | 'user_outbid'
  | 'auction_ending_soon';

/**
 * WebSocket message interface
 */
export interface WebSocketMessage {
  type: WebSocketMessageType;
  auctionId: string | number;
  bid?: Bid;
  auction?: Partial<Auction>;
  timestamp: Date;
  userId?: number;
}

/**
 * Auction statistics interface
 */
export interface AuctionStatistics {
  totalBids: number;
  uniqueBidders: number;
  averageBidIncrement: number;
  highestBid: number;
  lowestBid: number;
  bidFrequency: number; // bids per hour
  timeRemaining: number; // milliseconds
}

/**
 * Bid history filter options
 */
export interface BidHistoryFilter {
  showOnlyMyBids?: boolean;
  showAutoBids?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  sortBy?: 'timestamp' | 'amount';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Component props interfaces
 */
export interface BiddingInterfaceProps {
  auctionId: string | number;
  onBidPlaced?: (bid: Bid) => void;
  onAuctionUpdated?: (auction: Auction) => void;
  initialNotificationPreferences?: NotificationPreferences;
  theme?: 'light' | 'dark';
}

export interface AuctionInfoProps {
  auction: Auction;
  showExtendedInfo?: boolean;
}

export interface BidFormProps {
  auction: Pick<Auction, 'currentPrice' | 'incrementAmount'>;
  bidAmount: string;
  setBidAmount: (amount: string) => void;
  onPlaceBid: () => void;
  onIncrementBid: () => void;
  onDecrementBid: () => void;
  disabled?: boolean;
}

export interface AutoBidSettingsProps {
  auction: Pick<Auction, 'currentPrice' | 'incrementAmount'>;
  autoBidEnabled: boolean;
  maxAutoBid: string;
  setMaxAutoBid: (amount: string) => void;
  onToggleAutoBid: () => void;
  onSetAutoBid: () => void;
}

export interface BidHistoryProps {
  bids: Bid[];
  notifications?: boolean;
  onToggleNotifications?: () => void;
  showHistory?: boolean;
  onToggleHistory?: () => void;
  auctionEnded?: boolean;
  filter?: BidHistoryFilter;
  onFilterChange?: (filter: BidHistoryFilter) => void;
}
