/**
 * Bid validation result interface
 */
interface BidValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Validates a bid amount against auction rules
 * 
 * @param bidAmount - The bid amount to validate
 * @param currentPrice - The current highest bid
 * @param incrementAmount - The minimum bid increment
 * @returns Validation result with error message if invalid
 */
export const validateBidAmount = (
  bidAmount: number,
  currentPrice: number,
  incrementAmount: number
): BidValidationResult => {
  // Check if bid amount is a valid number
  if (!bidAmount || isNaN(bidAmount)) {
    return {
      isValid: false,
      error: 'Please enter a valid bid amount'
    };
  }
  
  // Check if bid amount is positive
  if (bidAmount <= 0) {
    return {
      isValid: false,
      error: 'Bid amount must be greater than zero'
    };
  }
  
  // Check if bid meets minimum increment requirement
  const minBid = currentPrice + incrementAmount;
  if (bidAmount < minBid) {
    return {
      isValid: false,
      error: `Bid must be at least $${minBid.toFixed(2)}`
    };
  }
  
  // Check for reasonable maximum (optional business rule)
  const maxReasonableBid = currentPrice * 10; // 10x current price as a sanity check
  if (bidAmount > maxReasonableBid) {
    return {
      isValid: false,
      error: `Bid amount seems unusually high. Please confirm the amount.`
    };
  }
  
  return {
    isValid: true
  };
};

/**
 * Formats a bid amount for display
 * 
 * @param amount - The amount to format
 * @returns Formatted amount string
 */
export const formatBidAmount = (amount: number): string => {
  return `$${amount.toFixed(2)}`;
};

/**
 * Calculates the next minimum bid amount
 * 
 * @param currentPrice - The current highest bid
 * @param incrementAmount - The minimum bid increment
 * @returns The next minimum bid amount
 */
export const calculateMinimumBid = (
  currentPrice: number,
  incrementAmount: number
): number => {
  return currentPrice + incrementAmount;
};

/**
 * Validates auto-bid settings
 * 
 * @param maxAutoBid - The maximum auto-bid amount
 * @param currentPrice - The current highest bid
 * @param incrementAmount - The minimum bid increment
 * @returns Validation result with error message if invalid
 */
export const validateAutoBidAmount = (
  maxAutoBid: number,
  currentPrice: number,
  incrementAmount: number
): BidValidationResult => {
  // Use the same validation as regular bids
  const basicValidation = validateBidAmount(maxAutoBid, currentPrice, incrementAmount);
  
  if (!basicValidation.isValid) {
    return basicValidation;
  }
  
  // Additional validation for auto-bid
  const minAutoBid = currentPrice + incrementAmount * 2; // Require at least 2 increments for auto-bid
  if (maxAutoBid < minAutoBid) {
    return {
      isValid: false,
      error: `Auto-bid maximum must be at least $${minAutoBid.toFixed(2)} (current price + 2 increments)`
    };
  }
  
  return {
    isValid: true
  };
};
