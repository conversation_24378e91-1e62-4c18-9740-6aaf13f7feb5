import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '../../../../config';

/**
 * Auction data interface
 */
interface Auction {
  id: string | number;
  title: string;
  description: string;
  seller: {
    id: number;
    name: string;
    rating: number;
  };
  item: {
    id: number;
    title: string;
    category: string;
    images: string[];
  };
  auctionType: string;
  startingPrice: number;
  currentPrice: number;
  incrementAmount: number;
  startDate: Date;
  endDate: Date;
  status: string;
  bidCount: number;
}

/**
 * Bid data interface
 */
interface Bid {
  id: number;
  userId: number;
  userName: string;
  amount: number;
  timestamp: Date;
}

/**
 * useAuctionData Hook
 * 
 * Manages auction and bid data fetching and state management.
 * Provides functions to update auction and bid data.
 */
export const useAuctionData = (auctionId: string | number) => {
  const [auction, setAuction] = useState<Auction | null>(null);
  const [bids, setBids] = useState<Bid[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch auction details
  const fetchAuctionDetails = async () => {
    setLoading(true);
    try {
      // In a real implementation, this would fetch from the API
      // const response = await axios.get(`${API_BASE_URL}/api/v1/auctions/${auctionId}`);
      // setAuction(response.data);
      
      // Mock data for development
      setTimeout(() => {
        const mockAuction: Auction = {
          id: auctionId,
          title: 'Professional Camera',
          description: 'High-quality professional camera with multiple lenses and accessories.',
          seller: {
            id: 101,
            name: 'John Smith',
            rating: 4.8
          },
          item: {
            id: 2,
            title: 'Professional Camera',
            category: 'Electronics & Technology',
            images: ['https://example.com/camera.jpg']
          },
          auctionType: 'standard',
          startingPrice: 75.00,
          currentPrice: 125.00,
          incrementAmount: 5.00,
          startDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
          endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
          status: 'active',
          bidCount: 8
        };
        
        setAuction(mockAuction);
        setLoading(false);
        
        // Fetch bids
        fetchBids(auctionId);
      }, 1000);
    } catch (err) {
      console.error('Error fetching auction details:', err);
      setError('Failed to load auction details. Please try again later.');
      setLoading(false);
    }
  };
  
  // Fetch bids
  const fetchBids = async (id: string | number) => {
    try {
      // In a real implementation, this would fetch from the API
      // const response = await axios.get(`${API_BASE_URL}/api/v1/auctions/${id}/bids`);
      // setBids(response.data);
      
      // Mock data for development
      const mockBids: Bid[] = [
        {
          id: 1,
          userId: 201,
          userName: 'Alice Johnson',
          amount: 125.00,
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
        },
        {
          id: 2,
          userId: 202,
          userName: 'Bob Williams',
          amount: 120.00,
          timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000) // 5 hours ago
        },
        {
          id: 3,
          userId: 203,
          userName: 'Charlie Brown',
          amount: 115.00,
          timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000) // 8 hours ago
        },
        {
          id: 4,
          userId: 204,
          userName: 'Diana Miller',
          amount: 110.00,
          timestamp: new Date(Date.now() - 10 * 60 * 60 * 1000) // 10 hours ago
        },
        {
          id: 5,
          userId: 205,
          userName: 'Edward Davis',
          amount: 105.00,
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000) // 12 hours ago
        },
        {
          id: 6,
          userId: 206,
          userName: 'Fiona Wilson',
          amount: 100.00,
          timestamp: new Date(Date.now() - 15 * 60 * 60 * 1000) // 15 hours ago
        },
        {
          id: 7,
          userId: 207,
          userName: 'George Taylor',
          amount: 95.00,
          timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000) // 18 hours ago
        },
        {
          id: 8,
          userId: 208,
          userName: 'Hannah Martin',
          amount: 90.00,
          timestamp: new Date(Date.now() - 20 * 60 * 60 * 1000) // 20 hours ago
        }
      ];
      
      setBids(mockBids);
    } catch (err) {
      console.error('Error fetching bids:', err);
    }
  };
  
  // Update auction data
  const updateAuction = (updates: Partial<Auction>) => {
    setAuction(prev => prev ? { ...prev, ...updates } : null);
  };
  
  // Update bids data
  const updateBids = (newBids: Bid[]) => {
    setBids(newBids);
  };
  
  // Fetch data on mount
  useEffect(() => {
    fetchAuctionDetails();
  }, [auctionId]);
  
  return {
    auction,
    bids,
    loading,
    error,
    updateAuction,
    updateBids,
    refetch: fetchAuctionDetails
  };
};
