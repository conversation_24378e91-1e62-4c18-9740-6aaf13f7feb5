import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '../../../../config';
import { validateBidAmount } from '../utils/bidValidation';

/**
 * Auction interface for bidding logic
 */
interface Auction {
  id: string | number;
  currentPrice: number;
  incrementAmount: number;
  bidCount: number;
}

/**
 * Bid interface
 */
interface Bid {
  id: number;
  userId: number;
  userName: string;
  amount: number;
  timestamp: Date;
}

/**
 * Confirmation dialog state
 */
interface ConfirmDialog {
  open: boolean;
  amount: number;
}

/**
 * useBiddingLogic Hook
 * 
 * Manages all bidding-related logic including bid placement,
 * auto-bidding, and bid amount calculations.
 */
export const useBiddingLogic = (
  auction: Auction | null,
  updateAuction: (updates: Partial<Auction>) => void,
  updateBids: (bids: Bid[]) => void,
  onBidPlaced?: (bid: Bid) => void
) => {
  const [bidAmount, setBidAmount] = useState('');
  const [bidLoading, setBidLoading] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialog>({ open: false, amount: 0 });
  const [autoBidEnabled, setAutoBidEnabled] = useState(false);
  const [maxAutoBid, setMaxAutoBid] = useState('');
  
  // Initialize bid amount when auction loads
  useEffect(() => {
    if (auction) {
      setBidAmount((auction.currentPrice + auction.incrementAmount).toFixed(2));
    }
  }, [auction]);
  
  // Handle increment bid
  const handleIncrementBid = () => {
    if (!auction) return;
    
    const currentBid = parseFloat(bidAmount) || auction.currentPrice;
    const newBid = currentBid + auction.incrementAmount;
    setBidAmount(newBid.toFixed(2));
  };
  
  // Handle decrement bid
  const handleDecrementBid = () => {
    if (!auction) return;
    
    const currentBid = parseFloat(bidAmount) || auction.currentPrice;
    const minBid = auction.currentPrice + auction.incrementAmount;
    const newBid = Math.max(minBid, currentBid - auction.incrementAmount);
    setBidAmount(newBid.toFixed(2));
  };
  
  // Handle place bid
  const handlePlaceBid = () => {
    if (!auction) return;
    
    const bidValue = parseFloat(bidAmount);
    const validation = validateBidAmount(bidValue, auction.currentPrice, auction.incrementAmount);
    
    if (!validation.isValid) {
      // This would typically show an error message
      // For now, we'll just return early
      console.error('Invalid bid:', validation.error);
      return;
    }
    
    // Open confirmation dialog
    setConfirmDialog({
      open: true,
      amount: bidValue
    });
  };
  
  // Handle confirm bid
  const handleConfirmBid = async () => {
    if (!auction) return;
    
    setBidLoading(true);
    
    try {
      // In a real implementation, this would submit to the API
      // const response = await axios.post(`${API_BASE_URL}/api/v1/auctions/${auction.id}/bids`, {
      //   amount: confirmDialog.amount
      // });
      
      // Mock submission for development
      setTimeout(() => {
        const newBid: Bid = {
          id: Math.floor(Math.random() * 1000),
          userId: 999, // Current user
          userName: 'You',
          amount: confirmDialog.amount,
          timestamp: new Date()
        };
        
        // Update auction and bids
        updateAuction({
          currentPrice: confirmDialog.amount,
          bidCount: auction.bidCount + 1
        });
        
        // Update bids list (assuming we get current bids from parent)
        // In a real implementation, we'd fetch updated bids from the server
        updateBids([newBid]); // This would be the full updated list
        
        // Update bid amount input
        setBidAmount((confirmDialog.amount + auction.incrementAmount).toFixed(2));
        
        // Close dialog
        setConfirmDialog({ open: false, amount: 0 });
        
        // Callback
        if (onBidPlaced) {
          onBidPlaced(newBid);
        }
        
        setBidLoading(false);
      }, 1500);
    } catch (err) {
      console.error('Error placing bid:', err);
      setBidLoading(false);
    }
  };
  
  // Handle toggle auto-bid
  const handleToggleAutoBid = () => {
    setAutoBidEnabled(prev => !prev);
  };
  
  // Handle set auto-bid
  const handleSetAutoBid = () => {
    if (!auction) return;
    
    const maxBid = parseFloat(maxAutoBid);
    const validation = validateBidAmount(maxBid, auction.currentPrice, auction.incrementAmount);
    
    if (!validation.isValid) {
      console.error('Invalid max bid:', validation.error);
      return;
    }
    
    // In a real implementation, this would submit to the API
    // const response = await axios.post(`${API_BASE_URL}/api/v1/auctions/${auction.id}/autobid`, {
    //   maxAmount: maxBid
    // });
    
    console.log(`Auto-bid set with maximum of $${maxBid.toFixed(2)}`);
  };
  
  return {
    bidAmount,
    setBidAmount,
    bidLoading,
    confirmDialog,
    setConfirmDialog,
    autoBidEnabled,
    setAutoBidEnabled,
    maxAutoBid,
    setMaxAutoBid,
    handlePlaceBid,
    handleConfirmBid,
    handleIncrementBid,
    handleDecrementBid,
    handleToggleAutoBid,
    handleSetAutoBid
  };
};
