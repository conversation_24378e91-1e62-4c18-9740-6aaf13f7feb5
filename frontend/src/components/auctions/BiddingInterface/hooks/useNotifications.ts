import { useState } from 'react';

/**
 * Snackbar state interface
 */
interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

/**
 * useNotifications Hook
 * 
 * Manages notification state and provides functions to show notifications
 * and toggle notification preferences.
 */
export const useNotifications = () => {
  const [notifications, setNotifications] = useState(false);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Handle toggle notifications
  const handleToggleNotifications = () => {
    setNotifications(prev => {
      const newValue = !prev;
      
      // Show feedback about the change
      setSnackbar({
        open: true,
        message: newValue ? 'Bid notifications enabled' : 'Bid notifications disabled',
        severity: 'info'
      });
      
      return newValue;
    });
  };
  
  // Show notification
  const showNotification = (
    message: string, 
    severity: 'success' | 'error' | 'info' | 'warning' = 'info'
  ) => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };
  
  // Handle close snackbar
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };
  
  return {
    notifications,
    snackbar,
    handleToggleNotifications,
    showNotification,
    handleCloseSnackbar
  };
};
