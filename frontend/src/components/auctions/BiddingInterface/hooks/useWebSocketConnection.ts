import { useEffect, useRef } from 'react';

/**
 * Auction interface for WebSocket updates
 */
interface Auction {
  id: string | number;
  currentPrice: number;
  bidCount: number;
  incrementAmount: number;
}

/**
 * Bid interface
 */
interface Bid {
  id: number;
  userId: number;
  userName: string;
  amount: number;
  timestamp: Date;
}

/**
 * WebSocket message interface
 */
interface WebSocketMessage {
  type: string;
  bid?: Bid;
  auction?: Partial<Auction>;
}

/**
 * useWebSocketConnection Hook
 * 
 * Manages WebSocket connection for real-time auction updates.
 * Handles incoming bid updates and auction status changes.
 */
export const useWebSocketConnection = (
  auctionId: string | number,
  updateAuction: (updates: Partial<Auction>) => void,
  updateBids: (bids: Bid[]) => void,
  showNotification: (message: string, severity?: 'success' | 'error' | 'info' | 'warning') => void,
  notificationsEnabled: boolean
) => {
  const wsRef = useRef<WebSocket | null>(null);
  
  useEffect(() => {
    // Set up WebSocket connection for real-time updates
    wsRef.current = new WebSocket(`wss://example.com/ws/auctions/${auctionId}`);
    
    wsRef.current.onopen = () => {
      console.log('WebSocket connection established');
    };
    
    wsRef.current.onmessage = (event) => {
      try {
        const data: WebSocketMessage = JSON.parse(event.data);
        
        if (data.type === 'new_bid' && data.bid) {
          // Update auction current price and bid count
          updateAuction({
            currentPrice: data.bid.amount,
            bidCount: (data.auction?.bidCount || 0) + 1
          });
          
          // Update bids list (in a real implementation, this would be the full updated list)
          // For now, we'll just add the new bid to the beginning
          updateBids([data.bid]);
          
          // Show notification if enabled
          if (notificationsEnabled) {
            showNotification(
              `New bid: $${data.bid.amount.toFixed(2)} by ${data.bid.userName}`,
              'info'
            );
          }
        } else if (data.type === 'auction_ended') {
          // Handle auction end
          if (data.auction) {
            updateAuction(data.auction);
          }
          
          showNotification('This auction has ended', 'info');
        } else if (data.type === 'auction_updated' && data.auction) {
          // Handle general auction updates
          updateAuction(data.auction);
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };
    
    wsRef.current.onerror = (error) => {
      console.error('WebSocket error:', error);
      showNotification('Connection error. Real-time updates may be delayed.', 'warning');
    };
    
    wsRef.current.onclose = (event) => {
      console.log('WebSocket connection closed', event.code, event.reason);
      
      // Attempt to reconnect after a delay if the connection was not closed intentionally
      if (event.code !== 1000) {
        setTimeout(() => {
          console.log('Attempting to reconnect WebSocket...');
          // In a real implementation, you might want to implement exponential backoff
        }, 5000);
      }
    };
    
    // Clean up WebSocket connection
    return () => {
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmounting');
      }
    };
  }, [auctionId, updateAuction, updateBids, showNotification, notificationsEnabled]);
  
  // Function to send messages through WebSocket
  const sendMessage = (message: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  };
  
  return {
    sendMessage,
    isConnected: wsRef.current?.readyState === WebSocket.OPEN
  };
};
