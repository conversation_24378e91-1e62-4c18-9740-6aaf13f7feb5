import React from 'react';
import { Box, CircularProgress, Alert } from '@mui/material';
import { AuctionInfo } from './components/AuctionInfo';
import { BidForm } from './components/BidForm';
import { AutoBidSettings } from './components/AutoBidSettings';
import { BidHistory } from './components/BidHistory';
import { BidConfirmationDialog } from './components/BidConfirmationDialog';
import { NotificationSnackbar } from './components/NotificationSnackbar';
import { useAuctionData } from './hooks/useAuctionData';
import { useBiddingLogic } from './hooks/useBiddingLogic';
import { useWebSocketConnection } from './hooks/useWebSocketConnection';
import { useNotifications } from './hooks/useNotifications';
import { isPast } from 'date-fns';

/**
 * BiddingInterface Component
 * 
 * Main component that orchestrates the auction bidding interface.
 * Handles real-time updates, bid placement, and user interactions.
 * 
 * @param auctionId - The ID of the auction
 * @param onBidPlaced - Callback function called when a bid is placed
 */
interface BiddingInterfaceProps {
  auctionId: string | number;
  onBidPlaced?: (bid: any) => void;
}

const BiddingInterface: React.FC<BiddingInterfaceProps> = ({ 
  auctionId, 
  onBidPlaced 
}) => {
  // Custom hooks for data and logic
  const { auction, bids, loading, error, updateAuction, updateBids } = useAuctionData(auctionId);
  
  const {
    bidAmount,
    setBidAmount,
    bidLoading,
    confirmDialog,
    setConfirmDialog,
    autoBidEnabled,
    setAutoBidEnabled,
    maxAutoBid,
    setMaxAutoBid,
    handlePlaceBid,
    handleConfirmBid,
    handleIncrementBid,
    handleDecrementBid,
    handleToggleAutoBid,
    handleSetAutoBid
  } = useBiddingLogic(auction, updateAuction, updateBids, onBidPlaced);
  
  const {
    notifications,
    snackbar,
    handleToggleNotifications,
    handleCloseSnackbar,
    showNotification
  } = useNotifications();
  
  // WebSocket connection for real-time updates
  useWebSocketConnection(auctionId, updateAuction, updateBids, showNotification, notifications);
  
  // Render loading state
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }
  
  // Render auction ended state
  if (auction && isPast(new Date(auction.endDate))) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info">
          This auction has ended. {bids.length > 0 
            ? `The winning bid was $${bids[0].amount.toFixed(2)} by ${bids[0].userName}.` 
            : 'No bids were placed.'
          }
        </Alert>
        
        <BidHistory 
          bids={bids} 
          showHistory={true}
          onToggleHistory={() => {}}
          notifications={notifications}
          onToggleNotifications={handleToggleNotifications}
          auctionEnded={true}
        />
      </Box>
    );
  }
  
  if (!auction) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="warning">Auction not found.</Alert>
      </Box>
    );
  }
  
  return (
    <Box>
      {/* Auction Information */}
      <AuctionInfo auction={auction} />
      
      {/* Bid Form */}
      <BidForm
        auction={auction}
        bidAmount={bidAmount}
        setBidAmount={setBidAmount}
        onPlaceBid={handlePlaceBid}
        onIncrementBid={handleIncrementBid}
        onDecrementBid={handleDecrementBid}
      />
      
      {/* Auto-Bid Settings */}
      <AutoBidSettings
        auction={auction}
        autoBidEnabled={autoBidEnabled}
        maxAutoBid={maxAutoBid}
        setMaxAutoBid={setMaxAutoBid}
        onToggleAutoBid={handleToggleAutoBid}
        onSetAutoBid={handleSetAutoBid}
      />
      
      {/* Bid History */}
      <BidHistory
        bids={bids}
        notifications={notifications}
        onToggleNotifications={handleToggleNotifications}
      />
      
      {/* Bid Confirmation Dialog */}
      <BidConfirmationDialog
        open={confirmDialog.open}
        amount={confirmDialog.amount}
        loading={bidLoading}
        onConfirm={handleConfirmBid}
        onCancel={() => setConfirmDialog({ open: false, amount: 0 })}
      />
      
      {/* Notification Snackbar */}
      <NotificationSnackbar
        snackbar={snackbar}
        onClose={handleCloseSnackbar}
      />
    </Box>
  );
};

export default BiddingInterface;
