import React, { useState, useEffect } from 'react';
import { useInView } from 'react-intersection-observer';
import { cn } from '../../utils/classNames';
import { generateSrcSet, getBestImageFormat } from '../../utils/imageOptimization';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  sizes?: string;
  className?: string;
  containerClassName?: string;
  loading?: 'lazy' | 'eager';
  decoding?: 'async' | 'sync' | 'auto';
  fetchPriority?: 'high' | 'low' | 'auto';
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  quality?: number;
  placeholder?: 'blur' | 'empty' | 'none';
  onLoad?: () => void;
  onError?: () => void;
  testId?: string;
}

/**
 * OptimizedImage Component
 * 
 * A component for rendering optimized, responsive images with:
 * - Lazy loading
 * - Responsive srcset
 * - Modern image formats (WebP/AVIF)
 * - Blur-up loading
 * - Proper aspect ratio preservation
 * 
 * @example
 * <OptimizedImage
 *   src="/images/example.jpg"
 *   alt="Example image"
 *   width={800}
 *   height={600}
 *   sizes="(max-width: 768px) 100vw, 50vw"
 *   loading="lazy"
 *   placeholder="blur"
 * />
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  sizes = '100vw',
  className = '',
  containerClassName = '',
  loading = 'lazy',
  decoding = 'async',
  fetchPriority = 'auto',
  objectFit = 'cover',
  quality = 80,
  placeholder = 'none',
  onLoad,
  onError,
  testId,
}) => {
  const [isLoaded, setIsLoaded] = useState(loading === 'eager');
  const [error, setError] = useState(false);
  const [blurDataUrl, setBlurDataUrl] = useState<string | null>(null);
  const [ref, inView] = useInView({
    triggerOnce: true,
    rootMargin: '200px 0px', // Start loading 200px before it comes into view
  });
  
  // Generate srcset for responsive images
  const generateImageSrcSet = () => {
    if (!src) return '';
    
    const widths = [640, 750, 828, 1080, 1200, 1920, 2048];
    const extension = src.split('.').pop() || 'jpg';
    const basePath = src.substring(0, src.lastIndexOf('.'));
    
    return widths
      .map(w => `${basePath}-${w}.${extension} ${w}w`)
      .join(', ');
  };
  
  // Generate blur placeholder
  useEffect(() => {
    if (placeholder === 'blur' && src) {
      // Create a tiny version of the image for the blur placeholder
      const createBlurPlaceholder = async () => {
        try {
          // This is a simplified implementation
          // In a real app, you would use a proper blur hash or tiny placeholder
          const blurUrl = `${src}?w=20&q=20&blur=10`;
          setBlurDataUrl(blurUrl);
        } catch (err) {
          console.error('Failed to create blur placeholder:', err);
        }
      };
      
      createBlurPlaceholder();
    }
  }, [src, placeholder]);
  
  // Handle image load
  const handleLoad = () => {
    setIsLoaded(true);
    onLoad?.();
  };
  
  // Handle image error
  const handleError = () => {
    setError(true);
    onError?.();
  };
  
  // Calculate aspect ratio for placeholder
  const aspectRatio = width && height ? `${width}/${height}` : undefined;
  
  return (
    <div 
      ref={ref}
      className={cn(
        'image-container relative overflow-hidden',
        containerClassName
      )}
      style={{ 
        aspectRatio,
        width: width ? `${width}px` : '100%',
      }}
      data-testid={testId}
    >
      {/* Placeholder */}
      {!isLoaded && placeholder !== 'none' && (
        <div 
          className={cn(
            'absolute inset-0 bg-gray-100',
            placeholder === 'blur' && blurDataUrl ? 'bg-cover bg-center' : ''
          )}
          style={
            placeholder === 'blur' && blurDataUrl 
              ? { backgroundImage: `url(${blurDataUrl})`, filter: 'blur(20px)' } 
              : {}
          }
          aria-hidden="true"
        />
      )}
      
      {/* Main image */}
      {inView && (
        <img
          src={src}
          srcSet={generateImageSrcSet()}
          sizes={sizes}
          alt={alt}
          width={width}
          height={height}
          loading={loading}
          decoding={decoding}
          fetchPriority={fetchPriority}
          onLoad={handleLoad}
          onError={handleError}
          className={cn(
            'w-full h-full transition-opacity duration-500',
            objectFit === 'contain' && 'object-contain',
            objectFit === 'cover' && 'object-cover',
            objectFit === 'fill' && 'object-fill',
            objectFit === 'none' && 'object-none',
            objectFit === 'scale-down' && 'object-scale-down',
            !isLoaded && 'opacity-0',
            isLoaded && 'opacity-100',
            className
          )}
        />
      )}
      
      {/* Error state */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-500">
          <span>Failed to load image</span>
        </div>
      )}
    </div>
  );
};

export default OptimizedImage;
