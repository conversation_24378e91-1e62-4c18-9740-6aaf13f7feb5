import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import RecommendationSection from '../RecommendationSection';
import { AuthProvider } from '../../../contexts/AuthContext';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock the UnifiedItemCard component
jest.mock('../../Item/UnifiedItemCard', () => ({
  __esModule: true,
  default: ({ item, onClick, className }: any) => (
    <div
      data-testid={`mock-item-card-${item.id}`}
      onClick={onClick}
      className={className}
    >
      {item.title}
    </div>
  ),
}));

// Mock the RecommendationExplanation component
jest.mock('../RecommendationExplanation', () => ({
  __esModule: true,
  default: ({ itemId, onClose, testId }: any) => (
    <div data-testid={testId || 'mock-recommendation-explanation'}>
      <p>Explanation for item {itemId}</p>
      <button onClick={onClose} data-testid="close-explanation-button">Close</button>
    </div>
  ),
}));

// Mock the auth hook
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id', name: 'Test User' },
    isAuthenticated: true,
  }),
}));

describe('RecommendationSection', () => {
  const mockItems = [
    { id: 'item1', title: 'Item 1', price: 100, category: 'electronics' },
    { id: 'item2', title: 'Item 2', price: 200, category: 'furniture' },
    { id: 'item3', title: 'Item 3', price: 300, category: 'clothing' },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockedAxios.get.mockResolvedValue({ data: mockItems });
  });

  it('renders the component with the correct title', async () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <RecommendationSection
            title="Test Recommendations"
            type="personalized"
          />
        </AuthProvider>
      </BrowserRouter>
    );

    // Check that the title is rendered
    expect(screen.getByText('Test Recommendations')).toBeInTheDocument();

    // Wait for the items to load
    await waitFor(() => {
      expect(screen.getByText('Item 1')).toBeInTheDocument();
      expect(screen.getByText('Item 2')).toBeInTheDocument();
      expect(screen.getByText('Item 3')).toBeInTheDocument();
    });
  });

  it('shows loading state initially', () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <RecommendationSection
            title="Test Recommendations"
            type="personalized"
          />
        </AuthProvider>
      </BrowserRouter>
    );

    // Check that the loading state is rendered
    expect(screen.getByTestId('recommendation-section-loading')).toBeInTheDocument();
  });

  it('shows error state when API call fails', async () => {
    // Mock the API call to fail
    mockedAxios.get.mockRejectedValue(new Error('API error'));

    render(
      <BrowserRouter>
        <AuthProvider>
          <RecommendationSection
            title="Test Recommendations"
            type="personalized"
          />
        </AuthProvider>
      </BrowserRouter>
    );

    // Wait for the error state to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('recommendation-section-error')).toBeInTheDocument();
      expect(screen.getByText('Failed to load recommendations')).toBeInTheDocument();
    });
  });

  it('shows empty state when no items are returned', async () => {
    // Mock the API call to return an empty array
    mockedAxios.get.mockResolvedValue({ data: [] });

    render(
      <BrowserRouter>
        <AuthProvider>
          <RecommendationSection
            title="Test Recommendations"
            type="personalized"
          />
        </AuthProvider>
      </BrowserRouter>
    );

    // Wait for the empty state to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('recommendation-section-empty')).toBeInTheDocument();
      expect(screen.getByText('No recommendations available.')).toBeInTheDocument();
    });
  });

  it('shows explanation modal when an item is clicked and showExplanations is true', async () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <RecommendationSection
            title="Test Recommendations"
            type="personalized"
            showExplanations={true}
          />
        </AuthProvider>
      </BrowserRouter>
    );

    // Wait for the items to load
    await waitFor(() => {
      expect(screen.getByText('Item 1')).toBeInTheDocument();
    });

    // Click on the first item
    fireEvent.click(screen.getByTestId('mock-item-card-item1'));

    // Check that the explanation modal is rendered
    await waitFor(() => {
      expect(screen.getByTestId('recommendation-section-explanation')).toBeInTheDocument();
      expect(screen.getByText('Explanation for item item1')).toBeInTheDocument();
    });

    // Close the explanation modal
    fireEvent.click(screen.getByTestId('close-explanation-button'));

    // Check that the explanation modal is no longer rendered
    await waitFor(() => {
      expect(screen.queryByTestId('recommendation-section-explanation')).not.toBeInTheDocument();
    });
  });

  it('shows explanation button when enhanced and showExplanations are true', async () => {
    render(
      <BrowserRouter>
        <AuthProvider>
          <RecommendationSection
            title="Test Recommendations"
            type="personalized"
            showExplanations={true}
            enhanced={true}
          />
        </AuthProvider>
      </BrowserRouter>
    );

    // Wait for the items to load
    await waitFor(() => {
      expect(screen.getByText('Item 1')).toBeInTheDocument();
    });

    // Check that the explanation buttons are rendered
    expect(screen.getByTestId('recommendation-section-explain-btn-item1')).toBeInTheDocument();
    expect(screen.getByTestId('recommendation-section-explain-btn-item2')).toBeInTheDocument();
    expect(screen.getByTestId('recommendation-section-explain-btn-item3')).toBeInTheDocument();
  });
});
