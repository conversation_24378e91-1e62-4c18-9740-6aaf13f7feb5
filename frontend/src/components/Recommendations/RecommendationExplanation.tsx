import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../hooks/useAuth';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import recommendationService from '../../services/recommendationService';

interface RecommendationExplanationProps {
  itemId: string;
  userId?: string; // Optional - if not provided, uses current user
  className?: string;
  compact?: boolean; // For smaller UI in constrained spaces
  onClose?: () => void; // Callback when explanation is closed
  testId?: string; // For testing purposes
}

interface FactorWeight {
  factor: string;
  weight: number;
  description: string;
}

interface ExplanationData {
  itemName: string;
  itemCategory: string;
  itemImage?: string;
  score: number;
  factorWeights: FactorWeight[];
  userPreferences?: {
    factor: string;
    strength: number;
    description: string;
  }[];
  similarItems?: {
    id: string;
    name: string;
    similarity: number;
  }[];
}

/**
 * A component that explains why a specific item was recommended to a user
 * Shows the factors and weights that influenced the recommendation
 * Allows users to adjust preference weights to see how recommendations would change
 */
const RecommendationExplanation: React.FC<RecommendationExplanationProps> = ({
  itemId,
  userId,
  className = '',
  compact = false,
  onClose,
  testId = 'recommendation-explanation',
}) => {
  const { user } = useAuth();
  const { handleError } = useErrorHandler();
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [explanationData, setExplanationData] = useState<ExplanationData | null>(null);
  const [adjustedWeights, setAdjustedWeights] = useState<Record<string, number>>({});
  const [showAdjustments, setShowAdjustments] = useState<boolean>(false);

  // Fetch explanation data
  useEffect(() => {
    const fetchExplanationData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Use the current user ID if not provided
        const targetUserId = userId || user?.id;
        
        if (!targetUserId) {
          throw new Error('User ID is required');
        }

        // Create a timeout for the request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        try {
          // Fetch recommendation explanation
          const response = await fetch(
            `${recommendationService.getApiUrl()}/api/v1/recommendations/explanation?item_id=${encodeURIComponent(itemId)}&user_id=${encodeURIComponent(targetUserId)}`,
            {
              method: 'GET',
              signal: controller.signal,
              headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
              },
              credentials: 'same-origin'
            }
          );

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }

          const data = await response.json();
          
          // Initialize adjusted weights with the original weights
          const initialWeights: Record<string, number> = {};
          data.factorWeights.forEach((factor: FactorWeight) => {
            initialWeights[factor.factor] = factor.weight;
          });
          
          setAdjustedWeights(initialWeights);
          setExplanationData(data);
          setLoading(false);
        } catch (err) {
          clearTimeout(timeoutId);
          throw err;
        }
      } catch (err) {
        handleError(err, {
          friendlyMessage: 'Failed to load recommendation explanation. Please try again.',
          logMessage: 'Error fetching recommendation explanation:',
          setError: setError,
        });
        setLoading(false);
      }
    };

    fetchExplanationData();
  }, [itemId, userId, user?.id, handleError]);

  // Handle weight adjustment
  const handleWeightChange = (factor: string, value: number) => {
    setAdjustedWeights(prev => ({
      ...prev,
      [factor]: value
    }));
  };

  // Reset weights to original values
  const resetWeights = () => {
    if (!explanationData) return;
    
    const originalWeights: Record<string, number> = {};
    explanationData.factorWeights.forEach(factor => {
      originalWeights[factor.factor] = factor.weight;
    });
    
    setAdjustedWeights(originalWeights);
  };

  // Apply adjusted weights
  const applyAdjustedWeights = async () => {
    try {
      setLoading(true);
      
      // Convert adjusted weights to the format expected by the API
      const weightUpdates = Object.entries(adjustedWeights).map(([factor, weight]) => ({
        factor,
        weight
      }));
      
      // Call the API to update preference weights
      await recommendationService.updatePreferenceWeights(user?.id || '', weightUpdates);
      
      // Fetch updated explanation data
      // This would typically be handled by the recommendation service
      // For now, we'll just simulate a delay and then hide the loading state
      setTimeout(() => {
        setLoading(false);
        setShowAdjustments(false);
      }, 1000);
    } catch (err) {
      handleError(err, {
        friendlyMessage: 'Failed to update preference weights. Please try again.',
        logMessage: 'Error updating preference weights:',
        setError: setError,
      });
      setLoading(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-loading`}
        aria-busy="true"
        aria-live="polite"
      >
        <div className="flex justify-between items-center mb-4">
          <div className="h-6 bg-gray-200 animate-pulse rounded w-1/3"></div>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
              aria-label="Close explanation"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
        <div className="space-y-4">
          <div className="h-20 bg-gray-100 animate-pulse rounded"></div>
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 animate-pulse rounded w-1/4"></div>
            <div className="h-24 bg-gray-100 animate-pulse rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-error`}
        role="alert"
        aria-live="assertive"
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className={`${compact ? 'text-base' : 'text-lg'} font-semibold`}>
            Recommendation Explanation
          </h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
              aria-label="Close explanation"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
        <div className="bg-red-50 text-red-600 p-4 rounded-md">
          <p className="mb-3">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // No data state
  if (!explanationData) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-empty`}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className={`${compact ? 'text-base' : 'text-lg'} font-semibold`}>
            Recommendation Explanation
          </h3>
          {onClose && (
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
              aria-label="Close explanation"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
        <div className="text-center py-8">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 mx-auto text-gray-400 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="text-gray-700 mb-2">No explanation data available</p>
          <p className="text-sm text-gray-500">We couldn't find explanation data for this recommendation.</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={testId}
    >
      <div className="flex justify-between items-center mb-4">
        <h3
          className={`${compact ? 'text-base' : 'text-lg'} font-semibold`}
          id={`${testId}-title`}
        >
          Why We Recommended This
        </h3>
        {onClose && (
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
            aria-label="Close explanation"
            data-testid={`${testId}-close`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>

      {/* Item information */}
      <div className="flex items-center mb-4">
        {explanationData.itemImage && (
          <img
            src={explanationData.itemImage}
            alt={explanationData.itemName}
            className="w-16 h-16 object-cover rounded-md mr-3"
          />
        )}
        <div>
          <h4 className="font-medium text-gray-900">{explanationData.itemName}</h4>
          <p className="text-sm text-gray-700 capitalize">{explanationData.itemCategory}</p>
          <div className="mt-1 flex items-center">
            <span className="text-sm font-medium text-gray-900 mr-1">Match Score:</span>
            <span className="text-sm text-gray-700">{Math.round(explanationData.score * 100)}%</span>
          </div>
        </div>
      </div>

      {/* Factor weights */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h4 className="text-sm font-medium text-gray-900">Recommendation Factors</h4>
          <button
            onClick={() => setShowAdjustments(!showAdjustments)}
            className="text-xs text-blue-600 hover:text-blue-800 focus:outline-none focus:underline"
            data-testid={`${testId}-toggle-adjustments`}
          >
            {showAdjustments ? 'Hide Adjustments' : 'Adjust Preferences'}
          </button>
        </div>

        <div className="space-y-3">
          {explanationData.factorWeights.map((factor) => (
            <div key={factor.factor} className="space-y-1">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-700 capitalize">{factor.factor}</span>
                <span className="text-xs text-gray-600">{Math.round(factor.weight * 100)}%</span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                <div
                  className="h-2 rounded-full bg-blue-600"
                  style={{ width: `${factor.weight * 100}%` }}
                  role="progressbar"
                  aria-valuenow={factor.weight * 100}
                  aria-valuemin={0}
                  aria-valuemax={100}
                ></div>
              </div>
              
              <p className="text-xs text-gray-600">{factor.description}</p>
              
              {/* Weight adjustment slider (only shown when adjustments are enabled) */}
              <AnimatePresence>
                {showAdjustments && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="pt-1 overflow-hidden"
                  >
                    <div className="flex items-center">
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.01"
                        value={adjustedWeights[factor.factor] || factor.weight}
                        onChange={(e) => handleWeightChange(factor.factor, parseFloat(e.target.value))}
                        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                        aria-label={`Adjust importance of ${factor.factor}`}
                        data-testid={`${testId}-slider-${factor.factor}`}
                      />
                      <span className="ml-2 text-xs font-medium text-gray-700 min-w-[40px] text-right">
                        {Math.round((adjustedWeights[factor.factor] || factor.weight) * 100)}%
                      </span>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </div>
      </div>

      {/* Adjustment controls */}
      <AnimatePresence>
        {showAdjustments && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="mb-4 pt-2 border-t border-gray-100 overflow-hidden"
          >
            <div className="flex justify-between">
              <button
                onClick={resetWeights}
                className="text-sm text-gray-600 hover:text-gray-800 focus:outline-none focus:underline"
                data-testid={`${testId}-reset-weights`}
              >
                Reset to Original
              </button>
              <button
                onClick={applyAdjustedWeights}
                className="text-sm bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                data-testid={`${testId}-apply-weights`}
              >
                Apply Changes
              </button>
            </div>
            <p className="text-xs text-gray-600 mt-2">
              Adjusting these preferences will affect future recommendations across the platform.
            </p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Similar items */}
      {explanationData.similarItems && explanationData.similarItems.length > 0 && (
        <div className="pt-4 border-t border-gray-100">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Similar Items You Might Like</h4>
          <div className="space-y-2">
            {explanationData.similarItems.map((item) => (
              <div key={item.id} className="flex justify-between items-center">
                <span className="text-sm text-gray-700">{item.name}</span>
                <span className="text-xs text-gray-600">{Math.round(item.similarity * 100)}% similar</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Explanation footer */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-600">
          Our recommendation system analyzes your preferences, browsing history, and similar users' behavior
          to suggest items you might be interested in. You can adjust your preferences to improve future recommendations.
        </p>
      </div>
    </div>
  );
};

export default RecommendationExplanation;
