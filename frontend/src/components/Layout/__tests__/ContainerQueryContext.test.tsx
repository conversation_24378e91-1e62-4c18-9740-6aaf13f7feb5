import React from 'react';
import { render, screen } from '@testing-library/react';
import { ContainerQueryContext, ResponsiveContainer } from '../ContainerQueryContext';

describe('ContainerQueryContext', () => {
  it('renders children with container query class', () => {
    render(
      <ContainerQueryContext testId="container-query">
        <div>Test Content</div>
      </ContainerQueryContext>
    );
    
    const container = screen.getByTestId('container-query');
    expect(container).toHaveClass('cq-container');
    expect(container).toHaveTextContent('Test Content');
  });
  
  it('applies strict container query class when strict prop is true', () => {
    render(
      <ContainerQueryContext strict testId="container-query-strict">
        <div>Test Content</div>
      </ContainerQueryContext>
    );
    
    const container = screen.getByTestId('container-query-strict');
    expect(container).toHaveClass('cq-container-strict');
    expect(container).not.toHaveClass('cq-container');
  });
  
  it('applies additional classes from className prop', () => {
    render(
      <ContainerQueryContext className="test-class" testId="container-query-class">
        <div>Test Content</div>
      </ContainerQueryContext>
    );
    
    const container = screen.getByTestId('container-query-class');
    expect(container).toHaveClass('cq-container');
    expect(container).toHaveClass('test-class');
  });
});

describe('ResponsiveContainer', () => {
  it('renders children with container query context', () => {
    render(
      <ResponsiveContainer testId="responsive-container">
        <div>Test Content</div>
      </ResponsiveContainer>
    );
    
    const container = screen.getByTestId('responsive-container');
    expect(container).toHaveClass('cq-container');
    expect(container).toHaveTextContent('Test Content');
  });
  
  it('applies breakpoint-specific classes', () => {
    render(
      <ResponsiveContainer
        xs="grid-cols-1 gap-2"
        md="grid-cols-2 gap-4"
        lg="grid-cols-3 gap-6"
        testId="responsive-container-breakpoints"
      >
        <div>Test Content</div>
      </ResponsiveContainer>
    );
    
    const container = screen.getByTestId('responsive-container-breakpoints');
    const innerDiv = container.firstChild as HTMLElement;
    
    expect(innerDiv).toHaveClass('cq-grid-cols-1-xs');
    expect(innerDiv).toHaveClass('cq-gap-2-xs');
    expect(innerDiv).toHaveClass('cq-grid-cols-2-md');
    expect(innerDiv).toHaveClass('cq-gap-4-md');
    expect(innerDiv).toHaveClass('cq-grid-cols-3-lg');
    expect(innerDiv).toHaveClass('cq-gap-6-lg');
  });
  
  it('applies additional classes from className prop', () => {
    render(
      <ResponsiveContainer className="test-class" testId="responsive-container-class">
        <div>Test Content</div>
      </ResponsiveContainer>
    );
    
    const container = screen.getByTestId('responsive-container-class');
    expect(container).toHaveClass('cq-container');
    expect(container).toHaveClass('test-class');
  });
});
