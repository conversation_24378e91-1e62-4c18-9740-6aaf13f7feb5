import React from 'react';
import { cn } from '../../utils/classNames';

interface ContainerQueryContextProps {
  children: React.ReactNode;
  className?: string;
  strict?: boolean;
  testId?: string;
}

/**
 * ContainerQueryContext Component
 * 
 * Creates a container query context for component-level responsiveness.
 * This allows child components to respond to the size of their container
 * rather than the viewport size.
 * 
 * @example
 * <ContainerQueryContext>
 *   <div className="cq-grid-cols-1-xs cq-grid-cols-2-md cq-grid-cols-3-lg">
 *     {items.map(item => (
 *       <div key={item.id} className="cq-p-2-xs cq-p-4-md">
 *         {item.name}
 *       </div>
 *     ))}
 *   </div>
 * </ContainerQueryContext>
 */
export const ContainerQueryContext: React.FC<ContainerQueryContextProps> = ({
  children,
  className,
  strict = false,
  testId,
}) => {
  return (
    <div
      className={cn(
        strict ? 'cq-container-strict' : 'cq-container',
        className
      )}
      data-testid={testId}
    >
      {children}
    </div>
  );
};

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  xs?: string;
  sm?: string;
  md?: string;
  lg?: string;
  xl?: string;
  testId?: string;
}

/**
 * ResponsiveContainer Component
 * 
 * A component that applies different classes based on the container size.
 * This is a convenience wrapper around the ContainerQueryContext component.
 * 
 * @example
 * <ResponsiveContainer
 *   xs="grid-cols-1 gap-2 p-2"
 *   md="grid-cols-2 gap-4 p-4"
 *   lg="grid-cols-3 gap-6 p-6"
 * >
 *   {items.map(item => (
 *     <div key={item.id}>{item.name}</div>
 *   ))}
 * </ResponsiveContainer>
 */
export const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className,
  xs,
  sm,
  md,
  lg,
  xl,
  testId,
}) => {
  // Convert space-separated class strings to cq-prefixed classes
  const convertToContainerClasses = (breakpoint: string, classes?: string) => {
    if (!classes) return '';
    
    return classes
      .split(' ')
      .map(cls => `cq-${cls}-${breakpoint}`)
      .join(' ');
  };
  
  const xsClasses = convertToContainerClasses('xs', xs);
  const smClasses = convertToContainerClasses('sm', sm);
  const mdClasses = convertToContainerClasses('md', md);
  const lgClasses = convertToContainerClasses('lg', lg);
  const xlClasses = convertToContainerClasses('xl', xl);
  
  return (
    <ContainerQueryContext
      className={className}
      testId={testId}
    >
      <div
        className={cn(
          xsClasses,
          smClasses,
          mdClasses,
          lgClasses,
          xlClasses
        )}
      >
        {children}
      </div>
    </ContainerQueryContext>
  );
};

export default {
  ContainerQueryContext,
  ResponsiveContainer,
};
