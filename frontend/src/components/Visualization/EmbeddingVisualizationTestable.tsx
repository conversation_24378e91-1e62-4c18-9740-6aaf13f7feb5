import React, { useState, useEffect } from 'react';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import recommendationService, { EmbeddingPoint } from '../../services/recommendationService';

interface EmbeddingVisualizationTestableProps {
  itemId?: string;
  categoryId?: string;
  limit?: number;
  className?: string;
  compact?: boolean;
  width?: number;
  height?: number;
  onDataLoaded?: (data: EmbeddingPoint[]) => void;
  testId?: string;
  // For testing purposes
  mockData?: EmbeddingPoint[];
  mockError?: boolean;
  mockEmpty?: boolean;
}

/**
 * A simplified version of EmbeddingVisualization for testing purposes
 * This component has the same API as EmbeddingVisualization but with simpler rendering
 * and more testable behavior
 */
const EmbeddingVisualizationTestable: React.FC<EmbeddingVisualizationTestableProps> = ({
  itemId,
  categoryId,
  limit = 100,
  className = '',
  compact = false,
  width: propWidth,
  height: propHeight,
  onDataLoaded,
  testId = 'embed-viz',
  // Testing props
  mockData,
  mockError = false,
  mockEmpty = false,
}) => {
  const [embeddings, setEmbeddings] = useState<EmbeddingPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(categoryId || null);
  const { handleError } = useErrorHandler();

  // Initialize state based on props
  useEffect(() => {
    // For testing: simulate error
    if (mockError) {
      setError('Error message');
      setLoading(false);
      return;
    }

    // For testing: use mock data if provided
    if (mockData) {
      setEmbeddings(mockData);
      if (onDataLoaded) {
        onDataLoaded(mockData);
      }
      setLoading(false);
      return;
    }

    // For testing: simulate empty data
    if (mockEmpty) {
      setEmbeddings([]);
      if (onDataLoaded) {
        onDataLoaded([]);
      }
      setLoading(false);
      return;
    }

    // If no testing props are provided, keep loading state
    // In a real component, we would fetch data here
  }, [mockData, mockError, mockEmpty, onDataLoaded]);

  // Handle category selection
  useEffect(() => {
    if (categoryId) {
      setSelectedCategory(categoryId);
    }
  }, [categoryId]);

  // Loading state
  if (loading) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-loading`}
        aria-busy="true"
        aria-live="polite"
      >
        <div>Loading visualization data...</div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-error`}
        role="alert"
        aria-live="assertive"
      >
        <h3>Item Relationships</h3>
        <div>
          <p>{error}</p>
          <button
            onClick={() => window.location.reload()}
            data-testid={`${testId}-retry-button`}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Empty state
  if (embeddings.length === 0) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-empty`}
      >
        <h3>Item Relationships</h3>
        <div>
          <p>No data available</p>
        </div>
      </div>
    );
  }

  // Get unique categories
  const categories = [...new Set(embeddings.map(point => point.category))];

  // Filter embeddings by selected category
  const filteredEmbeddings = selectedCategory
    ? embeddings.filter(point => point.category === selectedCategory)
    : embeddings;

  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={testId}
    >
      <h3>Item Relationships</h3>

      {/* Category filter */}
      <div data-testid={`${testId}-categories`}>
        <button
          onClick={() => setSelectedCategory(null)}
          data-testid={`${testId}-category-all`}
        >
          All Categories
        </button>
        {categories.map(category => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            data-testid={`${testId}-category-${category}`}
          >
            {category}
          </button>
        ))}
      </div>

      {/* Visualization container */}
      <div
        data-testid={`${testId}-container`}
      >
        {/* Points */}
        {filteredEmbeddings.map((point) => (
          <div
            key={point.id}
            data-testid={`${testId}-point-${point.id}`}
            data-category={point.category}
          >
            {point.name}
          </div>
        ))}
      </div>

      {/* Legend */}
      {!compact && (
        <div data-testid={`${testId}-legend`}>
          {categories.map(category => (
            <div key={category}>{category}</div>
          ))}
        </div>
      )}
    </div>
  );
};

export default EmbeddingVisualizationTestable;
