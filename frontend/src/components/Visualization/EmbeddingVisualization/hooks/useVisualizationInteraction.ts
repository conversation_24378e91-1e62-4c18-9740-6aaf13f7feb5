// Custom hook for managing visualization interactions
import { useState, useCallback } from 'react';
import { PanOffset, DragState, VisualizationFilters } from '../utils/types';
import { visualizationSettings } from '../utils/constants';

interface UseVisualizationInteractionProps {
  interactive: boolean;
  onItemSelect?: (itemId: string) => void;
  onCategorySelect?: (category: string | null) => void;
  itemId?: string;
  categoryId?: string;
}

/**
 * Custom hook for managing visualization interactions (zoom, pan, selection)
 */
export const useVisualizationInteraction = ({
  interactive,
  onItemSelect,
  onCategorySelect,
  itemId,
  categoryId,
}: UseVisualizationInteractionProps) => {
  // Selection state
  const [selectedItem, setSelectedItem] = useState<string | null>(itemId || null);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(categoryId || null);

  // Zoom and pan state
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [panOffset, setPanOffset] = useState<PanOffset>({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<DragState | null>(null);

  // Filter state
  const [filters, setFilters] = useState<VisualizationFilters>({
    categories: [],
    priceRange: null,
    searchTerm: '',
  });

  // Handle zoom in/out
  const handleZoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(prev + visualizationSettings.zoomStep, visualizationSettings.maxZoom));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(prev - visualizationSettings.zoomStep, visualizationSettings.minZoom));
  }, []);

  // Reset view
  const resetView = useCallback(() => {
    setZoomLevel(1);
    setPanOffset({ x: 0, y: 0 });
  }, []);

  // Pan functionality handlers
  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!interactive) return;
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
  }, [interactive]);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!interactive || !isDragging || !dragStart) return;

    const dx = e.clientX - dragStart.x;
    const dy = e.clientY - dragStart.y;

    setPanOffset(prev => ({
      x: prev.x + dx,
      y: prev.y + dy
    }));

    setDragStart({ x: e.clientX, y: e.clientY });
  }, [interactive, isDragging, dragStart]);

  const handleMouseUp = useCallback(() => {
    if (!interactive) return;
    setIsDragging(false);
    setDragStart(null);
  }, [interactive]);

  const handleMouseLeave = useCallback(() => {
    if (!interactive) return;
    setIsDragging(false);
    setDragStart(null);
  }, [interactive]);

  // Touch handlers
  const handleTouchStart = useCallback((e: React.TouchEvent<HTMLDivElement>) => {
    if (!interactive) return;
    setIsDragging(true);
    setDragStart({ x: e.touches[0].clientX, y: e.touches[0].clientY });
  }, [interactive]);

  const handleTouchMove = useCallback((e: React.TouchEvent<HTMLDivElement>) => {
    if (!interactive || !isDragging || !dragStart) return;

    const dx = e.touches[0].clientX - dragStart.x;
    const dy = e.touches[0].clientY - dragStart.y;

    setPanOffset(prev => ({
      x: prev.x + dx,
      y: prev.y + dy
    }));

    setDragStart({ x: e.touches[0].clientX, y: e.touches[0].clientY });
  }, [interactive, isDragging, dragStart]);

  const handleTouchEnd = useCallback(() => {
    if (!interactive) return;
    setIsDragging(false);
    setDragStart(null);
  }, [interactive]);

  // Item selection handlers
  const handleItemClick = useCallback((pointId: string) => {
    const newSelectedItem = selectedItem === pointId ? null : pointId;
    setSelectedItem(newSelectedItem);
    if (newSelectedItem && onItemSelect) {
      onItemSelect(newSelectedItem);
    }
  }, [selectedItem, onItemSelect]);

  const handleItemHover = useCallback((pointId: string | null) => {
    setHoveredItem(pointId);
  }, []);

  // Category selection handlers
  const handleCategorySelect = useCallback((category: string | null) => {
    setSelectedCategory(category);
    if (onCategorySelect) {
      onCategorySelect(category);
    }
  }, [onCategorySelect]);

  // Update selected item when itemId prop changes
  const updateSelectedItem = useCallback((newItemId?: string) => {
    setSelectedItem(newItemId || null);
  }, []);

  // Update selected category when categoryId prop changes
  const updateSelectedCategory = useCallback((newCategoryId?: string) => {
    setSelectedCategory(newCategoryId || null);
  }, []);

  return {
    // Selection state
    selectedItem,
    hoveredItem,
    selectedCategory,
    
    // Zoom and pan state
    zoomLevel,
    panOffset,
    isDragging,
    
    // Filter state
    filters,
    setFilters,
    
    // Zoom handlers
    handleZoomIn,
    handleZoomOut,
    resetView,
    
    // Pan handlers
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    
    // Selection handlers
    handleItemClick,
    handleItemHover,
    handleCategorySelect,
    
    // Update handlers
    updateSelectedItem,
    updateSelectedCategory,
  };
};

export default useVisualizationInteraction;
