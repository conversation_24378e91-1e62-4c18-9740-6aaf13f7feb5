// Custom hook for managing embedding data
import { useState, useEffect, useCallback } from 'react';
import { useErrorHandler } from '../../../../hooks/useErrorHandler';
import recommendationService from '../../../../services/recommendationService';
import { EmbeddingPoint } from '../utils/types';
import { validateEmbeddingPoint, visualizationSettings } from '../utils/calculations';

interface UseEmbeddingDataProps {
  itemId?: string;
  categoryId?: string;
  limit: number;
  timePeriod: string;
  onDataLoaded?: (data: EmbeddingPoint[]) => void;
}

/**
 * Custom hook for fetching and managing embedding data
 */
export const useEmbeddingData = ({
  itemId,
  categoryId,
  limit,
  timePeriod,
  onDataLoaded,
}: UseEmbeddingDataProps) => {
  const [embeddings, setEmbeddings] = useState<EmbeddingPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState<number>(0);
  const { handleError } = useErrorHandler();

  // Function to fetch embeddings with retry logic and enhanced security
  const fetchEmbeddings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate inputs
      if (limit < 1 || limit > 200) {
        throw new Error('Invalid limit parameter');
      }

      // Build query parameters
      const params = new URLSearchParams();
      if (itemId) params.append('item_id', encodeURIComponent(itemId));
      if (categoryId) params.append('category_id', encodeURIComponent(categoryId));
      if (limit) params.append('limit', limit.toString());
      if (timePeriod !== 'current') params.append('time_period', timePeriod);

      // Create a timeout for the request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), visualizationSettings.requestTimeout);

      try {
        // Use the service for consistent error handling
        const response = await fetch(`${recommendationService.getApiUrl()}/api/v1/embeddings/visualization?${params.toString()}`, {
          method: 'GET',
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest' // Helps prevent CSRF
          },
          credentials: 'same-origin' // Include cookies for authentication
        });

        clearTimeout(timeoutId);

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Parse and validate the response
        const data = await response.json();

        // Validate the data structure
        if (!Array.isArray(data)) {
          throw new Error('Invalid response format');
        }

        // Validate each point
        const validatedData = data.filter(validateEmbeddingPoint);

        // Call the callback if provided
        if (onDataLoaded) {
          onDataLoaded(validatedData);
        }

        setEmbeddings(validatedData);
        setLoading(false);
      } catch (err) {
        clearTimeout(timeoutId);
        throw err;
      }
    } catch (err) {
      handleError(err, {
        friendlyMessage: 'Failed to load visualization. Please try again.',
        logMessage: 'Error fetching embedding data:',
        setError: setError,
      });
      setLoading(false);
    }
  }, [itemId, categoryId, limit, handleError, onDataLoaded, timePeriod]);

  // Fetch embeddings on component mount and when dependencies change
  useEffect(() => {
    fetchEmbeddings();
  }, [fetchEmbeddings, retryCount]);

  // Retry loading embeddings
  const handleRetry = useCallback(() => {
    setRetryCount(prev => prev + 1);
  }, []);

  return {
    embeddings,
    loading,
    error,
    handleRetry,
  };
};

export default useEmbeddingData;
