// Main EmbeddingVisualization component - refactored for better maintainability
import React, { useEffect } from 'react';
import { useMediaQuery } from '../../../hooks/useMediaQuery';

// Import types
import { EmbeddingVisualizationProps } from './utils/types';

// Import hooks
import { useEmbeddingData } from './hooks/useEmbeddingData';
import { useVisualizationInteraction } from './hooks/useVisualizationInteraction';

// Import utilities
import { calculateBounds, createScalingFunctions, filterEmbeddings } from './utils/calculations';
import { defaultDimensions, breakpoints } from './utils/constants';

// Import components
import LoadingState from './components/LoadingState';
import ErrorState from './components/ErrorState';
import EmptyState from './components/EmptyState';
import VisualizationControls from './components/VisualizationControls';
import AdvancedFilters from './components/AdvancedFilters';
import CategoryFilter from './components/CategoryFilter';
import VisualizationCanvas from './components/VisualizationCanvas';
import VisualizationLegend from './components/VisualizationLegend';
import VisualizationExplanation from './components/VisualizationExplanation';

/**
 * A responsive component to visualize item embeddings in 2D space
 * Shows how items are related to each other based on their embeddings
 * Optimized for different screen sizes and touch devices
 *
 * Security features:
 * - Uses AbortController for request timeouts
 * - Sanitizes and validates data
 * - Uses HTTPS for API calls
 * - Implements proper error handling
 * - Prevents XSS by sanitizing data
 * - Implements ARIA attributes for accessibility
 */
const EmbeddingVisualization: React.FC<EmbeddingVisualizationProps> = ({
  itemId,
  categoryId,
  limit = 100,
  className = '',
  compact = false,
  width: propWidth,
  height: propHeight,
  onDataLoaded,
  testId = 'embedding-visualization',
  timePeriod = 'current',
  interactive = false,
  onItemSelect,
  onCategorySelect,
}) => {
  // Media queries for responsive design
  const isMobile = useMediaQuery(breakpoints.mobile);
  const isTablet = useMediaQuery(breakpoints.tablet);

  // Determine dimensions based on screen size and props
  const width = propWidth || (isMobile ? defaultDimensions.mobile.width : 
                              isTablet ? defaultDimensions.tablet.width : 
                              defaultDimensions.desktop.width);
  const height = propHeight || (isMobile ? defaultDimensions.mobile.height : 
                               isTablet ? defaultDimensions.tablet.height : 
                               defaultDimensions.desktop.height);

  // Data fetching hook
  const { embeddings, loading, error, handleRetry } = useEmbeddingData({
    itemId,
    categoryId,
    limit,
    timePeriod,
    onDataLoaded,
  });

  // Interaction hook
  const {
    selectedItem,
    hoveredItem,
    selectedCategory,
    zoomLevel,
    panOffset,
    isDragging,
    filters,
    setFilters,
    handleZoomIn,
    handleZoomOut,
    resetView,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    handleMouseLeave,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handleItemClick,
    handleItemHover,
    handleCategorySelect,
    updateSelectedItem,
    updateSelectedCategory,
  } = useVisualizationInteraction({
    interactive,
    onItemSelect,
    onCategorySelect,
    itemId,
    categoryId,
  });

  // Update selected item when itemId prop changes
  useEffect(() => {
    updateSelectedItem(itemId);
  }, [itemId, updateSelectedItem]);

  // Update selected category when categoryId prop changes
  useEffect(() => {
    updateSelectedCategory(categoryId);
  }, [categoryId, updateSelectedCategory]);

  // Loading state
  if (loading) {
    return (
      <LoadingState
        className={className}
        testId={testId}
        height={height}
      />
    );
  }

  // Error state
  if (error) {
    return (
      <ErrorState
        className={className}
        testId={testId}
        compact={compact}
        error={error}
        onRetry={handleRetry}
      />
    );
  }

  // Empty state
  if (embeddings.length === 0) {
    return (
      <EmptyState
        className={className}
        testId={testId}
        compact={compact}
      />
    );
  }

  // Calculate visualization data
  const bounds = calculateBounds(embeddings);
  const { scaleX, scaleY } = createScalingFunctions(
    bounds,
    width,
    height,
    zoomLevel,
    panOffset,
    interactive
  );

  // Get unique categories from embeddings
  const categories = [...new Set(embeddings.map(point => point.category))];

  // Apply all filters to embeddings
  const filteredEmbeddings = filterEmbeddings(
    embeddings,
    selectedCategory,
    categoryId,
    filters,
    interactive
  );

  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={testId}
    >
      {/* Header with title and controls */}
      <div className="flex justify-between items-center mb-4">
        <h3
          className={`${compact ? 'text-base' : 'text-lg'} font-semibold`}
          id={`${testId}-title`}
        >
          Item Relationships
        </h3>

        <VisualizationControls
          testId={testId}
          interactive={interactive}
          zoomLevel={zoomLevel}
          onZoomIn={handleZoomIn}
          onZoomOut={handleZoomOut}
          onResetView={resetView}
        />
      </div>

      {/* Advanced filtering controls */}
      <AdvancedFilters
        interactive={interactive}
        categories={categories}
        filters={filters}
        onFiltersChange={setFilters}
      />

      {/* Category filter */}
      <CategoryFilter
        testId={testId}
        categories={categories}
        selectedCategory={selectedCategory}
        onCategorySelect={handleCategorySelect}
      />

      {/* Visualization canvas */}
      <VisualizationCanvas
        testId={testId}
        width={width}
        height={height}
        filteredEmbeddings={filteredEmbeddings}
        selectedItem={selectedItem}
        hoveredItem={hoveredItem}
        interactive={interactive}
        isDragging={isDragging}
        isMobile={isMobile}
        compact={compact}
        scaleX={scaleX}
        scaleY={scaleY}
        onItemClick={handleItemClick}
        onItemHover={handleItemHover}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      />

      {/* Legend */}
      <VisualizationLegend
        testId={testId}
        compact={compact}
        filteredEmbeddings={filteredEmbeddings}
      />

      {/* Explanation */}
      <VisualizationExplanation
        testId={testId}
        compact={compact}
        selectedItem={selectedItem}
        interactive={interactive}
      />
    </div>
  );
};

export default EmbeddingVisualization;
