// Empty state component for EmbeddingVisualization
import React from 'react';

interface EmptyStateProps {
  className?: string;
  testId: string;
  compact: boolean;
}

/**
 * Empty state component when no data is available
 */
const EmptyState: React.FC<EmptyStateProps> = ({
  className = '',
  testId,
  compact,
}) => {
  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={`${testId}-empty`}
    >
      <h3
        className={`${compact ? 'text-base' : 'text-lg'} font-semibold mb-4`}
        id={`${testId}-empty-title`}
      >
        Item Relationships
      </h3>
      <div
        className="text-center py-8 text-gray-700"
        aria-labelledby={`${testId}-empty-title`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-12 w-12 mx-auto text-gray-500 mb-3"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" 
          />
        </svg>
        <p className="text-lg font-medium mb-1">No data available</p>
        <p className="mt-2 text-sm">There are no items to display in this visualization.</p>
      </div>
    </div>
  );
};

export default EmptyState;
