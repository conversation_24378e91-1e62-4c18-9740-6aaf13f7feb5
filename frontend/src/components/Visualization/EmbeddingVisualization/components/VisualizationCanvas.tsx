// Visualization canvas component
import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { EmbeddingPoint } from '../utils/types';
import { categoryColors, animationSettings } from '../utils/constants';
import { findClosestPoints, calculatePointSize } from '../utils/calculations';
import VisualizationTooltip from './VisualizationTooltip';

interface VisualizationCanvasProps {
  testId: string;
  width: number;
  height: number;
  filteredEmbeddings: EmbeddingPoint[];
  selectedItem: string | null;
  hoveredItem: string | null;
  interactive: boolean;
  isDragging: boolean;
  isMobile: boolean;
  compact: boolean;
  scaleX: (x: number) => number;
  scaleY: (y: number) => number;
  onItemClick: (itemId: string) => void;
  onItemHover: (itemId: string | null) => void;
  onMouseDown?: (e: React.MouseEvent<HTMLDivElement>) => void;
  onMouseMove?: (e: React.MouseEvent<HTMLDivElement>) => void;
  onMouseUp?: () => void;
  onMouseLeave?: () => void;
  onTouchStart?: (e: React.TouchEvent<HTMLDivElement>) => void;
  onTouchMove?: (e: React.TouchEvent<HTMLDivElement>) => void;
  onTouchEnd?: () => void;
}

/**
 * Main visualization canvas component
 */
const VisualizationCanvas: React.FC<VisualizationCanvasProps> = ({
  testId,
  width,
  height,
  filteredEmbeddings,
  selectedItem,
  hoveredItem,
  interactive,
  isDragging,
  isMobile,
  compact,
  scaleX,
  scaleY,
  onItemClick,
  onItemHover,
  onMouseDown,
  onMouseMove,
  onMouseUp,
  onMouseLeave,
  onTouchStart,
  onTouchMove,
  onTouchEnd,
}) => {
  return (
    <div
      className={`relative overflow-hidden rounded-lg border border-gray-200 ${
        interactive ? 'cursor-grab' : ''
      } ${isDragging ? 'cursor-grabbing' : ''}`}
      style={{ width: '100%', maxWidth: `${width}px`, height: `${height}px` }}
      role="figure"
      aria-labelledby={`${testId}-title`}
      data-testid={`${testId}-container`}
      onMouseDown={interactive ? onMouseDown : undefined}
      onMouseMove={interactive ? onMouseMove : undefined}
      onMouseUp={interactive ? onMouseUp : undefined}
      onMouseLeave={interactive ? onMouseLeave : undefined}
      onTouchStart={interactive ? onTouchStart : undefined}
      onTouchMove={interactive ? onTouchMove : undefined}
      onTouchEnd={interactive ? onTouchEnd : undefined}
    >
      {/* Draw connecting lines between similar items */}
      {selectedItem && (
        <svg
          width={width}
          height={height}
          className="absolute top-0 left-0 z-0"
          aria-hidden="true"
          data-testid={`${testId}-connections`}
        >
          {filteredEmbeddings
            .filter(point => point.id === selectedItem)
            .map(selectedPoint => {
              const closestPoints = findClosestPoints(selectedPoint, filteredEmbeddings);
              
              return closestPoints.map(targetPoint => (
                <line
                  key={`${selectedPoint.id}-${targetPoint.id}`}
                  x1={scaleX(selectedPoint.x)}
                  y1={scaleY(selectedPoint.y)}
                  x2={scaleX(targetPoint.x)}
                  y2={scaleY(targetPoint.y)}
                  stroke={categoryColors[selectedPoint.category] || categoryColors.other}
                  strokeWidth="1.5"
                  strokeDasharray="3,3"
                  opacity="0.6"
                />
              ));
            })}
        </svg>
      )}

      {/* Render the points */}
      {filteredEmbeddings.map((point) => {
        const isSelected = selectedItem === point.id;
        const isHovered = hoveredItem === point.id;
        const pointSize = calculatePointSize(isSelected, isHovered);
        const color = categoryColors[point.category] || categoryColors.other;
        const pointId = `${testId}-point-${point.id}`;

        return (
          <motion.div
            key={point.id}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: animationSettings.pointScale.duration }}
            className="absolute rounded-full transition-all duration-200 cursor-pointer touch-friendly"
            style={{
              left: `${scaleX(point.x)}px`,
              top: `${scaleY(point.y)}px`,
              width: `${pointSize}px`,
              height: `${pointSize}px`,
              backgroundColor: color,
              transform: 'translate(-50%, -50%)',
              border: isSelected || isHovered ? '2px solid #000' : 'none',
              zIndex: isSelected || isHovered ? 10 : 1,
              opacity: isSelected || isHovered ? 1 : 0.7,
              boxShadow: isSelected ? '0 0 0 2px rgba(0,0,0,0.3)' : 'none',
            }}
            onClick={() => onItemClick(point.id)}
            onMouseEnter={() => onItemHover(point.id)}
            onMouseLeave={() => onItemHover(null)}
            onFocus={() => onItemHover(point.id)}
            onBlur={() => onItemHover(null)}
            role="button"
            tabIndex={0}
            aria-pressed={isSelected}
            aria-label={`${point.name}, ${point.category} category${
              point.price ? `, price $${point.price}` : ''
            }`}
            title={point.name}
            id={pointId}
            data-testid={`${testId}-point-${point.id}`}
            data-category={point.category}
          />
        );
      })}

      {/* Show tooltip for hovered item */}
      <AnimatePresence>
        {hoveredItem && (
          <VisualizationTooltip
            testId={testId}
            hoveredItem={hoveredItem}
            filteredEmbeddings={filteredEmbeddings}
            scaleX={scaleX}
            scaleY={scaleY}
            isMobile={isMobile}
            interactive={interactive}
          />
        )}
      </AnimatePresence>

      {/* Mobile instructions */}
      {isMobile && !compact && (
        <div
          className="absolute bottom-2 right-2 bg-white bg-opacity-90 rounded-full p-1.5 text-xs text-gray-700"
          aria-hidden="true"
        >
          Tap to select
        </div>
      )}
    </div>
  );
};

export default VisualizationCanvas;
