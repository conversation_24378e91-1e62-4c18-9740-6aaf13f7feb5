// Loading state component for EmbeddingVisualization
import React from 'react';

interface LoadingStateProps {
  className?: string;
  testId: string;
  height: number;
}

/**
 * Loading state component with skeleton UI
 */
const LoadingState: React.FC<LoadingStateProps> = ({
  className = '',
  testId,
  height,
}) => {
  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={`${testId}-loading`}
      aria-busy="true"
      aria-live="polite"
    >
      <div className="h-6 bg-gray-200 animate-pulse rounded w-1/3 mb-6"></div>
      <div
        className="flex justify-center items-center"
        style={{ height: height * 0.8 }}
        aria-label="Loading visualization data"
      >
        <div className="w-full h-full bg-gray-100 animate-pulse rounded-lg"></div>
      </div>
    </div>
  );
};

export default LoadingState;
