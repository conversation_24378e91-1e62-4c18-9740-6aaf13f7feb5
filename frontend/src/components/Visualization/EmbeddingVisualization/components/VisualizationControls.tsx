// Visualization controls component
import React from 'react';

interface VisualizationControlsProps {
  testId: string;
  interactive: boolean;
  zoomLevel: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onResetView: () => void;
}

/**
 * Controls for zoom and view reset functionality
 */
const VisualizationControls: React.FC<VisualizationControlsProps> = ({
  testId,
  interactive,
  zoomLevel,
  onZoomIn,
  onZoomOut,
  onResetView,
}) => {
  return (
    <div
      className="flex space-x-1"
      role="toolbar"
      aria-label="Visualization controls"
    >
      {interactive && (
        <button
          onClick={onResetView}
          className="p-1 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700 touch-friendly focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Reset view"
          data-testid={`${testId}-reset-view`}
          type="button"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-4 w-4" 
            viewBox="0 0 20 20" 
            fill="currentColor" 
            aria-hidden="true"
          >
            <path 
              fillRule="evenodd" 
              d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" 
              clipRule="evenodd" 
            />
          </svg>
        </button>
      )}
      
      <button
        onClick={onZoomOut}
        className="p-1 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700 touch-friendly focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-label="Zoom out"
        disabled={zoomLevel <= 0.5}
        data-testid={`${testId}-zoom-out`}
        type="button"
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-4 w-4" 
          viewBox="0 0 20 20" 
          fill="currentColor" 
          aria-hidden="true"
        >
          <path 
            fillRule="evenodd" 
            d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" 
            clipRule="evenodd" 
          />
        </svg>
      </button>
      
      <button
        onClick={onZoomIn}
        className="p-1 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700 touch-friendly focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-label="Zoom in"
        disabled={zoomLevel >= 2}
        data-testid={`${testId}-zoom-in`}
        type="button"
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          className="h-4 w-4" 
          viewBox="0 0 20 20" 
          fill="currentColor" 
          aria-hidden="true"
        >
          <path 
            fillRule="evenodd" 
            d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" 
            clipRule="evenodd" 
          />
        </svg>
      </button>
    </div>
  );
};

export default VisualizationControls;
