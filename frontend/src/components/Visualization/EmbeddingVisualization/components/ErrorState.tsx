// Error state component for EmbeddingVisualization
import React from 'react';

interface ErrorStateProps {
  className?: string;
  testId: string;
  compact: boolean;
  error: string;
  onRetry: () => void;
}

/**
 * Error state component with retry functionality
 */
const ErrorState: React.FC<ErrorStateProps> = ({
  className = '',
  testId,
  compact,
  error,
  onRetry,
}) => {
  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={`${testId}-error`}
      role="alert"
      aria-live="assertive"
    >
      <h3
        className={`${compact ? 'text-base' : 'text-lg'} font-semibold mb-4`}
        id={`${testId}-error-title`}
      >
        Item Relationships
      </h3>
      <div className="bg-red-50 text-red-600 p-4 rounded-md">
        <p className="mb-3">{error}</p>
        <button
          onClick={onRetry}
          className="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
          aria-label="Retry loading visualization"
          data-testid={`${testId}-retry-button`}
        >
          Retry
        </button>
      </div>
    </div>
  );
};

export default ErrorState;
