// Advanced filters component for interactive mode
import React from 'react';
import { VisualizationFilters } from '../utils/types';

interface AdvancedFiltersProps {
  interactive: boolean;
  categories: string[];
  filters: VisualizationFilters;
  onFiltersChange: (filters: VisualizationFilters) => void;
}

/**
 * Advanced filtering controls for interactive mode
 */
const AdvancedFilters: React.FC<AdvancedFiltersProps> = ({
  interactive,
  categories,
  filters,
  onFiltersChange,
}) => {
  if (!interactive) {
    return null;
  }

  const handleSearchChange = (searchTerm: string) => {
    onFiltersChange({ ...filters, searchTerm });
  };

  const handleCategoryToggle = (category: string, checked: boolean) => {
    const newCategories = checked
      ? [...filters.categories, category]
      : filters.categories.filter(c => c !== category);
    
    onFiltersChange({ ...filters, categories: newCategories });
  };

  return (
    <div className="mb-4 p-3 bg-gray-50 rounded-lg">
      <div className="text-sm font-medium mb-2">Filter Items</div>

      {/* Search input */}
      <div className="mb-3">
        <input
          type="text"
          placeholder="Search by name..."
          className="w-full p-2 border border-gray-300 rounded-md text-sm"
          value={filters.searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          aria-label="Search items by name"
        />
      </div>

      {/* Category checkboxes */}
      <div className="mb-3">
        <div className="text-xs font-medium mb-1">Categories</div>
        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <label key={category} className="flex items-center text-xs">
              <input
                type="checkbox"
                checked={filters.categories.includes(category)}
                onChange={(e) => handleCategoryToggle(category, e.target.checked)}
                className="mr-1"
              />
              {category}
            </label>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdvancedFilters;
