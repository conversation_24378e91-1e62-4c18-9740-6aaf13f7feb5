// Explanation component for the visualization
import React from 'react';

interface VisualizationExplanationProps {
  testId: string;
  compact: boolean;
  selectedItem: string | null;
  interactive: boolean;
}

/**
 * Explanation component that describes how the visualization works
 */
const VisualizationExplanation: React.FC<VisualizationExplanationProps> = ({
  testId,
  compact,
  selectedItem,
  interactive,
}) => {
  if (compact) {
    return null;
  }

  return (
    <div
      className="mt-6 pt-4 border-t border-gray-100"
      aria-label="Visualization explanation"
    >
      <h4
        className="text-sm font-medium mb-2"
        id={`${testId}-explanation-title`}
      >
        What This Means
      </h4>
      
      <p
        className="text-xs text-gray-700"
        aria-labelledby={`${testId}-explanation-title`}
      >
        This visualization shows how items are related to each other based on their features.
        Items that are closer together are more similar. The colors represent different categories.
        {selectedItem && " Lines connect the selected item to similar items."}
        {interactive && " You can zoom, pan, and filter the visualization to explore the relationships between items."}
      </p>

      {interactive && (
        <div className="mt-3 text-xs text-gray-700">
          <strong>Interactive Features:</strong>
          <ul className="list-disc pl-4 mt-1 space-y-1">
            <li>Click and drag to pan the visualization</li>
            <li>Use the zoom controls to zoom in and out</li>
            <li>Click the reset button to return to the default view</li>
            <li>Use the filters to show specific categories or search for items</li>
            <li>Click on a point to select it and see connections to similar items</li>
            <li>Hover over points to see detailed information</li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default VisualizationExplanation;
