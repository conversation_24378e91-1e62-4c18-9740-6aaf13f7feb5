// Category filter component
import React from 'react';
import { motion } from 'framer-motion';
import { categoryColors, animationSettings } from '../utils/constants';

interface CategoryFilterProps {
  testId: string;
  categories: string[];
  selectedCategory: string | null;
  onCategorySelect: (category: string | null) => void;
}

/**
 * Category filter buttons component
 */
const CategoryFilter: React.FC<CategoryFilterProps> = ({
  testId,
  categories,
  selectedCategory,
  onCategorySelect,
}) => {
  if (categories.length <= 1) {
    return null;
  }

  return (
    <div
      className="mb-4 overflow-x-auto pb-2 -mx-1 px-1"
      role="radiogroup"
      aria-label="Category filter"
    >
      <div className="flex space-x-2 min-w-max">
        <motion.button
          whileTap={{ scale: animationSettings.categoryButton.scale }}
          className={`px-3 py-1 text-xs rounded-full touch-friendly focus:outline-none focus:ring-2 focus:ring-blue-500 ${
            selectedCategory === null
              ? 'bg-gray-800 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          onClick={() => onCategorySelect(null)}
          role="radio"
          aria-checked={selectedCategory === null}
          data-testid={`${testId}-category-all`}
        >
          All Categories
        </motion.button>
        
        {categories.map(category => (
          <motion.button
            key={category}
            whileTap={{ scale: animationSettings.categoryButton.scale }}
            className={`px-3 py-1 text-xs rounded-full touch-friendly focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              selectedCategory === category
                ? 'text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
            style={{
              backgroundColor: selectedCategory === category 
                ? categoryColors[category] || categoryColors.other 
                : undefined,
            }}
            onClick={() => onCategorySelect(category)}
            role="radio"
            aria-checked={selectedCategory === category}
            data-testid={`${testId}-category-${category}`}
          >
            {category}
          </motion.button>
        ))}
      </div>
    </div>
  );
};

export default CategoryFilter;
