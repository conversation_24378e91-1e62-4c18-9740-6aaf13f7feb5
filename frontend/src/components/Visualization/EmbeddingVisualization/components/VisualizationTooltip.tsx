// Tooltip component for visualization points
import React from 'react';
import { motion } from 'framer-motion';
import { EmbeddingPoint } from '../utils/types';
import { animationSettings } from '../utils/constants';

interface VisualizationTooltipProps {
  testId: string;
  hoveredItem: string;
  filteredEmbeddings: EmbeddingPoint[];
  scaleX: (x: number) => number;
  scaleY: (y: number) => number;
  isMobile: boolean;
  interactive: boolean;
}

/**
 * Tooltip component that shows detailed information about hovered items
 */
const VisualizationTooltip: React.FC<VisualizationTooltipProps> = ({
  testId,
  hoveredItem,
  filteredEmbeddings,
  scaleX,
  scaleY,
  isMobile,
  interactive,
}) => {
  const hoveredPoint = filteredEmbeddings.find(e => e.id === hoveredItem);
  
  if (!hoveredPoint) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      transition={{ duration: animationSettings.tooltip.duration }}
      className="absolute bg-white p-3 rounded shadow-md text-xs z-20 border border-gray-200"
      style={{
        left: `${scaleX(hoveredPoint.x) + 10}px`,
        top: `${scaleY(hoveredPoint.y) - 10}px`,
        maxWidth: isMobile ? '180px' : '250px',
      }}
      role="tooltip"
      id={`tooltip-${hoveredItem}`}
      aria-hidden="true" // Screen readers will get info from the aria-label on the point
      data-testid={`${testId}-tooltip`}
    >
      <div className="font-medium text-sm mb-1">{hoveredPoint.name}</div>

      <div className="grid grid-cols-2 gap-x-2 gap-y-1 mt-2">
        <div className="text-xs text-gray-600">Category:</div>
        <div className="text-xs text-gray-800 font-medium capitalize">
          {hoveredPoint.category}
        </div>

        {hoveredPoint.price && (
          <>
            <div className="text-xs text-gray-600">Price:</div>
            <div className="text-xs text-gray-800 font-medium">
              ${hoveredPoint.price.toLocaleString()}
            </div>
          </>
        )}

        {hoveredPoint.rating && (
          <>
            <div className="text-xs text-gray-600">Rating:</div>
            <div className="text-xs text-gray-800 font-medium">
              {hoveredPoint.rating} ★
            </div>
          </>
        )}

        {hoveredPoint.location && (
          <>
            <div className="text-xs text-gray-600">Location:</div>
            <div className="text-xs text-gray-800 font-medium">
              {hoveredPoint.location}
            </div>
          </>
        )}
      </div>

      {interactive && (
        <div className="mt-2 pt-2 border-t border-gray-100 text-xs text-blue-600">
          Click to select and see similar items
        </div>
      )}
    </motion.div>
  );
};

export default VisualizationTooltip;
