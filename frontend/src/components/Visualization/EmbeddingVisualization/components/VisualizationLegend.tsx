// Legend component for the visualization
import React from 'react';
import { EmbeddingPoint } from '../utils/types';
import { categoryColors } from '../utils/constants';

interface VisualizationLegendProps {
  testId: string;
  compact: boolean;
  filteredEmbeddings: EmbeddingPoint[];
}

/**
 * Legend component showing category colors
 */
const VisualizationLegend: React.FC<VisualizationLegendProps> = ({
  testId,
  compact,
  filteredEmbeddings,
}) => {
  if (compact) {
    return null;
  }

  return (
    <div
      className="mt-4 flex flex-wrap gap-2 md:gap-4"
      aria-label="Category legend"
      role="legend"
      data-testid={`${testId}-legend`}
    >
      {Object.entries(categoryColors)
        .filter(([category]) => 
          filteredEmbeddings.some(e => e.category === category) || category === 'other'
        )
        .map(([category, color]) => (
          <div
            key={category}
            className="flex items-center"
            aria-label={`${category} category`}
          >
            <div
              className="w-3 h-3 rounded-full mr-1"
              style={{ backgroundColor: color }}
              aria-hidden="true"
            />
            <span className="text-xs capitalize text-gray-700">{category}</span>
          </div>
        ))}
    </div>
  );
};

export default VisualizationLegend;
