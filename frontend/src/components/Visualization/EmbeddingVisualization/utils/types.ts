// Types for the EmbeddingVisualization component

export interface EmbeddingPoint {
  id: string;
  name: string;
  x: number;
  y: number;
  category: string;
  price?: number;
  rating?: number;
  location?: string;
}

export interface EmbeddingVisualizationProps {
  itemId?: string; // Optional - if provided, highlights this item and its neighbors
  categoryId?: string; // Optional - if provided, filters by category
  limit?: number; // Optional - number of items to show
  className?: string;
  compact?: boolean; // For smaller UI in constrained spaces
  width?: number; // Optional custom width
  height?: number; // Optional custom height
  onDataLoaded?: (data: EmbeddingPoint[]) => void; // Callback when data is loaded
  testId?: string; // For testing purposes
  timePeriod?: 'current' | '1month' | '3months' | '6months'; // Optional - for historical data
  interactive?: boolean; // Whether to enable interactive features (zoom, pan, filtering)
  onItemSelect?: (itemId: string) => void; // Optional - callback when an item is selected
  onCategorySelect?: (category: string | null) => void; // Optional - callback when a category is selected
}

export interface VisualizationFilters {
  categories: string[];
  priceRange: [number, number] | null;
  searchTerm: string;
}

export interface PanOffset {
  x: number;
  y: number;
}

export interface DragState {
  x: number;
  y: number;
}

export interface VisualizationBounds {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
  centerX: number;
  centerY: number;
}
