// Constants for the EmbeddingVisualization component

// Colors for different categories with high contrast for accessibility
export const categoryColors: Record<string, string> = {
  electronics: '#4338CA', // Darker Indigo for better contrast
  furniture: '#6D28D9', // Darker Purple
  clothing: '#1D4ED8', // Darker Blue
  tools: '#0284C7', // Darker Light Blue
  sports: '#0891B2', // Darker Cyan
  vehicles: '#0F766E', // Darker Teal
  apartment: '#059669', // Darker Emerald
  house: '#D97706', // Darker Amber
  condo: '#DB2777', // Darker Pink
  other: '#6B7280', // Darker Gray
};

// Default dimensions for different screen sizes
export const defaultDimensions = {
  mobile: { width: 320, height: 300 },
  tablet: { width: 500, height: 350 },
  desktop: { width: 600, height: 400 },
};

// Visualization settings
export const visualizationSettings = {
  boundsPadding: 20,
  pointPadding: 30,
  maxZoom: 2,
  minZoom: 0.5,
  zoomStep: 0.2,
  maxRetries: 3,
  requestTimeout: 15000,
  maxSimilarConnections: 3,
};

// Animation settings
export const animationSettings = {
  pointScale: { duration: 0.3 },
  tooltip: { duration: 0.2 },
  categoryButton: { scale: 0.95 },
};

// Responsive breakpoints
export const breakpoints = {
  mobile: '(max-width: 640px)',
  tablet: '(min-width: 641px) and (max-width: 1024px)',
  desktop: '(min-width: 1025px)',
};
