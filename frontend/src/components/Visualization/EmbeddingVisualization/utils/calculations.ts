// Calculation utilities for the EmbeddingVisualization component
import { EmbeddingPoint, VisualizationBounds, PanOffset } from './types';
import { visualizationSettings } from './constants';

/**
 * Calculate the bounds of the visualization
 */
export const calculateBounds = (embeddings: EmbeddingPoint[]): VisualizationBounds => {
  if (embeddings.length === 0) {
    return {
      minX: 0,
      maxX: 100,
      minY: 0,
      maxY: 100,
      centerX: 50,
      centerY: 50,
    };
  }

  const { boundsPadding } = visualizationSettings;
  const minX = Math.min(...embeddings.map(e => e.x)) - boundsPadding;
  const maxX = Math.max(...embeddings.map(e => e.x)) + boundsPadding;
  const minY = Math.min(...embeddings.map(e => e.y)) - boundsPadding;
  const maxY = Math.max(...embeddings.map(e => e.y)) + boundsPadding;

  return {
    minX,
    maxX,
    minY,
    maxY,
    centerX: (minX + maxX) / 2,
    centerY: (minY + maxY) / 2,
  };
};

/**
 * Scale point coordinates to fit the visualization area with zoom and pan
 */
export const createScalingFunctions = (
  bounds: VisualizationBounds,
  width: number,
  height: number,
  zoomLevel: number,
  panOffset: PanOffset,
  interactive: boolean
) => {
  const { pointPadding } = visualizationSettings;
  const { minX, maxX, minY, maxY, centerX, centerY } = bounds;

  const scaleX = (x: number): number => {
    const zoomedX = centerX + (x - centerX) / zoomLevel;
    // Apply pan offset only if interactive mode is enabled
    const pannedX = interactive ? zoomedX + (panOffset.x / (width - 2 * pointPadding)) * (maxX - minX) : zoomedX;
    return pointPadding + ((pannedX - minX) / (maxX - minX)) * (width - 2 * pointPadding);
  };

  const scaleY = (y: number): number => {
    const zoomedY = centerY + (y - centerY) / zoomLevel;
    // Apply pan offset only if interactive mode is enabled
    const pannedY = interactive ? zoomedY + (panOffset.y / (height - 2 * pointPadding)) * (maxY - minY) : zoomedY;
    return pointPadding + ((pannedY - minY) / (maxY - minY)) * (height - 2 * pointPadding);
  };

  return { scaleX, scaleY };
};

/**
 * Find the closest points to a given point
 */
export const findClosestPoints = (
  targetPoint: EmbeddingPoint,
  allPoints: EmbeddingPoint[],
  count: number = visualizationSettings.maxSimilarConnections
): EmbeddingPoint[] => {
  const distances = allPoints
    .filter(p => p.id !== targetPoint.id)
    .map(p => ({
      point: p,
      distance: Math.sqrt(Math.pow(p.x - targetPoint.x, 2) + Math.pow(p.y - targetPoint.y, 2))
    }))
    .sort((a, b) => a.distance - b.distance)
    .slice(0, count);

  return distances.map(d => d.point);
};

/**
 * Calculate point size based on selection and hover state
 */
export const calculatePointSize = (isSelected: boolean, isHovered: boolean): number => {
  if (isSelected) return 10;
  if (isHovered) return 8;
  return 6;
};

/**
 * Validate embedding point data
 */
export const validateEmbeddingPoint = (point: any): point is EmbeddingPoint => {
  return (
    typeof point.id === 'string' &&
    typeof point.name === 'string' &&
    typeof point.x === 'number' &&
    typeof point.y === 'number' &&
    typeof point.category === 'string'
  );
};

/**
 * Filter embeddings based on various criteria
 */
export const filterEmbeddings = (
  embeddings: EmbeddingPoint[],
  selectedCategory: string | null,
  categoryId: string | null,
  filters: {
    categories: string[];
    priceRange: [number, number] | null;
    searchTerm: string;
  },
  interactive: boolean
): EmbeddingPoint[] => {
  return embeddings.filter(point => {
    // Apply category filter from UI
    if (selectedCategory && !categoryId && point.category !== selectedCategory) {
      return false;
    }

    // Apply advanced filters if in interactive mode
    if (interactive) {
      // Filter by search term
      if (filters.searchTerm && !point.name.toLowerCase().includes(filters.searchTerm.toLowerCase())) {
        return false;
      }

      // Filter by selected categories
      if (filters.categories.length > 0 && !filters.categories.includes(point.category)) {
        return false;
      }

      // Filter by price range if available
      if (filters.priceRange && point.price) {
        const [min, max] = filters.priceRange;
        if (point.price < min || point.price > max) {
          return false;
        }
      }
    }

    return true;
  });
};
