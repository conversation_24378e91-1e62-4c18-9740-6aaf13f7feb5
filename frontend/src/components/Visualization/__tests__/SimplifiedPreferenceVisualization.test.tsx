import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SimplifiedTestProviders from '../../../testing/SimplifiedTestProviders';

// Mock data for testing
const mockPreferenceData = [
  { preferenceType: 'liked', weight: 0.8, count: 12 },
  { preferenceType: 'viewed', weight: 0.5, count: 30 },
  { preferenceType: 'favorited', weight: 0.9, count: 8 },
];

// Create a simplified version of the PreferenceVisualization component for testing
const MockPreferenceVisualization = ({
  testId = 'pref-viz',
  state = 'loading',
  data = [],
  error = null,
  compact = false,
  onDataLoaded = null
}) => {
  // Call the onDataLoaded callback if provided
  React.useEffect(() => {
    if (state === 'data' && onDataLoaded && data.length > 0) {
      onDataLoaded(data);
    }
  }, [state, data, onDataLoaded]);

  if (state === 'loading') {
    return (
      <div data-testid={`${testId}-loading`} role="status" aria-busy="true" aria-live="polite">
        <div className="sr-only">Loading preference data...</div>
        <div className="h-6 bg-gray-200 animate-pulse rounded w-1/3 mb-6"></div>
      </div>
    );
  }

  if (state === 'error') {
    return (
      <div data-testid={`${testId}-error`} role="alert" aria-live="assertive">
        <h3 className="text-lg font-semibold mb-4">Your Preferences</h3>
        <div className="bg-red-50 text-red-700 p-4 rounded-md">
          <p className="mb-3">{error || 'Error message'}</p>
          <button data-testid={`${testId}-retry-button`}>Retry</button>
        </div>
      </div>
    );
  }

  if (state === 'empty' || data.length === 0) {
    return (
      <div data-testid={`${testId}-empty`}>
        <h3 className="text-lg font-semibold mb-4">Your Preferences</h3>
        <div className="text-center py-8 text-gray-700">
          <p className="text-lg font-medium mb-1">No preference data yet</p>
          <p className="mt-2 text-sm">As you interact with items, your preferences will be tracked here.</p>
        </div>
      </div>
    );
  }

  return (
    <div data-testid={testId}>
      <h3 className="text-lg font-semibold mb-4">Your Preferences</h3>
      <div className="space-y-4">
        {data.map((pref) => (
          <div key={pref.preferenceType} data-testid={`${testId}-item-${pref.preferenceType}`}>
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium capitalize">{pref.preferenceType}</span>
              <span className="text-xs text-gray-700">{pref.count} items</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden">
              <div
                className="h-2.5 rounded-full"
                style={{
                  width: `${(pref.count / Math.max(...data.map(p => p.count))) * 100}%`,
                  backgroundColor: '#4338CA'
                }}
              ></div>
            </div>
            <div className="flex justify-between items-center text-xs text-gray-700">
              <span>Weight: {pref.weight.toFixed(1)}</span>
              <span>{Math.round((pref.weight / Math.max(...data.map(p => p.weight))) * 100)}% influence</span>
            </div>
          </div>
        ))}
      </div>

      {!compact && (
        <div className="mt-6 pt-4 border-t border-gray-100">
          <h4 className="text-sm font-medium mb-2">What This Means</h4>
          <p className="text-xs text-gray-700">
            This visualization shows how your preferences are tracked based on your interactions.
          </p>
        </div>
      )}
    </div>
  );
};

describe('PreferenceVisualization Component', () => {
  it('renders loading state initially', () => {
    render(
      <SimplifiedTestProviders>
        <MockPreferenceVisualization state="loading" />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('pref-viz-loading')).toBeInTheDocument();
    expect(screen.getByText('Loading preference data...')).toBeInTheDocument();
  });

  it('renders error state when API call fails', () => {
    render(
      <SimplifiedTestProviders>
        <MockPreferenceVisualization state="error" error="Error message" />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('pref-viz-error')).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByTestId('pref-viz-retry-button')).toBeInTheDocument();
  });

  it('renders empty state when no preference data', () => {
    render(
      <SimplifiedTestProviders>
        <MockPreferenceVisualization state="empty" />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('pref-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No preference data yet')).toBeInTheDocument();
  });

  it('renders preference data correctly', () => {
    render(
      <SimplifiedTestProviders>
        <MockPreferenceVisualization state="data" data={mockPreferenceData} />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('pref-viz')).toBeInTheDocument();

    // Check preference types are displayed
    expect(screen.getByText('liked')).toBeInTheDocument();
    expect(screen.getByText('viewed')).toBeInTheDocument();
    expect(screen.getByText('favorited')).toBeInTheDocument();

    // Check counts are displayed
    expect(screen.getByText('12 items')).toBeInTheDocument();
    expect(screen.getByText('30 items')).toBeInTheDocument();
    expect(screen.getByText('8 items')).toBeInTheDocument();

    // Check explanation text is present
    expect(screen.getByText('What This Means')).toBeInTheDocument();
  });

  it('calls onDataLoaded callback when data is loaded', () => {
    const onDataLoaded = jest.fn();

    render(
      <SimplifiedTestProviders>
        <MockPreferenceVisualization
          state="data"
          data={mockPreferenceData}
          onDataLoaded={onDataLoaded}
        />
      </SimplifiedTestProviders>
    );

    expect(onDataLoaded).toHaveBeenCalledWith(mockPreferenceData);
  });

  it('renders compact version when compact prop is true', () => {
    render(
      <SimplifiedTestProviders>
        <MockPreferenceVisualization
          state="data"
          data={mockPreferenceData}
          compact={true}
        />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('pref-viz')).toBeInTheDocument();

    // The explanation section should not be present in compact mode
    expect(screen.queryByText('What This Means')).not.toBeInTheDocument();
  });
});
