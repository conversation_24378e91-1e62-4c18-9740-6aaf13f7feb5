import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import SimpleVisualization from '../SimpleVisualization';

// Mock data for testing
const mockData = [
  { id: 'item1', name: 'Item 1', category: 'electronics' },
  { id: 'item2', name: 'Item 2', category: 'furniture' },
  { id: 'item3', name: 'Item 3', category: 'clothing' },
];

describe('SimpleVisualization Component', () => {
  it('renders loading state when state is loading', () => {
    render(<SimpleVisualization testId="simple-viz" state="loading" />);
    expect(screen.getByTestId('simple-viz-loading')).toBeInTheDocument();
    expect(screen.getByText('Loading visualization data...')).toBeInTheDocument();
  });

  it('renders error state when state is error', () => {
    render(<SimpleVisualization testId="simple-viz" state="error" />);
    expect(screen.getByTestId('simple-viz-error')).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByTestId('simple-viz-retry-button')).toBeInTheDocument();
  });

  it('renders empty state when state is empty', () => {
    render(<SimpleVisualization testId="simple-viz" state="empty" />);
    expect(screen.getByTestId('simple-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders empty state when data is empty array', () => {
    render(<SimpleVisualization testId="simple-viz" state="data" data={[]} />);
    expect(screen.getByTestId('simple-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders data correctly when state is data and data is provided', () => {
    render(
      <SimpleVisualization
        testId="simple-viz"
        state="data"
        data={mockData}
      />
    );

    // Check that the component renders
    expect(screen.getByTestId('simple-viz')).toBeInTheDocument();

    // Check category filter buttons
    expect(screen.getByTestId('simple-viz-category-all')).toBeInTheDocument();
    expect(screen.getByTestId('simple-viz-category-electronics')).toBeInTheDocument();
    expect(screen.getByTestId('simple-viz-category-furniture')).toBeInTheDocument();
    expect(screen.getByTestId('simple-viz-category-clothing')).toBeInTheDocument();

    // Check points are rendered
    expect(screen.getByTestId('simple-viz-point-item1')).toBeInTheDocument();
    expect(screen.getByTestId('simple-viz-point-item2')).toBeInTheDocument();
    expect(screen.getByTestId('simple-viz-point-item3')).toBeInTheDocument();

    // Check point names are displayed
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
  });

  it('calls onRetry when retry button is clicked', () => {
    const onRetry = jest.fn();
    render(
      <SimpleVisualization
        testId="simple-viz"
        state="error"
        onRetry={onRetry}
      />
    );

    // Click the retry button
    fireEvent.click(screen.getByTestId('simple-viz-retry-button'));

    // Check that onRetry was called
    expect(onRetry).toHaveBeenCalled();
  });

  it('does not render legend when compact prop is true', () => {
    render(
      <SimpleVisualization
        testId="simple-viz"
        state="data"
        data={mockData}
        compact={true}
      />
    );

    // Check that the legend is not rendered
    expect(screen.queryByTestId('simple-viz-legend')).not.toBeInTheDocument();
  });

  it('renders legend when compact prop is false', () => {
    render(
      <SimpleVisualization
        testId="simple-viz"
        state="data"
        data={mockData}
        compact={false}
      />
    );

    // Check that the legend is rendered
    expect(screen.getByTestId('simple-viz-legend')).toBeInTheDocument();
  });
});
