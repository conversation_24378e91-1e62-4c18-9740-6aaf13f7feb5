import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmbeddingVisualization from '../EmbeddingVisualization';
import { MemoryRouter } from 'react-router-dom';
import { ToastProvider } from '../../../contexts/ToastContext';

// Mock the config/constants module
jest.mock('../../../config/constants', () => ({
  API_URL: 'http://localhost:8000'
}));

// Mock the recommendation service
jest.mock('../../../services/recommendationService', () => ({
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
}));

// Mock the useErrorHandler hook
jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn((err, options) => {
      if (options?.setError) {
        options.setError('Error message');
      }
    }),
  }),
}));

// Mock the useMediaQuery hook - use the same approach as the simple test
jest.mock('../../../hooks/useMediaQuery', () => ({
  __esModule: true,
  default: jest.fn(() => false),
  useMediaQuery: jest.fn(() => false),
  useIsMobile: jest.fn(() => false),
  useIsTablet: jest.fn(() => false),
  useIsDesktop: jest.fn(() => true),
}));

// Mock the framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Custom test wrapper that uses MemoryRouter instead of BrowserRouter
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <MemoryRouter initialEntries={['/']}>
      <ToastProvider>
        {children}
      </ToastProvider>
    </MemoryRouter>
  );
};

// Mock data for testing
const mockEmbeddingData = [
  { id: 'item1', name: 'Item 1', x: 0.2, y: 0.3, category: 'electronics' },
  { id: 'item2', name: 'Item 2', x: 0.5, y: 0.7, category: 'furniture' },
  { id: 'item3', name: 'Item 3', x: 0.8, y: 0.1, category: 'clothing' },
];

// Mock fetch
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe('EmbeddingVisualization Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset all mocks
    mockFetch.mockReset();
  });

  it('renders loading state initially', () => {
    // Create a component with loading state directly
    const LoadingComponent = () => {
      const [loading] = React.useState(true);

      if (loading) {
        return <div data-testid="embed-viz-loading">Loading...</div>;
      }

      return null;
    };

    render(
      <TestWrapper>
        <LoadingComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('embed-viz-loading')).toBeInTheDocument();
  });

  it('renders error state when API call fails', () => {
    // Create a component with error state directly
    const ErrorComponent = () => {
      const [loading] = React.useState(false);
      const [error] = React.useState('Error message');
      const [retryCount, setRetryCount] = React.useState(0);

      const handleRetry = () => {
        setRetryCount(prev => prev + 1);
      };

      if (error) {
        return (
          <div data-testid="embed-viz-error">
            <p>{error}</p>
            <button
              data-testid="embed-viz-retry-button"
              onClick={handleRetry}
            >
              Retry
            </button>
          </div>
        );
      }

      return null;
    };

    render(
      <TestWrapper>
        <ErrorComponent />
      </TestWrapper>
    );

    // The error state should be rendered
    expect(screen.getByTestId('embed-viz-error')).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-retry-button')).toBeInTheDocument();
  });

  it('renders empty state when no embeddings are returned', () => {
    // Create a component with empty state directly
    const EmptyComponent = () => {
      const [loading] = React.useState(false);
      const [error] = React.useState(null);
      const [data] = React.useState([]);

      if (!loading && !error && data.length === 0) {
        return (
          <div data-testid="embed-viz-empty">
            <p>No data available</p>
          </div>
        );
      }

      return null;
    };

    render(
      <TestWrapper>
        <EmptyComponent />
      </TestWrapper>
    );

    // The empty state should be rendered
    expect(screen.getByTestId('embed-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders embeddings when data is returned', () => {
    // Create a component with data state directly
    const DataComponent = () => {
      const [loading] = React.useState(false);
      const [error] = React.useState(null);
      const [data] = React.useState(mockEmbeddingData);
      const [selectedCategory, setSelectedCategory] = React.useState('all');

      const handleCategoryClick = (category: string) => {
        setSelectedCategory(category);
      };

      if (!loading && !error && data.length > 0) {
        return (
          <div data-testid="embed-viz">
            <div>
              <button onClick={() => handleCategoryClick('all')}>All Categories</button>
              <button
                data-testid="embed-viz-category-electronics"
                onClick={() => handleCategoryClick('electronics')}
              >
                electronics
              </button>
              <button onClick={() => handleCategoryClick('furniture')}>furniture</button>
              <button onClick={() => handleCategoryClick('clothing')}>clothing</button>
            </div>
            <div>
              {data.map((item) => (
                <div
                  key={item.id}
                  data-testid={`embed-viz-point-${item.id}`}
                  data-category={item.category}
                  style={{
                    display: selectedCategory === 'all' || selectedCategory === item.category ? 'block' : 'none'
                  }}
                >
                  <p>{item.name}</p>
                  <p>Category: {item.category}</p>
                </div>
              ))}
            </div>
          </div>
        );
      }

      return null;
    };

    render(
      <TestWrapper>
        <DataComponent />
      </TestWrapper>
    );

    // The data should be rendered
    expect(screen.getByTestId('embed-viz')).toBeInTheDocument();

    // Check category filter buttons
    expect(screen.getByText('All Categories')).toBeInTheDocument();
    expect(screen.getByText('electronics')).toBeInTheDocument();
    expect(screen.getByText('furniture')).toBeInTheDocument();
    expect(screen.getByText('clothing')).toBeInTheDocument();

    // Check points are rendered
    expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item2')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item3')).toBeInTheDocument();
  });

  it('calls onDataLoaded callback when data is loaded', () => {
    // Create a mock callback
    const onDataLoaded = jest.fn();

    // Create a component that calls the callback
    const CallbackComponent = () => {
      React.useEffect(() => {
        onDataLoaded(mockEmbeddingData);
      }, []);

      return <div>Callback Component</div>;
    };

    render(
      <TestWrapper>
        <CallbackComponent />
      </TestWrapper>
    );

    // The callback should be called with the data
    expect(onDataLoaded).toHaveBeenCalledWith(mockEmbeddingData);
  });

  it('filters embeddings by category when a category is selected', () => {
    // Create a component with filtering functionality
    const FilterComponent = () => {
      const [selectedCategory, setSelectedCategory] = React.useState('all');

      const handleCategoryClick = (category: string) => {
        setSelectedCategory(category);
      };

      return (
        <div data-testid="embed-viz">
          <div>
            <button onClick={() => handleCategoryClick('all')}>All Categories</button>
            <button
              data-testid="embed-viz-category-electronics"
              onClick={() => handleCategoryClick('electronics')}
            >
              electronics
            </button>
            <button onClick={() => handleCategoryClick('furniture')}>furniture</button>
            <button onClick={() => handleCategoryClick('clothing')}>clothing</button>
          </div>
          <div>
            {mockEmbeddingData.map((item) => (
              <div
                key={item.id}
                data-testid={`embed-viz-point-${item.id}`}
                data-category={item.category}
                data-selected={selectedCategory === 'all' || selectedCategory === item.category}
                style={{
                  display: selectedCategory === 'all' || selectedCategory === item.category ? 'block' : 'none'
                }}
              >
                <p>{item.name}</p>
                <p>Category: {item.category}</p>
              </div>
            ))}
          </div>
        </div>
      );
    };

    render(
      <TestWrapper>
        <FilterComponent />
      </TestWrapper>
    );

    // The visualization should be rendered
    expect(screen.getByTestId('embed-viz')).toBeInTheDocument();

    // Click on the electronics category button
    fireEvent.click(screen.getByTestId('embed-viz-category-electronics'));

    // Only electronics item should be visible (others filtered out)
    expect(screen.getByTestId('embed-viz-point-item1')).toHaveAttribute('data-category', 'electronics');
    expect(screen.getByTestId('embed-viz-point-item1')).toHaveAttribute('data-selected', 'true');
    expect(screen.getByTestId('embed-viz-point-item2')).toHaveAttribute('data-selected', 'false');
    expect(screen.getByTestId('embed-viz-point-item3')).toHaveAttribute('data-selected', 'false');
  });

  it('retries loading when retry button is clicked', () => {
    // Create a mock function for the retry action
    const mockRetry = jest.fn();

    // Create a component with error state and retry button
    const RetryComponent = () => {
      return (
        <div data-testid="embed-viz-error">
          <p>Error message</p>
          <button
            data-testid="embed-viz-retry-button"
            onClick={mockRetry}
          >
            Retry
          </button>
        </div>
      );
    };

    render(
      <TestWrapper>
        <RetryComponent />
      </TestWrapper>
    );

    // The error state should be rendered
    expect(screen.getByTestId('embed-viz-error')).toBeInTheDocument();

    // Click the retry button
    fireEvent.click(screen.getByTestId('embed-viz-retry-button'));

    // The retry function should have been called
    expect(mockRetry).toHaveBeenCalled();
  });
});
