import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import VisualizationDemo from '../VisualizationDemo';
import recommendationService from '../../../services/recommendationService';

// Mock the useAuth hook
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'user-123' },
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn(),
    register: jest.fn(),
  }),
}));

// Mock the useErrorHandler hook
jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn((err, options) => {
      if (options?.setError) {
        options.setError('Error message');
      }
    }),
  }),
}));

// Mock the useMediaQuery hook
jest.mock('../../../hooks/useMediaQuery', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => false),
    useMediaQuery: jest.fn().mockImplementation(() => false),
    useIsMobile: jest.fn().mockImplementation(() => false),
    useIsTablet: jest.fn().mockImplementation(() => false),
    useIsDesktop: jest.fn().mockImplementation(() => true),
  };
});

// Mock the framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock the recommendation service
jest.mock('../../../services/recommendationService', () => ({
  getPreferenceVisualization: jest.fn().mockResolvedValue([]),
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
}));

// Mock fetch for EmbeddingVisualization
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock data
const mockPreferenceData = [
  { preferenceType: 'liked', weight: 0.8, count: 12 },
  { preferenceType: 'viewed', weight: 0.5, count: 30 },
  { preferenceType: 'favorited', weight: 0.9, count: 8 },
];

const mockEmbeddingData = [
  { id: 'item1', name: 'Item 1', x: 0.2, y: 0.3, category: 'electronics' },
  { id: 'item2', name: 'Item 2', x: 0.5, y: 0.7, category: 'furniture' },
  { id: 'item3', name: 'Item 3', x: 0.8, y: 0.1, category: 'clothing' },
];

describe('VisualizationDemo Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the preference visualization data
    recommendationService.getPreferenceVisualization.mockResolvedValue(mockPreferenceData);

    // Mock the embedding visualization data
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockEmbeddingData),
      })
    );
  });

  it('renders the component with preference visualization by default', async () => {
    render(<VisualizationDemo testId="viz-demo" />);

    // Check that the component renders
    expect(screen.getByTestId('viz-demo')).toBeInTheDocument();
    expect(screen.getByText('AI Recommendation Visualizations')).toBeInTheDocument();

    // Check that the preference tab is selected by default
    expect(screen.getByTestId('viz-demo-preferences-tab')).toHaveAttribute('aria-selected', 'true');
    expect(screen.getByTestId('viz-demo-embeddings-tab')).toHaveAttribute('aria-selected', 'false');

    // Check that the preference panel is visible
    expect(screen.getByTestId('viz-demo-preferences-panel')).toBeInTheDocument();

    // Wait for the preference visualization to load
    await waitFor(() => {
      expect(recommendationService.getPreferenceVisualization).toHaveBeenCalled();
    }, { timeout: 3000 });
  });

  it('switches to embedding visualization when the tab is clicked', async () => {
    render(<VisualizationDemo testId="viz-demo" />);

    // Click on the embeddings tab
    fireEvent.click(screen.getByTestId('viz-demo-embeddings-tab'));

    // Check that the embeddings tab is now selected
    expect(screen.getByTestId('viz-demo-embeddings-tab')).toHaveAttribute('aria-selected', 'true');
    expect(screen.getByTestId('viz-demo-preferences-tab')).toHaveAttribute('aria-selected', 'false');

    // Check that the embeddings panel is visible
    expect(screen.getByTestId('viz-demo-embeddings-panel')).toBeInTheDocument();

    // Wait for the embedding visualization to load
    await waitFor(() => {
      expect(mockFetch).toHaveBeenCalled();
    }, { timeout: 3000 });
  });

  it('handles preference data loading', async () => {
    // Create a spy on console.log
    const consoleSpy = jest.spyOn(console, 'log');

    render(<VisualizationDemo testId="viz-demo" />);

    // Wait for the preference data to be loaded and logged
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Preference data loaded:', expect.any(Array));
    }, { timeout: 3000 });

    consoleSpy.mockRestore();
  });

  it('handles embedding data loading', async () => {
    // Create a spy on console.log
    const consoleSpy = jest.spyOn(console, 'log');

    render(<VisualizationDemo testId="viz-demo" />);

    // Click on the embeddings tab
    fireEvent.click(screen.getByTestId('viz-demo-embeddings-tab'));

    // Wait for the embedding data to be loaded and logged
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Embedding data loaded:', expect.any(Array));
    }, { timeout: 3000 });

    consoleSpy.mockRestore();
  });
});
