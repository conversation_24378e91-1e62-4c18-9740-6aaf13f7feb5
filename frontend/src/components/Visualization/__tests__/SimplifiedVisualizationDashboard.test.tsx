import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SimplifiedTestProviders from '../../../testing/SimplifiedTestProviders';

// Mock data for testing
const mockPreferenceData = [
  { preferenceType: 'liked', weight: 0.8, count: 12 },
  { preferenceType: 'viewed', weight: 0.5, count: 30 },
  { preferenceType: 'favorited', weight: 0.9, count: 8 },
];

const mockEmbeddingData = [
  { id: 'item1', name: 'Item 1', x: 0.2, y: 0.3, category: 'electronics' },
  { id: 'item2', name: 'Item 2', x: 0.5, y: 0.7, category: 'furniture' },
  { id: 'item3', name: 'Item 3', x: 0.8, y: 0.1, category: 'clothing' },
];

// Create a simplified version of the VisualizationDashboard component for testing
const MockVisualizationDashboard = ({
  testId = 'viz-dashboard',
  state = 'loading',
  preferenceData = [],
  embeddingData = [],
  error = null
}) => {
  if (state === 'loading') {
    return (
      <div data-testid={`${testId}-loading`} role="status" aria-busy="true" aria-live="polite">
        <h2 className="text-2xl font-bold mb-6">AI Recommendation Dashboard</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg shadow-sm p-4 animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm p-4 animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="h-40 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (state === 'error') {
    return (
      <div data-testid={`${testId}-error`} role="alert" aria-live="assertive">
        <h2 className="text-2xl font-bold mb-6">AI Recommendation Dashboard</h2>
        <div className="bg-red-50 text-red-700 p-4 rounded-md">
          <p className="mb-3">{error || 'Error loading dashboard data'}</p>
          <button data-testid={`${testId}-retry-button`}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div data-testid={testId}>
      <h2 className="text-2xl font-bold mb-6">AI Recommendation Dashboard</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div data-testid={`${testId}-preference-section`} className="bg-white rounded-lg shadow-sm p-4">
          <h3 className="text-lg font-semibold mb-4">Your Preferences</h3>
          {preferenceData.length === 0 ? (
            <div data-testid={`${testId}-preference-empty`} className="text-center py-8 text-gray-700">
              <p>No preference data yet</p>
            </div>
          ) : (
            <div className="space-y-4">
              {preferenceData.map((pref) => (
                <div key={pref.preferenceType} data-testid={`${testId}-preference-${pref.preferenceType}`}>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium capitalize">{pref.preferenceType}</span>
                    <span className="text-xs text-gray-700">{pref.count} items</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div data-testid={`${testId}-embedding-section`} className="bg-white rounded-lg shadow-sm p-4">
          <h3 className="text-lg font-semibold mb-4">Item Relationships</h3>
          {embeddingData.length === 0 ? (
            <div data-testid={`${testId}-embedding-empty`} className="text-center py-8 text-gray-700">
              <p>No embedding data available</p>
            </div>
          ) : (
            <div data-testid={`${testId}-embedding-visualization`} className="h-40 bg-gray-100 rounded">
              <div className="text-center py-4">
                {embeddingData.map((item) => (
                  <div key={item.id} data-testid={`${testId}-embedding-item-${item.id}`}>
                    {item.name} ({item.category})
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

describe('VisualizationDashboard Component', () => {
  it('renders loading state initially', () => {
    render(
      <SimplifiedTestProviders>
        <MockVisualizationDashboard state="loading" />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('viz-dashboard-loading')).toBeInTheDocument();
    expect(screen.getByText('AI Recommendation Dashboard')).toBeInTheDocument();
  });

  it('renders error state when API call fails', () => {
    render(
      <SimplifiedTestProviders>
        <MockVisualizationDashboard state="error" error="Error loading dashboard data" />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('viz-dashboard-error')).toBeInTheDocument();
    expect(screen.getByText('Error loading dashboard data')).toBeInTheDocument();
    expect(screen.getByTestId('viz-dashboard-retry-button')).toBeInTheDocument();
  });

  it('renders empty state when no data is available', () => {
    render(
      <SimplifiedTestProviders>
        <MockVisualizationDashboard state="data" preferenceData={[]} embeddingData={[]} />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('viz-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('viz-dashboard-preference-empty')).toBeInTheDocument();
    expect(screen.getByTestId('viz-dashboard-embedding-empty')).toBeInTheDocument();
    expect(screen.getByText('No preference data yet')).toBeInTheDocument();
    expect(screen.getByText('No embedding data available')).toBeInTheDocument();
  });

  it('renders dashboard with preference and embedding data', () => {
    render(
      <SimplifiedTestProviders>
        <MockVisualizationDashboard
          state="data"
          preferenceData={mockPreferenceData}
          embeddingData={mockEmbeddingData}
        />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('viz-dashboard')).toBeInTheDocument();

    // Check preference section
    expect(screen.getByTestId('viz-dashboard-preference-section')).toBeInTheDocument();
    expect(screen.getByTestId('viz-dashboard-preference-liked')).toBeInTheDocument();
    expect(screen.getByTestId('viz-dashboard-preference-viewed')).toBeInTheDocument();
    expect(screen.getByTestId('viz-dashboard-preference-favorited')).toBeInTheDocument();

    // Check embedding section
    expect(screen.getByTestId('viz-dashboard-embedding-section')).toBeInTheDocument();
    expect(screen.getByTestId('viz-dashboard-embedding-visualization')).toBeInTheDocument();
    expect(screen.getByTestId('viz-dashboard-embedding-item-item1')).toBeInTheDocument();
    expect(screen.getByTestId('viz-dashboard-embedding-item-item2')).toBeInTheDocument();
    expect(screen.getByTestId('viz-dashboard-embedding-item-item3')).toBeInTheDocument();
  });
});
