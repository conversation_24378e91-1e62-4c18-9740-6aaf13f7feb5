import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import ComprehensiveVisualizationDashboard from '../ComprehensiveVisualizationDashboard';
import { useAuth } from '../../../hooks/useAuth';
import { useMediaQuery } from '../../../hooks/useMediaQuery';
import { useErrorHandler } from '../../../hooks/useErrorHandler';
import recommendationService from '../../../services/recommendationService';

// Mock the hooks
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));

jest.mock('../../../hooks/useMediaQuery', () => ({
  useMediaQuery: jest.fn(),
}));

jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: jest.fn(),
}));

jest.mock('../../../hooks/usePerformance', () => ({
  usePerformance: () => ({
    measureFunction: (name: string) => (fn: Function) => fn,
    measureApiCall: (name: string) => (fn: Function) => fn,
    measureEventHandler: (name: string) => (fn: Function) => fn,
  }),
}));

// Mock the recommendation service
jest.mock('../../../services/recommendationService', () => ({
  getRecommendationExplanation: jest.fn(),
  __esModule: true,
  default: {
    getRecommendationExplanation: jest.fn(),
  },
}));

// Mock the child components
jest.mock('../PreferenceVisualization', () => ({
  __esModule: true,
  default: jest.fn(({ onDataLoaded }) => {
    // Simulate data loading
    React.useEffect(() => {
      if (onDataLoaded) {
        onDataLoaded([
          { preferenceType: 'liked', weight: 0.8, count: 10 },
          { preferenceType: 'viewed', weight: 0.6, count: 20 },
          { preferenceType: 'rented', weight: 0.9, count: 5 },
        ]);
      }
    }, [onDataLoaded]);
    
    return <div data-testid="preference-visualization">Preference Visualization</div>;
  }),
}));

jest.mock('../EmbeddingVisualization', () => ({
  __esModule: true,
  default: jest.fn(({ onDataLoaded, onItemSelect, onCategorySelect }) => {
    // Simulate data loading
    React.useEffect(() => {
      if (onDataLoaded) {
        onDataLoaded([
          { id: 'item-1', name: 'Item 1', category: 'apartment', x: 0.1, y: 0.2 },
          { id: 'item-2', name: 'Item 2', category: 'house', x: 0.3, y: 0.4 },
          { id: 'item-3', name: 'Item 3', category: 'condo', x: 0.5, y: 0.6 },
        ]);
      }
    }, [onDataLoaded]);
    
    return (
      <div data-testid="embedding-visualization">
        Embedding Visualization
        <button 
          data-testid="select-item-button" 
          onClick={() => onItemSelect && onItemSelect('item-1')}
        >
          Select Item
        </button>
        <button 
          data-testid="select-category-button" 
          onClick={() => onCategorySelect && onCategorySelect('apartment')}
        >
          Select Category
        </button>
      </div>
    );
  }),
}));

describe('ComprehensiveVisualizationDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock useAuth
    (useAuth as jest.Mock).mockReturnValue({
      user: { id: 'test-user-id' },
    });
    
    // Mock useMediaQuery
    (useMediaQuery as jest.Mock).mockReturnValue(false);
    
    // Mock useErrorHandler
    (useErrorHandler as jest.Mock).mockReturnValue({
      handleError: jest.fn(),
    });
    
    // Mock recommendation service
    (recommendationService.getRecommendationExplanation as jest.Mock).mockResolvedValue({
      itemName: 'Test Item',
      itemCategory: 'Test Category',
      itemImage: 'test-image.jpg',
      score: 0.85,
      factorWeights: [
        { factor: 'Location', weight: 0.4, description: 'Based on your location preferences' },
        { factor: 'Price', weight: 0.3, description: 'Within your budget range' },
      ],
      userPreferences: [
        { factor: 'Location', strength: 0.8, description: 'You prefer urban areas' },
        { factor: 'Price', strength: 0.6, description: 'You prefer mid-range prices' },
      ],
      similarItems: [
        { id: 'item-2', name: 'Similar Item 2', similarity: 0.8 },
        { id: 'item-3', name: 'Similar Item 3', similarity: 0.7 },
      ],
    });
  });

  it('renders all tabs correctly', () => {
    render(
      <BrowserRouter>
        <ComprehensiveVisualizationDashboard />
      </BrowserRouter>
    );
    
    // Check if all tabs are rendered
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Preferences')).toBeInTheDocument();
    expect(screen.getByText('Item Relationships')).toBeInTheDocument();
    expect(screen.getByText('Recommendations')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    
    // Check if the time filter is rendered
    expect(screen.getByText('Time Period:')).toBeInTheDocument();
    expect(screen.getByText('Current')).toBeInTheDocument();
    expect(screen.getByText('1 Month')).toBeInTheDocument();
    expect(screen.getByText('3 Months')).toBeInTheDocument();
    expect(screen.getByText('6 Months')).toBeInTheDocument();
  });

  it('switches tabs when clicking on tab buttons', () => {
    render(
      <BrowserRouter>
        <ComprehensiveVisualizationDashboard />
      </BrowserRouter>
    );
    
    // Initially on Overview tab
    expect(screen.getByText('This dashboard provides insights into how our AI recommendation system works and how it personalizes content for you.')).toBeInTheDocument();
    
    // Click on Preferences tab
    fireEvent.click(screen.getByText('Preferences'));
    expect(screen.getByText('This visualization shows how your preferences are tracked and used for recommendations.')).toBeInTheDocument();
    expect(screen.getByTestId('preference-visualization')).toBeInTheDocument();
    
    // Click on Item Relationships tab
    fireEvent.click(screen.getByText('Item Relationships'));
    expect(screen.getByText('This visualization shows how items are related to each other in the recommendation space.')).toBeInTheDocument();
    expect(screen.getByTestId('embedding-visualization')).toBeInTheDocument();
    
    // Click on Dashboard tab
    fireEvent.click(screen.getByText('Dashboard'));
    expect(screen.getByText('Recommendation Performance')).toBeInTheDocument();
    expect(screen.getByText('Recommendation Categories')).toBeInTheDocument();
    expect(screen.getByText('Recommendation Insights')).toBeInTheDocument();
  });

  it('changes time period when clicking on time filter buttons', () => {
    render(
      <BrowserRouter>
        <ComprehensiveVisualizationDashboard />
      </BrowserRouter>
    );
    
    // Initially on Current time period
    const currentButton = screen.getByText('Current');
    expect(currentButton.className).toContain('bg-blue-600');
    
    // Click on 3 Months button
    fireEvent.click(screen.getByText('3 Months'));
    expect(screen.getByText('3 Months').className).toContain('bg-blue-600');
    expect(currentButton.className).not.toContain('bg-blue-600');
  });

  it('selects an item and shows recommendation explanation', async () => {
    render(
      <BrowserRouter>
        <ComprehensiveVisualizationDashboard />
      </BrowserRouter>
    );
    
    // Go to Item Relationships tab
    fireEvent.click(screen.getByText('Item Relationships'));
    
    // Select an item
    fireEvent.click(screen.getByTestId('select-item-button'));
    
    // Should automatically switch to Recommendations tab
    expect(screen.getByText('Recommendations')).toBeInTheDocument();
    
    // Wait for the recommendation explanation to load
    await waitFor(() => {
      expect(screen.getByText('Test Item')).toBeInTheDocument();
      expect(screen.getByText('Test Category')).toBeInTheDocument();
      expect(screen.getByText('Match Score: 85%')).toBeInTheDocument();
      expect(screen.getByText('Why This Was Recommended')).toBeInTheDocument();
      expect(screen.getByText('Location')).toBeInTheDocument();
      expect(screen.getByText('Price')).toBeInTheDocument();
      expect(screen.getByText('Based on Your Preferences')).toBeInTheDocument();
      expect(screen.getByText('Similar Items You Might Like')).toBeInTheDocument();
    });
    
    // Verify the recommendation service was called with the correct parameters
    expect(recommendationService.getRecommendationExplanation).toHaveBeenCalledWith('item-1', 'test-user-id');
  });

  it('selects a category and updates the visualization', () => {
    render(
      <BrowserRouter>
        <ComprehensiveVisualizationDashboard />
      </BrowserRouter>
    );
    
    // Go to Item Relationships tab
    fireEvent.click(screen.getByText('Item Relationships'));
    
    // Select a category
    fireEvent.click(screen.getByTestId('select-category-button'));
    
    // The category selection is handled by the EmbeddingVisualization component
    // We can verify that the mock was called correctly
    expect(screen.getByTestId('embedding-visualization')).toBeInTheDocument();
  });
});
