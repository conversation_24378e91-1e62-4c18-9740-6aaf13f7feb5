import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmbeddingVisualizationTestable from '../EmbeddingVisualizationTestable';

// Mock the useErrorHandler hook
jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn((err, options) => {
      if (options?.setError) {
        options.setError('Error message');
      }
    }),
  }),
}));

// Mock the recommendationService
jest.mock('../../../services/recommendationService', () => ({
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
}));

// Mock data for testing
const mockEmbeddingData = [
  { id: 'item1', name: 'Item 1', x: 0.2, y: 0.3, category: 'electronics' },
  { id: 'item2', name: 'Item 2', x: 0.5, y: 0.7, category: 'furniture' },
  { id: 'item3', name: 'Item 3', x: 0.8, y: 0.1, category: 'clothing' },
];

describe('EmbeddingVisualizationTestable Component (Robust Tests)', () => {
  // Basic rendering tests
  describe('Rendering', () => {
    it('renders loading state initially', () => {
      render(<EmbeddingVisualizationTestable testId="embed-viz" />);

      // Check loading state
      const loadingElement = screen.getByTestId('embed-viz-loading');
      expect(loadingElement).toBeInTheDocument();
      expect(loadingElement).toHaveTextContent('Loading visualization data...');

      // Verify accessibility attributes
      expect(loadingElement).toHaveAttribute('aria-busy', 'true');
      expect(loadingElement).toHaveAttribute('aria-live', 'polite');
    });

    it('renders error state when mockError is true', () => {
      render(<EmbeddingVisualizationTestable testId="embed-viz" mockError={true} />);

      // Check error state
      const errorElement = screen.getByTestId('embed-viz-error');
      expect(errorElement).toBeInTheDocument();
      expect(screen.getByText('Error message')).toBeInTheDocument();

      // Check retry button
      const retryButton = screen.getByTestId('embed-viz-retry-button');
      expect(retryButton).toBeInTheDocument();
      expect(retryButton).toHaveTextContent('Retry');

      // Verify accessibility attributes
      expect(errorElement).toHaveAttribute('role', 'alert');
      expect(errorElement).toHaveAttribute('aria-live', 'assertive');
    });

    it('renders empty state when mockEmpty is true', () => {
      render(<EmbeddingVisualizationTestable testId="embed-viz" mockEmpty={true} />);

      // Check empty state
      const emptyElement = screen.getByTestId('embed-viz-empty');
      expect(emptyElement).toBeInTheDocument();
      expect(screen.getByText('No data available')).toBeInTheDocument();
    });

    it('renders embedding data correctly when mockData is provided', () => {
      render(
        <EmbeddingVisualizationTestable
          testId="embed-viz"
          mockData={mockEmbeddingData}
        />
      );

      // Check that the component renders
      const vizElement = screen.getByTestId('embed-viz');
      expect(vizElement).toBeInTheDocument();
      expect(screen.getByText('Item Relationships')).toBeInTheDocument();

      // Check category filter
      const categoriesElement = screen.getByTestId('embed-viz-categories');
      expect(categoriesElement).toBeInTheDocument();
      expect(screen.getByTestId('embed-viz-category-all')).toBeInTheDocument();
      expect(screen.getByText('All Categories')).toBeInTheDocument();

      // Check category buttons by testId instead of text
      expect(screen.getByTestId('embed-viz-category-electronics')).toBeInTheDocument();
      expect(screen.getByTestId('embed-viz-category-furniture')).toBeInTheDocument();
      expect(screen.getByTestId('embed-viz-category-clothing')).toBeInTheDocument();

      // Check points
      const containerElement = screen.getByTestId('embed-viz-container');
      expect(containerElement).toBeInTheDocument();
      expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
      expect(screen.getByTestId('embed-viz-point-item2')).toBeInTheDocument();
      expect(screen.getByTestId('embed-viz-point-item3')).toBeInTheDocument();

      // Check point content
      expect(screen.getByText('Item 1')).toBeInTheDocument();
      expect(screen.getByText('Item 2')).toBeInTheDocument();
      expect(screen.getByText('Item 3')).toBeInTheDocument();

      // Check legend
      expect(screen.getByTestId('embed-viz-legend')).toBeInTheDocument();
    });
  });

  // Callback tests
  describe('Callbacks', () => {
    it('calls onDataLoaded callback when mockData is provided', () => {
      const onDataLoaded = jest.fn();
      render(
        <EmbeddingVisualizationTestable
          testId="embed-viz"
          mockData={mockEmbeddingData}
          onDataLoaded={onDataLoaded}
        />
      );

      // Check that the callback was called with the mock data
      expect(onDataLoaded).toHaveBeenCalledWith(mockEmbeddingData);
    });

    it('calls onDataLoaded callback when mockEmpty is true', () => {
      const onDataLoaded = jest.fn();
      render(
        <EmbeddingVisualizationTestable
          testId="embed-viz"
          mockEmpty={true}
          onDataLoaded={onDataLoaded}
        />
      );

      // Check that the callback was called with an empty array
      expect(onDataLoaded).toHaveBeenCalledWith([]);
    });
  });

  // Interaction tests
  describe('Interactions', () => {
    it('filters embeddings by category when a category button is clicked', () => {
      render(
        <EmbeddingVisualizationTestable
          testId="embed-viz"
          mockData={mockEmbeddingData}
        />
      );

      // Click the electronics category button
      fireEvent.click(screen.getByTestId('embed-viz-category-electronics'));

      // Check that only electronics points are visible
      expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
      expect(screen.queryByTestId('embed-viz-point-item2')).not.toBeInTheDocument();
      expect(screen.queryByTestId('embed-viz-point-item3')).not.toBeInTheDocument();

      // Click the All Categories button
      fireEvent.click(screen.getByTestId('embed-viz-category-all'));

      // Check that all points are visible again
      expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
      expect(screen.getByTestId('embed-viz-point-item2')).toBeInTheDocument();
      expect(screen.getByTestId('embed-viz-point-item3')).toBeInTheDocument();
    });

    it('category buttons are keyboard accessible', () => {
      render(
        <EmbeddingVisualizationTestable
          testId="embed-viz"
          mockData={mockEmbeddingData}
        />
      );

      // Focus and press Enter on the electronics category button
      const electronicsButton = screen.getByTestId('embed-viz-category-electronics');
      electronicsButton.focus();
      fireEvent.keyDown(electronicsButton, { key: 'Enter', code: 'Enter' });
      fireEvent.keyUp(electronicsButton, { key: 'Enter', code: 'Enter' });
      fireEvent.click(electronicsButton);

      // Check that only electronics points are visible
      expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
      expect(screen.queryByTestId('embed-viz-point-item2')).not.toBeInTheDocument();
      expect(screen.queryByTestId('embed-viz-point-item3')).not.toBeInTheDocument();
    });
  });

  // Responsive design tests
  describe('Responsive Design', () => {
    it('does not render legend when compact prop is true', () => {
      render(
        <EmbeddingVisualizationTestable
          testId="embed-viz"
          mockData={mockEmbeddingData}
          compact={true}
        />
      );

      // Check that the legend is not rendered
      expect(screen.queryByTestId('embed-viz-legend')).not.toBeInTheDocument();
    });

    it('renders legend when compact prop is false', () => {
      render(
        <EmbeddingVisualizationTestable
          testId="embed-viz"
          mockData={mockEmbeddingData}
          compact={false}
        />
      );

      // Check that the legend is rendered
      expect(screen.getByTestId('embed-viz-legend')).toBeInTheDocument();

      // Check legend content
      const legendElement = screen.getByTestId('embed-viz-legend');
      expect(legendElement).toHaveTextContent('electronics');
      expect(legendElement).toHaveTextContent('furniture');
      expect(legendElement).toHaveTextContent('clothing');
    });
  });

  // Accessibility tests
  describe('Accessibility', () => {
    it('data points have correct data attributes', () => {
      render(
        <EmbeddingVisualizationTestable
          testId="embed-viz"
          mockData={mockEmbeddingData}
        />
      );

      // Check data attributes for category
      const item1 = screen.getByTestId('embed-viz-point-item1');
      expect(item1).toHaveAttribute('data-category', 'electronics');

      const item2 = screen.getByTestId('embed-viz-point-item2');
      expect(item2).toHaveAttribute('data-category', 'furniture');

      const item3 = screen.getByTestId('embed-viz-point-item3');
      expect(item3).toHaveAttribute('data-category', 'clothing');
    });

    it('has proper heading hierarchy', () => {
      render(
        <EmbeddingVisualizationTestable
          testId="embed-viz"
          mockData={mockEmbeddingData}
        />
      );

      // Check heading
      const heading = screen.getByText('Item Relationships');
      expect(heading.tagName).toBe('H3');
    });
  });
});
