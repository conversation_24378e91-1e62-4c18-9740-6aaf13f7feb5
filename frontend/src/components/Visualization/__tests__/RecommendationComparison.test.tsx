import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import RecommendationComparison from '../RecommendationComparison';
import { useAuth } from '../../../hooks/useAuth';
import { useMediaQuery } from '../../../hooks/useMediaQuery';
import { useErrorHandler } from '../../../hooks/useErrorHandler';
import recommendationService from '../../../services/recommendationService';

// Mock the hooks
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));

jest.mock('../../../hooks/useMediaQuery', () => ({
  useMediaQuery: jest.fn(),
}));

jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: jest.fn(),
}));

jest.mock('../../../hooks/usePerformance', () => ({
  usePerformance: () => ({
    measureFunction: (name: string) => (fn: Function) => fn,
    measureApiCall: (name: string) => (fn: Function) => fn,
    measureEventHandler: (name: string) => (fn: Function) => fn,
  }),
}));

// Mock the recommendation service
jest.mock('../../../services/recommendationService', () => ({
  getRecommendationExplanation: jest.fn(),
  __esModule: true,
  default: {
    getRecommendationExplanation: jest.fn(),
  },
}));

describe('RecommendationComparison', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock useAuth
    (useAuth as jest.Mock).mockReturnValue({
      user: { id: 'test-user-id' },
    });
    
    // Mock useMediaQuery
    (useMediaQuery as jest.Mock).mockReturnValue(false);
    
    // Mock useErrorHandler
    (useErrorHandler as jest.Mock).mockReturnValue({
      handleError: jest.fn(),
    });
    
    // Mock recommendation service
    (recommendationService.getRecommendationExplanation as jest.Mock).mockImplementation((itemId) => {
      return Promise.resolve({
        itemId,
        itemName: `Test Item ${itemId}`,
        itemCategory: 'Test Category',
        itemImage: 'test-image.jpg',
        score: 0.85,
        factorWeights: [
          { factor: 'Location', weight: 0.4, description: 'Based on your location preferences' },
          { factor: 'Price', weight: 0.3, description: 'Within your budget range' },
          { factor: 'Amenities', weight: itemId === 'item-1' ? 0.2 : 0.1, description: 'Matches your preferred amenities' },
          ...(itemId === 'item-2' ? [{ factor: 'Reviews', weight: 0.2, description: 'Has positive reviews' }] : []),
          ...(itemId === 'item-3' ? [{ factor: 'Availability', weight: 0.2, description: 'Available during your preferred dates' }] : []),
        ],
        userPreferences: [
          { factor: 'Location', strength: 0.8, description: 'You prefer urban areas' },
          { factor: 'Price', strength: 0.6, description: 'You prefer mid-range prices' },
        ],
        similarItems: [
          { id: 'similar-1', name: 'Similar Item 1', similarity: 0.8 },
          { id: 'similar-2', name: 'Similar Item 2', similarity: 0.7 },
        ],
      });
    });
  });

  it('renders loading state initially', () => {
    render(
      <BrowserRouter>
        <RecommendationComparison
          itemIds={['item-1', 'item-2']}
          testId="recommendation-comparison"
        />
      </BrowserRouter>
    );
    
    expect(screen.getByTestId('recommendation-comparison-loading')).toBeInTheDocument();
  });

  it('renders empty state when no items are selected', () => {
    render(
      <BrowserRouter>
        <RecommendationComparison
          itemIds={[]}
          testId="recommendation-comparison"
        />
      </BrowserRouter>
    );
    
    expect(screen.getByTestId('recommendation-comparison-empty')).toBeInTheDocument();
    expect(screen.getByText('No items selected for comparison')).toBeInTheDocument();
  });

  it('renders comparison for selected items', async () => {
    render(
      <BrowserRouter>
        <RecommendationComparison
          itemIds={['item-1', 'item-2']}
          testId="recommendation-comparison"
        />
      </BrowserRouter>
    );
    
    // Wait for the data to load
    await waitFor(() => {
      expect(screen.queryByTestId('recommendation-comparison-loading')).not.toBeInTheDocument();
    });
    
    // Check if the component renders the comparison
    expect(screen.getByTestId('recommendation-comparison')).toBeInTheDocument();
    
    // Check if both items are displayed
    expect(screen.getByText('Test Item item-1')).toBeInTheDocument();
    expect(screen.getByText('Test Item item-2')).toBeInTheDocument();
    
    // Check if common factors section is displayed
    expect(screen.getByText('Common Factors')).toBeInTheDocument();
    
    // Check if unique factors section is displayed
    expect(screen.getByText('Unique Factors')).toBeInTheDocument();
    
    // Verify the recommendation service was called for both items
    expect(recommendationService.getRecommendationExplanation).toHaveBeenCalledTimes(2);
    expect(recommendationService.getRecommendationExplanation).toHaveBeenCalledWith('item-1', 'test-user-id');
    expect(recommendationService.getRecommendationExplanation).toHaveBeenCalledWith('item-2', 'test-user-id');
  });

  it('allows removing items from comparison', async () => {
    render(
      <BrowserRouter>
        <RecommendationComparison
          itemIds={['item-1', 'item-2']}
          testId="recommendation-comparison"
        />
      </BrowserRouter>
    );
    
    // Wait for the data to load
    await waitFor(() => {
      expect(screen.queryByTestId('recommendation-comparison-loading')).not.toBeInTheDocument();
    });
    
    // Find and click the remove button for the first item
    const removeButtons = screen.getAllByLabelText(/Remove .* from comparison/);
    fireEvent.click(removeButtons[0]);
    
    // Check if the onItemSelect callback was called
    expect(recommendationService.getRecommendationExplanation).toHaveBeenCalledTimes(2);
  });

  it('identifies common and unique factors correctly', async () => {
    render(
      <BrowserRouter>
        <RecommendationComparison
          itemIds={['item-1', 'item-2', 'item-3']}
          testId="recommendation-comparison"
        />
      </BrowserRouter>
    );
    
    // Wait for the data to load
    await waitFor(() => {
      expect(screen.queryByTestId('recommendation-comparison-loading')).not.toBeInTheDocument();
    });
    
    // Check if common factors are identified correctly
    expect(screen.getByText('Location')).toBeInTheDocument();
    expect(screen.getByText('Price')).toBeInTheDocument();
    
    // Check if unique factors are identified correctly
    expect(screen.getByText('Reviews')).toBeInTheDocument();
    expect(screen.getByText('Availability')).toBeInTheDocument();
  });

  it('handles errors gracefully', async () => {
    // Mock the service to throw an error
    (recommendationService.getRecommendationExplanation as jest.Mock).mockRejectedValue(new Error('Test error'));
    
    render(
      <BrowserRouter>
        <RecommendationComparison
          itemIds={['item-1', 'item-2']}
          testId="recommendation-comparison"
        />
      </BrowserRouter>
    );
    
    // Wait for the error state to be displayed
    await waitFor(() => {
      expect(screen.queryByTestId('recommendation-comparison-loading')).not.toBeInTheDocument();
    });
    
    // Check if the error message is displayed
    expect(screen.getByTestId('recommendation-comparison-error')).toBeInTheDocument();
    expect(screen.getByText('Error loading comparison')).toBeInTheDocument();
    
    // Check if the retry button is displayed
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });
});
