import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { ToastProvider } from '../../../contexts/ToastContext';

// Mock the recommendation service
jest.mock('../../../services/recommendationService', () => ({
  getApiUrl: jest.fn().mockReturnValue('http://localhost:8000'),
  getPreferenceVisualization: jest.fn(),
}));

// Mock the fetch function
global.fetch = jest.fn();

// Mock the framer-motion for testing
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock the useMediaQuery hook
jest.mock('../../../hooks/useMediaQuery', () => ({
  __esModule: true,
  useMediaQuery: jest.fn().mockReturnValue(false),
  useIsMobile: jest.fn().mockReturnValue(false),
  useIsTablet: jest.fn().mockReturnValue(false),
  useIsDesktop: jest.fn().mockReturnValue(true),
}));

// Custom test wrapper that uses MemoryRouter instead of BrowserRouter
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <MemoryRouter initialEntries={['/']}>
      <ToastProvider>
        {children}
      </ToastProvider>
    </MemoryRouter>
  );
};

// Mock data for testing
const mockEmbeddingData = [
  { id: '1', name: 'Item 1', category: 'electronics', x: 0.1, y: 0.2 },
  { id: '2', name: 'Item 2', category: 'furniture', x: 0.3, y: 0.4 },
  { id: '3', name: 'Item 3', category: 'clothing', x: 0.5, y: 0.6 },
];

// Create a simplified mock of the InteractiveEmbeddingVisualization component
const MockInteractiveEmbeddingVisualization: React.FC<{ testId: string }> = ({ testId }) => {
  const [viewMode, setViewMode] = React.useState('2d');
  const [colorMode, setColorMode] = React.useState('category');
  const [showLabels, setShowLabels] = React.useState(false);

  return (
    <div data-testid={testId}>
      <h2>Item Relationships</h2>
      <div className="controls">
        <div className="view-mode">
          <button
            onClick={() => setViewMode('2d')}
            aria-pressed={viewMode === '2d'}
          >
            2D
          </button>
          <button
            onClick={() => setViewMode('3d')}
            aria-pressed={viewMode === '3d'}
          >
            3D
          </button>
        </div>
        <div className="color-mode">
          <button
            onClick={() => setColorMode('category')}
            aria-pressed={colorMode === 'category'}
          >
            By Category
          </button>
          <button
            onClick={() => setColorMode('similarity')}
            aria-pressed={colorMode === 'similarity'}
          >
            By Similarity
          </button>
        </div>
        <div className="labels">
          <button onClick={() => setShowLabels(!showLabels)}>
            {showLabels ? 'Hide Labels' : 'Show Labels'}
          </button>
        </div>
      </div>
      <div
        data-testid={`${testId}-visualization`}
        className="p-0 shadow-none rounded-none"
      >
        {/* Mock visualization content */}
      </div>
    </div>
  );
};

describe('InteractiveEmbeddingVisualization', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the component with controls', () => {
    render(
      <TestWrapper>
        <MockInteractiveEmbeddingVisualization testId="test-viz" />
      </TestWrapper>
    );

    // Check that the component renders with controls
    expect(screen.getByText('Item Relationships')).toBeInTheDocument();
    expect(screen.getByText('2D')).toBeInTheDocument();
    expect(screen.getByText('3D')).toBeInTheDocument();
    expect(screen.getByText('By Category')).toBeInTheDocument();
    expect(screen.getByText('By Similarity')).toBeInTheDocument();
    expect(screen.getByText('Show Labels')).toBeInTheDocument();
  });

  it('toggles view mode when buttons are clicked', () => {
    render(
      <TestWrapper>
        <MockInteractiveEmbeddingVisualization testId="test-viz" />
      </TestWrapper>
    );

    // Click the 3D button
    fireEvent.click(screen.getByText('3D'));

    // Check that the 3D button is now active
    expect(screen.getByText('3D')).toHaveAttribute('aria-pressed', 'true');
    expect(screen.getByText('2D')).toHaveAttribute('aria-pressed', 'false');

    // Click the 2D button
    fireEvent.click(screen.getByText('2D'));

    // Check that the 2D button is now active
    expect(screen.getByText('2D')).toHaveAttribute('aria-pressed', 'true');
    expect(screen.getByText('3D')).toHaveAttribute('aria-pressed', 'false');
  });

  it('toggles color mode when buttons are clicked', () => {
    render(
      <TestWrapper>
        <MockInteractiveEmbeddingVisualization testId="test-viz" />
      </TestWrapper>
    );

    // Click the By Similarity button
    fireEvent.click(screen.getByText('By Similarity'));

    // Check that the By Similarity button is now active
    expect(screen.getByText('By Similarity')).toHaveAttribute('aria-pressed', 'true');
    expect(screen.getByText('By Category')).toHaveAttribute('aria-pressed', 'false');

    // Click the By Category button
    fireEvent.click(screen.getByText('By Category'));

    // Check that the By Category button is now active
    expect(screen.getByText('By Category')).toHaveAttribute('aria-pressed', 'true');
    expect(screen.getByText('By Similarity')).toHaveAttribute('aria-pressed', 'false');
  });

  it('toggles labels when button is clicked', () => {
    render(
      <TestWrapper>
        <MockInteractiveEmbeddingVisualization testId="test-viz" />
      </TestWrapper>
    );

    // Click the Show Labels button
    fireEvent.click(screen.getByText('Show Labels'));

    // Check that the button text changes to Hide Labels
    expect(screen.getByText('Hide Labels')).toBeInTheDocument();

    // Click the Hide Labels button
    fireEvent.click(screen.getByText('Hide Labels'));

    // Check that the button text changes back to Show Labels
    expect(screen.getByText('Show Labels')).toBeInTheDocument();
  });

  it('passes the interactive prop to the EmbeddingVisualization component', () => {
    render(
      <TestWrapper>
        <MockInteractiveEmbeddingVisualization testId="test-viz" />
      </TestWrapper>
    );

    // Check that the visualization has the expected classes
    expect(screen.getByTestId('test-viz-visualization')).toHaveClass('p-0');
    expect(screen.getByTestId('test-viz-visualization')).toHaveClass('shadow-none');
    expect(screen.getByTestId('test-viz-visualization')).toHaveClass('rounded-none');
  });
});
