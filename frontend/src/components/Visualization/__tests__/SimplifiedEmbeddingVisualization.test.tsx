import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SimplifiedTestProviders from '../../../testing/SimplifiedTestProviders';

// Mock data for testing
const mockEmbeddingData = [
  { id: 'item1', name: 'Item 1', x: 0.2, y: 0.3, category: 'electronics' },
  { id: 'item2', name: 'Item 2', x: 0.5, y: 0.7, category: 'furniture' },
  { id: 'item3', name: 'Item 3', x: 0.8, y: 0.1, category: 'clothing' },
];

// Create a simplified version of the EmbeddingVisualization component for testing
const MockEmbeddingVisualization = ({
  testId = 'embed-viz',
  state = 'loading',
  data = [],
  error = null,
  compact = false
}) => {
  if (state === 'loading') {
    return (
      <div data-testid={`${testId}-loading`}>Loading...</div>
    );
  }

  if (state === 'error') {
    return (
      <div data-testid={`${testId}-error`}>
        <p>{error || 'Error message'}</p>
        <button data-testid={`${testId}-retry-button`}>Retry</button>
      </div>
    );
  }

  if (state === 'empty' || data.length === 0) {
    return (
      <div data-testid={`${testId}-empty`}>
        <p>No data available</p>
      </div>
    );
  }

  return (
    <div data-testid={testId}>
      <h3>Item Relationships</h3>
      <div className="controls">
        <button data-testid={`${testId}-zoom-in`}>+</button>
        <button data-testid={`${testId}-zoom-out`}>-</button>
      </div>
      <div className="categories">
        <button data-testid={`${testId}-category-all`}>All Categories</button>
        {['electronics', 'furniture', 'clothing'].map(category => (
          <button key={category} data-testid={`${testId}-category-${category}`}>{category}</button>
        ))}
      </div>
      <div data-testid={`${testId}-container`}>
        {data.map(point => (
          <div
            key={point.id}
            data-testid={`${testId}-point-${point.id}`}
            data-category={point.category}
          >
            {point.name}
          </div>
        ))}
      </div>
      {!compact && (
        <div data-testid={`${testId}-legend`}>Legend</div>
      )}
    </div>
  );
};

describe('EmbeddingVisualization Component', () => {
  it('renders loading state initially', () => {
    render(
      <SimplifiedTestProviders>
        <MockEmbeddingVisualization state="loading" />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('embed-viz-loading')).toBeInTheDocument();
  });

  it('renders error state when API call fails', () => {
    render(
      <SimplifiedTestProviders>
        <MockEmbeddingVisualization state="error" error="Error message" />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('embed-viz-error')).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-retry-button')).toBeInTheDocument();
  });

  it('renders empty state when no embedding data', () => {
    render(
      <SimplifiedTestProviders>
        <MockEmbeddingVisualization state="empty" />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('embed-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders embedding data correctly', () => {
    render(
      <SimplifiedTestProviders>
        <MockEmbeddingVisualization state="data" data={mockEmbeddingData} />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('embed-viz')).toBeInTheDocument();
    expect(screen.getByText('Item Relationships')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item2')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item3')).toBeInTheDocument();
  });

  it('renders compact version when compact prop is true', () => {
    render(
      <SimplifiedTestProviders>
        <MockEmbeddingVisualization state="data" data={mockEmbeddingData} compact={true} />
      </SimplifiedTestProviders>
    );

    expect(screen.getByTestId('embed-viz')).toBeInTheDocument();
    expect(screen.queryByTestId('embed-viz-legend')).not.toBeInTheDocument();
  });
});
