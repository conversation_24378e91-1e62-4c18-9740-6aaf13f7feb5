import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmbeddingVisualizationTestable from '../EmbeddingVisualizationTestable';

// Mock the useErrorHandler hook
jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn((err, options) => {
      if (options?.setError) {
        options.setError('Error message');
      }
    }),
  }),
}));

// Mock the recommendationService
jest.mock('../../../services/recommendationService', () => ({
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
}));

// Mock data for testing
const mockEmbeddingData = [
  { id: 'item1', name: 'Item 1', x: 0.2, y: 0.3, category: 'electronics' },
  { id: 'item2', name: 'Item 2', x: 0.5, y: 0.7, category: 'furniture' },
  { id: 'item3', name: 'Item 3', x: 0.8, y: 0.1, category: 'clothing' },
];

describe('EmbeddingVisualizationTestable Component', () => {
  it('renders loading state initially', () => {
    render(<EmbeddingVisualizationTestable testId="embed-viz" />);
    expect(screen.getByTestId('embed-viz-loading')).toBeInTheDocument();
    expect(screen.getByText('Loading visualization data...')).toBeInTheDocument();
  });

  it('renders error state when mockError is true', () => {
    render(<EmbeddingVisualizationTestable testId="embed-viz" mockError={true} />);
    expect(screen.getByTestId('embed-viz-error')).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-retry-button')).toBeInTheDocument();
  });

  it('renders empty state when mockEmpty is true', () => {
    render(<EmbeddingVisualizationTestable testId="embed-viz" mockEmpty={true} />);
    expect(screen.getByTestId('embed-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders embedding data correctly when mockData is provided', () => {
    render(
      <EmbeddingVisualizationTestable 
        testId="embed-viz" 
        mockData={mockEmbeddingData} 
      />
    );
    
    // Check that the component renders
    expect(screen.getByTestId('embed-viz')).toBeInTheDocument();
    
    // Check category filter buttons
    expect(screen.getByText('All Categories')).toBeInTheDocument();
    expect(screen.getByText('electronics')).toBeInTheDocument();
    expect(screen.getByText('furniture')).toBeInTheDocument();
    expect(screen.getByText('clothing')).toBeInTheDocument();
    
    // Check points are rendered
    expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item2')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item3')).toBeInTheDocument();
    
    // Check point names are displayed
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
  });

  it('calls onDataLoaded callback when mockData is provided', () => {
    const onDataLoaded = jest.fn();
    render(
      <EmbeddingVisualizationTestable 
        testId="embed-viz" 
        mockData={mockEmbeddingData} 
        onDataLoaded={onDataLoaded}
      />
    );
    
    // Check that the callback was called with the mock data
    expect(onDataLoaded).toHaveBeenCalledWith(mockEmbeddingData);
  });

  it('filters embeddings by category when a category button is clicked', () => {
    render(
      <EmbeddingVisualizationTestable 
        testId="embed-viz" 
        mockData={mockEmbeddingData} 
      />
    );
    
    // Click the electronics category button
    fireEvent.click(screen.getByTestId('embed-viz-category-electronics'));
    
    // Check that only electronics points are visible
    expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
    expect(screen.queryByText('Item 2')).not.toBeInTheDocument();
    expect(screen.queryByText('Item 3')).not.toBeInTheDocument();
    
    // Click the All Categories button
    fireEvent.click(screen.getByTestId('embed-viz-category-all'));
    
    // Check that all points are visible again
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
  });

  it('does not render legend when compact prop is true', () => {
    render(
      <EmbeddingVisualizationTestable 
        testId="embed-viz" 
        mockData={mockEmbeddingData} 
        compact={true}
      />
    );
    
    // Check that the legend is not rendered
    expect(screen.queryByTestId('embed-viz-legend')).not.toBeInTheDocument();
  });

  it('renders legend when compact prop is false', () => {
    render(
      <EmbeddingVisualizationTestable 
        testId="embed-viz" 
        mockData={mockEmbeddingData} 
        compact={false}
      />
    );
    
    // Check that the legend is rendered
    expect(screen.getByTestId('embed-viz-legend')).toBeInTheDocument();
  });
});
