import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import ComprehensiveVisualizationDashboard from '../ComprehensiveVisualizationDashboard';
import { useAuth } from '../../../hooks/useAuth';
import { useMediaQuery } from '../../../hooks/useMediaQuery';
import recommendationService from '../../../services/recommendationService';

// Mock the hooks
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));

jest.mock('../../../hooks/useMediaQuery', () => ({
  useMediaQuery: jest.fn(),
}));

jest.mock('../../../hooks/usePerformance', () => ({
  usePerformance: () => ({
    measureFunction: (name: string) => (fn: Function) => fn,
    measureApiCall: (name: string) => (fn: Function) => fn,
    measureEventHandler: (name: string) => (fn: Function) => fn,
  }),
}));

jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn(),
  }),
}));

// Mock the recommendation service
jest.mock('../../../services/recommendationService', () => ({
  getPreferenceVisualization: jest.fn(),
  getEmbeddingVisualization: jest.fn(),
  getRecommendationExplanation: jest.fn(),
  __esModule: true,
  default: {
    getPreferenceVisualization: jest.fn(),
    getEmbeddingVisualization: jest.fn(),
    getRecommendationExplanation: jest.fn(),
  },
}));

// Mock the child components
jest.mock('../PreferenceVisualization', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="preference-visualization">Preference Visualization</div>),
}));

jest.mock('../EmbeddingVisualization', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="embedding-visualization">Embedding Visualization</div>),
}));

describe('ComprehensiveVisualizationDashboard', () => {
  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Mock useAuth
    (useAuth as jest.Mock).mockReturnValue({
      user: { id: 'test-user-id' },
    });
    
    // Mock useMediaQuery
    (useMediaQuery as jest.Mock).mockReturnValue(false);
    
    // Mock recommendation service
    (recommendationService.getRecommendationExplanation as jest.Mock).mockResolvedValue({
      itemName: 'Test Item',
      itemCategory: 'Test Category',
      itemImage: 'test-image.jpg',
      score: 0.85,
      factorWeights: [
        { factor: 'Location', weight: 0.4, description: 'Based on your location preferences' },
        { factor: 'Price', weight: 0.3, description: 'Within your budget range' },
        { factor: 'Features', weight: 0.2, description: 'Has features you like' },
        { factor: 'Rating', weight: 0.1, description: 'Has good ratings' },
      ],
      userPreferences: [
        { factor: 'Location', strength: 0.8, description: 'You prefer urban areas' },
        { factor: 'Price', strength: 0.6, description: 'You prefer mid-range prices' },
      ],
      similarItems: [
        { id: 'item-1', name: 'Similar Item 1', similarity: 0.9 },
        { id: 'item-2', name: 'Similar Item 2', similarity: 0.8 },
        { id: 'item-3', name: 'Similar Item 3', similarity: 0.7 },
      ],
    });
  });

  it('renders the component with default tab', () => {
    render(
      <BrowserRouter>
        <ComprehensiveVisualizationDashboard />
      </BrowserRouter>
    );
    
    // Check if the title is rendered
    expect(screen.getByText('AI Recommendation Insights')).toBeInTheDocument();
    
    // Check if the tabs are rendered
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Preferences')).toBeInTheDocument();
    expect(screen.getByText('Item Relationships')).toBeInTheDocument();
    expect(screen.getByText('Recommendations')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    
    // Check if the time filter is rendered
    expect(screen.getByText('Time Period:')).toBeInTheDocument();
    expect(screen.getByText('Current')).toBeInTheDocument();
    expect(screen.getByText('1 Month')).toBeInTheDocument();
    expect(screen.getByText('3 Months')).toBeInTheDocument();
    expect(screen.getByText('6 Months')).toBeInTheDocument();
    
    // Check if the overview content is rendered
    expect(screen.getByText('This dashboard provides insights into how our AI recommendation system works and how it personalizes content for you.')).toBeInTheDocument();
  });

  it('switches tabs when clicking on tab buttons', async () => {
    render(
      <BrowserRouter>
        <ComprehensiveVisualizationDashboard />
      </BrowserRouter>
    );
    
    // Click on the Preferences tab
    fireEvent.click(screen.getByText('Preferences'));
    
    // Check if the Preferences content is rendered
    await waitFor(() => {
      expect(screen.getByText('This visualization shows how your preferences are tracked and used for recommendations.')).toBeInTheDocument();
      expect(screen.getByTestId('preference-visualization')).toBeInTheDocument();
    });
    
    // Click on the Item Relationships tab
    fireEvent.click(screen.getByText('Item Relationships'));
    
    // Check if the Embeddings content is rendered
    await waitFor(() => {
      expect(screen.getByText('This visualization shows how items are related to each other in the recommendation space.')).toBeInTheDocument();
      expect(screen.getByTestId('embedding-visualization')).toBeInTheDocument();
    });
    
    // Click on the Dashboard tab
    fireEvent.click(screen.getByText('Dashboard'));
    
    // Check if the Dashboard content is rendered
    await waitFor(() => {
      expect(screen.getByText('Recommendation Performance')).toBeInTheDocument();
      expect(screen.getByText('Recommendation Categories')).toBeInTheDocument();
      expect(screen.getByText('Recommendation Insights')).toBeInTheDocument();
    });
  });

  it('changes time period when clicking on time filter buttons', async () => {
    render(
      <BrowserRouter>
        <ComprehensiveVisualizationDashboard />
      </BrowserRouter>
    );
    
    // Click on the 3 Months button
    fireEvent.click(screen.getByText('3 Months'));
    
    // Check if the button is highlighted
    await waitFor(() => {
      const button = screen.getByText('3 Months');
      expect(button.className).toContain('bg-blue-600');
      expect(button.className).toContain('text-white');
    });
  });
});
