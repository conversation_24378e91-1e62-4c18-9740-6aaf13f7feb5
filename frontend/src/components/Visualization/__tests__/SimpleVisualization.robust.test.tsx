import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import SimpleVisualization from '../SimpleVisualization';

// Mock data for testing
const mockData = [
  { id: 'item1', name: 'Item 1', category: 'electronics' },
  { id: 'item2', name: 'Item 2', category: 'furniture' },
  { id: 'item3', name: 'Item 3', category: 'clothing' },
];

describe('SimpleVisualization Component (Robust Tests)', () => {
  // Basic rendering tests
  describe('Rendering', () => {
    it('renders loading state correctly', () => {
      render(<SimpleVisualization testId="simple-viz" state="loading" />);

      // Check loading state
      const loadingElement = screen.getByTestId('simple-viz-loading');
      expect(loadingElement).toBeInTheDocument();
      expect(loadingElement).toHaveTextContent('Loading visualization data...');

      // Verify accessibility
      expect(loadingElement).toBeVisible();
    });

    it('renders error state correctly', () => {
      render(<SimpleVisualization testId="simple-viz" state="error" />);

      // Check error state
      const errorElement = screen.getByTestId('simple-viz-error');
      expect(errorElement).toBeInTheDocument();
      expect(screen.getByText('Error message')).toBeInTheDocument();

      // Check retry button
      const retryButton = screen.getByTestId('simple-viz-retry-button');
      expect(retryButton).toBeInTheDocument();
      expect(retryButton).toHaveTextContent('Retry');

      // Verify accessibility
      expect(retryButton).toBeEnabled();
      expect(retryButton).toBeVisible();
    });

    it('renders empty state correctly', () => {
      render(<SimpleVisualization testId="simple-viz" state="empty" />);

      // Check empty state
      const emptyElement = screen.getByTestId('simple-viz-empty');
      expect(emptyElement).toBeInTheDocument();
      expect(emptyElement).toHaveTextContent('No data available');

      // Verify accessibility
      expect(emptyElement).toBeVisible();
    });

    it('renders empty state when data is empty array', () => {
      render(<SimpleVisualization testId="simple-viz" state="data" data={[]} />);

      // Check empty state
      const emptyElement = screen.getByTestId('simple-viz-empty');
      expect(emptyElement).toBeInTheDocument();
      expect(emptyElement).toHaveTextContent('No data available');
    });

    it('renders data state correctly', () => {
      render(
        <SimpleVisualization
          testId="simple-viz"
          state="data"
          data={mockData}
        />
      );

      // Check main component
      const vizElement = screen.getByTestId('simple-viz');
      expect(vizElement).toBeInTheDocument();
      expect(screen.getByText('Item Relationships')).toBeInTheDocument();

      // Check category filter
      const categoriesElement = screen.getByTestId('simple-viz-categories');
      expect(categoriesElement).toBeInTheDocument();
      expect(screen.getByTestId('simple-viz-category-all')).toBeInTheDocument();
      expect(screen.getByTestId('simple-viz-category-electronics')).toBeInTheDocument();
      expect(screen.getByTestId('simple-viz-category-furniture')).toBeInTheDocument();
      expect(screen.getByTestId('simple-viz-category-clothing')).toBeInTheDocument();

      // Check points
      const containerElement = screen.getByTestId('simple-viz-container');
      expect(containerElement).toBeInTheDocument();
      expect(screen.getByTestId('simple-viz-point-item1')).toBeInTheDocument();
      expect(screen.getByTestId('simple-viz-point-item2')).toBeInTheDocument();
      expect(screen.getByTestId('simple-viz-point-item3')).toBeInTheDocument();

      // Check point content
      expect(screen.getByText('Item 1')).toBeInTheDocument();
      expect(screen.getByText('Item 2')).toBeInTheDocument();
      expect(screen.getByText('Item 3')).toBeInTheDocument();

      // Check legend
      expect(screen.getByTestId('simple-viz-legend')).toBeInTheDocument();
    });
  });

  // Interaction tests
  describe('Interactions', () => {
    it('calls onRetry when retry button is clicked', () => {
      const onRetry = jest.fn();
      render(
        <SimpleVisualization
          testId="simple-viz"
          state="error"
          onRetry={onRetry}
        />
      );

      // Click the retry button
      fireEvent.click(screen.getByTestId('simple-viz-retry-button'));

      // Check that onRetry was called
      expect(onRetry).toHaveBeenCalledTimes(1);
    });

    it('retry button is keyboard accessible', () => {
      const onRetry = jest.fn();
      render(
        <SimpleVisualization
          testId="simple-viz"
          state="error"
          onRetry={onRetry}
        />
      );

      // Focus and press Enter on the retry button
      const retryButton = screen.getByTestId('simple-viz-retry-button');
      retryButton.focus();
      fireEvent.keyDown(retryButton, { key: 'Enter', code: 'Enter' });
      fireEvent.keyUp(retryButton, { key: 'Enter', code: 'Enter' });

      // For native button elements, we need to use click event for keyboard simulation
      fireEvent.click(retryButton);

      // Check that onRetry was called
      expect(onRetry).toHaveBeenCalled();
    });
  });

  // Responsive design tests
  describe('Responsive Design', () => {
    it('does not render legend in compact mode', () => {
      render(
        <SimpleVisualization
          testId="simple-viz"
          state="data"
          data={mockData}
          compact={true}
        />
      );

      // Check that the legend is not rendered
      expect(screen.queryByTestId('simple-viz-legend')).not.toBeInTheDocument();
    });

    it('renders legend in normal mode', () => {
      render(
        <SimpleVisualization
          testId="simple-viz"
          state="data"
          data={mockData}
          compact={false}
        />
      );

      // Check that the legend is rendered
      expect(screen.getByTestId('simple-viz-legend')).toBeInTheDocument();

      // Check legend content
      const legendElement = screen.getByTestId('simple-viz-legend');
      expect(legendElement).toHaveTextContent('electronics');
      expect(legendElement).toHaveTextContent('furniture');
      expect(legendElement).toHaveTextContent('clothing');
    });
  });

  // Accessibility tests
  describe('Accessibility', () => {
    it('data points have correct data attributes', () => {
      render(
        <SimpleVisualization
          testId="simple-viz"
          state="data"
          data={mockData}
        />
      );

      // Check data attributes for category
      const item1 = screen.getByTestId('simple-viz-point-item1');
      expect(item1).toHaveAttribute('data-category', 'electronics');

      const item2 = screen.getByTestId('simple-viz-point-item2');
      expect(item2).toHaveAttribute('data-category', 'furniture');

      const item3 = screen.getByTestId('simple-viz-point-item3');
      expect(item3).toHaveAttribute('data-category', 'clothing');
    });

    it('has proper heading hierarchy', () => {
      render(
        <SimpleVisualization
          testId="simple-viz"
          state="data"
          data={mockData}
        />
      );

      // Check heading
      const heading = screen.getByText('Item Relationships');
      expect(heading.tagName).toBe('H3');
    });
  });
});
