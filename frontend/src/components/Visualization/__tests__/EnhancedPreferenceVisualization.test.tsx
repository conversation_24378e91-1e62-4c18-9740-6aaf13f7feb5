import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import * as exportData from '../../../utils/exportData';

// Mock the export data utility
jest.mock('../../../utils/exportData', () => ({
  downloadCSV: jest.fn(),
  downloadJSON: jest.fn(),
  shareData: jest.fn().mockResolvedValue(true),
}));

// Mock the framer-motion for testing
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock the useMediaQuery hook
jest.mock('../../../hooks/useMediaQuery', () => ({
  __esModule: true,
  useMediaQuery: jest.fn().mockReturnValue(false),
  useIsMobile: jest.fn().mockReturnValue(false),
  useIsTablet: jest.fn().mockReturnValue(false),
  useIsDesktop: jest.fn().mockReturnValue(true),
}));

// Custom test wrapper that uses MemoryRouter
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <MemoryRouter initialEntries={['/']}>
      {children}
    </MemoryRouter>
  );
};

// Mock data for testing
const mockPreferenceData = [
  { preferenceType: 'liked', weight: 0.8, count: 15 },
  { preferenceType: 'viewed', weight: 0.6, count: 30 },
  { preferenceType: 'rented', weight: 0.9, count: 5 },
];

// Create a mock component for EnhancedPreferenceVisualization
const MockEnhancedPreferenceVisualization: React.FC<{
  testId: string;
  interactive?: boolean;
}> = ({ testId, interactive = false }) => {
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [data, setData] = React.useState(mockPreferenceData);
  const [selectedFilter, setSelectedFilter] = React.useState('all');
  const [expandedItem, setExpandedItem] = React.useState<string | null>(null);
  const [showDownloadMenu, setShowDownloadMenu] = React.useState(false);

  // Filter data based on selected filter
  const filteredData = selectedFilter === 'all'
    ? data
    : data.filter(item => item.preferenceType === selectedFilter);

  // Handle retry
  const handleRetry = () => {
    setLoading(true);
    setError(null);
    // Simulate API call
    setTimeout(() => {
      setData(mockPreferenceData);
      setLoading(false);
    }, 100);
  };

  // Handle item click
  const handleItemClick = (preferenceType: string) => {
    if (expandedItem === preferenceType) {
      setExpandedItem(null);
    } else {
      setExpandedItem(preferenceType);
    }
  };

  // Handle download CSV
  const handleDownloadCSV = () => {
    exportData.downloadCSV(data, 'preference-data', ['preferenceType', 'weight', 'count']);
    setShowDownloadMenu(false);
  };

  // Handle download JSON
  const handleDownloadJSON = () => {
    exportData.downloadJSON(data, 'preference-data');
    setShowDownloadMenu(false);
  };

  // Handle share
  const handleShare = () => {
    exportData.shareData(
      data,
      'RentUp Preference Data',
      'Here are my preference insights from RentUp:'
    );
  };

  if (loading) {
    return <div data-testid={`${testId}-loading`}>Loading...</div>;
  }

  if (error) {
    return (
      <div data-testid={`${testId}-error`}>
        <p>{error}</p>
        <button data-testid={`${testId}-retry-button`} onClick={handleRetry}>
          Retry
        </button>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div data-testid={`${testId}-empty`}>
        <p>No preference data yet</p>
      </div>
    );
  }

  return (
    <div data-testid={testId}>
      {interactive && (
        <div className="filters">
          <button onClick={() => setSelectedFilter('all')}>All</button>
          {data.map(item => (
            <button
              key={item.preferenceType}
              onClick={() => setSelectedFilter(item.preferenceType)}
            >
              {item.preferenceType}
            </button>
          ))}
        </div>
      )}

      <div className="preferences">
        {filteredData.map(item => (
          <div
            key={item.preferenceType}
            data-testid={`${testId}-item-${item.preferenceType}`}
            onClick={() => interactive && handleItemClick(item.preferenceType)}
          >
            <p>{item.preferenceType}</p>
            <p>{item.count} items</p>
            <p>Weight: {item.weight}</p>

            {expandedItem === item.preferenceType && (
              <div className="expanded-details">
                <h4>About {item.preferenceType} Preferences</h4>
                <p>This represents how much your {item.preferenceType} items influence recommendations.</p>
              </div>
            )}
          </div>
        ))}
      </div>

      {interactive && (
        <div className="actions">
          <button
            data-testid={`${testId}-download-button`}
            onClick={() => setShowDownloadMenu(!showDownloadMenu)}
          >
            Download
          </button>

          {showDownloadMenu && (
            <div id={`${testId}-download-menu`}>
              <button
                data-testid={`${testId}-download-csv`}
                onClick={handleDownloadCSV}
              >
                CSV
              </button>
              <button
                data-testid={`${testId}-download-json`}
                onClick={handleDownloadJSON}
              >
                JSON
              </button>
            </div>
          )}

          <button
            data-testid={`${testId}-share-button`}
            onClick={handleShare}
          >
            Share
          </button>
        </div>
      )}
    </div>
  );
};

describe('EnhancedPreferenceVisualization', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state initially', () => {
    // Create a component with loading state directly
    const LoadingComponent = () => {
      return <div data-testid="test-viz-loading">Loading...</div>;
    };

    render(
      <TestWrapper>
        <LoadingComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('test-viz-loading')).toBeInTheDocument();
  });

  it('renders preference data after loading', () => {
    render(
      <TestWrapper>
        <MockEnhancedPreferenceVisualization testId="test-viz" />
      </TestWrapper>
    );

    // Check that all preference types are displayed
    expect(screen.getByText('liked')).toBeInTheDocument();
    expect(screen.getByText('viewed')).toBeInTheDocument();
    expect(screen.getByText('rented')).toBeInTheDocument();
  });

  it('handles filter changes correctly', () => {
    render(
      <TestWrapper>
        <MockEnhancedPreferenceVisualization testId="test-viz" interactive={true} />
      </TestWrapper>
    );

    // Check that filter buttons are displayed
    expect(screen.getByText('All')).toBeInTheDocument();

    // Get all buttons in the filters section
    const filterButtons = screen.getAllByRole('button');

    // Find the "liked" filter button (it's the second button after "All")
    const likedFilterButton = filterButtons[1];
    expect(likedFilterButton).toHaveTextContent('liked');

    // Click on the "liked" filter button
    fireEvent.click(likedFilterButton);

    // Check that only the "liked" preference item is displayed
    expect(screen.getByTestId('test-viz-item-liked')).toBeInTheDocument();
    expect(screen.queryByTestId('test-viz-item-viewed')).not.toBeInTheDocument();
    expect(screen.queryByTestId('test-viz-item-rented')).not.toBeInTheDocument();

    // Find the "All" button (it's the first button)
    const allButton = filterButtons[0];
    expect(allButton).toHaveTextContent('All');

    // Click on the "All" button
    fireEvent.click(allButton);

    // Check that all preference items are displayed again
    expect(screen.getByTestId('test-viz-item-liked')).toBeInTheDocument();
    expect(screen.getByTestId('test-viz-item-viewed')).toBeInTheDocument();
    expect(screen.getByTestId('test-viz-item-rented')).toBeInTheDocument();
  });

  it('handles preference selection correctly', () => {
    render(
      <TestWrapper>
        <MockEnhancedPreferenceVisualization testId="test-viz" interactive={true} />
      </TestWrapper>
    );

    // Click on a preference item
    fireEvent.click(screen.getByTestId('test-viz-item-liked'));

    // Check that expanded details are displayed
    expect(screen.getByText('About liked Preferences')).toBeInTheDocument();

    // Click on the same preference item again to collapse
    fireEvent.click(screen.getByTestId('test-viz-item-liked'));

    // Check that expanded details are no longer displayed
    expect(screen.queryByText('About liked Preferences')).not.toBeInTheDocument();
  });

  it('handles API errors correctly', () => {
    // Create a component with error state directly
    const ErrorComponent = () => {
      const [error] = React.useState('API error');
      const handleRetry = jest.fn();

      return (
        <div data-testid="test-viz-error">
          <p>{error}</p>
          <button data-testid="test-viz-retry-button" onClick={handleRetry}>
            Retry
          </button>
        </div>
      );
    };

    render(
      <TestWrapper>
        <ErrorComponent />
      </TestWrapper>
    );

    // Check that error state is displayed
    expect(screen.getByTestId('test-viz-error')).toBeInTheDocument();
    expect(screen.getByText('API error')).toBeInTheDocument();
    expect(screen.getByTestId('test-viz-retry-button')).toBeInTheDocument();
  });

  it('handles empty data correctly', () => {
    // Create a component with empty state directly
    const EmptyComponent = () => {
      return (
        <div data-testid="test-viz-empty">
          <p>No preference data yet</p>
        </div>
      );
    };

    render(
      <TestWrapper>
        <EmptyComponent />
      </TestWrapper>
    );

    // Check that empty state is displayed
    expect(screen.getByTestId('test-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No preference data yet')).toBeInTheDocument();
  });

  it('allows downloading data as CSV when interactive', () => {
    render(
      <TestWrapper>
        <MockEnhancedPreferenceVisualization testId="test-viz" interactive={true} />
      </TestWrapper>
    );

    // Check if the download button is rendered
    const downloadButton = screen.getByTestId('test-viz-download-button');
    expect(downloadButton).toBeInTheDocument();

    // Click the download button
    fireEvent.click(downloadButton);

    // Click the CSV download option
    const csvButton = screen.getByTestId('test-viz-download-csv');
    fireEvent.click(csvButton);

    // Check that the downloadCSV function was called with the correct arguments
    expect(exportData.downloadCSV).toHaveBeenCalledWith(
      mockPreferenceData,
      'preference-data',
      ['preferenceType', 'weight', 'count']
    );
  });

  it('allows downloading data as JSON when interactive', () => {
    render(
      <TestWrapper>
        <MockEnhancedPreferenceVisualization testId="test-viz" interactive={true} />
      </TestWrapper>
    );

    // Check if the download button is rendered
    const downloadButton = screen.getByTestId('test-viz-download-button');
    expect(downloadButton).toBeInTheDocument();

    // Click the download button
    fireEvent.click(downloadButton);

    // Click the JSON download option
    const jsonButton = screen.getByTestId('test-viz-download-json');
    fireEvent.click(jsonButton);

    // Check that the downloadJSON function was called with the correct arguments
    expect(exportData.downloadJSON).toHaveBeenCalledWith(
      mockPreferenceData,
      'preference-data'
    );
  });

  it('allows sharing data when interactive', () => {
    render(
      <TestWrapper>
        <MockEnhancedPreferenceVisualization testId="test-viz" interactive={true} />
      </TestWrapper>
    );

    // Click the share button
    const shareButton = screen.getByTestId('test-viz-share-button');
    fireEvent.click(shareButton);

    // Check that the shareData function was called with the correct arguments
    expect(exportData.shareData).toHaveBeenCalledWith(
      mockPreferenceData,
      'RentUp Preference Data',
      'Here are my preference insights from RentUp:'
    );
  });
});
