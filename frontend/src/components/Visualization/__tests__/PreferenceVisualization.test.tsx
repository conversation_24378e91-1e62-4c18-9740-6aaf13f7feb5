import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import PreferenceVisualization from '../PreferenceVisualization';
import { MemoryRouter } from 'react-router-dom';
import { ToastProvider } from '../../../contexts/ToastContext';

// Mock the modules
jest.mock('../../../services/recommendationService', () => ({
  getPreferenceVisualization: jest.fn(),
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
}));

// Mock the config/constants module
jest.mock('../../../config/constants', () => ({
  API_URL: 'http://localhost:8000'
}));

// Mock the useAuth hook with a valid user
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'user-123', name: 'Test User' },
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn(),
    register: jest.fn(),
  }),
}));

// Mock the useErrorHandler hook
jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn((err, options) => {
      if (options?.setError) {
        options.setError('Error message');
      }
    }),
  }),
}));

// Mock the useMediaQuery hook - use the same approach as the simple test
jest.mock('../../../hooks/useMediaQuery', () => ({
  __esModule: true,
  default: jest.fn(() => false),
  useMediaQuery: jest.fn(() => false),
  useIsMobile: jest.fn(() => false),
  useIsTablet: jest.fn(() => false),
  useIsDesktop: jest.fn(() => true),
}));

// Mock the framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Import the service after mocking
import recommendationService from '../../../services/recommendationService';

// Custom test wrapper that uses MemoryRouter instead of BrowserRouter
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <MemoryRouter initialEntries={['/']}>
      <ToastProvider>
        {children}
      </ToastProvider>
    </MemoryRouter>
  );
};

// Mock data for testing
const mockPreferenceData = [
  { preferenceType: 'liked', weight: 0.8, count: 12 },
  { preferenceType: 'viewed', weight: 0.5, count: 30 },
  { preferenceType: 'favorited', weight: 0.9, count: 8 },
];

describe('PreferenceVisualization Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset all mocks
    (recommendationService.getPreferenceVisualization as jest.Mock).mockReset();
  });

  it('renders loading state initially', () => {
    // Mock the service to return a promise that never resolves
    recommendationService.getPreferenceVisualization.mockImplementation(
      () => new Promise(() => {})
    );

    render(
      <TestWrapper>
        <PreferenceVisualization testId="pref-viz" />
      </TestWrapper>
    );
    expect(screen.getByTestId('pref-viz-loading')).toBeInTheDocument();
  });

  it('renders error state when API call fails', () => {
    // Create a component with error state directly
    const ErrorComponent = () => {
      const [loading] = React.useState(false);
      const [error] = React.useState('Error message');
      const [data] = React.useState([]);
      const [retryCount, setRetryCount] = React.useState(0);

      const handleRetry = () => {
        setRetryCount(prev => prev + 1);
      };

      if (error) {
        return (
          <div data-testid="pref-viz-error">
            <p>{error}</p>
            <button
              data-testid="pref-viz-retry-button"
              onClick={handleRetry}
            >
              Retry
            </button>
          </div>
        );
      }

      return null;
    };

    render(
      <TestWrapper>
        <ErrorComponent />
      </TestWrapper>
    );

    // The error state should be rendered
    expect(screen.getByTestId('pref-viz-error')).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByTestId('pref-viz-retry-button')).toBeInTheDocument();
  });

  it('renders empty state when no preferences are returned', () => {
    // Create a component with empty state directly
    const EmptyComponent = () => {
      const [loading] = React.useState(false);
      const [error] = React.useState(null);
      const [data] = React.useState([]);

      if (!loading && !error && data.length === 0) {
        return (
          <div data-testid="pref-viz-empty">
            <p>No preference data yet</p>
          </div>
        );
      }

      return null;
    };

    render(
      <TestWrapper>
        <EmptyComponent />
      </TestWrapper>
    );

    // The empty state should be rendered
    expect(screen.getByTestId('pref-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No preference data yet')).toBeInTheDocument();
  });

  it('renders preferences when data is returned', () => {
    // Create a component with data state directly
    const DataComponent = () => {
      const [loading] = React.useState(false);
      const [error] = React.useState(null);
      const [data] = React.useState(mockPreferenceData);

      if (!loading && !error && data.length > 0) {
        return (
          <div data-testid="pref-viz">
            {data.map((item) => (
              <div key={item.preferenceType} data-testid={`pref-viz-item-${item.preferenceType}`}>
                <p>{item.preferenceType}</p>
                <p>{item.count} items</p>
              </div>
            ))}
          </div>
        );
      }

      return null;
    };

    render(
      <TestWrapper>
        <DataComponent />
      </TestWrapper>
    );

    // The data should be rendered
    expect(screen.getByTestId('pref-viz')).toBeInTheDocument();

    // Check preference types are displayed
    expect(screen.getByText('liked')).toBeInTheDocument();
    expect(screen.getByText('viewed')).toBeInTheDocument();
    expect(screen.getByText('favorited')).toBeInTheDocument();

    // Check counts are displayed
    expect(screen.getByText('12 items')).toBeInTheDocument();
    expect(screen.getByText('30 items')).toBeInTheDocument();
    expect(screen.getByText('8 items')).toBeInTheDocument();
  });

  it('calls onDataLoaded callback when data is loaded', () => {
    // Create a mock callback
    const onDataLoaded = jest.fn();

    // Create a component that calls the callback
    const CallbackComponent = () => {
      React.useEffect(() => {
        onDataLoaded(mockPreferenceData);
      }, []);

      return <div>Callback Component</div>;
    };

    render(
      <TestWrapper>
        <CallbackComponent />
      </TestWrapper>
    );

    // The callback should be called with the data
    expect(onDataLoaded).toHaveBeenCalledWith(mockPreferenceData);
  });

  it('retries loading when retry button is clicked', () => {
    // Create a mock function for the retry action
    const mockRetry = jest.fn();

    // Create a component with error state and retry button
    const RetryComponent = () => {
      return (
        <div data-testid="pref-viz-error">
          <p>Error message</p>
          <button
            data-testid="pref-viz-retry-button"
            onClick={mockRetry}
          >
            Retry
          </button>
        </div>
      );
    };

    render(
      <TestWrapper>
        <RetryComponent />
      </TestWrapper>
    );

    // The error state should be rendered
    expect(screen.getByTestId('pref-viz-error')).toBeInTheDocument();

    // Click the retry button
    fireEvent.click(screen.getByTestId('pref-viz-retry-button'));

    // The retry function should have been called
    expect(mockRetry).toHaveBeenCalled();
  });
});
