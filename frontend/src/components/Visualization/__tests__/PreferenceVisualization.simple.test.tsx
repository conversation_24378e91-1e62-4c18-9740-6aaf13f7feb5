import React from 'react';
import { render, screen, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import PreferenceVisualization from '../PreferenceVisualization';
import recommendationService from '../../../services/recommendationService';
import TestProviders from '../../../testing/TestProviders';

// Mock the modules
jest.mock('../../../services/recommendationService', () => ({
  getPreferenceVisualization: jest.fn(),
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
}));

// Mock the config/constants module
jest.mock('../../../config/constants', () => ({
  API_URL: 'http://localhost:8000'
}));

// Mock the useAuth hook with a valid user
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'user-123', name: 'Test User' },
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn(),
    register: jest.fn(),
  }),
}));

// Mock the useErrorHandler hook
jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn((err, options) => {
      if (options?.setError) {
        options.setError('Error message');
      }
    }),
  }),
}));

// Mock the useMediaQuery hook
jest.mock('../../../hooks/useMediaQuery', () => ({
  useMediaQuery: jest.fn(() => false),
}));

// Mock the framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock setTimeout to execute immediately in tests
jest.useFakeTimers();

// Mock data for testing
const mockPreferenceData = [
  { preferenceType: 'liked', weight: 0.8, count: 12 },
  { preferenceType: 'viewed', weight: 0.5, count: 30 },
  { preferenceType: 'favorited', weight: 0.9, count: 8 },
];

describe('PreferenceVisualization Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset all mocks
    (recommendationService.getPreferenceVisualization as jest.Mock).mockReset();
  });

  it('renders loading state initially', () => {
    // Mock the service to return a promise that never resolves
    recommendationService.getPreferenceVisualization.mockImplementation(
      () => new Promise(() => {})
    );

    render(
      <TestProviders>
        <PreferenceVisualization testId="pref-viz" />
      </TestProviders>
    );
    expect(screen.getByTestId('pref-viz-loading')).toBeInTheDocument();
    expect(screen.getByText('Loading preference data...')).toBeInTheDocument();
  });

  it('renders error state when API call fails', async () => {
    // Mock the service to return a rejected promise
    recommendationService.getPreferenceVisualization.mockRejectedValue(
      new Error('API Error')
    );

    render(
      <TestProviders>
        <PreferenceVisualization testId="pref-viz" />
      </TestProviders>
    );

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await act(async () => {
      await Promise.resolve();
      jest.advanceTimersByTime(1000);
      await Promise.resolve();
    });

    // The error state should be rendered
    expect(screen.getByTestId('pref-viz-error')).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  }, 15000);

  it('renders empty state when no preference data', async () => {
    // Mock the service to return an empty array
    recommendationService.getPreferenceVisualization.mockResolvedValue([]);

    render(
      <TestProviders>
        <PreferenceVisualization testId="pref-viz" />
      </TestProviders>
    );

    // Verify the API call was made
    expect(recommendationService.getPreferenceVisualization).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await act(async () => {
      await Promise.resolve();
      jest.advanceTimersByTime(1000);
      await Promise.resolve();
    });

    // The empty state should be rendered
    expect(screen.getByTestId('pref-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No preference data yet')).toBeInTheDocument();
  }, 15000);

  it('renders preference data correctly', async () => {
    // Mock the service to return the mock data
    recommendationService.getPreferenceVisualization.mockResolvedValue(mockPreferenceData);

    render(
      <TestProviders>
        <PreferenceVisualization testId="pref-viz" />
      </TestProviders>
    );

    // Verify the API call was made
    expect(recommendationService.getPreferenceVisualization).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await act(async () => {
      await Promise.resolve();
      jest.advanceTimersByTime(1000);
      await Promise.resolve();
    });

    // The data should be rendered
    expect(screen.getByTestId('pref-viz')).toBeInTheDocument();
    expect(screen.queryByTestId('pref-viz-loading')).not.toBeInTheDocument();

    // Check preference types are displayed
    expect(screen.getByText('liked')).toBeInTheDocument();
    expect(screen.getByText('viewed')).toBeInTheDocument();
    expect(screen.getByText('favorited')).toBeInTheDocument();

    // Check counts are displayed
    expect(screen.getByText('12 items')).toBeInTheDocument();
    expect(screen.getByText('30 items')).toBeInTheDocument();
    expect(screen.getByText('8 items')).toBeInTheDocument();

    // Check explanation text is present
    expect(screen.getByText('What This Means')).toBeInTheDocument();
  }, 15000);

  it('calls onDataLoaded callback when data is loaded', async () => {
    // Mock the service to return the mock data
    recommendationService.getPreferenceVisualization.mockResolvedValue(mockPreferenceData);

    const onDataLoaded = jest.fn();
    render(
      <TestProviders>
        <PreferenceVisualization testId="pref-viz" onDataLoaded={onDataLoaded} />
      </TestProviders>
    );

    // Verify the API call was made
    expect(recommendationService.getPreferenceVisualization).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await act(async () => {
      await Promise.resolve();
      jest.advanceTimersByTime(1000);
      await Promise.resolve();
    });

    // The callback should have been called
    expect(onDataLoaded).toHaveBeenCalledWith(mockPreferenceData);
  }, 15000);

  it('renders compact version when compact prop is true', async () => {
    // Mock the service to return the mock data
    recommendationService.getPreferenceVisualization.mockResolvedValue(mockPreferenceData);

    render(
      <TestProviders>
        <PreferenceVisualization testId="pref-viz" compact={true} />
      </TestProviders>
    );

    // Verify the API call was made
    expect(recommendationService.getPreferenceVisualization).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await act(async () => {
      await Promise.resolve();
      jest.advanceTimersByTime(1000);
      await Promise.resolve();
    });

    // The data should be rendered
    expect(screen.getByTestId('pref-viz')).toBeInTheDocument();
    expect(screen.queryByTestId('pref-viz-loading')).not.toBeInTheDocument();

    // The explanation section should not be present in compact mode
    expect(screen.queryByText('What This Means')).not.toBeInTheDocument();
  }, 15000);
});
