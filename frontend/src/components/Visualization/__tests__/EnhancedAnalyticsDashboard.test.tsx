import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import EnhancedAnalyticsDashboard from '../EnhancedAnalyticsDashboard';
import { useAuth } from '../../../hooks/useAuth';
import { useMediaQuery } from '../../../hooks/useMediaQuery';
import { useErrorHandler } from '../../../hooks/useErrorHandler';

// Mock the hooks
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: jest.fn(),
}));

jest.mock('../../../hooks/useMediaQuery', () => ({
  useMediaQuery: jest.fn(),
}));

jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: jest.fn(),
}));

jest.mock('../../../hooks/usePerformance', () => ({
  usePerformance: () => ({
    measureFunction: (name: string) => (fn: Function) => fn,
    measureApiCall: (name: string) => (fn: Function) => fn,
    measureEventHandler: (name: string) => (fn: Function) => fn,
  }),
}));

describe('EnhancedAnalyticsDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock useAuth
    (useAuth as jest.Mock).mockReturnValue({
      user: { id: 'test-user-id' },
    });
    
    // Mock useMediaQuery
    (useMediaQuery as jest.Mock).mockReturnValue(false);
    
    // Mock useErrorHandler
    (useErrorHandler as jest.Mock).mockReturnValue({
      handleError: jest.fn(),
    });
    
    // Mock fetch (used internally by the component)
    global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve({}),
      })
    ) as jest.Mock;
  });

  it('renders loading state initially', () => {
    render(
      <BrowserRouter>
        <EnhancedAnalyticsDashboard
          testId="enhanced-analytics"
        />
      </BrowserRouter>
    );
    
    expect(screen.getByTestId('enhanced-analytics-loading')).toBeInTheDocument();
  });

  it('renders analytics dashboard after loading', async () => {
    render(
      <BrowserRouter>
        <EnhancedAnalyticsDashboard
          testId="enhanced-analytics"
        />
      </BrowserRouter>
    );
    
    // Wait for the data to load
    await waitFor(() => {
      expect(screen.queryByTestId('enhanced-analytics-loading')).not.toBeInTheDocument();
    });
    
    // Check if the component renders the dashboard
    expect(screen.getByTestId('enhanced-analytics')).toBeInTheDocument();
    
    // Check if key metrics are displayed
    expect(screen.getByText('Click-through Rate')).toBeInTheDocument();
    expect(screen.getByText('Conversion Rate')).toBeInTheDocument();
    expect(screen.getByText('Satisfaction Score')).toBeInTheDocument();
    
    // Check if the performance trends section is displayed
    expect(screen.getByText('Performance Trends')).toBeInTheDocument();
    
    // Check if the category distribution section is displayed
    expect(screen.getByText('Category Distribution')).toBeInTheDocument();
    
    // Check if the top influencing factors section is displayed
    expect(screen.getByText('Top Influencing Factors')).toBeInTheDocument();
    
    // Check if the recommendation quality section is displayed
    expect(screen.getByText('Recommendation Quality')).toBeInTheDocument();
  });

  it('allows selecting different metrics', async () => {
    render(
      <BrowserRouter>
        <EnhancedAnalyticsDashboard
          testId="enhanced-analytics"
        />
      </BrowserRouter>
    );
    
    // Wait for the data to load
    await waitFor(() => {
      expect(screen.queryByTestId('enhanced-analytics-loading')).not.toBeInTheDocument();
    });
    
    // Find and click on the Conversion Rate metric
    const conversionRateMetric = screen.getByText('Conversion Rate').closest('div');
    if (conversionRateMetric) {
      fireEvent.click(conversionRateMetric);
    }
    
    // Check if the Conversion Rate metric is highlighted
    expect(conversionRateMetric?.className).toContain('bg-green-50');
    
    // Find and click on the Satisfaction Score metric
    const satisfactionScoreMetric = screen.getByText('Satisfaction Score').closest('div');
    if (satisfactionScoreMetric) {
      fireEvent.click(satisfactionScoreMetric);
    }
    
    // Check if the Satisfaction Score metric is highlighted
    expect(satisfactionScoreMetric?.className).toContain('bg-purple-50');
  });

  it('handles errors gracefully', async () => {
    // Mock the fetch to throw an error
    global.fetch = jest.fn(() =>
      Promise.reject(new Error('Test error'))
    ) as jest.Mock;
    
    render(
      <BrowserRouter>
        <EnhancedAnalyticsDashboard
          testId="enhanced-analytics"
        />
      </BrowserRouter>
    );
    
    // Wait for the error state to be displayed
    await waitFor(() => {
      expect(screen.queryByTestId('enhanced-analytics-loading')).not.toBeInTheDocument();
    });
    
    // Check if the error message is displayed
    expect(screen.getByTestId('enhanced-analytics-error')).toBeInTheDocument();
    expect(screen.getByText('Error loading analytics')).toBeInTheDocument();
    
    // Check if the retry button is displayed
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('respects the timePeriod prop', async () => {
    render(
      <BrowserRouter>
        <EnhancedAnalyticsDashboard
          testId="enhanced-analytics"
          timePeriod="3months"
        />
      </BrowserRouter>
    );
    
    // Wait for the data to load
    await waitFor(() => {
      expect(screen.queryByTestId('enhanced-analytics-loading')).not.toBeInTheDocument();
    });
    
    // Check if the component renders the dashboard
    expect(screen.getByTestId('enhanced-analytics')).toBeInTheDocument();
    
    // In a real implementation, we would verify that the data shown corresponds to the 3-month period
    // For this test, we'll just check that the component renders successfully
  });

  it('renders correctly on mobile', async () => {
    // Mock useMediaQuery to return true for mobile
    (useMediaQuery as jest.Mock).mockImplementation((query) => {
      if (query === '(max-width: 640px)') {
        return true;
      }
      return false;
    });
    
    render(
      <BrowserRouter>
        <EnhancedAnalyticsDashboard
          testId="enhanced-analytics"
        />
      </BrowserRouter>
    );
    
    // Wait for the data to load
    await waitFor(() => {
      expect(screen.queryByTestId('enhanced-analytics-loading')).not.toBeInTheDocument();
    });
    
    // Check if the component renders the dashboard
    expect(screen.getByTestId('enhanced-analytics')).toBeInTheDocument();
    
    // In a real implementation, we would verify that the mobile layout is used
    // For this test, we'll just check that the component renders successfully
  });
});
