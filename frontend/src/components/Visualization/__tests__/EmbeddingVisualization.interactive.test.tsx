import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import EmbeddingVisualization from '../EmbeddingVisualization';
import { useMediaQuery } from '../../../hooks/useMediaQuery';
import { useErrorHandler } from '../../../hooks/useErrorHandler';

// Mock the hooks
jest.mock('../../../hooks/useMediaQuery', () => ({
  useMediaQuery: jest.fn(),
}));

jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: jest.fn(),
}));

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve([
      {
        id: 'item-1',
        name: 'Test Item 1',
        category: 'apartment',
        x: 0.1,
        y: 0.2,
        price: 1000,
        rating: 4.5,
        location: 'Test Location 1',
      },
      {
        id: 'item-2',
        name: 'Test Item 2',
        category: 'house',
        x: 0.3,
        y: 0.4,
        price: 2000,
        rating: 4.0,
        location: 'Test Location 2',
      },
      {
        id: 'item-3',
        name: 'Test Item 3',
        category: 'condo',
        x: 0.5,
        y: 0.6,
        price: 1500,
        rating: 4.2,
        location: 'Test Location 3',
      },
    ]),
  })
) as jest.Mock;

describe('EmbeddingVisualization Interactive Features', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock useMediaQuery to return false for all media queries
    (useMediaQuery as jest.Mock).mockReturnValue(false);
    
    // Mock useErrorHandler
    (useErrorHandler as jest.Mock).mockReturnValue({
      handleError: jest.fn(),
    });
  });

  it('calls onItemSelect when an item is clicked', async () => {
    const onItemSelect = jest.fn();
    
    render(
      <BrowserRouter>
        <EmbeddingVisualization
          interactive={true}
          onItemSelect={onItemSelect}
          testId="embedding-viz"
        />
      </BrowserRouter>
    );
    
    // Wait for the visualization to load
    await waitFor(() => {
      expect(screen.queryByTestId('embedding-viz-loading')).not.toBeInTheDocument();
    });
    
    // Find and click on an item point
    const itemPoint = await screen.findByTestId('embedding-viz-point-item-1');
    fireEvent.click(itemPoint);
    
    // Check if onItemSelect was called with the correct item ID
    expect(onItemSelect).toHaveBeenCalledWith('item-1');
  });

  it('calls onCategorySelect when a category is selected', async () => {
    const onCategorySelect = jest.fn();
    
    render(
      <BrowserRouter>
        <EmbeddingVisualization
          interactive={true}
          onCategorySelect={onCategorySelect}
          testId="embedding-viz"
        />
      </BrowserRouter>
    );
    
    // Wait for the visualization to load
    await waitFor(() => {
      expect(screen.queryByTestId('embedding-viz-loading')).not.toBeInTheDocument();
    });
    
    // Find and click on a category button
    const categoryButton = await screen.findByTestId('embedding-viz-category-apartment');
    fireEvent.click(categoryButton);
    
    // Check if onCategorySelect was called with the correct category
    expect(onCategorySelect).toHaveBeenCalledWith('apartment');
  });

  it('calls onCategorySelect with null when "All Categories" is selected', async () => {
    const onCategorySelect = jest.fn();
    
    render(
      <BrowserRouter>
        <EmbeddingVisualization
          interactive={true}
          onCategorySelect={onCategorySelect}
          categoryId="apartment" // Start with a category selected
          testId="embedding-viz"
        />
      </BrowserRouter>
    );
    
    // Wait for the visualization to load
    await waitFor(() => {
      expect(screen.queryByTestId('embedding-viz-loading')).not.toBeInTheDocument();
    });
    
    // Find and click on the "All Categories" button
    const allCategoriesButton = await screen.findByTestId('embedding-viz-category-all');
    fireEvent.click(allCategoriesButton);
    
    // Check if onCategorySelect was called with null
    expect(onCategorySelect).toHaveBeenCalledWith(null);
  });

  it('highlights the selected item when itemId prop is provided', async () => {
    render(
      <BrowserRouter>
        <EmbeddingVisualization
          interactive={true}
          itemId="item-2"
          testId="embedding-viz"
        />
      </BrowserRouter>
    );
    
    // Wait for the visualization to load
    await waitFor(() => {
      expect(screen.queryByTestId('embedding-viz-loading')).not.toBeInTheDocument();
    });
    
    // Find the selected item point
    const selectedItemPoint = await screen.findByTestId('embedding-viz-point-item-2');
    
    // Check if the selected item has the correct styling (border)
    expect(selectedItemPoint).toHaveStyle('border: 2px solid #000');
  });

  it('filters by the selected category when categoryId prop is provided', async () => {
    render(
      <BrowserRouter>
        <EmbeddingVisualization
          interactive={true}
          categoryId="apartment"
          testId="embedding-viz"
        />
      </BrowserRouter>
    );
    
    // Wait for the visualization to load
    await waitFor(() => {
      expect(screen.queryByTestId('embedding-viz-loading')).not.toBeInTheDocument();
    });
    
    // Check if the category button is highlighted
    const categoryButton = await screen.findByTestId('embedding-viz-category-apartment');
    expect(categoryButton).toHaveAttribute('aria-checked', 'true');
  });

  it('supports zoom in and out functionality', async () => {
    render(
      <BrowserRouter>
        <EmbeddingVisualization
          interactive={true}
          testId="embedding-viz"
        />
      </BrowserRouter>
    );
    
    // Wait for the visualization to load
    await waitFor(() => {
      expect(screen.queryByTestId('embedding-viz-loading')).not.toBeInTheDocument();
    });
    
    // Find and click on the zoom in button
    const zoomInButton = screen.getByTestId('embedding-viz-zoom-in');
    fireEvent.click(zoomInButton);
    
    // Find and click on the zoom out button
    const zoomOutButton = screen.getByTestId('embedding-viz-zoom-out');
    fireEvent.click(zoomOutButton);
    
    // These assertions are limited since we can't easily test the visual changes
    // But we can at least verify the buttons are present and clickable
    expect(zoomInButton).toBeInTheDocument();
    expect(zoomOutButton).toBeInTheDocument();
  });

  it('shows tooltip when hovering over an item', async () => {
    render(
      <BrowserRouter>
        <EmbeddingVisualization
          interactive={true}
          testId="embedding-viz"
        />
      </BrowserRouter>
    );
    
    // Wait for the visualization to load
    await waitFor(() => {
      expect(screen.queryByTestId('embedding-viz-loading')).not.toBeInTheDocument();
    });
    
    // Find an item point
    const itemPoint = await screen.findByTestId('embedding-viz-point-item-1');
    
    // Hover over the item
    fireEvent.mouseEnter(itemPoint);
    
    // Check if the tooltip is shown
    const tooltip = await screen.findByTestId('embedding-viz-tooltip');
    expect(tooltip).toBeInTheDocument();
    expect(tooltip).toHaveTextContent('Test Item 1');
  });
});
