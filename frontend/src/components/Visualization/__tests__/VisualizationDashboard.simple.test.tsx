import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import VisualizationDashboard from '../VisualizationDashboard';

// Mock the dependencies
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'user-123' },
    isAuthenticated: true,
  }),
}));

jest.mock('../../../hooks/useMediaQuery', () => ({
  useMediaQuery: () => false,
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

jest.mock('react-router-dom', () => ({
  Link: ({ children, ...props }: any) => <a {...props}>{children}</a>,
}));

jest.mock('../../../services/recommendationService', () => ({
  getPreferenceVisualization: jest.fn(() => Promise.resolve([])),
  getEmbeddingVisualization: jest.fn(() => Promise.resolve([])),
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
}));

jest.mock('../PreferenceVisualization', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-preference-visualization">Mock Preference Visualization</div>,
}));

jest.mock('../EmbeddingVisualization', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-embedding-visualization">Mock Embedding Visualization</div>,
}));

describe('VisualizationDashboard Component (Simple Tests)', () => {
  it('renders the component with loading state', () => {
    render(<VisualizationDashboard testId="viz-dashboard" />);
    
    // Check that the loading state is displayed
    expect(screen.getByTestId('viz-dashboard-loading')).toBeInTheDocument();
    expect(screen.getByText('AI Recommendation Dashboard')).toBeInTheDocument();
  });
});
