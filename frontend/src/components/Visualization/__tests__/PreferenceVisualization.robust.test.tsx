import React from 'react';
import { render, screen, waitFor, fireEvent, act } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  useNavigate: () => jest.fn(),
  useLocation: () => ({ pathname: '/' }),
  useParams: () => ({}),
  Link: ({ children, to }: { children: React.ReactNode, to: string }) => <a href={to}>{children}</a>,
}));

// Mock the AuthContext
const mockUser = { id: 'user-123' };
const mockAuth = {
  user: mockUser,
  isAuthenticated: true,
  login: jest.fn(),
  logout: jest.fn(),
  register: jest.fn(),
};

jest.mock('../../../contexts/AuthContext', () => ({
  useAuth: () => mockAuth,
  AuthProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

// Mock the useAuth hook
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => mockAuth,
}));

// Create a mock handleError function
const mockHandleError = jest.fn((err, options) => {
  if (options?.setError) {
    options.setError('Error message');
  }
});

// Mock the useErrorHandler hook
jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: mockHandleError,
  }),
}));

// Mock the useMediaQuery hook
jest.mock('../../../hooks/useMediaQuery', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => false),
  useMediaQuery: jest.fn().mockImplementation(() => false),
  useIsMobile: jest.fn().mockImplementation(() => false),
  useIsTablet: jest.fn().mockImplementation(() => false),
  useIsDesktop: jest.fn().mockImplementation(() => true),
}));

// Mock the framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, whileTap, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, whileTap, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

import PreferenceVisualization from '../PreferenceVisualization';

// Import testing utilities
import { testAccessibility } from '../../../testing/utils/accessibilityTest';
import { testResponsive } from '../../../testing/utils/responsiveTest';
import { testInteractions } from '../../../testing/utils/interactionTest';

// Import custom matchers
import '../../../testing/matchers/visualizationMatchers';

// Mock data for testing
const mockPreferenceData = [
  { preferenceType: 'liked', weight: 0.8, count: 10 },
  { preferenceType: 'viewed', weight: 0.5, count: 20 },
  { preferenceType: 'favorited', weight: 0.9, count: 5 },
];

// Create a mock implementation of the recommendation service
const mockGetPreferenceVisualization = jest.fn();

// Mock the recommendation service
jest.mock('../../../services/recommendationService', () => {
  return {
    __esModule: true,
    default: {
      getPreferenceVisualization: mockGetPreferenceVisualization,
      getApiUrl: () => 'http://localhost:8000',
    },
  };
});

describe('PreferenceVisualization Component (Robust Tests)', () => {
  // Reset all mocks after each test
  afterEach(() => {
    jest.clearAllMocks();
  });

  // Basic rendering tests
  describe('Rendering', () => {
    it('renders error state with proper message', async () => {
      // Mock the service to return a rejected promise with a specific error
      mockGetPreferenceVisualization.mockImplementation(() => Promise.reject(new Error('Custom API error')));

      // Render the component with act to handle async updates
      await act(async () => {
        render(<PreferenceVisualization testId="pref-viz" />);
      });

      // Check that the error state is rendered with the expected message
      expect(screen.getByTestId('pref-viz-error')).toBeInTheDocument();
      expect(screen.getByText('Error message')).toBeInTheDocument();
    });

    it('renders error state when API call fails', async () => {
      // Mock the service to return a rejected promise
      mockGetPreferenceVisualization.mockImplementation(() => Promise.reject(new Error('API error')));

      // Render the component with act to handle async updates
      await act(async () => {
        render(<PreferenceVisualization testId="pref-viz" />);
      });

      // Check that the error state is rendered
      expect(screen.getByTestId('pref-viz-error')).toBeInTheDocument();
      expect(screen.getByText('Error message')).toBeInTheDocument();
      expect(screen.getByTestId('pref-viz-retry-button')).toBeInTheDocument();
    });
  });

  // User interaction tests
  describe('User Interactions', () => {
    it('has a retry button in error state', async () => {
      // Mock the service to return a rejected promise
      mockGetPreferenceVisualization.mockImplementation(() => Promise.reject(new Error('API error')));

      // Render the component with act to handle async updates
      await act(async () => {
        render(<PreferenceVisualization testId="pref-viz" />);
      });

      // Check that the error state is rendered with a retry button
      expect(screen.getByTestId('pref-viz-error')).toBeInTheDocument();
      expect(screen.getByTestId('pref-viz-retry-button')).toBeInTheDocument();

      // Verify the button has the correct text
      expect(screen.getByTestId('pref-viz-retry-button')).toHaveTextContent('Retry');
    });
  });



  // Accessibility tests
  describe('Accessibility', () => {
    it('has proper ARIA attributes in error state', async () => {
      // Mock the service to return a rejected promise
      mockGetPreferenceVisualization.mockImplementation(() => Promise.reject(new Error('API error')));

      // Render the component with act to handle async updates
      await act(async () => {
        render(<PreferenceVisualization testId="pref-viz" />);
      });

      // Check that the component has proper ARIA attributes
      expect(screen.getByRole('alert')).toBeInTheDocument();
      expect(screen.getByRole('heading', { name: 'Your Preferences' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Retry loading preferences' })).toBeInTheDocument();
    });
  });

  // Responsive design tests
  describe('Responsive Design', () => {
    it('renders in compact mode when compact prop is true', async () => {
      // Mock the service to return a rejected promise (we're just testing the error state UI)
      mockGetPreferenceVisualization.mockImplementation(() => Promise.reject(new Error('API error')));

      // Render the component with act to handle async updates
      await act(async () => {
        render(<PreferenceVisualization testId="pref-viz" compact={true} />);
      });

      // Check that the error state is rendered
      expect(screen.getByTestId('pref-viz-error')).toBeInTheDocument();
    });
  });
});
