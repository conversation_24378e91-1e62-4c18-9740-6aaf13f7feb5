import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmbeddingVisualization from '../EmbeddingVisualization';
import TestProviders from '../../../testing/TestProviders';
import * as useErrorHandlerModule from '../../../hooks/useErrorHandler';

// Mock the recommendationService
jest.mock('../../../services/recommendationService', () => ({
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
  getEmbeddingVisualization: jest.fn(),
}));

// Mock the useErrorHandler hook
jest.mock('../../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn((err, options) => {
      if (options?.setError) {
        options.setError('Error message');
      }
    }),
  }),
}));

// Mock the useMediaQuery hook
jest.mock('../../../hooks/useMediaQuery', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => false),
    useMediaQuery: jest.fn().mockImplementation(() => false),
    useIsMobile: jest.fn().mockImplementation(() => false),
    useIsTablet: jest.fn().mockImplementation(() => false),
    useIsDesktop: jest.fn().mockImplementation(() => true),
  };
});

// Mock the framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock data for testing
const mockEmbeddingData = [
  { id: 'item1', name: 'Item 1', x: 0.2, y: 0.3, category: 'electronics' },
  { id: 'item2', name: 'Item 2', x: 0.5, y: 0.7, category: 'furniture' },
  { id: 'item3', name: 'Item 3', x: 0.8, y: 0.1, category: 'clothing' },
];

// Mock fetch
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock setTimeout to execute immediately in tests
jest.useFakeTimers();

// Mock implementation for fetch that resolves immediately
const createMockResponse = (data, ok = true, status = 200) => {
  return Promise.resolve({
    ok,
    status,
    json: () => Promise.resolve(data),
  });
};

describe('EmbeddingVisualization Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset timers
    jest.clearAllTimers();
    // Default mock implementation for fetch
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve([]),
      })
    );
  });

  afterEach(() => {
    // Advance timers to complete any pending timeouts
    jest.runAllTimers();
  });

  it('renders loading state initially', () => {
    // Mock fetch to never resolve
    mockFetch.mockImplementation(() => new Promise(() => {}));

    render(
      <TestProviders>
        <EmbeddingVisualization testId="embed-viz" />
      </TestProviders>
    );
    expect(screen.getByTestId('embed-viz-loading')).toBeInTheDocument();
  });

  it('renders error state when API call fails', async () => {
    // Mock the handleError function to set the error state immediately
    jest.spyOn(require('../../../hooks/useErrorHandler'), 'useErrorHandler').mockImplementation(() => ({
      handleError: jest.fn((err, options) => {
        if (options?.setError) {
          options.setError('Error message');
        }
      }),
    }));

    // Mock fetch to return an error response
    mockFetch.mockImplementation(() => createMockResponse(null, false, 500));

    render(
      <TestProviders>
        <EmbeddingVisualization testId="embed-viz" />
      </TestProviders>
    );

    // Wait for the fetch to be called
    expect(mockFetch).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await Promise.resolve();
    jest.advanceTimersByTime(1000);
    await Promise.resolve();

    // The error state should be rendered
    expect(screen.getByTestId('embed-viz-error')).toBeInTheDocument();
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  }, 15000);

  it('renders empty state when no embedding data', async () => {
    // Mock fetch to return an empty array
    mockFetch.mockImplementation(() => createMockResponse([]));

    render(
      <TestProviders>
        <EmbeddingVisualization testId="embed-viz" />
      </TestProviders>
    );

    // Wait for the fetch to be called
    expect(mockFetch).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await Promise.resolve();
    jest.advanceTimersByTime(1000);
    await Promise.resolve();

    // The empty state should be rendered
    expect(screen.getByTestId('embed-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No data available')).toBeInTheDocument();
  }, 15000);

  it('renders embedding data correctly', async () => {
    // Mock fetch to return embedding data
    mockFetch.mockImplementation(() => createMockResponse(mockEmbeddingData));

    render(
      <TestProviders>
        <EmbeddingVisualization testId="embed-viz" />
      </TestProviders>
    );

    // Wait for the fetch to be called
    expect(mockFetch).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await Promise.resolve();
    jest.advanceTimersByTime(1000);
    await Promise.resolve();

    // The visualization should be rendered
    expect(screen.getByTestId('embed-viz')).toBeInTheDocument();

    // Check category filter buttons
    expect(screen.getByText('All Categories')).toBeInTheDocument();
    expect(screen.getByText('electronics')).toBeInTheDocument();
    expect(screen.getByText('furniture')).toBeInTheDocument();
    expect(screen.getByText('clothing')).toBeInTheDocument();

    // Check points are rendered (using data-testid)
    expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item2')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item3')).toBeInTheDocument();

    // Check explanation text is present
    expect(screen.getByText('Item Relationships')).toBeInTheDocument();
  }, 15000);

  it('calls onDataLoaded callback when data is loaded', async () => {
    // Mock fetch to return embedding data
    mockFetch.mockImplementation(() => createMockResponse(mockEmbeddingData));

    const onDataLoaded = jest.fn();
    render(
      <TestProviders>
        <EmbeddingVisualization testId="embed-viz" onDataLoaded={onDataLoaded} />
      </TestProviders>
    );

    // Wait for the fetch to be called
    expect(mockFetch).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await Promise.resolve();
    jest.advanceTimersByTime(1000);
    await Promise.resolve();

    // The callback should have been called
    expect(onDataLoaded).toHaveBeenCalledWith(mockEmbeddingData);
  }, 15000);

  it('filters by category when category button is clicked', async () => {
    // Mock fetch to return embedding data
    mockFetch.mockImplementation(() => createMockResponse(mockEmbeddingData));

    render(
      <TestProviders>
        <EmbeddingVisualization testId="embed-viz" />
      </TestProviders>
    );

    // Wait for the fetch to be called
    expect(mockFetch).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await Promise.resolve();
    jest.advanceTimersByTime(1000);
    await Promise.resolve();

    // The visualization should be rendered
    expect(screen.getByTestId('embed-viz')).toBeInTheDocument();

    // Click on the electronics category button
    fireEvent.click(screen.getByTestId('embed-viz-category-electronics'));

    // Only electronics item should be visible (others filtered out)
    expect(screen.getByTestId('embed-viz-point-item1')).toHaveAttribute('data-category', 'electronics');
  }, 15000);

  it('zooms in and out when zoom buttons are clicked', async () => {
    // Mock fetch to return embedding data
    mockFetch.mockImplementation(() => createMockResponse(mockEmbeddingData));

    render(
      <TestProviders>
        <EmbeddingVisualization testId="embed-viz" />
      </TestProviders>
    );

    // Wait for the fetch to be called
    expect(mockFetch).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await Promise.resolve();
    jest.advanceTimersByTime(1000);
    await Promise.resolve();

    // The visualization should be rendered
    expect(screen.getByTestId('embed-viz')).toBeInTheDocument();

    // Get zoom buttons
    const zoomInButton = screen.getByTestId('embed-viz-zoom-in');
    const zoomOutButton = screen.getByTestId('embed-viz-zoom-out');

    // Click zoom in
    fireEvent.click(zoomInButton);

    // Click zoom out
    fireEvent.click(zoomOutButton);

    // Just checking that the buttons exist and are clickable
    expect(zoomInButton).toBeInTheDocument();
    expect(zoomOutButton).toBeInTheDocument();
  }, 15000);

  it('renders compact version when compact prop is true', async () => {
    // Mock fetch to return embedding data
    mockFetch.mockImplementation(() => createMockResponse(mockEmbeddingData));

    render(
      <TestProviders>
        <EmbeddingVisualization testId="embed-viz" compact={true} />
      </TestProviders>
    );

    // Wait for the fetch to be called
    expect(mockFetch).toHaveBeenCalled();

    // Run all timers to resolve promises
    jest.runAllTimers();

    // Advance timers and flush promises
    await Promise.resolve();
    jest.advanceTimersByTime(1000);
    await Promise.resolve();

    // The visualization should be rendered
    expect(screen.getByTestId('embed-viz')).toBeInTheDocument();

    // The legend should not be present in compact mode
    expect(screen.queryByTestId('embed-viz-legend')).not.toBeInTheDocument();
  }, 15000);
});
