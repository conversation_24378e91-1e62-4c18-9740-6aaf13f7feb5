import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import VisualizationDashboard from '../VisualizationDashboard';
import recommendationService from '../../../services/recommendationService';
import TestProviders from '../../../testing/TestProviders';

// Mock the useAuth hook
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    user: { id: 'user-123' },
    isAuthenticated: true,
    login: jest.fn(),
    logout: jest.fn(),
    register: jest.fn(),
  }),
}));

// Mock the useMediaQuery hook
jest.mock('../../../hooks/useMediaQuery', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => false),
    useMediaQuery: jest.fn().mockImplementation(() => false),
    useIsMobile: jest.fn().mockImplementation(() => false),
    useIsTablet: jest.fn().mockImplementation(() => false),
    useIsDesktop: jest.fn().mockImplementation(() => true),
  };
});

// Mock the framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, whileTap, whileHover, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, whileTap, whileHover, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

// Mock the recommendation service
jest.mock('../../../services/recommendationService', () => ({
  getPreferenceVisualization: jest.fn(),
  getEmbeddingVisualization: jest.fn(),
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
}));

// Mock the Link component from react-router-dom
jest.mock('react-router-dom', () => ({
  Link: ({ children, to, className, 'data-testid': testId }) => (
    <a href={to} className={className} data-testid={testId}>
      {children}
    </a>
  ),
}));

// Mock fetch for EmbeddingVisualization
const mockFetch = jest.fn();
global.fetch = mockFetch;

// Mock data
const mockPreferenceData = [
  { preferenceType: 'liked', weight: 0.8, count: 12 },
  { preferenceType: 'viewed', weight: 0.5, count: 30 },
  { preferenceType: 'favorited', weight: 0.9, count: 8 },
];

const mockEmbeddingData = [
  { id: 'item1', name: 'Item 1', x: 0.2, y: 0.3, category: 'electronics' },
  { id: 'item2', name: 'Item 2', x: 0.5, y: 0.7, category: 'furniture' },
  { id: 'item3', name: 'Item 3', x: 0.8, y: 0.1, category: 'clothing' },
];

describe('VisualizationDashboard Component', () => {
  // Setup before each test
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock the preference visualization data
    recommendationService.getPreferenceVisualization.mockResolvedValue(mockPreferenceData);

    // Mock the embedding visualization data
    recommendationService.getEmbeddingVisualization.mockResolvedValue(mockEmbeddingData);

    // Mock the fetch response
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockEmbeddingData),
      })
    );
  });

  // Cleanup after all tests
  afterAll(() => {
    jest.restoreAllMocks();
  });

  // Tests for the VisualizationDashboard component

  it('renders loading state initially', () => {
    render(
      <TestProviders>
        <VisualizationDashboard testId="viz-dashboard" />
      </TestProviders>
    );

    // Check that the loading state is displayed
    expect(screen.getByTestId('viz-dashboard-loading')).toBeInTheDocument();
    expect(screen.getByText('AI Recommendation Dashboard')).toBeInTheDocument();
  });

  it('renders dashboard with data after loading', async () => {
    render(
      <TestProviders>
        <VisualizationDashboard testId="viz-dashboard" />
      </TestProviders>
    );

    // Wait for the data to load
    await waitFor(() => {
      expect(screen.queryByTestId('viz-dashboard-loading')).not.toBeInTheDocument();
    });

    // Check that the dashboard is displayed
    expect(screen.getByTestId('viz-dashboard')).toBeInTheDocument();
    expect(screen.getByText('AI Recommendation Dashboard')).toBeInTheDocument();

    // Check that the summary cards are displayed
    expect(screen.getByText('Recommendation Strength')).toBeInTheDocument();
    expect(screen.getByText('Dominant Preference')).toBeInTheDocument();
    expect(screen.getByText('Similar Items')).toBeInTheDocument();

    // Check that the visualizations are displayed
    expect(screen.getByText('Your Preference Profile')).toBeInTheDocument();
    expect(screen.getByText('Item Relationships')).toBeInTheDocument();

    // Check that the activity and recommendations are displayed
    expect(screen.getByText('Recently Viewed Items')).toBeInTheDocument();
    expect(screen.getByText('Top Recommendations')).toBeInTheDocument();
  });

  it('renders error state when API calls fail', async () => {
    // Mock the preference visualization data to reject
    recommendationService.getPreferenceVisualization.mockRejectedValue(new Error('API error'));

    render(
      <TestProviders>
        <VisualizationDashboard testId="viz-dashboard" />
      </TestProviders>
    );

    // Wait for the error state to be displayed
    await waitFor(() => {
      expect(screen.getByTestId('viz-dashboard-error')).toBeInTheDocument();
    });

    // Check that the error message is displayed
    expect(screen.getByText('Failed to load visualization data. Please try again.')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('calculates recommendation strength correctly', async () => {
    render(
      <TestProviders>
        <VisualizationDashboard testId="viz-dashboard" />
      </TestProviders>
    );

    // Wait for the data to load
    await waitFor(() => {
      expect(screen.queryByTestId('viz-dashboard-loading')).not.toBeInTheDocument();
    });

    // Calculate the expected recommendation strength
    const totalWeight = mockPreferenceData.reduce((sum, pref) => sum + pref.weight, 0);
    const avgWeight = totalWeight / mockPreferenceData.length;
    const expectedStrength = Math.min(Math.round(avgWeight * 100), 100);

    // Check that the recommendation strength is displayed correctly
    expect(screen.getByText(`${expectedStrength}%`)).toBeInTheDocument();
  });

  it('identifies dominant preference correctly', async () => {
    render(
      <TestProviders>
        <VisualizationDashboard testId="viz-dashboard" />
      </TestProviders>
    );

    // Wait for the data to load
    await waitFor(() => {
      expect(screen.queryByTestId('viz-dashboard-loading')).not.toBeInTheDocument();
    });

    // Identify the expected dominant preference
    const sorted = [...mockPreferenceData].sort((a, b) => b.weight - a.weight);
    const expectedDominantPreference = sorted[0]?.preferenceType;

    // Check that the dominant preference is displayed correctly
    expect(screen.getByText(expectedDominantPreference, { exact: false })).toBeInTheDocument();
  });
});
