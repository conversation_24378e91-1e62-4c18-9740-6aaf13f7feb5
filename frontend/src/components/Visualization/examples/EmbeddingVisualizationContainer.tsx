import React, { useState, useEffect, useCallback } from 'react';
import { useErrorHandler } from '../../../hooks/useErrorHandler';
import recommendationService, { EmbeddingPoint } from '../../../services/recommendationService';
import EmbeddingVisualizationPresenter from './EmbeddingVisualizationPresenter';
import { useIsMobile } from '../../../hooks/useMediaQuery';

interface EmbeddingVisualizationContainerProps {
  itemId?: string;
  categoryId?: string;
  limit?: number;
  className?: string;
  compact?: boolean;
  width?: number;
  height?: number;
  onDataLoaded?: (data: EmbeddingPoint[]) => void;
  testId?: string;
}

/**
 * Container component for embedding visualization
 * This component handles data fetching and state management
 * It delegates rendering to the presenter component
 */
const EmbeddingVisualizationContainer: React.FC<EmbeddingVisualizationContainerProps> = ({
  itemId,
  categoryId,
  limit = 100,
  className = '',
  compact = false,
  width: propWidth,
  height: propHeight,
  onDataLoaded,
  testId = 'embed-viz',
}) => {
  // Responsive dimensions
  const isMobile = useIsMobile();
  const width = propWidth || (isMobile ? 320 : 600);
  const height = propHeight || (compact ? 320 : 400);

  // State
  const [embeddings, setEmbeddings] = useState<EmbeddingPoint[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(categoryId || null);
  const [selectedItem, setSelectedItem] = useState<string | null>(itemId || null);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [retryCount, setRetryCount] = useState<number>(0);

  // Error handling
  const { handleError } = useErrorHandler();

  // Function to fetch embeddings
  const fetchEmbeddings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate inputs
      if (limit < 1 || limit > 200) {
        throw new Error('Invalid limit parameter');
      }

      // Build query parameters
      const params = new URLSearchParams();
      if (itemId) params.append('item_id', encodeURIComponent(itemId));
      if (selectedCategory) params.append('category_id', encodeURIComponent(selectedCategory));
      if (limit) params.append('limit', limit.toString());

      // Create a timeout for the request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);

      try {
        const response = await fetch(`${recommendationService.getApiUrl()}/api/v1/embeddings/visualization?${params.toString()}`, {
          method: 'GET',
          signal: controller.signal,
          headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
          },
          credentials: 'same-origin'
        });

        clearTimeout(timeoutId);

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Parse and validate the response
        const data = await response.json();

        // Validate the data structure
        if (!Array.isArray(data)) {
          throw new Error('Invalid response format');
        }

        // Validate each point
        const validatedData = data.filter((point: any) =>
          typeof point.id === 'string' &&
          typeof point.name === 'string' &&
          typeof point.x === 'number' &&
          typeof point.y === 'number' &&
          typeof point.category === 'string'
        );

        // Call the callback if provided
        if (onDataLoaded) {
          onDataLoaded(validatedData);
        }

        setEmbeddings(validatedData);
        setLoading(false);
      } catch (err) {
        clearTimeout(timeoutId);
        throw err;
      }
    } catch (err) {
      handleError(err, {
        friendlyMessage: 'Failed to load visualization. Please try again.',
        logMessage: 'Error fetching embedding data:',
        setError: setError,
      });
      setLoading(false);
    }
  }, [itemId, selectedCategory, limit, handleError, onDataLoaded]);

  // Fetch embeddings on component mount and when dependencies change
  useEffect(() => {
    fetchEmbeddings();
  }, [fetchEmbeddings, retryCount]);

  // Update selected item when itemId prop changes
  useEffect(() => {
    setSelectedItem(itemId || null);
  }, [itemId]);

  // Update selected category when categoryId prop changes
  useEffect(() => {
    setSelectedCategory(categoryId || null);
  }, [categoryId]);

  // Handlers for presenter component
  const handleCategorySelect = (category: string | null) => {
    setSelectedCategory(category);
  };

  const handleItemSelect = (item: string | null) => {
    setSelectedItem(item === selectedItem ? null : item);
  };

  const handleItemHover = (item: string | null) => {
    setHoveredItem(item);
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.2, 2));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.2, 0.5));
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };

  // Render the presenter component with all the props it needs
  return (
    <EmbeddingVisualizationPresenter
      // Data
      embeddings={embeddings}
      selectedCategory={selectedCategory}
      selectedItem={selectedItem}
      hoveredItem={hoveredItem}
      
      // UI state
      loading={loading}
      error={error}
      zoomLevel={zoomLevel}
      
      // Dimensions
      width={width}
      height={height}
      
      // Styling
      className={className}
      compact={compact}
      
      // Callbacks
      onCategorySelect={handleCategorySelect}
      onItemSelect={handleItemSelect}
      onItemHover={handleItemHover}
      onZoomIn={handleZoomIn}
      onZoomOut={handleZoomOut}
      onRetry={handleRetry}
      
      // Testing
      testId={testId}
    />
  );
};

export default EmbeddingVisualizationContainer;
