import React from 'react';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SimpleVisualization from '../../SimpleVisualization';
import mockRecommendationService from '../../../../services/testing/mockRecommendationService';

// Import all testing utilities
import {
  setupVisualizationTest,
  resetVisualizationTest,
  testAccessibility,
  testAccessibilityInStates,
  testResponsive,
  renderAtBreakpoint,
  testPerformance,
  testPerformanceWithProps,
  testInteractions,
} from '../../../../testing/utils';

// Import custom matchers
import '../../../../testing/matchers/visualizationMatchers';

describe('Comprehensive Test Example', () => {
  // Reset all mocks after each test
  afterEach(() => {
    resetVisualizationTest();
  });

  // Basic rendering tests
  describe('Rendering', () => {
    it('renders in all states', () => {
      // Test loading state
      setupVisualizationTest(
        SimpleVisualization,
        {
          componentProps: {
            testId: 'simple-viz',
            state: 'loading',
          },
        }
      );
      expect('simple-viz').toBeInLoadingState();
      
      // Test error state
      setupVisualizationTest(
        SimpleVisualization,
        {
          componentProps: {
            testId: 'simple-viz',
            state: 'error',
          },
        }
      );
      expect('simple-viz').toBeInErrorState();
      
      // Test empty state
      setupVisualizationTest(
        SimpleVisualization,
        {
          componentProps: {
            testId: 'simple-viz',
            state: 'empty',
          },
        }
      );
      expect('simple-viz').toBeInEmptyState();
      
      // Test data state
      setupVisualizationTest(
        SimpleVisualization,
        {
          componentProps: {
            testId: 'simple-viz',
            state: 'data',
            data: mockRecommendationService.mockEmbeddingData,
          },
        }
      );
      expect('simple-viz').toBeInDataState();
    });
  });

  // Accessibility tests
  describe('Accessibility', () => {
    it('passes accessibility tests in all states', async () => {
      await testAccessibilityInStates(
        SimpleVisualization,
        {
          loading: { testId: 'simple-viz', state: 'loading' },
          error: { testId: 'simple-viz', state: 'error' },
          empty: { testId: 'simple-viz', state: 'empty' },
          data: { 
            testId: 'simple-viz', 
            state: 'data', 
            data: mockRecommendationService.mockEmbeddingData,
          },
        }
      );
    });
  });

  // Responsive design tests
  describe('Responsive Design', () => {
    it('adapts to different screen sizes', async () => {
      await testResponsive({
        component: (
          <SimpleVisualization 
            testId="simple-viz" 
            state="data" 
            data={mockRecommendationService.mockEmbeddingData} 
          />
        ),
        breakpoints: ['xs', 'md', 'xl'],
        callback: (result, breakpoint) => {
          // Check that the component renders in all breakpoints
          expect(screen.getByTestId('simple-viz')).toBeInTheDocument();
          
          // On mobile (xs), we might expect a more compact view
          if (breakpoint === 'xs') {
            // In a real test, we would check for mobile-specific elements
            // For this example, we'll just check that the component renders
            expect(screen.getByTestId('simple-viz')).toBeInTheDocument();
          }
          
          // On desktop (xl), we might expect a more detailed view
          if (breakpoint === 'xl') {
            // In a real test, we would check for desktop-specific elements
            // For this example, we'll just check that the component renders
            expect(screen.getByTestId('simple-viz')).toBeInTheDocument();
          }
        },
      });
    });

    it('renders correctly on mobile', () => {
      const { getByTestId } = renderAtBreakpoint(
        'xs',
        <SimpleVisualization 
          testId="simple-viz" 
          state="data" 
          data={mockRecommendationService.mockEmbeddingData} 
        />
      );
      
      // Check that the component renders on mobile
      expect(getByTestId('simple-viz')).toBeInTheDocument();
    });
  });

  // Performance tests
  describe('Performance', () => {
    it('renders efficiently', () => {
      const results = testPerformance({
        component: (
          <SimpleVisualization 
            testId="simple-viz" 
            state="data" 
            data={mockRecommendationService.mockEmbeddingData} 
          />
        ),
        iterations: 10, // Reduced for example
        maxAverageRenderTime: 100, // Increased for example
      });
      
      // Check that the component renders efficiently
      expect(results.passed).toBe(true);
    });

    it('renders efficiently with different props', () => {
      const results = testPerformanceWithProps(
        SimpleVisualization,
        {
          loading: { testId: 'simple-viz', state: 'loading' },
          error: { testId: 'simple-viz', state: 'error' },
          empty: { testId: 'simple-viz', state: 'empty' },
          data: { 
            testId: 'simple-viz', 
            state: 'data', 
            data: mockRecommendationService.mockEmbeddingData,
          },
        },
        { iterations: 5 } // Reduced for example
      );
      
      // Check that the component renders efficiently in all states
      expect(results.loading.passed).toBe(true);
      expect(results.error.passed).toBe(true);
      expect(results.empty.passed).toBe(true);
      expect(results.data.passed).toBe(true);
    });
  });

  // User interaction tests
  describe('User Interactions', () => {
    it('handles user interactions correctly', async () => {
      await testInteractions({
        component: (
          <SimpleVisualization 
            testId="simple-viz" 
            state="error" 
            onRetry={() => {}} 
          />
        ),
        interactions: [
          {
            type: 'click',
            testId: 'simple-viz-retry-button',
            description: 'Click the retry button',
            callback: () => {
              // In a real test, we would check that the retry callback was called
              // For this example, we'll just check that the button exists
              expect(screen.getByTestId('simple-viz-retry-button')).toBeInTheDocument();
            },
          },
        ],
      });
    });
  });

  // Comprehensive test that combines all the above
  describe('Comprehensive', () => {
    it('passes all tests', async () => {
      // 1. Render the component
      setupVisualizationTest(
        SimpleVisualization,
        {
          componentProps: {
            testId: 'simple-viz',
            state: 'data',
            data: mockRecommendationService.mockEmbeddingData,
          },
        }
      );
      
      // 2. Check that it renders correctly
      expect('simple-viz').toBeInDataState();
      expect('simple-viz').toHavePoint('item1');
      expect('simple-viz').toHaveCategory('electronics');
      
      // 3. Check accessibility
      await testAccessibility({
        component: (
          <SimpleVisualization 
            testId="simple-viz" 
            state="data" 
            data={mockRecommendationService.mockEmbeddingData} 
          />
        ),
      });
      
      // 4. Check responsive design
      await testResponsive({
        component: (
          <SimpleVisualization 
            testId="simple-viz" 
            state="data" 
            data={mockRecommendationService.mockEmbeddingData} 
          />
        ),
        breakpoints: ['xs', 'xl'],
        callback: () => {
          expect(screen.getByTestId('simple-viz')).toBeInTheDocument();
        },
      });
      
      // 5. Check performance
      const performanceResults = testPerformance({
        component: (
          <SimpleVisualization 
            testId="simple-viz" 
            state="data" 
            data={mockRecommendationService.mockEmbeddingData} 
          />
        ),
        iterations: 5, // Reduced for example
      });
      expect(performanceResults.passed).toBe(true);
      
      // 6. Check user interactions
      await testInteractions({
        component: (
          <SimpleVisualization 
            testId="simple-viz" 
            state="error" 
            onRetry={() => {}} 
          />
        ),
        interactions: [
          {
            type: 'click',
            testId: 'simple-viz-retry-button',
            description: 'Click the retry button',
          },
        ],
      });
    });
  });
});
