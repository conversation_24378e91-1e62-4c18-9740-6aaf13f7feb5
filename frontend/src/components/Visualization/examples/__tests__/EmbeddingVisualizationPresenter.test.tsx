import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmbeddingVisualizationPresenter from '../EmbeddingVisualizationPresenter';
import { mockEmbeddingData } from '../../../../services/testing/mockRecommendationService';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, whileTap, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, whileTap, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
}));

describe('EmbeddingVisualizationPresenter', () => {
  // Mock callback functions
  const onCategorySelect = jest.fn();
  const onItemSelect = jest.fn();
  const onItemHover = jest.fn();
  const onZoomIn = jest.fn();
  const onZoomOut = jest.fn();
  const onRetry = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state when loading is true', () => {
    render(
      <EmbeddingVisualizationPresenter
        embeddings={[]}
        selectedCategory={null}
        selectedItem={null}
        hoveredItem={null}
        loading={true}
        error={null}
        zoomLevel={1}
        width={600}
        height={400}
        onCategorySelect={onCategorySelect}
        onItemSelect={onItemSelect}
        onItemHover={onItemHover}
        onZoomIn={onZoomIn}
        onZoomOut={onZoomOut}
        onRetry={onRetry}
        testId="embed-viz"
      />
    );

    expect(screen.getByTestId('embed-viz-loading')).toBeInTheDocument();
  });

  it('renders error state when error is provided', () => {
    render(
      <EmbeddingVisualizationPresenter
        embeddings={[]}
        selectedCategory={null}
        selectedItem={null}
        hoveredItem={null}
        loading={false}
        error="Test error message"
        zoomLevel={1}
        width={600}
        height={400}
        onCategorySelect={onCategorySelect}
        onItemSelect={onItemSelect}
        onItemHover={onItemHover}
        onZoomIn={onZoomIn}
        onZoomOut={onZoomOut}
        onRetry={onRetry}
        testId="embed-viz"
      />
    );

    expect(screen.getByTestId('embed-viz-error')).toBeInTheDocument();
    expect(screen.getByText('Test error message')).toBeInTheDocument();
    
    // Test retry button
    fireEvent.click(screen.getByTestId('embed-viz-retry-button'));
    expect(onRetry).toHaveBeenCalledTimes(1);
  });

  it('renders empty state when embeddings array is empty', () => {
    render(
      <EmbeddingVisualizationPresenter
        embeddings={[]}
        selectedCategory={null}
        selectedItem={null}
        hoveredItem={null}
        loading={false}
        error={null}
        zoomLevel={1}
        width={600}
        height={400}
        onCategorySelect={onCategorySelect}
        onItemSelect={onItemSelect}
        onItemHover={onItemHover}
        onZoomIn={onZoomIn}
        onZoomOut={onZoomOut}
        onRetry={onRetry}
        testId="embed-viz"
      />
    );

    expect(screen.getByTestId('embed-viz-empty')).toBeInTheDocument();
    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders data correctly when embeddings are provided', () => {
    render(
      <EmbeddingVisualizationPresenter
        embeddings={mockEmbeddingData}
        selectedCategory={null}
        selectedItem={null}
        hoveredItem={null}
        loading={false}
        error={null}
        zoomLevel={1}
        width={600}
        height={400}
        onCategorySelect={onCategorySelect}
        onItemSelect={onItemSelect}
        onItemHover={onItemHover}
        onZoomIn={onZoomIn}
        onZoomOut={onZoomOut}
        onRetry={onRetry}
        testId="embed-viz"
      />
    );

    // Check that the component renders
    expect(screen.getByTestId('embed-viz')).toBeInTheDocument();
    
    // Check category filter buttons
    expect(screen.getByTestId('embed-viz-category-all')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-category-electronics')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-category-furniture')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-category-clothing')).toBeInTheDocument();
    
    // Check points are rendered
    expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item2')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item3')).toBeInTheDocument();
    
    // Check point names are displayed
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
  });

  it('calls onCategorySelect when a category button is clicked', () => {
    render(
      <EmbeddingVisualizationPresenter
        embeddings={mockEmbeddingData}
        selectedCategory={null}
        selectedItem={null}
        hoveredItem={null}
        loading={false}
        error={null}
        zoomLevel={1}
        width={600}
        height={400}
        onCategorySelect={onCategorySelect}
        onItemSelect={onItemSelect}
        onItemHover={onItemHover}
        onZoomIn={onZoomIn}
        onZoomOut={onZoomOut}
        onRetry={onRetry}
        testId="embed-viz"
      />
    );
    
    // Click the electronics category button
    fireEvent.click(screen.getByTestId('embed-viz-category-electronics'));
    expect(onCategorySelect).toHaveBeenCalledWith('electronics');
    
    // Click the all categories button
    fireEvent.click(screen.getByTestId('embed-viz-category-all'));
    expect(onCategorySelect).toHaveBeenCalledWith(null);
  });

  it('calls onZoomIn and onZoomOut when zoom buttons are clicked', () => {
    render(
      <EmbeddingVisualizationPresenter
        embeddings={mockEmbeddingData}
        selectedCategory={null}
        selectedItem={null}
        hoveredItem={null}
        loading={false}
        error={null}
        zoomLevel={1}
        width={600}
        height={400}
        onCategorySelect={onCategorySelect}
        onItemSelect={onItemSelect}
        onItemHover={onItemHover}
        onZoomIn={onZoomIn}
        onZoomOut={onZoomOut}
        onRetry={onRetry}
        testId="embed-viz"
      />
    );
    
    // Click the zoom in button
    fireEvent.click(screen.getByTestId('embed-viz-zoom-in'));
    expect(onZoomIn).toHaveBeenCalledTimes(1);
    
    // Click the zoom out button
    fireEvent.click(screen.getByTestId('embed-viz-zoom-out'));
    expect(onZoomOut).toHaveBeenCalledTimes(1);
  });
});
