import React from 'react';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { setupVisualizationTest, resetVisualizationTest } from '../../../../testing/utils/setupVisualizationTest';
import SimpleVisualization from '../../SimpleVisualization';
import mockRecommendationService from '../../../../services/testing/mockRecommendationService';

// Import custom matchers
import '../../../../testing/matchers/visualizationMatchers';

// Extend Jest's expect
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInLoadingState(testId: string): R;
      toBeInErrorState(testId: string): R;
      toBeInEmptyState(testId: string): R;
      toBeInDataState(testId: string): R;
      toHavePoint(testId: string, pointId: string): R;
      toHaveCategory(testId: string, category: string): R;
      toHaveZoomControls(testId: string): R;
      toHaveLegend(testId: string): R;
    }
  }
}

describe('Visualization Test Example', () => {
  // Reset all mocks after each test
  afterEach(() => {
    resetVisualizationTest();
  });

  it('renders loading state initially', () => {
    // Set up the test with the component in loading state
    setupVisualizationTest(
      SimpleVisualization,
      {
        componentProps: {
          testId: 'simple-viz',
          state: 'loading',
        },
      }
    );

    // Use custom matcher to check loading state
    expect('simple-viz').toBeInLoadingState();
    expect(screen.getByText('Loading visualization data...')).toBeInTheDocument();
  });

  it('renders error state when there is an error', () => {
    // Set up the test with the component in error state
    setupVisualizationTest(
      SimpleVisualization,
      {
        componentProps: {
          testId: 'simple-viz',
          state: 'error',
        },
      }
    );

    // Use custom matcher to check error state
    expect('simple-viz').toBeInErrorState();
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByTestId('simple-viz-retry-button')).toBeInTheDocument();
  });

  it('renders empty state when there is no data', () => {
    // Set up the test with the component in empty state
    setupVisualizationTest(
      SimpleVisualization,
      {
        componentProps: {
          testId: 'simple-viz',
          state: 'empty',
        },
      }
    );

    // Use custom matcher to check empty state
    expect('simple-viz').toBeInEmptyState();
    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders data state when there is data', () => {
    // Set up the test with the component in data state
    setupVisualizationTest(
      SimpleVisualization,
      {
        componentProps: {
          testId: 'simple-viz',
          state: 'data',
          data: mockRecommendationService.mockEmbeddingData,
        },
      }
    );

    // Use custom matcher to check data state
    expect('simple-viz').toBeInDataState();
    
    // Use custom matchers to check points and categories
    expect('simple-viz').toHavePoint('item1');
    expect('simple-viz').toHavePoint('item2');
    expect('simple-viz').toHavePoint('item3');
    
    expect('simple-viz').toHaveCategory('electronics');
    expect('simple-viz').toHaveCategory('furniture');
    expect('simple-viz').toHaveCategory('clothing');
    
    // Check that the points are rendered with the correct names
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
    expect(screen.getByText('Item 3')).toBeInTheDocument();
  });

  it('calls onRetry when retry button is clicked', () => {
    // Create a mock callback
    const onRetry = jest.fn();
    
    // Set up the test with the component in error state
    setupVisualizationTest(
      SimpleVisualization,
      {
        componentProps: {
          testId: 'simple-viz',
          state: 'error',
          onRetry,
        },
      }
    );

    // Click the retry button
    fireEvent.click(screen.getByTestId('simple-viz-retry-button'));
    
    // Check that the callback was called
    expect(onRetry).toHaveBeenCalledTimes(1);
  });

  it('does not render legend when compact prop is true', () => {
    // Set up the test with the component in data state and compact prop
    setupVisualizationTest(
      SimpleVisualization,
      {
        componentProps: {
          testId: 'simple-viz',
          state: 'data',
          data: mockRecommendationService.mockEmbeddingData,
          compact: true,
        },
      }
    );

    // Check that the legend is not rendered
    expect(screen.queryByTestId('simple-viz-legend')).not.toBeInTheDocument();
  });

  it('renders legend when compact prop is false', () => {
    // Set up the test with the component in data state and compact prop
    setupVisualizationTest(
      SimpleVisualization,
      {
        componentProps: {
          testId: 'simple-viz',
          state: 'data',
          data: mockRecommendationService.mockEmbeddingData,
          compact: false,
        },
      }
    );

    // Use custom matcher to check legend
    expect('simple-viz').toHaveLegend();
  });
});
