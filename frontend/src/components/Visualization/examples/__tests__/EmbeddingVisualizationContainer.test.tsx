import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import EmbeddingVisualizationContainer from '../EmbeddingVisualizationContainer';
import mockRecommendationService from '../../../../services/testing/mockRecommendationService';

// Mock the recommendationService
jest.mock('../../../../services/recommendationService', () => ({
  __esModule: true,
  default: mockRecommendationService,
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
}));

// Mock the useErrorHandler hook
jest.mock('../../../../hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn((err, options) => {
      if (options?.setError) {
        options.setError('Error message');
      }
    }),
  }),
}));

// Mock the useMediaQuery hook
jest.mock('../../../../hooks/useMediaQuery', () => ({
  useIsMobile: jest.fn(() => false),
  useIsTablet: jest.fn(() => false),
  useIsDesktop: jest.fn(() => true),
}));

// Mock the EmbeddingVisualizationPresenter component
jest.mock('../EmbeddingVisualizationPresenter', () => {
  return {
    __esModule: true,
    default: jest.fn(props => {
      // Render a simplified version for testing
      if (props.loading) {
        return <div data-testid={`${props.testId}-loading`}>Loading...</div>;
      }
      if (props.error) {
        return (
          <div data-testid={`${props.testId}-error`}>
            <p>{props.error}</p>
            <button 
              data-testid={`${props.testId}-retry-button`}
              onClick={props.onRetry}
            >
              Retry
            </button>
          </div>
        );
      }
      if (props.embeddings.length === 0) {
        return <div data-testid={`${props.testId}-empty`}>No data available</div>;
      }
      return (
        <div data-testid={props.testId}>
          <div data-testid={`${props.testId}-container`}>
            {props.embeddings.map(item => (
              <div 
                key={item.id}
                data-testid={`${props.testId}-point-${item.id}`}
                onClick={() => props.onItemSelect(item.id)}
              >
                {item.name}
              </div>
            ))}
          </div>
        </div>
      );
    }),
  };
});

// Mock fetch
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe('EmbeddingVisualizationContainer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockRecommendationService.reset();
    
    // Default mock implementation for fetch
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve([]),
      })
    );
  });

  it('renders loading state initially', async () => {
    // Mock fetch to never resolve
    mockFetch.mockImplementation(() => new Promise(() => {}));

    render(<EmbeddingVisualizationContainer testId="embed-viz" />);
    expect(screen.getByTestId('embed-viz-loading')).toBeInTheDocument();
  });

  it('renders error state when API call fails', async () => {
    // Mock fetch to return an error
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      })
    );

    await act(async () => {
      render(<EmbeddingVisualizationContainer testId="embed-viz" />);
    });

    // Wait for the error state to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('embed-viz-error')).toBeInTheDocument();
    }, { timeout: 3000 });

    expect(screen.getByText('Error message')).toBeInTheDocument();
  });

  it('renders empty state when no embeddings are returned', async () => {
    // Mock fetch to return empty data
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve([]),
      })
    );

    await act(async () => {
      render(<EmbeddingVisualizationContainer testId="embed-viz" />);
    });

    // Wait for the empty state to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('embed-viz-empty')).toBeInTheDocument();
    }, { timeout: 3000 });

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders embeddings when data is returned', async () => {
    // Mock fetch to return mock data
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockRecommendationService.mockEmbeddingData),
      })
    );

    await act(async () => {
      render(<EmbeddingVisualizationContainer testId="embed-viz" />);
    });

    // Wait for the data to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('embed-viz')).toBeInTheDocument();
    }, { timeout: 3000 });

    // Check that the points are rendered
    expect(screen.getByTestId('embed-viz-point-item1')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item2')).toBeInTheDocument();
    expect(screen.getByTestId('embed-viz-point-item3')).toBeInTheDocument();
  });

  it('calls onDataLoaded callback when data is loaded', async () => {
    // Mock fetch to return mock data
    mockFetch.mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockRecommendationService.mockEmbeddingData),
      })
    );

    const onDataLoaded = jest.fn();
    
    await act(async () => {
      render(<EmbeddingVisualizationContainer testId="embed-viz" onDataLoaded={onDataLoaded} />);
    });

    // Wait for the data to be loaded
    await waitFor(() => {
      expect(onDataLoaded).toHaveBeenCalled();
    }, { timeout: 3000 });

    expect(onDataLoaded).toHaveBeenCalledWith(expect.arrayContaining([
      expect.objectContaining({ id: 'item1' }),
      expect.objectContaining({ id: 'item2' }),
      expect.objectContaining({ id: 'item3' }),
    ]));
  });
});
