import React from 'react';
import { EmbeddingPoint } from '../../../services/recommendationService';
import { motion, AnimatePresence } from 'framer-motion';

interface EmbeddingVisualizationPresenterProps {
  // Data
  embeddings: EmbeddingPoint[];
  selectedCategory: string | null;
  selectedItem: string | null;
  hoveredItem: string | null;
  
  // UI state
  loading: boolean;
  error: string | null;
  zoomLevel: number;
  
  // Dimensions
  width: number;
  height: number;
  
  // Styling
  className?: string;
  compact?: boolean;
  
  // Callbacks
  onCategorySelect: (category: string | null) => void;
  onItemSelect: (itemId: string | null) => void;
  onItemHover: (itemId: string | null) => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onRetry: () => void;
  
  // Testing
  testId?: string;
}

/**
 * Pure presenter component for embedding visualization
 * This component only handles rendering and user interactions
 * It doesn't fetch data or manage complex state
 */
const EmbeddingVisualizationPresenter: React.FC<EmbeddingVisualizationPresenterProps> = ({
  // Data
  embeddings,
  selectedCategory,
  selectedItem,
  hoveredItem,
  
  // UI state
  loading,
  error,
  zoomLevel,
  
  // Dimensions
  width,
  height,
  
  // Styling
  className = '',
  compact = false,
  
  // Callbacks
  onCategorySelect,
  onItemSelect,
  onItemHover,
  onZoomIn,
  onZoomOut,
  onRetry,
  
  // Testing
  testId = 'embed-viz',
}) => {
  // Category colors for visualization
  const categoryColors: Record<string, string> = {
    electronics: '#4299E1', // blue
    furniture: '#48BB78',   // green
    clothing: '#F56565',    // red
    appliances: '#9F7AEA',  // purple
    other: '#718096',       // gray
  };

  // Get unique categories from embeddings
  const categories = [...new Set(embeddings.map(point => point.category))];

  // Filter embeddings by selected category
  const filteredEmbeddings = selectedCategory
    ? embeddings.filter(point => point.category === selectedCategory)
    : embeddings;

  // Loading state
  if (loading) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-loading`}
        aria-busy="true"
        aria-live="polite"
      >
        <div className="h-6 bg-gray-200 animate-pulse rounded w-1/3 mb-6"></div>
        <div
          className="flex justify-center items-center"
          style={{ height: height * 0.8 }}
          aria-label="Loading visualization data"
        >
          <div className="w-full h-full bg-gray-100 animate-pulse rounded-lg"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-error`}
        role="alert"
        aria-live="assertive"
      >
        <h3 className={`${compact ? 'text-base' : 'text-lg'} font-semibold mb-4`}>
          Item Relationships
        </h3>
        <div className="bg-red-50 text-red-600 p-4 rounded-md">
          <p className="mb-3">{error}</p>
          <button
            onClick={onRetry}
            className="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
            aria-label="Retry loading visualization"
            data-testid={`${testId}-retry-button`}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Empty state
  if (embeddings.length === 0) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-empty`}
      >
        <h3 className={`${compact ? 'text-base' : 'text-lg'} font-semibold mb-4`}>
          Item Relationships
        </h3>
        <div className="text-center py-8 text-gray-700">
          <p className="text-lg font-medium mb-1">No data available</p>
          <p className="mt-2 text-sm">There are no items to display in this visualization.</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={testId}
    >
      <div className="flex justify-between items-center mb-4">
        <h3 className={`${compact ? 'text-base' : 'text-lg'} font-semibold`}>
          Item Relationships
        </h3>

        {/* Zoom controls */}
        <div className="flex space-x-1" role="toolbar" aria-label="Zoom controls">
          <button
            onClick={onZoomOut}
            className="p-1 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Zoom out"
            disabled={zoomLevel <= 0.5}
            data-testid={`${testId}-zoom-out`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
          </button>
          <button
            onClick={onZoomIn}
            className="p-1 rounded-md bg-gray-100 hover:bg-gray-200 text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            aria-label="Zoom in"
            disabled={zoomLevel >= 2}
            data-testid={`${testId}-zoom-in`}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      {/* Category filter */}
      {categories.length > 1 && (
        <div className="mb-4 overflow-x-auto pb-2 -mx-1 px-1" role="radiogroup" aria-label="Category filter">
          <div className="flex space-x-2 min-w-max">
            <motion.button
              whileTap={{ scale: 0.95 }}
              className={`px-3 py-1 text-xs rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                selectedCategory === null
                  ? 'bg-gray-800 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
              onClick={() => onCategorySelect(null)}
              role="radio"
              aria-checked={selectedCategory === null}
              data-testid={`${testId}-category-all`}
            >
              All Categories
            </motion.button>
            {categories.map(category => (
              <motion.button
                key={category}
                whileTap={{ scale: 0.95 }}
                className={`px-3 py-1 text-xs rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  selectedCategory === category
                    ? 'text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
                style={{
                  backgroundColor: selectedCategory === category ? categoryColors[category] || categoryColors.other : undefined,
                }}
                onClick={() => onCategorySelect(category)}
                role="radio"
                aria-checked={selectedCategory === category}
                data-testid={`${testId}-category-${category}`}
              >
                {category}
              </motion.button>
            ))}
          </div>
        </div>
      )}

      {/* Visualization container */}
      <div
        className="relative overflow-hidden rounded-lg border border-gray-200"
        style={{ width: '100%', maxWidth: `${width}px`, height: `${height}px` }}
        role="figure"
        data-testid={`${testId}-container`}
      >
        {/* Points would be rendered here */}
        {filteredEmbeddings.map((point) => (
          <div
            key={point.id}
            data-testid={`${testId}-point-${point.id}`}
            data-category={point.category}
            className="absolute rounded-full cursor-pointer"
            onClick={() => onItemSelect(point.id)}
            onMouseEnter={() => onItemHover(point.id)}
            onMouseLeave={() => onItemHover(null)}
          >
            {point.name}
          </div>
        ))}
      </div>
    </div>
  );
};

export default EmbeddingVisualizationPresenter;
