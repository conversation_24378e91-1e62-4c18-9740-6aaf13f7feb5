import React from 'react';

interface SimpleVisualizationProps {
  testId?: string;
  state?: 'loading' | 'error' | 'empty' | 'data';
  data?: Array<{
    id: string;
    name: string;
    category: string;
  }>;
  compact?: boolean;
  onRetry?: () => void;
}

/**
 * A very simple visualization component for testing purposes
 * This component doesn't have any complex state management or async operations
 */
const SimpleVisualization: React.FC<SimpleVisualizationProps> = ({
  testId = 'simple-viz',
  state = 'loading',
  data = [],
  compact = false,
  onRetry,
}) => {
  // Get unique categories from data
  const categories = [...new Set(data.map(item => item.category))];

  // Loading state
  if (state === 'loading') {
    return (
      <div data-testid={`${testId}-loading`}>
        Loading visualization data...
      </div>
    );
  }

  // Error state
  if (state === 'error') {
    return (
      <div data-testid={`${testId}-error`}>
        <p>Error message</p>
        <button 
          data-testid={`${testId}-retry-button`}
          onClick={onRetry}
        >
          Retry
        </button>
      </div>
    );
  }

  // Empty state
  if (state === 'empty' || data.length === 0) {
    return (
      <div data-testid={`${testId}-empty`}>
        <p>No data available</p>
      </div>
    );
  }

  // Data state
  return (
    <div data-testid={testId}>
      <h3>Item Relationships</h3>
      
      {/* Category filter */}
      <div data-testid={`${testId}-categories`}>
        <button data-testid={`${testId}-category-all`}>
          All Categories
        </button>
        {categories.map(category => (
          <button
            key={category}
            data-testid={`${testId}-category-${category}`}
          >
            {category}
          </button>
        ))}
      </div>
      
      {/* Points */}
      <div data-testid={`${testId}-container`}>
        {data.map(item => (
          <div
            key={item.id}
            data-testid={`${testId}-point-${item.id}`}
            data-category={item.category}
          >
            {item.name}
          </div>
        ))}
      </div>
      
      {/* Legend */}
      {!compact && (
        <div data-testid={`${testId}-legend`}>
          {categories.map(category => (
            <div key={category}>{category}</div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SimpleVisualization;
