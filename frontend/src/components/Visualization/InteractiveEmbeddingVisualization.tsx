import React, { useState, useEffect } from 'react';
import EmbeddingVisualization from './EmbeddingVisualization';
import { motion, AnimatePresence } from 'framer-motion';
import { useMediaQuery } from '../../hooks/useMediaQuery';
import { cn } from '../../utils/classNames';
import { LiveRegion } from '../../utils/accessibility';
import recommendationService, { EmbeddingPoint } from '../../services/recommendationService';
import { downloadCSV, downloadJSON, shareData } from '../../utils/exportData';

interface InteractiveEmbeddingVisualizationProps {
  itemId?: string;
  categoryId?: string;
  limit?: number;
  className?: string;
  width?: number;
  height?: number;
  onDataLoaded?: (data: EmbeddingPoint[]) => void;
  testId?: string;
  timePeriod?: 'current' | '1month' | '3months' | '6months';
}

/**
 * InteractiveEmbeddingVisualization Component
 *
 * An enhanced version of the EmbeddingVisualization component with interactive features
 * enabled by default and additional controls for visualization options.
 *
 * Features:
 * - Zoom, pan, and filtering capabilities
 * - View mode switching (2D/3D)
 * - Enhanced tooltips
 * - Accessibility improvements
 * - Responsive design
 */
const InteractiveEmbeddingVisualization: React.FC<InteractiveEmbeddingVisualizationProps> = ({
  itemId,
  categoryId,
  limit = 100,
  className = '',
  width,
  height,
  onDataLoaded,
  testId = 'interactive-embedding-visualization',
  timePeriod = 'current',
}) => {
  const [viewMode, setViewMode] = useState<'2d' | '3d'>('2d');
  const [colorMode, setColorMode] = useState<'category' | 'similarity'>('category');
  const [showLabels, setShowLabels] = useState<boolean>(false);
  const [screenReaderMessage, setScreenReaderMessage] = useState<string>('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [embeddingData, setEmbeddingData] = useState<EmbeddingPoint[]>([]);

  // Use media query to determine if we're on mobile
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isTablet = useMediaQuery('(min-width: 641px) and (max-width: 1024px)');

  // Handle data loading from the child component
  const handleDataLoaded = (data: EmbeddingPoint[]) => {
    setEmbeddingData(data);
    if (onDataLoaded) {
      onDataLoaded(data);
    }

    // Announce to screen readers that data has loaded
    setScreenReaderMessage(`${data.length} items loaded in the embedding visualization.`);
  };

  // Handle view mode change
  const handleViewModeChange = (mode: '2d' | '3d') => {
    setViewMode(mode);
    setScreenReaderMessage(`View mode changed to ${mode === '2d' ? '2D' : '3D'}.`);
  };

  // Handle color mode change
  const handleColorModeChange = (mode: 'category' | 'similarity') => {
    setColorMode(mode);
    setScreenReaderMessage(`Color mode changed to ${mode}.`);
  };

  // Handle labels toggle
  const handleLabelsToggle = () => {
    setShowLabels(!showLabels);
    setScreenReaderMessage(`Item labels are now ${!showLabels ? 'visible' : 'hidden'}.`);
  };

  // Handle item selection from the child component
  const handleItemSelect = (itemId: string) => {
    // Toggle selection
    if (selectedItems.includes(itemId)) {
      setSelectedItems(selectedItems.filter(id => id !== itemId));
      setScreenReaderMessage(`Item deselected.`);
    } else {
      setSelectedItems([...selectedItems, itemId]);

      // Find the item in the data
      const item = embeddingData.find(item => item.id === itemId);
      if (item) {
        setScreenReaderMessage(`Item selected: ${item.name}, ${item.category} category.`);
      }
    }
  };

  return (
    <div className={cn("bg-white rounded-lg shadow-md", className)} data-testid={testId}>
      {/* Screen reader announcements */}
      <LiveRegion message={screenReaderMessage} />

      {/* Visualization controls */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex flex-wrap gap-3 justify-between items-center">
          <h3 className="text-lg font-semibold">Item Relationships</h3>

          <div className="flex flex-wrap gap-2">
            {/* View mode toggle */}
            <div className="inline-flex rounded-md shadow-sm" role="group" aria-label="View mode">
              <button
                type="button"
                className={cn(
                  "px-3 py-1 text-xs font-medium rounded-l-lg border",
                  viewMode === '2d'
                    ? "bg-primary-600 text-white border-primary-600"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                )}
                onClick={() => handleViewModeChange('2d')}
                aria-pressed={viewMode === '2d'}
              >
                2D
              </button>
              <button
                type="button"
                className={cn(
                  "px-3 py-1 text-xs font-medium rounded-r-lg border",
                  viewMode === '3d'
                    ? "bg-primary-600 text-white border-primary-600"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                )}
                onClick={() => handleViewModeChange('3d')}
                aria-pressed={viewMode === '3d'}
              >
                3D
              </button>
            </div>

            {/* Color mode toggle */}
            <div className="inline-flex rounded-md shadow-sm" role="group" aria-label="Color mode">
              <button
                type="button"
                className={cn(
                  "px-3 py-1 text-xs font-medium rounded-l-lg border",
                  colorMode === 'category'
                    ? "bg-primary-600 text-white border-primary-600"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                )}
                onClick={() => handleColorModeChange('category')}
                aria-pressed={colorMode === 'category'}
              >
                By Category
              </button>
              <button
                type="button"
                className={cn(
                  "px-3 py-1 text-xs font-medium rounded-r-lg border",
                  colorMode === 'similarity'
                    ? "bg-primary-600 text-white border-primary-600"
                    : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                )}
                onClick={() => handleColorModeChange('similarity')}
                aria-pressed={colorMode === 'similarity'}
              >
                By Similarity
              </button>
            </div>

            {/* Show labels toggle */}
            <button
              type="button"
              className={cn(
                "px-3 py-1 text-xs font-medium rounded-lg border",
                showLabels
                  ? "bg-primary-600 text-white border-primary-600"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              )}
              onClick={handleLabelsToggle}
              aria-pressed={showLabels}
            >
              {showLabels ? "Hide Labels" : "Show Labels"}
            </button>
          </div>
        </div>

        {/* Data export controls */}
        {embeddingData.length > 0 && (
          <div className="mt-4 pt-3 border-t border-gray-200">
            <div className="flex flex-wrap gap-2">
              <div className="relative">
                <button
                  type="button"
                  className="px-3 py-1 text-xs font-medium rounded-lg border bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                  aria-label="Download embedding data"
                  onClick={() => {
                    const downloadMenu = document.getElementById(`${testId}-download-menu`);
                    if (downloadMenu) {
                      downloadMenu.classList.toggle('hidden');
                    }
                  }}
                  aria-expanded={false}
                  aria-controls={`${testId}-download-menu`}
                  data-testid={`${testId}-download-button`}
                >
                  <span className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Export Data
                  </span>
                </button>
                <div
                  id={`${testId}-download-menu`}
                  className="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden z-10"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby={`${testId}-download-button`}
                >
                  <div className="py-1" role="none">
                    <button
                      className="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                      role="menuitem"
                      onClick={() => {
                        // Download as CSV
                        const headers = [
                          { key: 'id', label: 'ID' },
                          { key: 'name', label: 'Name' },
                          { key: 'category', label: 'Category' },
                          { key: 'x', label: 'X Coordinate' },
                          { key: 'y', label: 'Y Coordinate' }
                        ];
                        downloadCSV(embeddingData, 'embedding-data', headers);

                        // Close the menu
                        const downloadMenu = document.getElementById(`${testId}-download-menu`);
                        if (downloadMenu) {
                          downloadMenu.classList.add('hidden');
                        }

                        // Announce to screen readers
                        setScreenReaderMessage('Embedding data downloaded as CSV.');
                      }}
                      data-testid={`${testId}-download-csv`}
                    >
                      Download as CSV
                    </button>
                    <button
                      className="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                      role="menuitem"
                      onClick={() => {
                        // Download as JSON
                        downloadJSON(embeddingData, 'embedding-data');

                        // Close the menu
                        const downloadMenu = document.getElementById(`${testId}-download-menu`);
                        if (downloadMenu) {
                          downloadMenu.classList.add('hidden');
                        }

                        // Announce to screen readers
                        setScreenReaderMessage('Embedding data downloaded as JSON.');
                      }}
                      data-testid={`${testId}-download-json`}
                    >
                      Download as JSON
                    </button>
                  </div>
                </div>
              </div>

              <button
                type="button"
                className="px-3 py-1 text-xs font-medium rounded-lg border bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
                aria-label="Share embedding visualization"
                onClick={async () => {
                  const success = await shareData(
                    embeddingData,
                    'RentUp Item Relationships',
                    'Here are the item relationships from RentUp:'
                  );

                  if (success) {
                    setScreenReaderMessage('Embedding data shared successfully.');
                  } else {
                    setScreenReaderMessage('Failed to share embedding data. Data copied to clipboard instead.');
                  }
                }}
                data-testid={`${testId}-share-button`}
              >
                <span className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                  </svg>
                  Share
                </span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Embedding visualization */}
      <EmbeddingVisualization
        itemId={itemId}
        categoryId={categoryId}
        limit={limit}
        width={width}
        height={height}
        onDataLoaded={handleDataLoaded}
        testId={`${testId}-visualization`}
        timePeriod={timePeriod}
        interactive={true} // Always enable interactive mode
        className="p-0 shadow-none rounded-none"
      />

      {/* Selected items panel */}
      <AnimatePresence>
        {selectedItems.length > 0 && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="border-t border-gray-200 p-4"
          >
            <h4 className="text-sm font-medium mb-2">Selected Items ({selectedItems.length})</h4>
            <div className="flex flex-wrap gap-2">
              {selectedItems.map(id => {
                const item = embeddingData.find(item => item.id === id);
                return item ? (
                  <div
                    key={id}
                    className="px-2 py-1 bg-gray-100 rounded-md text-xs flex items-center"
                  >
                    <span className="mr-1">{item.name}</span>
                    <button
                      className="text-gray-500 hover:text-gray-700"
                      onClick={() => handleItemSelect(id)}
                      aria-label={`Remove ${item.name} from selection`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                ) : null;
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InteractiveEmbeddingVisualization;
