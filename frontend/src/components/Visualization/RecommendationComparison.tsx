import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../hooks/useAuth';
import { useMediaQuery } from '../../hooks/useMediaQuery';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import { usePerformance } from '../../hooks/usePerformance';
import recommendationService, { 
  ExplanationData,
  FactorWeight
} from '../../services/recommendationService';

interface RecommendationComparisonProps {
  itemIds?: string[]; // Optional - if provided, compares these items
  userId?: string; // Optional - if not provided, uses current user
  className?: string;
  testId?: string;
  onItemSelect?: (itemId: string) => void;
}

/**
 * A component for comparing multiple item recommendations
 * Shows side-by-side comparison of recommendation factors and explanations
 * 
 * Features:
 * - Compare up to 3 items side by side
 * - Visualize factor weights for each item
 * - Show common factors across items
 * - Highlight differences in recommendations
 * - Interactive selection of items to compare
 */
const RecommendationComparison: React.FC<RecommendationComparisonProps> = ({
  itemIds = [],
  userId,
  className = '',
  testId = 'recommendation-comparison',
  onItemSelect,
}) => {
  // State
  const [selectedItemIds, setSelectedItemIds] = useState<string[]>(itemIds);
  const [explanations, setExplanations] = useState<Record<string, ExplanationData>>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [commonFactors, setCommonFactors] = useState<string[]>([]);
  const [uniqueFactors, setUniqueFactors] = useState<Record<string, string[]>>({});
  
  // Hooks
  const { user } = useAuth();
  const { handleError } = useErrorHandler();
  const targetUserId = userId || user?.id;
  const { measureFunction, measureApiCall } = usePerformance('RecommendationComparison', {
    userId: targetUserId,
    itemCount: selectedItemIds.length
  });
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isTablet = useMediaQuery('(min-width: 641px) and (max-width: 1024px)');

  // Fetch recommendation explanations for selected items
  const fetchExplanations = useCallback(async () => {
    if (!targetUserId || selectedItemIds.length === 0) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const newExplanations: Record<string, ExplanationData> = {};
      
      // Fetch explanations for each selected item
      for (const itemId of selectedItemIds) {
        try {
          // Use measureApiCall to track the performance of the API call
          const getExplanation = measureApiCall('getRecommendationExplanation')(
            async () => await recommendationService.getRecommendationExplanation(itemId, targetUserId)
          );
          
          const data = await getExplanation();
          newExplanations[itemId] = data;
        } catch (err) {
          console.error(`Error fetching explanation for item ${itemId}:`, err);
          // Continue with other items even if one fails
        }
      }
      
      setExplanations(newExplanations);
      
      // Calculate common and unique factors
      if (Object.keys(newExplanations).length > 0) {
        calculateFactorComparison(newExplanations);
      }
      
      setLoading(false);
    } catch (err) {
      handleError(err, {
        friendlyMessage: 'Failed to load recommendation explanations. Please try again.',
        logMessage: 'Error fetching recommendation explanations:',
        setError: setError,
      });
      setLoading(false);
    }
  }, [selectedItemIds, targetUserId, handleError, measureApiCall]);

  // Calculate common and unique factors across explanations
  const calculateFactorComparison = useCallback((explanationData: Record<string, ExplanationData>) => {
    const factorSets: Record<string, Set<string>> = {};
    const allFactors = new Set<string>();
    
    // Create sets of factors for each item
    Object.entries(explanationData).forEach(([itemId, explanation]) => {
      factorSets[itemId] = new Set(explanation.factorWeights.map(fw => fw.factor));
      explanation.factorWeights.forEach(fw => allFactors.add(fw.factor));
    });
    
    // Find common factors (present in all items)
    const common = Array.from(allFactors).filter(factor => 
      Object.values(factorSets).every(set => set.has(factor))
    );
    
    // Find unique factors for each item
    const unique: Record<string, string[]> = {};
    Object.entries(factorSets).forEach(([itemId, factors]) => {
      unique[itemId] = Array.from(factors).filter(factor => !common.includes(factor));
    });
    
    setCommonFactors(common);
    setUniqueFactors(unique);
  }, []);

  // Fetch explanations when selected items change
  useEffect(() => {
    fetchExplanations();
  }, [fetchExplanations]);

  // Update selected items when itemIds prop changes
  useEffect(() => {
    if (itemIds.length > 0) {
      setSelectedItemIds(itemIds);
    }
  }, [itemIds]);

  // Handle item selection
  const handleItemSelect = useCallback((itemId: string) => {
    const selectItem = measureFunction('handleItemSelect')(() => {
      // Toggle item selection
      if (selectedItemIds.includes(itemId)) {
        setSelectedItemIds(prev => prev.filter(id => id !== itemId));
      } else {
        // Limit to 3 items maximum
        setSelectedItemIds(prev => {
          if (prev.length >= 3) {
            return [...prev.slice(1), itemId];
          }
          return [...prev, itemId];
        });
      }
      
      // Call the onItemSelect callback if provided
      if (onItemSelect) {
        onItemSelect(itemId);
      }
    });
    
    selectItem();
  }, [selectedItemIds, onItemSelect, measureFunction]);

  // Render loading state
  if (loading) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-loading`}
        aria-busy="true"
        aria-live="polite"
      >
        <h3 className="text-lg font-semibold mb-4">Recommendation Comparison</h3>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-error`}
        role="alert"
        aria-live="assertive"
      >
        <h3 className="text-lg font-semibold mb-4">Recommendation Comparison</h3>
        <div className="bg-red-50 text-red-700 p-4 rounded-md mb-4">
          <p className="font-medium mb-2">Error loading comparison</p>
          <p>{error}</p>
          <button
            onClick={fetchExplanations}
            className="mt-2 px-3 py-1 bg-red-100 text-red-800 rounded-md hover:bg-red-200 transition-colors text-sm"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Render empty state
  if (selectedItemIds.length === 0) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-empty`}
      >
        <h3 className="text-lg font-semibold mb-4">Recommendation Comparison</h3>
        <div className="text-center py-8 text-gray-700">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 mx-auto text-gray-500 mb-3"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          <p className="text-lg font-medium mb-1">No items selected for comparison</p>
          <p className="mt-2 text-sm">Select items from the Item Relationships tab to compare their recommendations.</p>
        </div>
      </div>
    );
  }

  // Render comparison view
  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={testId}
    >
      <h3 className="text-lg font-semibold mb-4">Recommendation Comparison</h3>
      
      {/* Selected items */}
      <div className="flex flex-wrap gap-2 mb-6">
        {selectedItemIds.map(itemId => {
          const explanation = explanations[itemId];
          return (
            <div
              key={itemId}
              className="flex items-center bg-gray-100 rounded-full px-3 py-1"
            >
              <span className="text-sm font-medium mr-2">
                {explanation?.itemName || `Item ${itemId.substring(0, 6)}...`}
              </span>
              <button
                onClick={() => handleItemSelect(itemId)}
                className="text-gray-500 hover:text-gray-700"
                aria-label={`Remove ${explanation?.itemName || 'item'} from comparison`}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          );
        })}
      </div>
      
      {/* Comparison grid */}
      <div className={`grid ${isMobile ? 'grid-cols-1' : isTablet ? 'grid-cols-2' : 'grid-cols-3'} gap-4 mb-6`}>
        {selectedItemIds.map(itemId => {
          const explanation = explanations[itemId];
          if (!explanation) return null;
          
          return (
            <div
              key={itemId}
              className="bg-gray-50 rounded-lg p-4"
              data-testid={`${testId}-item-${itemId}`}
            >
              <div className="flex items-start mb-4">
                {explanation.itemImage && (
                  <img
                    src={explanation.itemImage}
                    alt={explanation.itemName}
                    className="w-16 h-16 object-cover rounded-md mr-3"
                  />
                )}
                <div>
                  <h4 className="text-md font-semibold">{explanation.itemName}</h4>
                  <p className="text-sm text-gray-600">{explanation.itemCategory}</p>
                  <div className="mt-1 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Match Score: {Math.round(explanation.score * 100)}%
                  </div>
                </div>
              </div>
              
              <h5 className="text-sm font-medium mb-2">Recommendation Factors</h5>
              <div className="space-y-2">
                {explanation.factorWeights.map((factor, index) => (
                  <div
                    key={index}
                    className={`p-2 rounded-md ${
                      commonFactors.includes(factor.factor)
                        ? 'bg-blue-50 border border-blue-100'
                        : 'bg-gray-100'
                    }`}
                  >
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-xs font-medium">{factor.factor}</span>
                      <span className="text-xs">{Math.round(factor.weight * 100)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div
                        className={`h-1.5 rounded-full ${
                          commonFactors.includes(factor.factor)
                            ? 'bg-blue-600'
                            : 'bg-gray-600'
                        }`}
                        style={{ width: `${factor.weight * 100}%` }}
                      ></div>
                    </div>
                    {factor.description && (
                      <p className="text-xs text-gray-600 mt-1">{factor.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>
      
      {/* Common factors section */}
      {commonFactors.length > 0 && (
        <div className="mb-6">
          <h4 className="text-md font-medium mb-3">Common Factors</h4>
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-800 mb-2">
              These factors influence all compared recommendations:
            </p>
            <div className="flex flex-wrap gap-2">
              {commonFactors.map(factor => (
                <span
                  key={factor}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {factor}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}
      
      {/* Unique factors section */}
      {Object.keys(uniqueFactors).length > 0 && (
        <div>
          <h4 className="text-md font-medium mb-3">Unique Factors</h4>
          <div className="bg-gray-50 p-3 rounded-lg">
            <p className="text-sm text-gray-700 mb-2">
              These factors are unique to specific recommendations:
            </p>
            <div className="space-y-2">
              {Object.entries(uniqueFactors).map(([itemId, factors]) => {
                if (factors.length === 0) return null;
                const explanation = explanations[itemId];
                if (!explanation) return null;
                
                return (
                  <div key={itemId} className="flex flex-wrap items-center">
                    <span className="text-xs font-medium mr-2">{explanation.itemName}:</span>
                    <div className="flex flex-wrap gap-1">
                      {factors.map(factor => (
                        <span
                          key={factor}
                          className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-800"
                        >
                          {factor}
                        </span>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RecommendationComparison;
