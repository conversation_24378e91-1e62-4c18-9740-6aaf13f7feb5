import React from 'react';
import { LoadingState } from './components/LoadingState';
import { ErrorState } from './components/ErrorState';
import { EmptyState } from './components/EmptyState';
import { FilterControls } from './components/FilterControls';
import { PreferenceList } from './components/PreferenceList';
import { DataControls } from './components/DataControls';
import { InformationSection } from './components/InformationSection';
import { usePreferenceData } from './hooks/usePreferenceData';
import { usePreferenceFiltering } from './hooks/usePreferenceFiltering';
import { usePreferenceInteraction } from './hooks/usePreferenceInteraction';
import { useAccessibility } from './hooks/useAccessibility';
import { LiveRegion } from '../../../utils/accessibility';
import { EnhancedPreferenceVisualizationProps } from './utils/types';

/**
 * EnhancedPreferenceVisualization Component
 * 
 * An enhanced, interactive component to visualize user preferences.
 * Shows preference types, their weights, and counts in a simple bar chart.
 * Includes filtering, tooltips, and interactive elements.
 * Optimized for different screen sizes and touch devices.
 */
const EnhancedPreferenceVisualization: React.FC<EnhancedPreferenceVisualizationProps> = ({
  userId,
  className = '',
  compact = false,
  onDataLoaded,
  testId = 'enhanced-preference-visualization',
  timePeriod = 'current',
  interactive = true,
}) => {
  // Data management hook
  const {
    preferences,
    loading,
    error,
    retryCount,
    handleRetry
  } = usePreferenceData(userId, timePeriod, onDataLoaded);

  // Filtering hook
  const {
    filter,
    filteredPreferences,
    preferenceTypes,
    handleFilterChange
  } = usePreferenceFiltering(preferences);

  // Interaction hook
  const {
    selectedPreference,
    handlePreferenceSelect
  } = usePreferenceInteraction();

  // Accessibility hook
  const { screenReaderMessage } = useAccessibility();

  // Render loading state
  if (loading) {
    return (
      <LoadingState
        className={className}
        testId={testId}
        compact={compact}
      />
    );
  }

  // Render error state
  if (error) {
    return (
      <ErrorState
        className={className}
        testId={testId}
        error={error}
        onRetry={handleRetry}
      />
    );
  }

  // Render empty state
  if (preferences.length === 0) {
    return (
      <EmptyState
        className={className}
        testId={testId}
      />
    );
  }

  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={testId}
    >
      {/* Screen reader announcements */}
      <LiveRegion message={screenReaderMessage} />

      {/* Title */}
      <h3
        className={`${compact ? 'text-base' : 'text-lg'} font-semibold mb-4`}
        id={`${testId}-title`}
      >
        Your Preferences
      </h3>

      {/* Interactive filters */}
      {interactive && (
        <FilterControls
          filter={filter}
          preferenceTypes={preferenceTypes}
          timePeriod={timePeriod}
          onFilterChange={handleFilterChange}
          testId={testId}
        />
      )}

      {/* Preference list */}
      <PreferenceList
        preferences={filteredPreferences}
        selectedPreference={selectedPreference}
        onPreferenceSelect={handlePreferenceSelect}
        interactive={interactive}
        compact={compact}
        testId={testId}
      />

      {/* Information section and data controls */}
      {!compact && (
        <>
          <InformationSection interactive={interactive} />
          
          {interactive && preferences.length > 0 && (
            <DataControls
              preferences={preferences}
              testId={testId}
            />
          )}
        </>
      )}
    </div>
  );
};

export default EnhancedPreferenceVisualization;
