import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../../../utils/classNames';
import { PREFERENCE_COLORS } from '../utils/constants';
import { PreferenceData } from '../utils/types';

/**
 * PreferenceItem Component
 * 
 * Renders an individual preference item with progress bar, details, and interactions.
 * Supports selection, keyboard navigation, and detailed information display.
 */
interface PreferenceItemProps {
  preference: PreferenceData;
  index: number;
  maxCount: number;
  sortedPreferences: PreferenceData[];
  selectedPreference: string | null;
  onPreferenceSelect: (preferenceType: string) => void;
  interactive: boolean;
  compact: boolean;
  testId: string;
}

export const PreferenceItem: React.FC<PreferenceItemProps> = ({
  preference,
  index,
  maxCount,
  sortedPreferences,
  selectedPreference,
  onPreferenceSelect,
  interactive,
  compact,
  testId
}) => {
  const isSelected = selectedPreference === preference.preferenceType;
  const color = PREFERENCE_COLORS[preference.preferenceType] || '#6B7280';
  const relativeInfluence = Math.round((preference.weight / (sortedPreferences[0]?.weight || 1)) * 100);

  const handleClick = () => {
    if (interactive) {
      onPreferenceSelect(preference.preferenceType);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (interactive && (e.key === 'Enter' || e.key === ' ')) {
      e.preventDefault();
      onPreferenceSelect(preference.preferenceType);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: index * 0.1 }}
      className={cn(
        "space-y-1 p-2 rounded-lg transition-colors",
        interactive && "cursor-pointer hover:bg-gray-50 focus-within:bg-gray-50",
        isSelected && "bg-gray-50 ring-1 ring-primary-200"
      )}
      data-testid={`${testId}-item-${preference.preferenceType}`}
      onClick={handleClick}
      tabIndex={interactive ? 0 : undefined}
      role={interactive ? "button" : undefined}
      aria-pressed={interactive ? isSelected : undefined}
      onKeyDown={handleKeyDown}
    >
      {/* Header with label and count */}
      <div className="flex justify-between items-center">
        <span
          className={`${compact ? 'text-xs' : 'text-sm'} font-medium capitalize`}
          id={`${testId}-label-${preference.preferenceType}`}
        >
          {preference.preferenceType}
        </span>
        <span
          className="text-xs text-gray-700"
          aria-label={`${preference.count} items`}
        >
          {preference.count} items
        </span>
      </div>

      {/* Progress bar */}
      <div
        className="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden"
        role="progressbar"
        aria-valuenow={(preference.count / maxCount) * 100}
        aria-valuemin={0}
        aria-valuemax={100}
        aria-labelledby={`${testId}-label-${preference.preferenceType}`}
      >
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: `${(preference.count / maxCount) * 100}%` }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="h-2.5 rounded-full"
          style={{ backgroundColor: color }}
        />
      </div>

      {/* Weight and influence */}
      <div className="flex justify-between items-center text-xs text-gray-700">
        <span>Weight: {preference.weight.toFixed(1)}</span>
        <span
          className="text-xs px-2 py-0.5 rounded-full font-bold"
          style={{
            backgroundColor: `${color}20`,
            color: color,
          }}
          aria-label={`${relativeInfluence}% influence on recommendations`}
        >
          {relativeInfluence}% influence
        </span>
      </div>

      {/* Expanded details when selected */}
      {interactive && isSelected && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mt-3 pt-3 border-t border-gray-100 text-xs text-gray-700"
        >
          <h4 className="font-medium mb-1">About {preference.preferenceType} Preferences</h4>
          <p className="mb-2">
            This preference is based on items you've {preference.preferenceType.toLowerCase()}.
            It has a significant impact on the recommendations you see.
          </p>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <span className="font-medium">Total Items:</span> {preference.count}
            </div>
            <div>
              <span className="font-medium">Weight:</span> {preference.weight.toFixed(1)}
            </div>
            <div>
              <span className="font-medium">Relative Influence:</span> {relativeInfluence}%
            </div>
            <div>
              <span className="font-medium">Category Rank:</span> {index + 1} of {sortedPreferences.length}
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};
