import React from 'react';
import { AnimatePresence } from 'framer-motion';
import { PreferenceItem } from './PreferenceItem';
import { PreferenceData } from '../utils/types';

/**
 * PreferenceList Component
 * 
 * Renders a list of preference items with animations and interactions.
 * Handles sorting and provides accessible list structure.
 */
interface PreferenceListProps {
  preferences: PreferenceData[];
  selectedPreference: string | null;
  onPreferenceSelect: (preferenceType: string) => void;
  interactive: boolean;
  compact: boolean;
  testId: string;
}

export const PreferenceList: React.FC<PreferenceListProps> = ({
  preferences,
  selectedPreference,
  onPreferenceSelect,
  interactive,
  compact,
  testId
}) => {
  // Sort preferences by weight (descending)
  const sortedPreferences = [...preferences].sort((a, b) => b.weight - a.weight);
  
  // Find the maximum count for scaling
  const maxCount = Math.max(...preferences.map(pref => pref.count));

  return (
    <div
      className="space-y-4"
      aria-labelledby={`${testId}-title`}
      role="region"
    >
      <AnimatePresence>
        {sortedPreferences.map((preference, index) => (
          <PreferenceItem
            key={preference.preferenceType}
            preference={preference}
            index={index}
            maxCount={maxCount}
            sortedPreferences={sortedPreferences}
            selectedPreference={selectedPreference}
            onPreferenceSelect={onPreferenceSelect}
            interactive={interactive}
            compact={compact}
            testId={testId}
          />
        ))}
      </AnimatePresence>
    </div>
  );
};
