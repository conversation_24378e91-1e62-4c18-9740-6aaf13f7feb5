import React from 'react';

/**
 * EmptyState Component
 * 
 * Displays a message when no preference data is available.
 * Provides helpful guidance for users to start building preferences.
 */
interface EmptyStateProps {
  className?: string;
  testId: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  className = '',
  testId
}) => {
  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={`${testId}-empty`}
    >
      <h3
        className="text-lg font-semibold mb-4"
        id={`${testId}-empty-title`}
      >
        Your Preferences
      </h3>
      
      <div
        className="text-center py-8 text-gray-700"
        aria-labelledby={`${testId}-empty-title`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-12 w-12 mx-auto text-gray-400 mb-4"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          aria-hidden="true"
          role="img"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
          />
        </svg>
        
        <h4 className="text-lg font-medium mb-2 text-gray-900">
          No preference data yet
        </h4>
        
        <p className="text-sm text-gray-600 mb-4 max-w-sm mx-auto">
          As you interact with items on RentUp, your preferences will be tracked and displayed here.
        </p>
        
        <div className="text-xs text-gray-500 space-y-1">
          <p>Start by:</p>
          <ul className="list-disc list-inside space-y-1 text-left max-w-xs mx-auto">
            <li>Liking items you're interested in</li>
            <li>Adding items to your favorites</li>
            <li>Browsing different categories</li>
            <li>Searching for specific items</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
