import React from 'react';
import { cn } from '../../../../utils/classNames';
import { PREFERENCE_COLORS, TIME_PERIOD_OPTIONS } from '../utils/constants';
import { TimePeriod } from '../utils/types';

/**
 * FilterControls Component
 * 
 * Provides interactive filters for preference types and time periods.
 * Includes accessible button controls and dropdown selection.
 */
interface FilterControlsProps {
  filter: string | null;
  preferenceTypes: string[];
  timePeriod: TimePeriod;
  onFilterChange: (filter: string | null) => void;
  testId: string;
}

export const FilterControls: React.FC<FilterControlsProps> = ({
  filter,
  preferenceTypes,
  timePeriod,
  onFilterChange,
  testId
}) => {
  return (
    <div className="space-y-4 mb-6">
      {/* Preference type filters */}
      <div className="flex flex-wrap gap-2" role="toolbar" aria-label="Preference filters">
        <button
          className={cn(
            "px-3 py-1 rounded-full text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
            filter === null
              ? "bg-primary-600 text-white"
              : "bg-gray-200 text-gray-700 hover:bg-gray-300"
          )}
          onClick={() => onFilterChange(null)}
          aria-pressed={filter === null}
          data-testid={`${testId}-filter-all`}
        >
          All
        </button>
        
        {preferenceTypes.map(type => (
          <button
            key={type}
            className={cn(
              "px-3 py-1 rounded-full text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",
              filter === type
                ? "text-white"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            )}
            onClick={() => onFilterChange(type)}
            aria-pressed={filter === type}
            style={{
              backgroundColor: filter === type ? PREFERENCE_COLORS[type] : undefined
            }}
            data-testid={`${testId}-filter-${type}`}
          >
            <span className="capitalize">{type}</span>
          </button>
        ))}
      </div>

      {/* Time period selector */}
      <div>
        <label 
          htmlFor={`${testId}-time-period`} 
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Time Period
        </label>
        <select
          id={`${testId}-time-period`}
          className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
          value={timePeriod}
          onChange={(e) => {
            // Note: This would need to be handled by the parent component
            // as it requires refetching data
            console.log('Time period changed to:', e.target.value);
          }}
          aria-label="Select time period for preference data"
          data-testid={`${testId}-time-period-select`}
        >
          {TIME_PERIOD_OPTIONS.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};
