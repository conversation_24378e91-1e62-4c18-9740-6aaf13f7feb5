import React from 'react';

/**
 * InformationSection Component
 * 
 * Provides educational information about how preferences work
 * and how they influence recommendations.
 */
interface InformationSectionProps {
  interactive: boolean;
}

export const InformationSection: React.FC<InformationSectionProps> = ({
  interactive
}) => {
  return (
    <div className="mt-6 pt-4 border-t border-gray-100">
      <h4 className="text-sm font-medium mb-2">What This Means</h4>
      <p className="text-xs text-gray-700 mb-2">
        This visualization shows how your preferences are tracked based on your interactions.
        Items you've liked, favorited, or rented have higher weights and influence your recommendations more.
        The percentage indicates how much each preference type influences your recommendations relative to the strongest preference.
      </p>
      
      {interactive && (
        <p className="text-xs text-gray-700">
          Click on any preference to see more details about how it affects your recommendations.
        </p>
      )}
    </div>
  );
};
