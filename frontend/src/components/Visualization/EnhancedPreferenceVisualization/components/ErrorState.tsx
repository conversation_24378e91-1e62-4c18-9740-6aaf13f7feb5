import React from 'react';

/**
 * ErrorState Component
 * 
 * Displays error messages with retry functionality.
 * Provides accessible error handling and user feedback.
 */
interface ErrorStateProps {
  className?: string;
  testId: string;
  error: string;
  onRetry: () => void;
}

export const ErrorState: React.FC<ErrorStateProps> = ({
  className = '',
  testId,
  error,
  onRetry
}) => {
  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={`${testId}-error`}
      role="alert"
      aria-live="assertive"
    >
      <h3
        className="text-lg font-semibold mb-4"
        id={`${testId}-error-title`}
      >
        Your Preferences
      </h3>
      
      <div
        className="bg-red-50 text-red-700 p-4 rounded-md"
        aria-labelledby={`${testId}-error-title`}
      >
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-red-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          
          <div className="ml-3 flex-1">
            <p className="mb-3 text-sm">{error}</p>
            
            <button
              onClick={onRetry}
              className="px-3 py-1 bg-red-100 text-red-800 rounded-md hover:bg-red-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              aria-label="Retry loading preferences"
              data-testid={`${testId}-retry-button`}
            >
              <span className="flex items-center">
                <svg
                  className="h-4 w-4 mr-1"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                  />
                </svg>
                Retry
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
