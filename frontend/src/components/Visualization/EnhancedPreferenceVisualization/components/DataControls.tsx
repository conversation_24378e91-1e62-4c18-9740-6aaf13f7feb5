import React, { useState } from 'react';
import { downloadCSV, downloadJSON, shareData } from '../../../../utils/exportData';
import { useAccessibility } from '../hooks/useAccessibility';
import { PreferenceData } from '../utils/types';

/**
 * DataControls Component
 * 
 * Provides download and share functionality for preference data.
 * Includes dropdown menu for different export formats and share options.
 */
interface DataControlsProps {
  preferences: PreferenceData[];
  testId: string;
}

export const DataControls: React.FC<DataControlsProps> = ({
  preferences,
  testId
}) => {
  const [downloadMenuOpen, setDownloadMenuOpen] = useState(false);
  const { setScreenReaderMessage } = useAccessibility();

  const handleDownloadCSV = () => {
    const headers = [
      { key: 'preferenceType', label: 'Preference Type' },
      { key: 'weight', label: 'Weight' },
      { key: 'count', label: 'Count' }
    ];
    downloadCSV(preferences, 'preference-data', headers);
    setDownloadMenuOpen(false);
    setScreenReaderMessage('Preference data downloaded as CSV.');
  };

  const handleDownloadJSON = () => {
    downloadJSON(preferences, 'preference-data');
    setDownloadMenuOpen(false);
    setScreenReaderMessage('Preference data downloaded as JSON.');
  };

  const handleShare = async () => {
    const success = await shareData(
      preferences,
      'RentUp Preference Data',
      'Here are my preference insights from RentUp:'
    );

    if (success) {
      setScreenReaderMessage('Preference data shared successfully.');
    } else {
      setScreenReaderMessage('Failed to share preference data. Data copied to clipboard instead.');
    }
  };

  return (
    <div className="mt-4 pt-3 border-t border-gray-100">
      <h4 className="text-sm font-medium mb-2">Data Controls</h4>
      <div className="flex flex-wrap gap-3">
        {/* Download dropdown */}
        <div className="relative">
          <button
            className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            aria-label="Download preference data"
            onClick={() => setDownloadMenuOpen(!downloadMenuOpen)}
            aria-expanded={downloadMenuOpen}
            aria-controls={`${testId}-download-menu`}
            data-testid={`${testId}-download-button`}
          >
            <span className="flex items-center">
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className="h-4 w-4 mr-1" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={2} 
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" 
                />
              </svg>
              Download Data
              <svg
                className={`ml-1 h-4 w-4 transition-transform ${downloadMenuOpen ? 'rotate-180' : ''}`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </span>
          </button>
          
          {downloadMenuOpen && (
            <div
              id={`${testId}-download-menu`}
              className="absolute left-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby={`${testId}-download-button`}
            >
              <div className="py-1" role="none">
                <button
                  className="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
                  role="menuitem"
                  onClick={handleDownloadCSV}
                  data-testid={`${testId}-download-csv`}
                >
                  <span className="flex items-center">
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download as CSV
                  </span>
                </button>
                <button
                  className="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
                  role="menuitem"
                  onClick={handleDownloadJSON}
                  data-testid={`${testId}-download-json`}
                >
                  <span className="flex items-center">
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                    </svg>
                    Download as JSON
                  </span>
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Share button */}
        <button
          className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          aria-label="Share preference visualization"
          onClick={handleShare}
          data-testid={`${testId}-share-button`}
        >
          <span className="flex items-center">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-4 w-4 mr-1" 
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" 
              />
            </svg>
            Share
          </span>
        </button>
      </div>
    </div>
  );
};
