import React from 'react';

/**
 * LoadingState Component
 * 
 * Displays a skeleton loading UI while preference data is being fetched.
 * Provides accessible loading indicators and maintains layout structure.
 */
interface LoadingStateProps {
  className?: string;
  testId: string;
  compact?: boolean;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
  className = '',
  testId,
  compact = false
}) => {
  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={`${testId}-loading`}
      aria-busy="true"
      aria-live="polite"
      role="status"
    >
      <div className="sr-only">Loading preference data...</div>
      
      {/* Title skeleton */}
      <div className={`h-${compact ? '5' : '6'} bg-gray-200 animate-pulse rounded w-1/3 mb-6`}></div>
      
      {/* Preference items skeleton */}
      <div className="space-y-4">
        {[1, 2, 3, 4].map((i) => (
          <div key={i} className="space-y-2">
            {/* Header with label and count */}
            <div className="flex justify-between items-center">
              <div className="h-4 bg-gray-200 animate-pulse rounded w-1/4"></div>
              <div className="h-3 bg-gray-200 animate-pulse rounded w-1/6"></div>
            </div>
            
            {/* Progress bar */}
            <div className="w-full bg-gray-200 animate-pulse rounded-full h-2.5"></div>
            
            {/* Weight and influence */}
            <div className="flex justify-between items-center">
              <div className="h-3 bg-gray-200 animate-pulse rounded w-1/5"></div>
              <div className="h-3 bg-gray-200 animate-pulse rounded w-1/4"></div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Information section skeleton (if not compact) */}
      {!compact && (
        <div className="mt-6 pt-4 border-t border-gray-100">
          <div className="h-4 bg-gray-200 animate-pulse rounded w-1/4 mb-2"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-200 animate-pulse rounded w-full"></div>
            <div className="h-3 bg-gray-200 animate-pulse rounded w-3/4"></div>
          </div>
        </div>
      )}
    </div>
  );
};
