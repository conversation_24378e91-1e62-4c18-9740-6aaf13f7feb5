/**
 * TypeScript interfaces and types for the EnhancedPreferenceVisualization component
 */

/**
 * Preference data interface
 */
export interface PreferenceData {
  preferenceType: string;
  weight: number;
  count: number;
}

/**
 * Time period options for preference data
 */
export type TimePeriod = 'current' | '1month' | '3months' | '6months';

/**
 * Main component props interface
 */
export interface EnhancedPreferenceVisualizationProps {
  userId?: string; // Optional - if not provided, uses current user
  className?: string;
  compact?: boolean; // For smaller UI in constrained spaces
  onDataLoaded?: (data: PreferenceData[]) => void; // Callback when data is loaded
  testId?: string; // For testing purposes
  timePeriod?: TimePeriod; // Optional - for historical data
  interactive?: boolean; // Whether to enable interactive features
}

/**
 * Component props interfaces
 */
export interface LoadingStateProps {
  className?: string;
  testId: string;
  compact?: boolean;
}

export interface ErrorStateProps {
  className?: string;
  testId: string;
  error: string;
  onRetry: () => void;
}

export interface EmptyStateProps {
  className?: string;
  testId: string;
}

export interface FilterControlsProps {
  filter: string | null;
  preferenceTypes: string[];
  timePeriod: TimePeriod;
  onFilterChange: (filter: string | null) => void;
  testId: string;
}

export interface PreferenceListProps {
  preferences: PreferenceData[];
  selectedPreference: string | null;
  onPreferenceSelect: (preferenceType: string) => void;
  interactive: boolean;
  compact: boolean;
  testId: string;
}

export interface PreferenceItemProps {
  preference: PreferenceData;
  index: number;
  maxCount: number;
  sortedPreferences: PreferenceData[];
  selectedPreference: string | null;
  onPreferenceSelect: (preferenceType: string) => void;
  interactive: boolean;
  compact: boolean;
  testId: string;
}

export interface DataControlsProps {
  preferences: PreferenceData[];
  testId: string;
}

export interface InformationSectionProps {
  interactive: boolean;
}

/**
 * Hook return types
 */
export interface UsePreferenceDataReturn {
  preferences: PreferenceData[];
  loading: boolean;
  error: string | null;
  retryCount: number;
  handleRetry: () => void;
}

export interface UsePreferenceFilteringReturn {
  filter: string | null;
  filteredPreferences: PreferenceData[];
  preferenceTypes: string[];
  handleFilterChange: (filter: string | null) => void;
}

export interface UsePreferenceInteractionReturn {
  selectedPreference: string | null;
  handlePreferenceSelect: (preferenceType: string) => void;
}

export interface UseAccessibilityReturn {
  screenReaderMessage: string;
  setScreenReaderMessage: (message: string) => void;
}

/**
 * Service options interface
 */
export interface PreferenceVisualizationOptions {
  timePeriod: TimePeriod;
  onDataLoaded?: (data: PreferenceData[]) => void;
}

/**
 * Time period option interface
 */
export interface TimePeriodOption {
  value: TimePeriod;
  label: string;
}

/**
 * Export header interface
 */
export interface ExportHeader {
  key: keyof PreferenceData;
  label: string;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

/**
 * Color mapping interface
 */
export interface PreferenceColors {
  [key: string]: string;
}
