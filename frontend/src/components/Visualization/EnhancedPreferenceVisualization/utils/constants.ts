import { PreferenceColors, TimePeriodOption } from './types';

/**
 * Constants for the EnhancedPreferenceVisualization component
 */

/**
 * Colors for different preference types with high contrast for accessibility
 */
export const PREFERENCE_COLORS: PreferenceColors = {
  liked: '#4338CA', // Darker Indigo for better contrast
  favorited: '#6D28D9', // Darker Purple
  rented: '#1D4ED8', // Darker Blue
  viewed: '#0284C7', // Darker Light Blue
  searched: '#0891B2', // <PERSON><PERSON>an
  recommended: '#0F766E', // Darker Teal
  clicked: '#D97706', // Darker Amber
  bookmarked: '#059669', // Darker Emerald
  shared: '#DB2777', // Darker Pink
  default: '#6B7280' // Darker gray for better contrast
};

/**
 * Time period options for the selector
 */
export const TIME_PERIOD_OPTIONS: TimePeriodOption[] = [
  { value: 'current', label: 'Current' },
  { value: '1month', label: 'Last Month' },
  { value: '3months', label: 'Last 3 Months' },
  { value: '6months', label: 'Last 6 Months' }
];

/**
 * Animation configuration
 */
export const ANIMATION_CONFIG = {
  ITEM_DELAY: 0.1,
  DURATION: 0.3,
  PROGRESS_DURATION: 0.5
} as const;

/**
 * Request timeout configuration
 */
export const REQUEST_CONFIG = {
  TIMEOUT_MS: 15000,
  MIN_USER_ID_LENGTH: 3
} as const;

/**
 * Accessibility configuration
 */
export const ACCESSIBILITY_CONFIG = {
  ANNOUNCEMENT_DELAY: 1000
} as const;

/**
 * Validation patterns
 */
export const VALIDATION_PATTERNS = {
  PREFERENCE_TYPE: /^[a-zA-Z0-9_-]+$/
} as const;

/**
 * Default values
 */
export const DEFAULTS = {
  TEST_ID: 'enhanced-preference-visualization',
  TIME_PERIOD: 'current' as const,
  INTERACTIVE: true,
  COMPACT: false
} as const;

/**
 * CSS class names for consistent styling
 */
export const CSS_CLASSES = {
  CONTAINER: 'bg-white rounded-lg shadow-sm p-4 md:p-6',
  TITLE: 'font-semibold mb-4',
  TITLE_COMPACT: 'text-base',
  TITLE_NORMAL: 'text-lg',
  FILTER_BUTTON: 'px-3 py-1 rounded-full text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
  FILTER_BUTTON_ACTIVE: 'text-white',
  FILTER_BUTTON_INACTIVE: 'bg-gray-200 text-gray-700 hover:bg-gray-300',
  PREFERENCE_ITEM: 'space-y-1 p-2 rounded-lg transition-colors',
  PREFERENCE_ITEM_INTERACTIVE: 'cursor-pointer hover:bg-gray-50 focus-within:bg-gray-50',
  PREFERENCE_ITEM_SELECTED: 'bg-gray-50 ring-1 ring-primary-200',
  PROGRESS_BAR: 'w-full bg-gray-200 rounded-full h-2.5 overflow-hidden',
  PROGRESS_FILL: 'h-2.5 rounded-full',
  DOWNLOAD_BUTTON: 'px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2',
  MENU_ITEM: 'text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 focus:outline-none focus:bg-gray-100'
} as const;

/**
 * ARIA labels and descriptions
 */
export const ARIA_LABELS = {
  PREFERENCE_FILTERS: 'Preference filters',
  TIME_PERIOD_SELECT: 'Select time period for preference data',
  DOWNLOAD_DATA: 'Download preference data',
  SHARE_DATA: 'Share preference visualization',
  RETRY_LOADING: 'Retry loading preferences'
} as const;

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  AUTH_REQUIRED: 'Authentication required to view preferences',
  AUTH_REQUIRED_OTHER: 'Authentication required to view other user preferences',
  USER_ID_REQUIRED: 'User ID is required',
  INVALID_USER_ID: 'Invalid user ID format',
  FETCH_FAILED: 'Failed to load preference data. Please try again.'
} as const;

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  DATA_LOADED: 'Preference data loaded successfully.',
  CSV_DOWNLOADED: 'Preference data downloaded as CSV.',
  JSON_DOWNLOADED: 'Preference data downloaded as JSON.',
  DATA_SHARED: 'Preference data shared successfully.',
  SHARE_FALLBACK: 'Failed to share preference data. Data copied to clipboard instead.'
} as const;
