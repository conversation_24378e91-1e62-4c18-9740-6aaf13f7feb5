import { PreferenceData } from './types';
import { VALIDATION_PATTERNS, ERROR_MESSAGES, REQUEST_CONFIG } from './constants';

/**
 * Validation utilities for the EnhancedPreferenceVisualization component
 */

/**
 * Validates user permissions for viewing preference data
 */
export const validateUserPermissions = (userId?: string, user?: any): string | null => {
  // If no userId is provided, user must be logged in to see their own preferences
  if (!userId && !user?.id) {
    return ERROR_MESSAGES.AUTH_REQUIRED;
  }

  // If userId is provided and different from current user, check if current user has permission
  if (userId && user?.id !== userId) {
    // In a real app, we would check if the current user has admin rights or other permissions
    // For now, we'll just allow it if the user is logged in
    if (!user?.id) {
      return ERROR_MESSAGES.AUTH_REQUIRED_OTHER;
    }
  }

  return null;
};

/**
 * Validates and sanitizes preference data
 */
export const validatePreferenceData = (data: any[]): PreferenceData[] => {
  if (!Array.isArray(data)) {
    return [];
  }

  return data.filter((pref: any) => {
    // Check if all required fields exist and have correct types
    if (
      typeof pref.preferenceType !== 'string' ||
      typeof pref.weight !== 'number' ||
      typeof pref.count !== 'number'
    ) {
      return false;
    }

    // Check if values are valid
    if (pref.weight < 0 || pref.count < 0) {
      return false;
    }

    // Sanitize the preference type to prevent XSS
    if (!VALIDATION_PATTERNS.PREFERENCE_TYPE.test(pref.preferenceType)) {
      return false;
    }

    return true;
  });
};

/**
 * Validates user ID format
 */
export const validateUserId = (userId: string): boolean => {
  return (
    typeof userId === 'string' && 
    userId.length >= REQUEST_CONFIG.MIN_USER_ID_LENGTH
  );
};

/**
 * Validates preference type string
 */
export const validatePreferenceType = (preferenceType: string): boolean => {
  return (
    typeof preferenceType === 'string' &&
    VALIDATION_PATTERNS.PREFERENCE_TYPE.test(preferenceType)
  );
};

/**
 * Validates numeric values for preferences
 */
export const validateNumericValue = (value: number): boolean => {
  return typeof value === 'number' && value >= 0 && !isNaN(value);
};

/**
 * Sanitizes preference data for display
 */
export const sanitizePreferenceData = (preferences: PreferenceData[]): PreferenceData[] => {
  return preferences.map(pref => ({
    preferenceType: pref.preferenceType.toLowerCase().trim(),
    weight: Math.max(0, Number(pref.weight) || 0),
    count: Math.max(0, Math.floor(Number(pref.count)) || 0)
  }));
};
