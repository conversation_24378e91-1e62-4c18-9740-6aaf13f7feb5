import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../../../hooks/useAuth';
import { useErrorHandler } from '../../../hooks/useErrorHandler';
import recommendationService from '../../../services/recommendationService';
import { PreferenceData, TimePeriod } from '../utils/types';
import { validatePreferenceData, validateUserPermissions } from '../utils/validation';

/**
 * usePreferenceData Hook
 * 
 * Manages preference data fetching, validation, and state management.
 * Provides loading states, error handling, and retry functionality.
 */
export const usePreferenceData = (
  userId?: string,
  timePeriod: TimePeriod = 'current',
  onDataLoaded?: (data: PreferenceData[]) => void
) => {
  const { user } = useAuth();
  const { handleError } = useErrorHandler();
  
  const [preferences, setPreferences] = useState<PreferenceData[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState<number>(0);

  // Function to fetch preferences with retry logic and enhanced security
  const fetchPreferences = useCallback(async () => {
    // Validate user permissions
    const permissionError = validateUserPermissions(userId, user);
    if (permissionError) {
      setError(permissionError);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const targetUserId = userId || user?.id;

      if (!targetUserId) {
        setError('User ID is required');
        setLoading(false);
        return;
      }

      // Validate the user ID format
      if (typeof targetUserId !== 'string' || targetUserId.length < 3) {
        setError('Invalid user ID format');
        setLoading(false);
        return;
      }

      // Create a timeout for the request
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000);

      try {
        // Use the service to fetch data
        const data = await recommendationService.getPreferenceVisualization(targetUserId, {
          timePeriod,
          onDataLoaded,
        });

        clearTimeout(timeoutId);

        // Validate and sanitize the data
        const validatedData = validatePreferenceData(data);

        // Sort by weight for consistent display
        validatedData.sort((a, b) => b.weight - a.weight);

        setPreferences(validatedData);

        // Call the callback if provided
        if (onDataLoaded) {
          onDataLoaded(validatedData);
        }

        setLoading(false);
      } catch (err) {
        clearTimeout(timeoutId);
        throw err;
      }
    } catch (err) {
      handleError(err, {
        friendlyMessage: 'Failed to load preference data. Please try again.',
        logMessage: 'Error fetching preference data:',
        setError: setError,
      });
      setLoading(false);
    }
  }, [userId, user?.id, user, handleError, onDataLoaded, timePeriod]);

  // Fetch preferences on component mount and when dependencies change
  useEffect(() => {
    fetchPreferences();
  }, [fetchPreferences, retryCount]);

  // Retry loading preferences
  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
  };

  return {
    preferences,
    loading,
    error,
    retryCount,
    handleRetry
  };
};
