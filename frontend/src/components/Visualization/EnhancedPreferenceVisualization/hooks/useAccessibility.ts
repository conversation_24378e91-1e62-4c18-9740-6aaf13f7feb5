import { useState, useEffect } from 'react';

/**
 * useAccessibility Hook
 * 
 * Manages accessibility features including screen reader announcements.
 * Provides centralized accessibility state management.
 */
export const useAccessibility = () => {
  const [screenReaderMessage, setScreenReaderMessage] = useState<string>('');

  // Clear screen reader message after announcement
  useEffect(() => {
    if (screenReaderMessage) {
      const timer = setTimeout(() => {
        setScreenReaderMessage('');
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [screenReaderMessage]);

  return {
    screenReaderMessage,
    setScreenReaderMessage
  };
};
