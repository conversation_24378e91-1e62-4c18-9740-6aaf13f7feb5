import { useState } from 'react';
import { useAccessibility } from './useAccessibility';

/**
 * usePreferenceInteraction Hook
 * 
 * Manages user interactions with preference items.
 * Handles selection state and accessibility announcements.
 */
export const usePreferenceInteraction = () => {
  const [selectedPreference, setSelectedPreference] = useState<string | null>(null);
  const { setScreenReaderMessage } = useAccessibility();

  // Handle preference selection with accessibility announcements
  const handlePreferenceSelect = (preferenceType: string) => {
    if (selectedPreference === preferenceType) {
      setSelectedPreference(null);
      setScreenReaderMessage(`Deselected ${preferenceType} preference.`);
    } else {
      setSelectedPreference(preferenceType);
      setScreenReaderMessage(`Selected ${preferenceType} preference. View details below.`);
    }
  };

  return {
    selectedPreference,
    handlePreferenceSelect
  };
};
