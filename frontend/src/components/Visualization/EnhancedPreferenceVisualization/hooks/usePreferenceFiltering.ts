import { useState, useMemo } from 'react';
import { PreferenceData } from '../utils/types';
import { useAccessibility } from './useAccessibility';

/**
 * usePreferenceFiltering Hook
 * 
 * Manages filtering functionality for preference data.
 * Provides filter state management and filtered results.
 */
export const usePreferenceFiltering = (preferences: PreferenceData[]) => {
  const [filter, setFilter] = useState<string | null>(null);
  const { setScreenReaderMessage } = useAccessibility();

  // Get unique preference types for filter options
  const preferenceTypes = useMemo(() => {
    return [...new Set(preferences.map(pref => pref.preferenceType))];
  }, [preferences]);

  // Get filtered preferences
  const filteredPreferences = useMemo(() => {
    return filter
      ? preferences.filter(pref => pref.preferenceType === filter)
      : preferences;
  }, [preferences, filter]);

  // Handle filter change with accessibility announcements
  const handleFilterChange = (filterValue: string | null) => {
    setFilter(filterValue);

    if (filterValue) {
      setScreenReaderMessage(`Filtered to show only ${filterValue} preferences.`);
    } else {
      setScreenReaderMessage('Showing all preferences.');
    }
  };

  return {
    filter,
    filteredPreferences,
    preferenceTypes,
    handleFilterChange
  };
};
