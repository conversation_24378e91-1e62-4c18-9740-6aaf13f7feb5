import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useAuth } from '../../hooks/useAuth';
import { useMediaQuery } from '../../hooks/useMediaQuery';
import { Link } from 'react-router-dom';
import PreferenceVisualization from './PreferenceVisualization';
import EmbeddingVisualization from './EmbeddingVisualization';
import SimpleVisualization from './SimpleVisualization';
import recommendationService, { PreferenceData, EmbeddingPoint } from '../../services/recommendationService';
import { downloadCSV, downloadJSON, shareData } from '../../utils/exportData';

interface VisualizationDashboardProps {
  className?: string;
  testId?: string;
}

/**
 * A comprehensive dashboard for all visualization components
 * Provides an overview of user preferences, item embeddings, and recommendation insights
 */
const VisualizationDashboard: React.FC<VisualizationDashboardProps> = ({
  className = '',
  testId = 'visualization-dashboard',
}) => {
  const { user } = useAuth();
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isTablet = useMediaQuery('(min-width: 641px) and (max-width: 1024px)');

  // State for visualization data
  const [preferenceData, setPreferenceData] = useState<PreferenceData[]>([]);
  const [embeddingData, setEmbeddingData] = useState<EmbeddingPoint[]>([]);
  const [recentlyViewedItems, setRecentlyViewedItems] = useState<any[]>([]);
  const [recommendedItems, setRecommendedItems] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch data for the dashboard
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user?.id) return;

      try {
        setLoading(true);
        setError(null);

        // Fetch preference data
        const preferences = await recommendationService.getPreferenceVisualization(user.id);
        setPreferenceData(preferences);

        // Fetch embedding data
        const embeddings = await recommendationService.getEmbeddingVisualization(user.id, {
          limit: 50
        });
        setEmbeddingData(embeddings);

        // Fetch recently viewed items (mock data for now)
        setRecentlyViewedItems([
          { id: 'item1', name: 'Modern Desk Chair', category: 'furniture', viewedAt: new Date().toISOString() },
          { id: 'item2', name: 'Professional Camera', category: 'electronics', viewedAt: new Date(Date.now() - 3600000).toISOString() },
          { id: 'item3', name: 'Leather Jacket', category: 'clothing', viewedAt: new Date(Date.now() - 7200000).toISOString() },
        ]);

        // Fetch recommended items (mock data for now)
        setRecommendedItems([
          { id: 'rec1', name: 'Ergonomic Office Chair', category: 'furniture', score: 0.95 },
          { id: 'rec2', name: 'DSLR Camera Lens', category: 'electronics', score: 0.92 },
          { id: 'rec3', name: 'Winter Coat', category: 'clothing', score: 0.89 },
        ]);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load visualization data. Please try again.');
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user?.id]);

  // Calculate recommendation strength based on preference data
  const calculateRecommendationStrength = (): number => {
    if (preferenceData.length === 0) return 0;

    // Calculate the average weight of preferences
    const totalWeight = preferenceData.reduce((sum, pref) => sum + pref.weight, 0);
    const avgWeight = totalWeight / preferenceData.length;

    // Normalize to a 0-100 scale
    return Math.min(Math.round(avgWeight * 100), 100);
  };

  // Get the dominant preference category
  const getDominantPreference = (): string => {
    if (preferenceData.length === 0) return 'None';

    // Sort by weight and return the highest
    const sorted = [...preferenceData].sort((a, b) => b.weight - a.weight);
    return sorted[0]?.preferenceType || 'None';
  };

  // Get the most similar items based on embedding data
  const getMostSimilarItems = (): { item1: string, item2: string } => {
    if (embeddingData.length < 2) return { item1: 'None', item2: 'None' };

    let minDistance = Infinity;
    let closestPair = { item1: 'None', item2: 'None' };

    // Find the closest pair of items
    for (let i = 0; i < embeddingData.length; i++) {
      for (let j = i + 1; j < embeddingData.length; j++) {
        const distance = Math.sqrt(
          Math.pow(embeddingData[i].x - embeddingData[j].x, 2) +
          Math.pow(embeddingData[i].y - embeddingData[j].y, 2)
        );

        if (distance < minDistance) {
          minDistance = distance;
          closestPair = {
            item1: embeddingData[i].name,
            item2: embeddingData[j].name
          };
        }
      }
    }

    return closestPair;
  };

  // Loading state
  if (loading) {
    return (
      <div
        className={`bg-white rounded-lg shadow-md p-6 ${className}`}
        data-testid={`${testId}-loading`}
      >
        <h2 className="text-xl font-semibold mb-6">AI Recommendation Dashboard</h2>
        <div className="animate-pulse space-y-6">
          <div className="h-40 bg-gray-200 rounded"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="h-60 bg-gray-200 rounded"></div>
            <div className="h-60 bg-gray-200 rounded"></div>
          </div>
          <div className="h-40 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div
        className={`bg-white rounded-lg shadow-md p-6 ${className}`}
        data-testid={`${testId}-error`}
      >
        <h2 className="text-xl font-semibold mb-4">AI Recommendation Dashboard</h2>
        <div className="bg-red-50 text-red-700 p-4 rounded-md">
          <p className="mb-3">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-3 py-1 bg-red-100 text-red-800 rounded-md hover:bg-red-200 transition-colors text-sm"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white rounded-lg shadow-md p-6 ${className}`}
      data-testid={testId}
    >
      <div className="flex justify-between items-start mb-6">
        <h2 className="text-xl font-semibold">AI Recommendation Dashboard</h2>

        <div className="flex space-x-2">
          <div className="relative">
            <button
              className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              aria-label="Download dashboard data"
              onClick={() => {
                const downloadMenu = document.getElementById(`${testId}-download-menu`);
                if (downloadMenu) {
                  downloadMenu.classList.toggle('hidden');
                }
              }}
              aria-expanded={false}
              aria-controls={`${testId}-download-menu`}
              data-testid={`${testId}-download-button`}
            >
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export
              </span>
            </button>
            <div
              id={`${testId}-download-menu`}
              className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden z-10"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby={`${testId}-download-button`}
            >
              <div className="py-1" role="none">
                <button
                  className="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                  role="menuitem"
                  onClick={() => {
                    // Prepare dashboard data
                    const metricsData = [
                      { metric: 'Recommendation Strength', value: calculateRecommendationStrength() },
                      { metric: 'Dominant Preference', value: getDominantPreference() },
                      { metric: 'Similar Items', value: `${getMostSimilarItems().item1} & ${getMostSimilarItems().item2}` }
                    ];

                    downloadCSV(metricsData, 'dashboard-metrics');

                    // Close the menu
                    const downloadMenu = document.getElementById(`${testId}-download-menu`);
                    if (downloadMenu) {
                      downloadMenu.classList.add('hidden');
                    }
                  }}
                  data-testid={`${testId}-download-metrics-csv`}
                >
                  Download Metrics (CSV)
                </button>
                <button
                  className="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                  role="menuitem"
                  onClick={() => {
                    // Prepare dashboard data
                    const dashboardData = {
                      metrics: {
                        recommendationStrength: calculateRecommendationStrength(),
                        dominantPreference: getDominantPreference(),
                        similarItems: getMostSimilarItems()
                      },
                      preferences: preferenceData,
                      embeddings: embeddingData,
                      recentlyViewedItems,
                      recommendedItems
                    };

                    // Download as JSON
                    downloadJSON(dashboardData, 'dashboard-data');

                    // Close the menu
                    const downloadMenu = document.getElementById(`${testId}-download-menu`);
                    if (downloadMenu) {
                      downloadMenu.classList.add('hidden');
                    }
                  }}
                  data-testid={`${testId}-download-full-json`}
                >
                  Download Full Data (JSON)
                </button>
              </div>
            </div>
          </div>

          <button
            className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            aria-label="Share dashboard"
            onClick={async () => {
              // Prepare dashboard data
              const dashboardData = {
                metrics: {
                  recommendationStrength: calculateRecommendationStrength(),
                  dominantPreference: getDominantPreference(),
                  similarItems: getMostSimilarItems()
                }
              };

              const success = await shareData(
                dashboardData,
                'RentUp Recommendation Dashboard',
                `My RentUp metrics: Recommendation Strength: ${calculateRecommendationStrength()}%, Dominant Preference: ${getDominantPreference()}`
              );
            }}
            data-testid={`${testId}-share-button`}
          >
            <span className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
              </svg>
              Share
            </span>
          </button>
        </div>
      </div>

      {/* Summary cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-blue-50 p-4 rounded-lg"
        >
          <h3 className="text-lg font-medium text-blue-800 mb-2">Recommendation Strength</h3>
          <div className="flex items-center">
            <div className="text-3xl font-bold text-blue-700">{calculateRecommendationStrength()}%</div>
            <div className="ml-4 text-sm text-blue-600">
              Based on {preferenceData.length} preference factors
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-purple-50 p-4 rounded-lg"
        >
          <h3 className="text-lg font-medium text-purple-800 mb-2">Dominant Preference</h3>
          <div className="flex items-center">
            <div className="text-3xl font-bold text-purple-700 capitalize">{getDominantPreference()}</div>
            <div className="ml-4 text-sm text-purple-600">
              Highest influence on recommendations
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-green-50 p-4 rounded-lg"
        >
          <h3 className="text-lg font-medium text-green-800 mb-2">Similar Items</h3>
          <div className="text-sm text-green-600">
            Most similar items in your profile:
            <div className="mt-1 font-medium text-green-700">
              {getMostSimilarItems().item1} & {getMostSimilarItems().item2}
            </div>
          </div>
        </motion.div>
      </div>

      {/* Visualizations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div>
          <h3 className="text-lg font-medium mb-4">Your Preference Profile</h3>
          <PreferenceVisualization
            userId={user?.id}
            testId={`${testId}-preference-viz`}
            compact={isMobile}
          />
        </div>

        <div>
          <h3 className="text-lg font-medium mb-4">Item Relationships</h3>
          <EmbeddingVisualization
            testId={`${testId}-embedding-viz`}
            compact={isMobile}
            limit={30}
          />
        </div>
      </div>

      {/* Activity and recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <h3 className="text-lg font-medium mb-4">Recently Viewed Items</h3>
          <div className="bg-gray-50 rounded-lg p-4">
            {recentlyViewedItems.length > 0 ? (
              <ul className="divide-y divide-gray-200">
                {recentlyViewedItems.map(item => (
                  <li key={item.id} className="py-3">
                    <div className="flex justify-between">
                      <div>
                        <span className="font-medium">{item.name}</span>
                        <span className="ml-2 text-sm text-gray-500 capitalize">{item.category}</span>
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(item.viewedAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500 text-center py-4">No recently viewed items</p>
            )}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <h3 className="text-lg font-medium mb-4">Top Recommendations</h3>
          <div className="bg-gray-50 rounded-lg p-4">
            {recommendedItems.length > 0 ? (
              <ul className="divide-y divide-gray-200">
                {recommendedItems.map(item => (
                  <li key={item.id} className="py-3">
                    <div className="flex justify-between">
                      <div>
                        <span className="font-medium">{item.name}</span>
                        <span className="ml-2 text-sm text-gray-500 capitalize">{item.category}</span>
                      </div>
                      <div className="text-sm font-medium text-primary">
                        {Math.round(item.score * 100)}% match
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500 text-center py-4">No recommendations available</p>
            )}
          </div>
        </motion.div>
      </div>

      {/* Actions */}
      <div className="flex flex-wrap justify-center gap-4 mt-8">
        <Link
          to="/visualization/comparison"
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
          data-testid={`${testId}-comparison-link`}
        >
          Compare Over Time
        </Link>
        <Link
          to="/items"
          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
          data-testid={`${testId}-browse-link`}
        >
          Browse Recommended Items
        </Link>
      </div>
    </div>
  );
};

export default VisualizationDashboard;
