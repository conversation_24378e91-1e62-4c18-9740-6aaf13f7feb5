import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMediaQuery } from '../../hooks/useMediaQuery';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import { LiveRegion } from '../../utils/accessibility';
import { cn } from '../../utils/classNames';
import { downloadCSV, downloadJSON, shareData } from '../../utils/exportData';
import PreferenceVisualization from './PreferenceVisualization';
import EmbeddingVisualization from './EmbeddingVisualization';
import { useAuth } from '../../hooks/useAuth';
import recommendationService, { PreferenceData, EmbeddingPoint } from '../../services/recommendationService';

interface VisualizationComparisonProps {
  className?: string;
  testId?: string;
  comparisonType?: 'preferences' | 'embeddings';
  title?: string;
  description?: string;
}

/**
 * A component that allows users to compare different visualizations side by side
 * Supports comparing preferences over time or comparing different embedding visualizations
 */
const VisualizationComparison: React.FC<VisualizationComparisonProps> = ({
  className = '',
  testId = 'visualization-comparison',
  comparisonType = 'preferences',
  title = 'Visualization Comparison',
  description = 'Compare different visualizations side by side to see how they change over time.',
}) => {
  const { user } = useAuth();
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isTablet = useMediaQuery('(min-width: 641px) and (max-width: 1024px)');
  const { handleError } = useErrorHandler();

  // State for time periods
  const [timePeriod, setTimePeriod] = useState<'current' | '1month' | '3months' | '6months'>('current');
  const [compareTimePeriod, setCompareTimePeriod] = useState<'1month' | '3months' | '6months'>('1month');

  // State for categories (for embedding comparison)
  const [category, setCategory] = useState<string | null>(null);
  const [compareCategory, setCompareCategory] = useState<string | null>(null);

  // State for data loaded from visualizations
  const [currentData, setCurrentData] = useState<PreferenceData[] | EmbeddingPoint[]>([]);
  const [comparisonData, setComparisonData] = useState<PreferenceData[] | EmbeddingPoint[]>([]);

  // State for screen reader announcements
  const [screenReaderMessage, setScreenReaderMessage] = useState<string>('');

  // State for loading and error
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Handle data loaded from visualizations
  const handleCurrentDataLoaded = (data: PreferenceData[] | EmbeddingPoint[]) => {
    setCurrentData(data);
  };

  const handleComparisonDataLoaded = (data: PreferenceData[] | EmbeddingPoint[]) => {
    setComparisonData(data);
  };

  // Get time period label
  const getTimePeriodLabel = (period: 'current' | '1month' | '3months' | '6months') => {
    switch (period) {
      case 'current': return 'Current';
      case '1month': return '1 Month Ago';
      case '3months': return '3 Months Ago';
      case '6months': return '6 Months Ago';
      default: return 'Current';
    }
  };

  // Download comparison data
  const handleDownloadData = (format: 'csv' | 'json') => {
    try {
      setLoading(true);

      // Prepare data for download
      const comparisonDataForDownload = {
        firstPeriod: {
          label: getTimePeriodLabel(timePeriod),
          data: currentData
        },
        secondPeriod: {
          label: getTimePeriodLabel(compareTimePeriod),
          data: comparisonData
        },
        comparisonType,
        categories: comparisonType === 'embeddings' ? {
          firstCategory: category,
          secondCategory: compareCategory
        } : undefined
      };

      const filename = `${comparisonType}-comparison`;

      if (format === 'csv') {
        // For CSV, we need to flatten the data
        const flatData = [
          ...currentData.map((item: any) => ({
            period: getTimePeriodLabel(timePeriod),
            category: comparisonType === 'embeddings' ? category || 'All' : undefined,
            ...item
          })),
          ...comparisonData.map((item: any) => ({
            period: getTimePeriodLabel(compareTimePeriod),
            category: comparisonType === 'embeddings' ? compareCategory || 'All' : undefined,
            ...item
          }))
        ];

        downloadCSV(flatData, filename);
        setScreenReaderMessage(`${comparisonType} comparison data downloaded as CSV.`);
      } else {
        downloadJSON(comparisonDataForDownload, filename);
        setScreenReaderMessage(`${comparisonType} comparison data downloaded as JSON.`);
      }

      setLoading(false);
    } catch (err) {
      handleError(err, {
        friendlyMessage: `Failed to download ${format.toUpperCase()} data.`,
        logMessage: `Error downloading ${format} data:`,
        setError: setError,
      });
      setLoading(false);
    }
  };

  // Share comparison data
  const handleShareData = async () => {
    try {
      setLoading(true);

      // Prepare data for sharing
      const comparisonDataForSharing = {
        firstPeriod: {
          label: getTimePeriodLabel(timePeriod),
          itemCount: currentData.length
        },
        secondPeriod: {
          label: getTimePeriodLabel(compareTimePeriod),
          itemCount: comparisonData.length
        },
        comparisonType
      };

      const success = await shareData(
        comparisonDataForSharing,
        `RentUp ${comparisonType.charAt(0).toUpperCase() + comparisonType.slice(1)} Comparison`,
        `Here's my ${comparisonType} comparison from RentUp: ${getTimePeriodLabel(timePeriod)} (${currentData.length} items) vs ${getTimePeriodLabel(compareTimePeriod)} (${comparisonData.length} items)`
      );

      if (success) {
        setScreenReaderMessage(`${comparisonType} comparison data shared successfully.`);
      } else {
        setScreenReaderMessage(`Failed to share ${comparisonType} comparison data. Data copied to clipboard instead.`);
      }

      setLoading(false);
    } catch (err) {
      handleError(err, {
        friendlyMessage: 'Failed to share comparison data.',
        logMessage: 'Error sharing comparison data:',
        setError: setError,
      });
      setLoading(false);
    }
  };

  return (
    <div
      className={`bg-white rounded-lg shadow-md p-4 md:p-6 ${className}`}
      data-testid={testId}
    >
      {/* Screen reader announcements */}
      <LiveRegion message={screenReaderMessage} />

      <div className="flex justify-between items-start mb-4">
        <h2
          className="text-xl font-semibold"
          id={`${testId}-title`}
        >
          {title}
        </h2>

        <div className="flex space-x-2">
          <div className="relative">
            <button
              className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
              aria-label="Download comparison data"
              onClick={() => {
                const downloadMenu = document.getElementById(`${testId}-download-menu`);
                if (downloadMenu) {
                  downloadMenu.classList.toggle('hidden');
                }
              }}
              aria-expanded={false}
              aria-controls={`${testId}-download-menu`}
              data-testid={`${testId}-download-button`}
              disabled={loading || currentData.length === 0 || comparisonData.length === 0}
            >
              <span className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export
              </span>
            </button>
            <div
              id={`${testId}-download-menu`}
              className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 hidden z-10"
              role="menu"
              aria-orientation="vertical"
              aria-labelledby={`${testId}-download-button`}
            >
              <div className="py-1" role="none">
                <button
                  className="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                  role="menuitem"
                  onClick={() => handleDownloadData('csv')}
                  data-testid={`${testId}-download-csv`}
                  disabled={loading || currentData.length === 0 || comparisonData.length === 0}
                >
                  Download as CSV
                </button>
                <button
                  className="text-gray-700 block w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
                  role="menuitem"
                  onClick={() => handleDownloadData('json')}
                  data-testid={`${testId}-download-json`}
                  disabled={loading || currentData.length === 0 || comparisonData.length === 0}
                >
                  Download as JSON
                </button>
              </div>
            </div>
          </div>

          <button
            className="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
            aria-label="Share comparison data"
            onClick={handleShareData}
            data-testid={`${testId}-share-button`}
            disabled={loading || currentData.length === 0 || comparisonData.length === 0}
          >
            <span className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
              </svg>
              Share
            </span>
          </button>
        </div>
      </div>

      <p className="text-gray-600 mb-6">
        {description}
      </p>

      {/* Time period selection */}
      <div className="mb-6">
        <h3 className="text-sm font-medium mb-2">Select Time Periods to Compare</h3>
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label htmlFor={`${testId}-current-period`} className="block text-sm font-medium text-gray-700 mb-1">
              Current Period
            </label>
            <select
              id={`${testId}-current-period`}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
              value={timePeriod}
              onChange={(e) => setTimePeriod(e.target.value as any)}
              data-testid={`${testId}-current-period-select`}
            >
              <option value="current">Current</option>
              <option value="1month">1 Month Ago</option>
              <option value="3months">3 Months Ago</option>
              <option value="6months">6 Months Ago</option>
            </select>
          </div>

          <div className="flex-1">
            <label htmlFor={`${testId}-compare-period`} className="block text-sm font-medium text-gray-700 mb-1">
              Comparison Period
            </label>
            <select
              id={`${testId}-compare-period`}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
              value={compareTimePeriod}
              onChange={(e) => setCompareTimePeriod(e.target.value as any)}
              data-testid={`${testId}-compare-period-select`}
            >
              <option value="1month">1 Month Ago</option>
              <option value="3months">3 Months Ago</option>
              <option value="6months">6 Months Ago</option>
            </select>
          </div>
        </div>
      </div>

      {/* Category selection (only for embeddings) */}
      {comparisonType === 'embeddings' && (
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-2">Select Categories to Compare</h3>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <label htmlFor={`${testId}-current-category`} className="block text-sm font-medium text-gray-700 mb-1">
                First Category
              </label>
              <select
                id={`${testId}-current-category`}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                value={category || ''}
                onChange={(e) => setCategory(e.target.value || null)}
                data-testid={`${testId}-current-category-select`}
              >
                <option value="">All Categories</option>
                <option value="electronics">Electronics</option>
                <option value="furniture">Furniture</option>
                <option value="clothing">Clothing</option>
                <option value="tools">Tools</option>
                <option value="sports">Sports</option>
              </select>
            </div>

            <div className="flex-1">
              <label htmlFor={`${testId}-compare-category`} className="block text-sm font-medium text-gray-700 mb-1">
                Second Category
              </label>
              <select
                id={`${testId}-compare-category`}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                value={compareCategory || ''}
                onChange={(e) => setCompareCategory(e.target.value || null)}
                data-testid={`${testId}-compare-category-select`}
              >
                <option value="">All Categories</option>
                <option value="electronics">Electronics</option>
                <option value="furniture">Furniture</option>
                <option value="clothing">Clothing</option>
                <option value="tools">Tools</option>
                <option value="sports">Sports</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Visualization comparison */}
      <div className={`grid ${isMobile ? 'grid-cols-1 gap-6' : 'grid-cols-2 gap-4'}`}>
        {/* First visualization */}
        <div>
          <h3 className="text-md font-medium mb-3">{getTimePeriodLabel(timePeriod)}</h3>
          {comparisonType === 'preferences' ? (
            <PreferenceVisualization
              userId={user?.id}
              testId={`${testId}-current-preference`}
              onDataLoaded={handleCurrentDataLoaded}
              // Add time period as a query parameter
              timePeriod={timePeriod}
            />
          ) : (
            <EmbeddingVisualization
              testId={`${testId}-current-embedding`}
              onDataLoaded={handleCurrentDataLoaded}
              categoryId={category || undefined}
              compact={!isMobile}
              // Add time period as a query parameter
              timePeriod={timePeriod}
            />
          )}
        </div>

        {/* Second visualization */}
        <div>
          <h3 className="text-md font-medium mb-3">{getTimePeriodLabel(compareTimePeriod)}</h3>
          {comparisonType === 'preferences' ? (
            <PreferenceVisualization
              userId={user?.id}
              testId={`${testId}-compare-preference`}
              onDataLoaded={handleComparisonDataLoaded}
              // Add time period as a query parameter
              timePeriod={compareTimePeriod}
            />
          ) : (
            <EmbeddingVisualization
              testId={`${testId}-compare-embedding`}
              onDataLoaded={handleComparisonDataLoaded}
              categoryId={compareCategory || undefined}
              compact={!isMobile}
              // Add time period as a query parameter
              timePeriod={compareTimePeriod}
            />
          )}
        </div>
      </div>

      {/* Insights section */}
      <div className="mt-8 pt-4 border-t border-gray-200">
        <h3 className="text-lg font-medium mb-3">Insights</h3>

        {currentData.length === 0 || comparisonData.length === 0 ? (
          <div className="bg-blue-50 p-4 rounded-md">
            <p className="text-sm text-blue-800">
              {comparisonType === 'preferences' ? (
                'Select time periods to see insights about how your preferences have changed over time.'
              ) : (
                'Select time periods and categories to see insights about how item relationships have changed over time.'
              )}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-md">
              <h4 className="font-medium text-blue-800 mb-2">Summary</h4>
              <p className="text-sm text-blue-800">
                {comparisonType === 'preferences' ? (
                  <>
                    Your preferences have evolved over time. You now have {currentData.length} preference types compared to {comparisonData.length} in the {getTimePeriodLabel(compareTimePeriod).toLowerCase()} period.
                    {currentData.length > comparisonData.length ?
                      ' Your preference profile has become more diverse.' :
                      currentData.length < comparisonData.length ?
                        ' Your preference profile has become more focused.' :
                        ' The number of preference types has remained stable.'}
                  </>
                ) : (
                  <>
                    The item relationships have changed over time. There are now {currentData.length} items in the visualization compared to {comparisonData.length} in the {getTimePeriodLabel(compareTimePeriod).toLowerCase()} period.
                    {currentData.length > comparisonData.length ?
                      ' The item catalog has expanded.' :
                      currentData.length < comparisonData.length ?
                        ' The item catalog has been refined.' :
                        ' The number of items has remained stable.'}
                  </>
                )}
              </p>
            </div>

            {comparisonType === 'preferences' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-purple-50 p-4 rounded-md">
                  <h4 className="font-medium text-purple-800 mb-2">Preference Changes</h4>
                  <ul className="text-sm text-purple-800 list-disc list-inside space-y-1">
                    {currentData.length > 0 && (currentData as PreferenceData[]).slice(0, 3).map((pref, index) => {
                      const oldPref = (comparisonData as PreferenceData[]).find(p => p.preferenceType === pref.preferenceType);
                      const changeDirection = !oldPref ? 'new' : pref.weight > oldPref.weight ? 'increased' : pref.weight < oldPref.weight ? 'decreased' : 'unchanged';
                      const changeText = !oldPref ?
                        'is a new preference type' :
                        changeDirection === 'increased' ?
                          `has increased by ${Math.round((pref.weight - oldPref.weight) * 100)}%` :
                          changeDirection === 'decreased' ?
                            `has decreased by ${Math.round((oldPref.weight - pref.weight) * 100)}%` :
                            'has remained stable';

                      return (
                        <li key={pref.preferenceType} className="capitalize">
                          <span className="font-medium">{pref.preferenceType}</span> {changeText}
                        </li>
                      );
                    })}
                    {currentData.length === 0 && (
                      <li>No preference data available for the current period</li>
                    )}
                  </ul>
                </div>

                <div className="bg-green-50 p-4 rounded-md">
                  <h4 className="font-medium text-green-800 mb-2">Recommendation Impact</h4>
                  <p className="text-sm text-green-800 mb-2">
                    These changes in preferences will affect your recommendations in the following ways:
                  </p>
                  <ul className="text-sm text-green-800 list-disc list-inside space-y-1">
                    {currentData.length > 0 && (
                      <>
                        <li>
                          {currentData.length > comparisonData.length ?
                            'More diverse recommendations across different categories' :
                            currentData.length < comparisonData.length ?
                              'More focused recommendations in specific categories' :
                              'Similar diversity of recommendations'}
                        </li>
                        <li>
                          {(currentData as PreferenceData[])[0]?.weight > (comparisonData as PreferenceData[])[0]?.weight ?
                            'Stronger influence of your top preferences' :
                            'More balanced influence across preferences'}
                        </li>
                      </>
                    )}
                    {currentData.length === 0 && (
                      <li>No data available to analyze recommendation impact</li>
                    )}
                  </ul>
                </div>
              </div>
            )}

            {comparisonType === 'embeddings' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-purple-50 p-4 rounded-md">
                  <h4 className="font-medium text-purple-800 mb-2">Category Distribution</h4>
                  <ul className="text-sm text-purple-800 list-disc list-inside space-y-1">
                    {currentData.length > 0 ? (
                      <>
                        <li>
                          {category ?
                            `Showing items in the "${category}" category` :
                            'Showing items across all categories'}
                        </li>
                        <li>
                          {currentData.length > comparisonData.length ?
                            `${currentData.length - comparisonData.length} more items than before` :
                            currentData.length < comparisonData.length ?
                              `${comparisonData.length - currentData.length} fewer items than before` :
                              'Same number of items as before'}
                        </li>
                      </>
                    ) : (
                      <li>No embedding data available for the current period</li>
                    )}
                  </ul>
                </div>

                <div className="bg-green-50 p-4 rounded-md">
                  <h4 className="font-medium text-green-800 mb-2">Item Relationship Insights</h4>
                  <p className="text-sm text-green-800 mb-2">
                    The changes in item relationships suggest:
                  </p>
                  <ul className="text-sm text-green-800 list-disc list-inside space-y-1">
                    <li>
                      {currentData.length > comparisonData.length ?
                        'More options available in the marketplace' :
                        currentData.length < comparisonData.length ?
                          'More curated selection of items' :
                          'Stable marketplace with consistent options'}
                    </li>
                    <li>
                      {category && compareCategory && category !== compareCategory ?
                        `Comparing "${category}" with "${compareCategory}" shows different item clustering patterns` :
                        'Similar clustering patterns across time periods'}
                    </li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default VisualizationComparison;
