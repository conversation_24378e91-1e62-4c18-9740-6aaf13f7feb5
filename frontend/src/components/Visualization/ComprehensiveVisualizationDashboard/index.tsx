// Main ComprehensiveVisualizationDashboard component - refactored for better maintainability
import React, { useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';

// Import types
import { ComprehensiveVisualizationDashboardProps, TabType } from './utils/types';

// Import hooks
import { useDashboardState } from './hooks/useDashboardState';
import { useRecommendationExplanation } from './hooks/useRecommendationExplanation';

// Import components
import TabNavigation from './components/TabNavigation';
import TimeFilter from './components/TimeFilter';
import OverviewTab from './components/OverviewTab';
import PreferencesTab from './components/PreferencesTab';
import EmbeddingsTab from './components/EmbeddingsTab';
import RecommendationsTab from './components/RecommendationsTab';
import DashboardTab from './components/DashboardTab';

/**
 * Comprehensive Visualization Dashboard Component
 * 
 * A multi-tab dashboard that provides various visualizations and insights:
 * - Overview: Introduction and navigation
 * - Preferences: User preference visualizations
 * - Embeddings: Item relationship visualizations
 * - Recommendations: Recommendation explanations
 * - Dashboard: Analytics and metrics
 * 
 * Features:
 * - Responsive design for all screen sizes
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Performance optimized with lazy loading
 * - Error handling and retry mechanisms
 * - Time-based filtering
 * - Interactive visualizations
 */
const ComprehensiveVisualizationDashboard: React.FC<ComprehensiveVisualizationDashboardProps> = ({
  userId,
  className = '',
  testId = 'comprehensive-visualization-dashboard',
  initialTab = 'overview',
  showExplanations = true,
  showComparison = false,
  showAnalytics = true,
  interactive = true,
  timePeriod = 'current',
}) => {
  const { user } = useAuth();
  const targetUserId = userId || user?.id;

  // Dashboard state management
  const {
    activeTab,
    preferenceData,
    embeddingData,
    explanationData,
    selectedItemId,
    selectedCategory,
    comparisonItems,
    loading,
    error,
    timeFilter,
    setActiveTab,
    setPreferenceData,
    setEmbeddingData,
    setExplanationData,
    setSelectedItemId,
    setSelectedCategory,
    addItemToComparison,
    removeItemFromComparison,
    clearComparison,
    setLoading,
    setError,
    setTimeFilter,
    clearErrorAndSelection,
  } = useDashboardState({
    initialTab,
    timePeriod,
  });

  // Recommendation explanation hook
  const { retryFetch } = useRecommendationExplanation({
    selectedItemId,
    targetUserId,
    onDataLoaded: setExplanationData,
    onLoadingChange: setLoading,
    onErrorChange: setError,
  });

  // Handle tab changes
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    clearErrorAndSelection();
  };

  // Handle item selection from embeddings
  const handleItemSelect = (itemId: string) => {
    setSelectedItemId(itemId);
    if (showComparison) {
      addItemToComparison(itemId);
    }
  };

  // Handle retry for various operations
  const handleRetry = () => {
    if (activeTab === 'recommendations') {
      retryFetch();
    } else {
      clearErrorAndSelection();
    }
  };

  // Update time filter and clear data when it changes
  useEffect(() => {
    setPreferenceData([]);
    setEmbeddingData([]);
    clearErrorAndSelection();
  }, [timeFilter, setPreferenceData, setEmbeddingData, clearErrorAndSelection]);

  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-6 ${className}`}
      data-testid={testId}
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">
            Visualization Dashboard
          </h2>
          <p className="text-gray-600 text-sm">
            Explore your preferences and understand how our AI recommendations work
          </p>
        </div>
        
        {/* Time Filter */}
        <div className="mt-4 sm:mt-0">
          <TimeFilter
            timeFilter={timeFilter}
            onTimeFilterChange={setTimeFilter}
            testId={testId}
          />
        </div>
      </div>

      {/* Tab Navigation */}
      <TabNavigation
        activeTab={activeTab}
        selectedItemId={selectedItemId}
        comparisonItems={comparisonItems}
        showExplanations={showExplanations}
        showComparison={showComparison}
        showAnalytics={showAnalytics}
        testId={testId}
        onTabChange={handleTabChange}
      />

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'overview' && (
          <OverviewTab
            testId={testId}
            onTabChange={handleTabChange}
          />
        )}

        {activeTab === 'preferences' && (
          <PreferencesTab
            testId={testId}
            userId={targetUserId}
            preferenceData={preferenceData}
            timeFilter={timeFilter}
            loading={loading}
            error={error}
            onDataLoaded={setPreferenceData}
            onRetry={handleRetry}
          />
        )}

        {activeTab === 'embeddings' && (
          <EmbeddingsTab
            testId={testId}
            selectedItemId={selectedItemId}
            selectedCategory={selectedCategory}
            embeddingData={embeddingData}
            timeFilter={timeFilter}
            interactive={interactive}
            onDataLoaded={setEmbeddingData}
            onItemSelect={handleItemSelect}
            onCategorySelect={setSelectedCategory}
          />
        )}

        {activeTab === 'recommendations' && showExplanations && (
          <RecommendationsTab
            testId={testId}
            selectedItemId={selectedItemId}
            explanationData={explanationData}
            loading={loading}
            error={error}
            onRetry={handleRetry}
          />
        )}

        {activeTab === 'dashboard' && showAnalytics && (
          <DashboardTab testId={testId} />
        )}
      </div>
    </div>
  );
};

export default ComprehensiveVisualizationDashboard;
