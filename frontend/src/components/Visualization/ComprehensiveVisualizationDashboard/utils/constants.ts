// Constants for the ComprehensiveVisualizationDashboard component
import { TabConfig, DashboardMetric, CategoryMetric, InsightData } from './types';

// Tab configurations
export const TAB_CONFIGS: TabConfig[] = [
  { id: 'overview', label: 'Overview' },
  { id: 'preferences', label: 'Preferences' },
  { id: 'embeddings', label: 'Item Relationships' },
  { id: 'recommendations', label: 'Recommendations' },
  { id: 'comparison', label: 'Comparison' },
  { id: 'analytics', label: 'Analytics' },
  { id: 'dashboard', label: 'Dashboard' },
];

// Time period options
export const TIME_PERIODS = [
  { value: 'current', label: 'Current' },
  { value: '1month', label: '1 Month' },
  { value: '3months', label: '3 Months' },
  { value: '6months', label: '6 Months' },
] as const;

// Animation settings
export const ANIMATION_SETTINGS = {
  tabTransition: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
} as const;

// Dashboard metrics (mock data)
export const DASHBOARD_METRICS: DashboardMetric[] = [
  {
    label: 'Click-through Rate',
    value: '24.5%',
    percentage: 24.5,
    color: 'bg-blue-600',
  },
  {
    label: 'Conversion Rate',
    value: '8.3%',
    percentage: 8.3,
    color: 'bg-green-600',
  },
  {
    label: 'Satisfaction Score',
    value: '92%',
    percentage: 92,
    color: 'bg-purple-600',
  },
];

// Category metrics (mock data)
export const CATEGORY_METRICS: CategoryMetric[] = [
  {
    category: 'Apartments',
    percentage: 42,
    color: 'bg-indigo-600',
  },
  {
    category: 'Houses',
    percentage: 28,
    color: 'bg-pink-600',
  },
  {
    category: 'Condos',
    percentage: 18,
    color: 'bg-yellow-600',
  },
  {
    category: 'Other',
    percentage: 12,
    color: 'bg-gray-600',
  },
];

// Insight data (mock data)
export const INSIGHT_DATA: InsightData = {
  mostEffectiveFactor: 'Location preferences',
  factorInfluence: 32,
  recommendationAccuracy: 87,
  preferenceStability: 'High (changed less than 10% in last 30 days)',
  diversityScore: 72,
};

// Maximum number of comparison items
export const MAX_COMPARISON_ITEMS = 3;
