// Types for the ComprehensiveVisualizationDashboard component
import { PreferenceData, EmbeddingPoint, ExplanationData } from '../../../services/recommendationService';

export interface ComprehensiveVisualizationDashboardProps {
  userId?: string; // Optional - if not provided, uses current user
  className?: string;
  testId?: string;
  initialTab?: TabType;
  showExplanations?: boolean;
  showComparison?: boolean;
  showAnalytics?: boolean;
  interactive?: boolean;
  timePeriod?: TimePeriod;
}

export type TabType = 'overview' | 'preferences' | 'embeddings' | 'recommendations' | 'comparison' | 'analytics' | 'dashboard';

export type TimePeriod = 'current' | '1month' | '3months' | '6months';

export interface DashboardState {
  activeTab: TabType;
  preferenceData: PreferenceData[];
  embeddingData: EmbeddingPoint[];
  explanationData: ExplanationData | null;
  selectedItemId: string | null;
  selectedCategory: string | null;
  comparisonItems: string[];
  loading: boolean;
  error: string | null;
  timeFilter: TimePeriod;
}

export interface TabConfig {
  id: TabType;
  label: string;
  disabled?: boolean;
  disabledReason?: string;
}

export interface OverviewCardProps {
  title: string;
  description: string;
  buttonText: string;
  onClick: () => void;
  disabled?: boolean;
}

export interface DashboardMetric {
  label: string;
  value: string;
  percentage: number;
  color: string;
}

export interface CategoryMetric {
  category: string;
  percentage: number;
  color: string;
}

export interface InsightData {
  mostEffectiveFactor: string;
  factorInfluence: number;
  recommendationAccuracy: number;
  preferenceStability: string;
  diversityScore: number;
}
