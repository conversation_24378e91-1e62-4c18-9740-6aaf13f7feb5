// Helper functions for the ComprehensiveVisualizationDashboard component
import { TabType, TabConfig } from './types';
import { MAX_COMPARISON_ITEMS } from './constants';

/**
 * Get tab configuration with dynamic disabled state
 */
export const getTabConfig = (
  tabId: TabType,
  selectedItemId: string | null,
  comparisonItems: string[],
  showExplanations: boolean,
  showComparison: boolean,
  showAnalytics: boolean
): TabConfig & { disabled: boolean; disabledReason?: string } => {
  const baseConfig = {
    overview: { id: 'overview' as const, label: 'Overview' },
    preferences: { id: 'preferences' as const, label: 'Preferences' },
    embeddings: { id: 'embeddings' as const, label: 'Item Relationships' },
    recommendations: { id: 'recommendations' as const, label: 'Recommendations' },
    comparison: { id: 'comparison' as const, label: 'Comparison' },
    analytics: { id: 'analytics' as const, label: 'Analytics' },
    dashboard: { id: 'dashboard' as const, label: 'Dashboard' },
  };

  const config = baseConfig[tabId];

  // Determine if tab should be disabled
  let disabled = false;
  let disabledReason: string | undefined;

  switch (tabId) {
    case 'recommendations':
      if (!showExplanations) {
        disabled = true;
        disabledReason = 'Explanations are disabled';
      } else if (!selectedItemId) {
        disabled = true;
        disabledReason = 'Select an item first';
      }
      break;
    case 'comparison':
      if (!showComparison) {
        disabled = true;
        disabledReason = 'Comparison is disabled';
      } else if (comparisonItems.length === 0) {
        disabled = true;
        disabledReason = 'Select items to compare';
      }
      break;
    case 'analytics':
      if (!showAnalytics) {
        disabled = true;
        disabledReason = 'Analytics are disabled';
      }
      break;
    default:
      disabled = false;
  }

  return {
    ...config,
    disabled,
    disabledReason,
  };
};

/**
 * Add item to comparison list with max limit
 */
export const addToComparison = (
  currentItems: string[],
  newItemId: string
): string[] => {
  if (currentItems.includes(newItemId)) {
    return currentItems;
  }

  if (currentItems.length >= MAX_COMPARISON_ITEMS) {
    // Remove the oldest item and add the new one
    return [...currentItems.slice(1), newItemId];
  }

  return [...currentItems, newItemId];
};

/**
 * Remove item from comparison list
 */
export const removeFromComparison = (
  currentItems: string[],
  itemIdToRemove: string
): string[] => {
  return currentItems.filter(id => id !== itemIdToRemove);
};

/**
 * Format percentage for display
 */
export const formatPercentage = (value: number): string => {
  return `${Math.round(value)}%`;
};

/**
 * Get color class for metric bars
 */
export const getMetricColor = (metricType: string): string => {
  const colorMap: Record<string, string> = {
    'click-through': 'bg-blue-600',
    'conversion': 'bg-green-600',
    'satisfaction': 'bg-purple-600',
    'apartments': 'bg-indigo-600',
    'houses': 'bg-pink-600',
    'condos': 'bg-yellow-600',
    'other': 'bg-gray-600',
  };

  return colorMap[metricType.toLowerCase()] || 'bg-gray-600';
};

/**
 * Validate tab type
 */
export const isValidTabType = (tab: string): tab is TabType => {
  const validTabs: TabType[] = [
    'overview',
    'preferences', 
    'embeddings',
    'recommendations',
    'comparison',
    'analytics',
    'dashboard'
  ];
  return validTabs.includes(tab as TabType);
};

/**
 * Get accessible tab attributes
 */
export const getTabAttributes = (
  tabId: TabType,
  isActive: boolean,
  testId: string
) => {
  return {
    role: 'tab',
    'aria-selected': isActive,
    'aria-controls': `${testId}-${tabId}-panel`,
    id: `${testId}-${tabId}-tab`,
    'data-testid': `${testId}-${tabId}-tab`,
  };
};

/**
 * Get accessible panel attributes
 */
export const getPanelAttributes = (
  tabId: TabType,
  testId: string
) => {
  return {
    role: 'tabpanel',
    'aria-labelledby': `${testId}-${tabId}-tab`,
    id: `${testId}-${tabId}-panel`,
    'data-testid': `${testId}-${tabId}-panel`,
  };
};
