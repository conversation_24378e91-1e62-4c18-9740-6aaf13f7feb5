// Custom hook for fetching recommendation explanations
import { useEffect, useCallback } from 'react';
import { useErrorHand<PERSON> } from '../../../hooks/useErrorHandler';
import { usePerformance } from '../../../hooks/usePerformance';
import recommendationService, { ExplanationData } from '../../../services/recommendationService';

interface UseRecommendationExplanationProps {
  selectedItemId: string | null;
  targetUserId: string | undefined;
  onDataLoaded: (data: ExplanationData | null) => void;
  onLoadingChange: (loading: boolean) => void;
  onErrorChange: (error: string | null) => void;
}

/**
 * Custom hook for fetching recommendation explanations
 */
export const useRecommendationExplanation = ({
  selectedItemId,
  targetUserId,
  onDataLoaded,
  onLoadingChange,
  onErrorChange,
}: UseRecommendationExplanationProps) => {
  const { handleError } = useErrorHandler();
  const { measureApiCall } = usePerformance('RecommendationExplanation', {
    selectedItemId,
    targetUserId,
  });

  // Fetch explanation when item is selected
  const fetchExplanation = useCallback(async (itemId: string, userId: string) => {
    try {
      onLoadingChange(true);
      onErrorChange(null);

      // Use measureApiCall to track the performance of the API call
      const getExplanation = measureApiCall('getRecommendationExplanation')(
        async () => await recommendationService.getRecommendationExplanation(itemId, userId)
      );

      const data = await getExplanation();
      onDataLoaded(data);
    } catch (err) {
      handleError(err, {
        friendlyMessage: 'Failed to load recommendation explanation. Please try again.',
        logMessage: 'Error fetching recommendation explanation:',
        setError: onErrorChange,
      });
      onDataLoaded(null);
    } finally {
      onLoadingChange(false);
    }
  }, [handleError, measureApiCall, onDataLoaded, onLoadingChange, onErrorChange]);

  // Effect to fetch explanation when dependencies change
  useEffect(() => {
    if (selectedItemId && targetUserId) {
      fetchExplanation(selectedItemId, targetUserId);
    } else {
      onDataLoaded(null);
    }
  }, [selectedItemId, targetUserId, fetchExplanation, onDataLoaded]);

  // Manual retry function
  const retryFetch = useCallback(() => {
    if (selectedItemId && targetUserId) {
      fetchExplanation(selectedItemId, targetUserId);
    }
  }, [selectedItemId, targetUserId, fetchExplanation]);

  return {
    retryFetch,
  };
};
