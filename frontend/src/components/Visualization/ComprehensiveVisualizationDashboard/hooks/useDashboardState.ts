// Custom hook for managing dashboard state
import { useState, useCallback } from 'react';
import { DashboardState, TabType, TimePeriod } from '../utils/types';
import { addToComparison } from '../utils/helpers';
import { PreferenceData, EmbeddingPoint, ExplanationData } from '../../../services/recommendationService';

interface UseDashboardStateProps {
  initialTab: TabType;
  timePeriod: TimePeriod;
}

/**
 * Custom hook for managing comprehensive dashboard state
 */
export const useDashboardState = ({
  initialTab,
  timePeriod,
}: UseDashboardStateProps) => {
  // Initialize state
  const [state, setState] = useState<DashboardState>({
    activeTab: initialTab,
    preferenceData: [],
    embeddingData: [],
    explanationData: null,
    selectedItemId: null,
    selectedCategory: null,
    comparisonItems: [],
    loading: false,
    error: null,
    timeFilter: timePeriod,
  });

  // Update active tab
  const setActiveTab = useCallback((tab: TabType) => {
    setState(prev => ({ ...prev, activeTab: tab }));
  }, []);

  // Update preference data
  const setPreferenceData = useCallback((data: PreferenceData[]) => {
    setState(prev => ({ ...prev, preferenceData: data }));
  }, []);

  // Update embedding data
  const setEmbeddingData = useCallback((data: EmbeddingPoint[]) => {
    setState(prev => ({ ...prev, embeddingData: data }));
  }, []);

  // Update explanation data
  const setExplanationData = useCallback((data: ExplanationData | null) => {
    setState(prev => ({ ...prev, explanationData: data }));
  }, []);

  // Update selected item
  const setSelectedItemId = useCallback((itemId: string | null) => {
    setState(prev => ({ ...prev, selectedItemId: itemId }));
  }, []);

  // Update selected category
  const setSelectedCategory = useCallback((category: string | null) => {
    setState(prev => ({ ...prev, selectedCategory: category }));
  }, []);

  // Add item to comparison
  const addItemToComparison = useCallback((itemId: string) => {
    setState(prev => ({
      ...prev,
      comparisonItems: addToComparison(prev.comparisonItems, itemId)
    }));
  }, []);

  // Remove item from comparison
  const removeItemFromComparison = useCallback((itemId: string) => {
    setState(prev => ({
      ...prev,
      comparisonItems: prev.comparisonItems.filter(id => id !== itemId)
    }));
  }, []);

  // Clear comparison items
  const clearComparison = useCallback(() => {
    setState(prev => ({ ...prev, comparisonItems: [] }));
  }, []);

  // Update loading state
  const setLoading = useCallback((loading: boolean) => {
    setState(prev => ({ ...prev, loading }));
  }, []);

  // Update error state
  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  // Update time filter
  const setTimeFilter = useCallback((timeFilter: TimePeriod) => {
    setState(prev => ({ ...prev, timeFilter }));
  }, []);

  // Clear error and selected item
  const clearErrorAndSelection = useCallback(() => {
    setState(prev => ({
      ...prev,
      error: null,
      selectedItemId: null,
      explanationData: null
    }));
  }, []);

  return {
    // State
    ...state,
    
    // Actions
    setActiveTab,
    setPreferenceData,
    setEmbeddingData,
    setExplanationData,
    setSelectedItemId,
    setSelectedCategory,
    addItemToComparison,
    removeItemFromComparison,
    clearComparison,
    setLoading,
    setError,
    setTimeFilter,
    clearErrorAndSelection,
  };
};
