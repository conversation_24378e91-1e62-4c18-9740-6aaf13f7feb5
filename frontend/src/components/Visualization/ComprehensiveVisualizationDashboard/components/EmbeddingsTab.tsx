// Embeddings tab component
import React from 'react';
import { motion } from 'framer-motion';
import EmbeddingVisualization from '../../EmbeddingVisualization';
import { EmbeddingPoint } from '../../../services/recommendationService';
import { TimePeriod } from '../utils/types';
import { ANIMATION_SETTINGS } from '../utils/constants';
import { getPanelAttributes } from '../utils/helpers';

interface EmbeddingsTabProps {
  testId: string;
  selectedItemId: string | null;
  selectedCategory: string | null;
  embeddingData: EmbeddingPoint[];
  timeFilter: TimePeriod;
  interactive: boolean;
  onDataLoaded: (data: EmbeddingPoint[]) => void;
  onItemSelect: (itemId: string) => void;
  onCategorySelect: (category: string | null) => void;
}

/**
 * Embeddings tab component showing item relationship visualizations
 */
const EmbeddingsTab: React.FC<EmbeddingsTabProps> = ({
  testId,
  selectedItemId,
  selectedCategory,
  embeddingData,
  timeFilter,
  interactive,
  onDataLoaded,
  onItemSelect,
  onCategorySelect,
}) => {
  const panelAttributes = getPanelAttributes('embeddings', testId);

  return (
    <motion.div
      {...ANIMATION_SETTINGS.tabTransition}
      transition={{ duration: 0.3 }}
      {...panelAttributes}
    >
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Item Relationships
        </h3>
        <p className="text-gray-600">
          This visualization shows how items are related to each other in our recommendation system. 
          Items that are closer together are more similar.
        </p>
      </div>

      <EmbeddingVisualization
        itemId={selectedItemId || undefined}
        categoryId={selectedCategory || undefined}
        limit={100}
        timePeriod={timeFilter}
        interactive={interactive}
        onDataLoaded={onDataLoaded}
        onItemSelect={onItemSelect}
        onCategorySelect={onCategorySelect}
        testId={`${testId}-embeddings`}
        className="bg-white rounded-lg shadow-sm"
      />

      {embeddingData.length > 0 && (
        <div className="mt-6 bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Relationship Insights
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Total Items:</span>
              <span className="ml-2 font-medium text-gray-900">
                {embeddingData.length}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Categories:</span>
              <span className="ml-2 font-medium text-gray-900">
                {new Set(embeddingData.map(item => item.category)).size}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Selected Item:</span>
              <span className="ml-2 font-medium text-gray-900">
                {selectedItemId ? 
                  embeddingData.find(item => item.id === selectedItemId)?.name || 'Unknown' : 
                  'None'
                }
              </span>
            </div>
          </div>
        </div>
      )}

      {interactive && (
        <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Interactive Features
              </h3>
              <div className="mt-1 text-sm text-blue-700">
                <p>
                  Click on any point to select an item and see its relationships. 
                  Use the zoom and pan controls to explore the visualization in detail.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default EmbeddingsTab;
