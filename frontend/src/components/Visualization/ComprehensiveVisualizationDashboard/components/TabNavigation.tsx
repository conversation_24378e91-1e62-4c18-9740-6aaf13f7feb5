// Tab navigation component
import React from 'react';
import { TabType } from '../utils/types';
import { getTabConfig, getTabAttributes } from '../utils/helpers';

interface TabNavigationProps {
  activeTab: TabType;
  selectedItemId: string | null;
  comparisonItems: string[];
  showExplanations: boolean;
  showComparison: boolean;
  showAnalytics: boolean;
  testId: string;
  onTabChange: (tab: TabType) => void;
}

/**
 * Tab navigation component for the dashboard
 */
const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  selectedItemId,
  comparisonItems,
  showExplanations,
  showComparison,
  showAnalytics,
  testId,
  onTabChange,
}) => {
  const tabs: TabType[] = [
    'overview',
    'preferences',
    'embeddings',
    ...(showExplanations ? ['recommendations' as const] : []),
    ...(showComparison ? ['comparison' as const] : []),
    ...(showAnalytics ? ['analytics' as const] : []),
    'dashboard',
  ];

  return (
    <div
      className="flex flex-wrap border-b border-gray-200 mb-6"
      role="tablist"
      aria-label="Visualization tabs"
    >
      {tabs.map((tabId) => {
        const config = getTabConfig(
          tabId,
          selectedItemId,
          comparisonItems,
          showExplanations,
          showComparison,
          showAnalytics
        );
        
        const isActive = activeTab === tabId;
        const tabAttributes = getTabAttributes(tabId, isActive, testId);

        return (
          <button
            key={tabId}
            className={`py-2 px-4 font-medium text-sm transition-colors ${
              isActive
                ? 'border-b-2 border-blue-600 text-blue-600'
                : config.disabled
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => !config.disabled && onTabChange(tabId)}
            disabled={config.disabled}
            title={config.disabled ? config.disabledReason : undefined}
            {...tabAttributes}
          >
            {config.label}
          </button>
        );
      })}
    </div>
  );
};

export default TabNavigation;
