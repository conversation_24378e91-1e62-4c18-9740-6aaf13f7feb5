// Time filter component
import React from 'react';
import { TimePeriod } from '../utils/types';
import { TIME_PERIODS } from '../utils/constants';

interface TimeFilterProps {
  timeFilter: TimePeriod;
  onTimeFilterChange: (period: TimePeriod) => void;
  testId: string;
}

/**
 * Time filter component for selecting time periods
 */
const TimeFilter: React.FC<TimeFilterProps> = ({
  timeFilter,
  onTimeFilterChange,
  testId,
}) => {
  return (
    <div className="flex items-center space-x-2 mb-6">
      <label htmlFor={`${testId}-time-filter`} className="text-sm font-medium text-gray-700">
        Time Period:
      </label>
      <select
        id={`${testId}-time-filter`}
        value={timeFilter}
        onChange={(e) => onTimeFilterChange(e.target.value as TimePeriod)}
        className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        data-testid={`${testId}-time-filter`}
      >
        {TIME_PERIODS.map((period) => (
          <option key={period.value} value={period.value}>
            {period.label}
          </option>
        ))}
      </select>
    </div>
  );
};

export default TimeFilter;
