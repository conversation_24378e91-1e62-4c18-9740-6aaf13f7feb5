// Preferences tab component
import React from 'react';
import { motion } from 'framer-motion';
import { PreferenceVisualization } from '../../PreferenceVisualization';
import { PreferenceData } from '../../../services/recommendationService';
import { TimePeriod } from '../utils/types';
import { ANIMATION_SETTINGS } from '../utils/constants';
import { getPanelAttributes } from '../utils/helpers';

interface PreferencesTabProps {
  testId: string;
  userId?: string;
  preferenceData: PreferenceData[];
  timeFilter: TimePeriod;
  loading: boolean;
  error: string | null;
  onDataLoaded: (data: PreferenceData[]) => void;
  onRetry: () => void;
}

/**
 * Preferences tab component showing user preference visualizations
 */
const PreferencesTab: React.FC<PreferencesTabProps> = ({
  testId,
  userId,
  preferenceData,
  timeFilter,
  loading,
  error,
  onDataLoaded,
  onRetry,
}) => {
  const panelAttributes = getPanelAttributes('preferences', testId);

  return (
    <motion.div
      {...ANIMATION_SETTINGS.tabTransition}
      transition={{ duration: 0.3 }}
      {...panelAttributes}
    >
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Your Preferences
        </h3>
        <p className="text-gray-600">
          This visualization shows your preferences across different categories based on your browsing and interaction history.
        </p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Preferences</h3>
              <div className="mt-1 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-3">
                <button
                  onClick={onRetry}
                  className="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <PreferenceVisualization
        userId={userId}
        timePeriod={timeFilter}
        onDataLoaded={onDataLoaded}
        testId={`${testId}-preferences`}
        className="bg-white rounded-lg shadow-sm"
      />

      {preferenceData.length > 0 && (
        <div className="mt-6 bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Preference Insights
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Top Category:</span>
              <span className="ml-2 font-medium text-gray-900">
                {preferenceData[0]?.category || 'N/A'}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Total Categories:</span>
              <span className="ml-2 font-medium text-gray-900">
                {preferenceData.length}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Preference Strength:</span>
              <span className="ml-2 font-medium text-gray-900">
                {preferenceData[0]?.strength ? `${Math.round(preferenceData[0].strength * 100)}%` : 'N/A'}
              </span>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default PreferencesTab;
