// Dashboard tab component with analytics and metrics
import React from 'react';
import { motion } from 'framer-motion';
import { ANIMATION_SETTINGS, DASHBOARD_METRICS, CATEGORY_METRICS, INSIGHT_DATA } from '../utils/constants';
import { getPanelAttributes, formatPercentage } from '../utils/helpers';

interface DashboardTabProps {
  testId: string;
}

/**
 * Dashboard tab component showing analytics and metrics
 */
const DashboardTab: React.FC<DashboardTabProps> = ({ testId }) => {
  const panelAttributes = getPanelAttributes('dashboard', testId);

  return (
    <motion.div
      {...ANIMATION_SETTINGS.tabTransition}
      transition={{ duration: 0.3 }}
      {...panelAttributes}
    >
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Analytics Dashboard
        </h3>
        <p className="text-gray-600">
          Comprehensive analytics about your preferences, interactions, and recommendation performance.
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {DASHBOARD_METRICS.map((metric, index) => (
          <motion.div
            key={metric.label}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.label}</p>
                <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                <div className={`w-8 h-8 rounded-full ${metric.color}`}></div>
              </div>
            </div>
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${metric.color}`}
                  style={{ width: `${metric.percentage}%` }}
                ></div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Category Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <h4 className="text-lg font-semibold text-gray-900 mb-4">
            Category Preferences
          </h4>
          <div className="space-y-4">
            {CATEGORY_METRICS.map((category) => (
              <div key={category.category} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full ${category.color} mr-3`}></div>
                  <span className="text-sm font-medium text-gray-700">{category.category}</span>
                </div>
                <div className="flex items-center">
                  <span className="text-sm text-gray-600 mr-2">{formatPercentage(category.percentage)}</span>
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${category.color}`}
                      style={{ width: `${category.percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Insights */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <h4 className="text-lg font-semibold text-gray-900 mb-4">
            AI Insights
          </h4>
          <div className="space-y-4">
            <div>
              <p className="text-sm font-medium text-gray-700">Most Effective Factor</p>
              <p className="text-sm text-gray-600">{INSIGHT_DATA.mostEffectiveFactor}</p>
              <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full bg-green-600"
                  style={{ width: `${INSIGHT_DATA.factorInfluence}%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-700">Recommendation Accuracy</p>
              <p className="text-sm text-gray-600">{formatPercentage(INSIGHT_DATA.recommendationAccuracy)}</p>
              <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full bg-blue-600"
                  style={{ width: `${INSIGHT_DATA.recommendationAccuracy}%` }}
                ></div>
              </div>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-700">Preference Stability</p>
              <p className="text-sm text-gray-600">{INSIGHT_DATA.preferenceStability}</p>
            </div>

            <div>
              <p className="text-sm font-medium text-gray-700">Diversity Score</p>
              <p className="text-sm text-gray-600">{formatPercentage(INSIGHT_DATA.diversityScore)}</p>
              <div className="mt-1 w-full bg-gray-200 rounded-full h-2">
                <div
                  className="h-2 rounded-full bg-purple-600"
                  style={{ width: `${INSIGHT_DATA.diversityScore}%` }}
                ></div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Additional Information */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="text-sm font-medium text-gray-900 mb-2">
          About These Metrics
        </h4>
        <div className="text-sm text-gray-600 space-y-2">
          <p>
            <strong>Click-through Rate:</strong> Percentage of recommended items you clicked on.
          </p>
          <p>
            <strong>Conversion Rate:</strong> Percentage of clicked recommendations that led to bookings.
          </p>
          <p>
            <strong>Satisfaction Score:</strong> Based on your ratings and feedback.
          </p>
          <p>
            <strong>Diversity Score:</strong> How varied your preferences are across different categories.
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default DashboardTab;
