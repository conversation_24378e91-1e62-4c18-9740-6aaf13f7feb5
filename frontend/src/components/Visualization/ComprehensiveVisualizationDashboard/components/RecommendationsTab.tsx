// Recommendations tab component
import React from 'react';
import { motion } from 'framer-motion';
import { RecommendationExplanation } from '../../RecommendationExplanation';
import { ExplanationData } from '../../../services/recommendationService';
import { ANIMATION_SETTINGS } from '../utils/constants';
import { getPanelAttributes } from '../utils/helpers';

interface RecommendationsTabProps {
  testId: string;
  selectedItemId: string | null;
  explanationData: ExplanationData | null;
  loading: boolean;
  error: string | null;
  onRetry: () => void;
}

/**
 * Recommendations tab component showing recommendation explanations
 */
const RecommendationsTab: React.FC<RecommendationsTabProps> = ({
  testId,
  selectedItemId,
  explanationData,
  loading,
  error,
  onRetry,
}) => {
  const panelAttributes = getPanelAttributes('recommendations', testId);

  if (!selectedItemId) {
    return (
      <motion.div
        {...ANIMATION_SETTINGS.tabTransition}
        transition={{ duration: 0.3 }}
        {...panelAttributes}
      >
        <div className="text-center py-12">
          <svg className="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Item Selected
          </h3>
          <p className="text-gray-600">
            Select an item from the Item Relationships tab to see why it was recommended to you.
          </p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      {...ANIMATION_SETTINGS.tabTransition}
      transition={{ duration: 0.3 }}
      {...panelAttributes}
    >
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Recommendation Explanation
        </h3>
        <p className="text-gray-600">
          Understand why this item was recommended to you based on your preferences and behavior.
        </p>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Explanation</h3>
              <div className="mt-1 text-sm text-red-700">
                <p>{error}</p>
              </div>
              <div className="mt-3">
                <button
                  onClick={onRetry}
                  className="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <RecommendationExplanation
        itemId={selectedItemId}
        explanationData={explanationData}
        loading={loading}
        testId={`${testId}-explanation`}
        className="bg-white rounded-lg shadow-sm"
      />

      {explanationData && (
        <div className="mt-6 bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Explanation Summary
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Confidence Score:</span>
              <span className="ml-2 font-medium text-gray-900">
                {explanationData.confidence ? `${Math.round(explanationData.confidence * 100)}%` : 'N/A'}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Main Factors:</span>
              <span className="ml-2 font-medium text-gray-900">
                {explanationData.factors?.length || 0}
              </span>
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default RecommendationsTab;
