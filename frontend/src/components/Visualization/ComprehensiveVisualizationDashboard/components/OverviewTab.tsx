// Overview tab component
import React from 'react';
import { motion } from 'framer-motion';
import { TabType } from '../utils/types';
import { ANIMATION_SETTINGS } from '../utils/constants';
import { getPanelAttributes } from '../utils/helpers';

interface OverviewTabProps {
  testId: string;
  onTabChange: (tab: TabType) => void;
}

/**
 * Overview tab component showing dashboard introduction and navigation cards
 */
const OverviewTab: React.FC<OverviewTabProps> = ({ testId, onTabChange }) => {
  const panelAttributes = getPanelAttributes('overview', testId);

  const cards = [
    {
      title: 'User Preferences',
      description: 'Visualize your preferences across different categories and see how they influence recommendations.',
      buttonText: 'View Preferences',
      onClick: () => onTabChange('preferences'),
    },
    {
      title: 'Item Relationships',
      description: 'Explore how items are related to each other in our recommendation system.',
      buttonText: 'View Relationships',
      onClick: () => onTabChange('embeddings'),
    },
    {
      title: 'Recommendation Insights',
      description: 'Understand why specific items are recommended to you with detailed explanations.',
      buttonText: 'View Insights',
      onClick: () => onTabChange('recommendations'),
    },
    {
      title: 'Analytics Dashboard',
      description: 'View comprehensive analytics about your browsing patterns and preferences.',
      buttonText: 'View Analytics',
      onClick: () => onTabChange('dashboard'),
    },
  ];

  return (
    <motion.div
      {...ANIMATION_SETTINGS.tabTransition}
      transition={{ duration: 0.3 }}
      {...panelAttributes}
    >
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Visualization Dashboard
        </h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Explore your preferences, understand recommendations, and gain insights into how our AI system works for you.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {cards.map((card, index) => (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-3">
              {card.title}
            </h3>
            <p className="text-gray-600 mb-4">
              {card.description}
            </p>
            <button
              onClick={card.onClick}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              {card.buttonText}
            </button>
          </motion.div>
        ))}
      </div>

      <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              About This Dashboard
            </h3>
            <div className="mt-1 text-sm text-blue-700">
              <p>
                This dashboard provides insights into how our recommendation system works. 
                All visualizations are based on your actual usage patterns and preferences, 
                helping you understand and control your personalized experience.
              </p>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default OverviewTab;
