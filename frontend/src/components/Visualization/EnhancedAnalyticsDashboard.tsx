import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '../../hooks/useAuth';
import { useMediaQuery } from '../../hooks/useMediaQuery';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import { usePerformance } from '../../hooks/usePerformance';
import recommendationService from '../../services/recommendationService';

interface AnalyticsData {
  clickThroughRate: number;
  conversionRate: number;
  satisfactionScore: number;
  categoryDistribution: Record<string, number>;
  topFactors: { factor: string; influence: number }[];
  recommendationAccuracy: number;
  preferenceStability: number;
  diversityScore: number;
  timeSeriesData: {
    date: string;
    ctr: number;
    conversion: number;
    satisfaction: number;
  }[];
}

interface EnhancedAnalyticsDashboardProps {
  userId?: string; // Optional - if not provided, uses current user
  className?: string;
  testId?: string;
  timePeriod?: 'current' | '1month' | '3months' | '6months';
}

/**
 * Enhanced analytics dashboard for recommendation system performance
 * Shows detailed metrics and visualizations for recommendation effectiveness
 * 
 * Features:
 * - Performance metrics (CTR, conversion rate, satisfaction)
 * - Category distribution analysis
 * - Time series data visualization
 * - Top influencing factors
 * - Recommendation quality metrics
 */
const EnhancedAnalyticsDashboard: React.FC<EnhancedAnalyticsDashboardProps> = ({
  userId,
  className = '',
  testId = 'enhanced-analytics',
  timePeriod = 'current',
}) => {
  // State
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeMetric, setActiveMetric] = useState<'ctr' | 'conversion' | 'satisfaction'>('ctr');
  
  // Hooks
  const { user } = useAuth();
  const { handleError } = useErrorHandler();
  const targetUserId = userId || user?.id;
  const { measureFunction, measureApiCall } = usePerformance('EnhancedAnalyticsDashboard', {
    userId: targetUserId,
    timePeriod
  });
  const isMobile = useMediaQuery('(max-width: 640px)');
  const isTablet = useMediaQuery('(min-width: 641px) and (max-width: 1024px)');

  // Mock data function - in a real app, this would be an API call
  const fetchAnalyticsData = useCallback(async () => {
    if (!targetUserId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // In a real app, this would be an API call to get analytics data
      // For now, we'll use mock data
      const mockData: AnalyticsData = {
        clickThroughRate: 24.5,
        conversionRate: 8.3,
        satisfactionScore: 92,
        categoryDistribution: {
          'Apartments': 42,
          'Houses': 28,
          'Condos': 18,
          'Other': 12,
        },
        topFactors: [
          { factor: 'Location', influence: 32 },
          { factor: 'Price', influence: 28 },
          { factor: 'Amenities', influence: 18 },
          { factor: 'Reviews', influence: 12 },
          { factor: 'Availability', influence: 10 },
        ],
        recommendationAccuracy: 87,
        preferenceStability: 90,
        diversityScore: 72,
        timeSeriesData: [
          { date: '2025-01-01', ctr: 18.2, conversion: 6.1, satisfaction: 85 },
          { date: '2025-02-01', ctr: 19.5, conversion: 6.8, satisfaction: 87 },
          { date: '2025-03-01', ctr: 21.3, conversion: 7.2, satisfaction: 88 },
          { date: '2025-04-01', ctr: 22.7, conversion: 7.5, satisfaction: 90 },
          { date: '2025-05-01', ctr: 24.5, conversion: 8.3, satisfaction: 92 },
        ],
      };
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAnalyticsData(mockData);
      setLoading(false);
    } catch (err) {
      handleError(err, {
        friendlyMessage: 'Failed to load analytics data. Please try again.',
        logMessage: 'Error fetching analytics data:',
        setError: setError,
      });
      setLoading(false);
    }
  }, [targetUserId, handleError]);

  // Fetch analytics data on component mount and when dependencies change
  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData, timePeriod]);

  // Handle metric selection
  const handleMetricSelect = useCallback((metric: 'ctr' | 'conversion' | 'satisfaction') => {
    const selectMetric = measureFunction('handleMetricSelect')(() => {
      setActiveMetric(metric);
    });
    
    selectMetric();
  }, [measureFunction]);

  // Render loading state
  if (loading) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-loading`}
        aria-busy="true"
        aria-live="polite"
      >
        <h3 className="text-lg font-semibold mb-4">Recommendation Analytics</h3>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-error`}
        role="alert"
        aria-live="assertive"
      >
        <h3 className="text-lg font-semibold mb-4">Recommendation Analytics</h3>
        <div className="bg-red-50 text-red-700 p-4 rounded-md mb-4">
          <p className="font-medium mb-2">Error loading analytics</p>
          <p>{error}</p>
          <button
            onClick={fetchAnalyticsData}
            className="mt-2 px-3 py-1 bg-red-100 text-red-800 rounded-md hover:bg-red-200 transition-colors text-sm"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Render empty state
  if (!analyticsData) {
    return (
      <div
        className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
        data-testid={`${testId}-empty`}
      >
        <h3 className="text-lg font-semibold mb-4">Recommendation Analytics</h3>
        <div className="text-center py-8 text-gray-700">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 mx-auto text-gray-500 mb-3"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
          <p className="text-lg font-medium mb-1">No analytics data available</p>
          <p className="mt-2 text-sm">Analytics data will be available once you have more interactions with recommendations.</p>
        </div>
      </div>
    );
  }

  // Render analytics dashboard
  return (
    <div
      className={`bg-white rounded-lg shadow-sm p-4 md:p-6 ${className}`}
      data-testid={testId}
    >
      <h3 className="text-lg font-semibold mb-6">Recommendation Analytics</h3>
      
      {/* Key metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div 
          className={`p-4 rounded-lg ${activeMetric === 'ctr' ? 'bg-blue-50 border border-blue-100' : 'bg-gray-50'}`}
          onClick={() => handleMetricSelect('ctr')}
          role="button"
          tabIndex={0}
          aria-pressed={activeMetric === 'ctr'}
        >
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-sm font-medium">Click-through Rate</h4>
            <span className={`text-lg font-semibold ${activeMetric === 'ctr' ? 'text-blue-700' : 'text-gray-700'}`}>
              {analyticsData.clickThroughRate}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${activeMetric === 'ctr' ? 'bg-blue-600' : 'bg-gray-500'}`} 
              style={{ width: `${analyticsData.clickThroughRate}%` }}
            ></div>
          </div>
          <p className="text-xs text-gray-600 mt-2">
            Percentage of users who click on recommendations
          </p>
        </div>
        
        <div 
          className={`p-4 rounded-lg ${activeMetric === 'conversion' ? 'bg-green-50 border border-green-100' : 'bg-gray-50'}`}
          onClick={() => handleMetricSelect('conversion')}
          role="button"
          tabIndex={0}
          aria-pressed={activeMetric === 'conversion'}
        >
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-sm font-medium">Conversion Rate</h4>
            <span className={`text-lg font-semibold ${activeMetric === 'conversion' ? 'text-green-700' : 'text-gray-700'}`}>
              {analyticsData.conversionRate}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${activeMetric === 'conversion' ? 'bg-green-600' : 'bg-gray-500'}`} 
              style={{ width: `${analyticsData.conversionRate * 3}%` }}
            ></div>
          </div>
          <p className="text-xs text-gray-600 mt-2">
            Percentage of recommendations that lead to bookings
          </p>
        </div>
        
        <div 
          className={`p-4 rounded-lg ${activeMetric === 'satisfaction' ? 'bg-purple-50 border border-purple-100' : 'bg-gray-50'}`}
          onClick={() => handleMetricSelect('satisfaction')}
          role="button"
          tabIndex={0}
          aria-pressed={activeMetric === 'satisfaction'}
        >
          <div className="flex justify-between items-center mb-2">
            <h4 className="text-sm font-medium">Satisfaction Score</h4>
            <span className={`text-lg font-semibold ${activeMetric === 'satisfaction' ? 'text-purple-700' : 'text-gray-700'}`}>
              {analyticsData.satisfactionScore}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full ${activeMetric === 'satisfaction' ? 'bg-purple-600' : 'bg-gray-500'}`} 
              style={{ width: `${analyticsData.satisfactionScore}%` }}
            ></div>
          </div>
          <p className="text-xs text-gray-600 mt-2">
            User satisfaction with recommendations
          </p>
        </div>
      </div>
      
      {/* Time series chart */}
      <div className="mb-8">
        <h4 className="text-md font-medium mb-3">Performance Trends</h4>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="h-48 relative">
            {/* Chart axes */}
            <div className="absolute left-0 top-0 bottom-0 w-px bg-gray-300"></div>
            <div className="absolute left-0 right-0 bottom-0 h-px bg-gray-300"></div>
            
            {/* Chart data */}
            <div className="absolute left-0 right-0 top-0 bottom-0 flex items-end">
              {analyticsData.timeSeriesData.map((point, index) => {
                const value = activeMetric === 'ctr' 
                  ? point.ctr 
                  : activeMetric === 'conversion' 
                    ? point.conversion 
                    : point.satisfaction;
                
                const maxValue = activeMetric === 'ctr' 
                  ? 30 
                  : activeMetric === 'conversion' 
                    ? 10 
                    : 100;
                
                const height = `${(value / maxValue) * 100}%`;
                const width = `${100 / analyticsData.timeSeriesData.length}%`;
                
                return (
                  <div 
                    key={index} 
                    className="h-full flex flex-col justify-end items-center"
                    style={{ width }}
                  >
                    <div 
                      className={`w-4/5 rounded-t ${
                        activeMetric === 'ctr' 
                          ? 'bg-blue-500' 
                          : activeMetric === 'conversion' 
                            ? 'bg-green-500' 
                            : 'bg-purple-500'
                      }`}
                      style={{ height }}
                      title={`${new Date(point.date).toLocaleDateString()}: ${value}%`}
                    ></div>
                    <span className="text-xs text-gray-600 mt-1">
                      {new Date(point.date).toLocaleDateString(undefined, { month: 'short' })}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
          <div className="text-center mt-2">
            <span className="text-xs text-gray-600">
              {activeMetric === 'ctr' 
                ? 'Click-through Rate' 
                : activeMetric === 'conversion' 
                  ? 'Conversion Rate' 
                  : 'Satisfaction Score'} over time
            </span>
          </div>
        </div>
      </div>
      
      {/* Category distribution */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div>
          <h4 className="text-md font-medium mb-3">Category Distribution</h4>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="space-y-3">
              {Object.entries(analyticsData.categoryDistribution).map(([category, percentage]) => (
                <div key={category}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">{category}</span>
                    <span className="text-sm">{percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={
                        category === 'Apartments' ? 'bg-indigo-600' :
                        category === 'Houses' ? 'bg-pink-600' :
                        category === 'Condos' ? 'bg-yellow-600' :
                        'bg-gray-600'
                      }
                      style={{ width: `${percentage}%`, height: '100%', borderRadius: '9999px' }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div>
          <h4 className="text-md font-medium mb-3">Top Influencing Factors</h4>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="space-y-3">
              {analyticsData.topFactors.map((factor) => (
                <div key={factor.factor}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">{factor.factor}</span>
                    <span className="text-sm">{factor.influence}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: `${factor.influence}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      
      {/* Recommendation quality metrics */}
      <div>
        <h4 className="text-md font-medium mb-3">Recommendation Quality</h4>
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-3 bg-white rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-blue-700">{analyticsData.recommendationAccuracy}%</div>
              <div className="text-sm text-gray-600">Accuracy</div>
              <div className="text-xs text-gray-500 mt-1">Match with eventual selections</div>
            </div>
            
            <div className="text-center p-3 bg-white rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-green-700">{analyticsData.preferenceStability}%</div>
              <div className="text-sm text-gray-600">Preference Stability</div>
              <div className="text-xs text-gray-500 mt-1">Consistency over time</div>
            </div>
            
            <div className="text-center p-3 bg-white rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-purple-700">{analyticsData.diversityScore}%</div>
              <div className="text-sm text-gray-600">Diversity Score</div>
              <div className="text-xs text-gray-500 mt-1">Variety in recommendations</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedAnalyticsDashboard;
