import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { BusinessMember, BusinessRole } from '../../types/BusinessAccount';
import { useTranslation } from 'react-i18next';

/**
 * BusinessAccountSwitcher Component
 * 
 * A dropdown component that allows users to switch between personal and business accounts.
 */
const BusinessAccountSwitcher: React.FC = () => {
  const { t } = useTranslation();
  const { user, currentBusiness, isBusinessAccount, switchToBusiness, switchToPersonal } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Toggle dropdown
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };
  
  // Handle account switch
  const handleSwitchAccount = (businessId?: string) => {
    if (businessId) {
      switchToBusiness(businessId);
    } else {
      switchToPersonal();
    }
    setIsOpen(false);
  };
  
  // Get role display name
  const getRoleDisplay = (role: BusinessRole) => {
    switch (role) {
      case BusinessRole.OWNER:
        return t('business.roles.owner');
      case BusinessRole.ADMIN:
        return t('business.roles.admin');
      case BusinessRole.MEMBER:
        return t('business.roles.member');
      case BusinessRole.VIEWER:
        return t('business.roles.viewer');
      default:
        return role;
    }
  };
  
  // If user has no business accounts, don't render the switcher
  if (!user?.businessAccounts || user.businessAccounts.length === 0) {
    return null;
  }
  
  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary focus:outline-none"
        onClick={toggleDropdown}
        aria-expanded={isOpen}
        aria-haspopup="true"
        aria-label={t('business.switchAccount')}
      >
        {isBusinessAccount ? (
          <>
            <div className="w-6 h-6 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 font-bold">
              {currentBusiness?.name.charAt(0)}
            </div>
            <span className="hidden md:inline-block max-w-[120px] truncate">{currentBusiness?.name}</span>
          </>
        ) : (
          <>
            <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center text-gray-700">
              {user?.name.charAt(0)}
            </div>
            <span className="hidden md:inline-block max-w-[120px] truncate">{user?.name}</span>
          </>
        )}
        <svg
          className={`h-5 w-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>
      
      {/* Dropdown menu */}
      {isOpen && (
        <div
          className="absolute right-0 mt-2 w-64 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50 divide-y divide-gray-100"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="account-menu"
        >
          {/* Personal account */}
          <div className="py-1" role="none">
            <button
              className={`w-full text-left px-4 py-2 text-sm ${
                !isBusinessAccount
                  ? 'bg-gray-100 text-gray-900 font-medium'
                  : 'text-gray-700 hover:bg-gray-50'
              } flex items-center space-x-3`}
              role="menuitem"
              onClick={() => handleSwitchAccount()}
            >
              <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center text-gray-700">
                {user?.name.charAt(0)}
              </div>
              <div className="flex flex-col items-start">
                <span className="font-medium">{user?.name}</span>
                <span className="text-xs text-gray-500">{t('business.personalAccount')}</span>
              </div>
              {!isBusinessAccount && (
                <svg
                  className="ml-auto h-5 w-5 text-primary-600"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              )}
            </button>
          </div>
          
          {/* Business accounts */}
          <div className="py-1" role="none">
            <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider">
              {t('business.businessAccounts')}
            </div>
            {user?.businessAccounts.map((account: BusinessMember) => (
              <button
                key={account.business_id}
                className={`w-full text-left px-4 py-2 text-sm ${
                  isBusinessAccount && currentBusiness?.id === account.business_id
                    ? 'bg-gray-100 text-gray-900 font-medium'
                    : 'text-gray-700 hover:bg-gray-50'
                } flex items-center space-x-3`}
                role="menuitem"
                onClick={() => handleSwitchAccount(account.business_id)}
              >
                <div className="w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center text-primary-700 font-bold">
                  {account.name.charAt(0)}
                </div>
                <div className="flex flex-col items-start">
                  <span className="font-medium">{account.name}</span>
                  <span className="text-xs text-gray-500">{getRoleDisplay(account.role)}</span>
                </div>
                {isBusinessAccount && currentBusiness?.id === account.business_id && (
                  <svg
                    className="ml-auto h-5 w-5 text-primary-600"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
            ))}
          </div>
          
          {/* Create new business account */}
          <div className="py-1" role="none">
            <Link
              to="/business/create"
              className="block px-4 py-2 text-sm text-primary-600 hover:bg-gray-50 font-medium"
              role="menuitem"
              onClick={() => setIsOpen(false)}
            >
              <div className="flex items-center space-x-2">
                <svg
                  className="h-5 w-5"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <span>{t('business.createNewBusiness')}</span>
              </div>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessAccountSwitcher;
