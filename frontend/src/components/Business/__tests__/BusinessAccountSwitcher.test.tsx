import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import BusinessAccountSwitcher from '../BusinessAccountSwitcher';
import { useAuth } from '../../../hooks/useAuth';

// Mock the useAuth hook
jest.mock('../../../hooks/useAuth', () => ({
  useAuth: jest.fn()
}));

// Mock the useTranslation hook
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key
  })
}));

describe('BusinessAccountSwitcher Component', () => {
  const mockSwitchToBusiness = jest.fn();
  const mockSwitchToPersonal = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders nothing when user has no business accounts', () => {
    // Mock the useAuth hook to return a user with no business accounts
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        businessAccounts: []
      },
      currentBusiness: null,
      isBusinessAccount: false,
      switchToBusiness: mockSwitchToBusiness,
      switchToPersonal: mockSwitchToPersonal
    });

    const { container } = render(
      <BrowserRouter>
        <BusinessAccountSwitcher />
      </BrowserRouter>
    );

    // Component should not render anything
    expect(container.firstChild).toBeNull();
  });

  test('renders the switcher with personal account selected', () => {
    // Mock the useAuth hook to return a user with business accounts but no current business
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        businessAccounts: [
          {
            business_id: 'b1',
            user_id: '1',
            role: 'owner',
            name: 'Test Business',
            email: '<EMAIL>',
            joined_at: '2023-01-01',
            invited_by: '1',
            is_active: true,
            permissions: {
              manage_members: true,
              manage_listings: true,
              manage_rentals: true,
              manage_billing: true,
              manage_settings: true,
              view_analytics: true,
              approve_transactions: true
            }
          }
        ]
      },
      currentBusiness: null,
      isBusinessAccount: false,
      switchToBusiness: mockSwitchToBusiness,
      switchToPersonal: mockSwitchToPersonal
    });

    render(
      <BrowserRouter>
        <BusinessAccountSwitcher />
      </BrowserRouter>
    );

    // Button should be visible
    const button = screen.getByRole('button', { name: /business.switchAccount/i });
    expect(button).toBeInTheDocument();

    // Personal account info should be visible
    expect(screen.getByText('T')).toBeInTheDocument(); // First letter of Test User

    // Click the button to open the dropdown
    fireEvent.click(button);

    // Personal account option should be visible and selected
    const personalOption = screen.getAllByText('Test User')[0];
    expect(personalOption).toBeInTheDocument();

    // Business account option should be visible
    const businessOption = screen.getAllByText('Test Business')[0];
    expect(businessOption).toBeInTheDocument();

    // Create new business link should be visible
    const createLink = screen.getByText('business.createNewBusiness');
    expect(createLink).toBeInTheDocument();
  });

  test('renders the switcher with business account selected', () => {
    // Mock the useAuth hook to return a user with a current business
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1',
        businessAccounts: [
          {
            business_id: 'b1',
            user_id: '1',
            role: 'owner',
            name: 'Test Business',
            email: '<EMAIL>',
            joined_at: '2023-01-01',
            invited_by: '1',
            is_active: true,
            permissions: {
              manage_members: true,
              manage_listings: true,
              manage_rentals: true,
              manage_billing: true,
              manage_settings: true,
              view_analytics: true,
              approve_transactions: true
            }
          }
        ]
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 1,
        listing_count: 0,
        rental_count: 0,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      switchToBusiness: mockSwitchToBusiness,
      switchToPersonal: mockSwitchToPersonal
    });

    render(
      <BrowserRouter>
        <BusinessAccountSwitcher />
      </BrowserRouter>
    );

    // Button should be visible
    const button = screen.getByRole('button', { name: /business.switchAccount/i });
    expect(button).toBeInTheDocument();

    // Business account info should be visible
    expect(screen.getByText('T')).toBeInTheDocument(); // First letter of Test Business

    // Click the button to open the dropdown
    fireEvent.click(button);

    // Personal account option should be visible
    const personalOption = screen.getByText('Test User');
    expect(personalOption).toBeInTheDocument();

    // Business account option should be visible and selected
    const businessOption = screen.getAllByText('Test Business')[0];
    expect(businessOption).toBeInTheDocument();
  });

  test('switches to business account when clicked', () => {
    // Mock the useAuth hook to return a user with business accounts but no current business
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        businessAccounts: [
          {
            business_id: 'b1',
            user_id: '1',
            role: 'owner',
            name: 'Test Business',
            email: '<EMAIL>',
            joined_at: '2023-01-01',
            invited_by: '1',
            is_active: true,
            permissions: {
              manage_members: true,
              manage_listings: true,
              manage_rentals: true,
              manage_billing: true,
              manage_settings: true,
              view_analytics: true,
              approve_transactions: true
            }
          }
        ]
      },
      currentBusiness: null,
      isBusinessAccount: false,
      switchToBusiness: mockSwitchToBusiness,
      switchToPersonal: mockSwitchToPersonal
    });

    render(
      <BrowserRouter>
        <BusinessAccountSwitcher />
      </BrowserRouter>
    );

    // Click the button to open the dropdown
    fireEvent.click(screen.getByRole('button', { name: /business.switchAccount/i }));

    // Click the business account option
    fireEvent.click(screen.getByText('Test Business'));

    // Check that switchToBusiness was called with the correct business ID
    expect(mockSwitchToBusiness).toHaveBeenCalledWith('b1');
  });

  test('switches to personal account when clicked', () => {
    // Mock the useAuth hook to return a user with a current business
    (useAuth as jest.Mock).mockReturnValue({
      user: {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        currentBusinessId: 'b1',
        businessAccounts: [
          {
            business_id: 'b1',
            user_id: '1',
            role: 'owner',
            name: 'Test Business',
            email: '<EMAIL>',
            joined_at: '2023-01-01',
            invited_by: '1',
            is_active: true,
            permissions: {
              manage_members: true,
              manage_listings: true,
              manage_rentals: true,
              manage_billing: true,
              manage_settings: true,
              view_analytics: true,
              approve_transactions: true
            }
          }
        ]
      },
      currentBusiness: {
        id: 'b1',
        name: 'Test Business',
        industry: 'other',
        size: 'small',
        tier: 'professional',
        is_verified: true,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        owner_id: '1',
        member_count: 1,
        listing_count: 0,
        rental_count: 0,
        is_active: true,
        subscription_status: 'active',
        features: {
          max_members: 20,
          max_listings: 200,
          bulk_upload: true,
          analytics: true,
          priority_support: true,
          custom_branding: false,
          api_access: false,
          dedicated_account_manager: false,
          insurance_discount: 5,
          transaction_fee_discount: 10
        }
      },
      isBusinessAccount: true,
      switchToBusiness: mockSwitchToBusiness,
      switchToPersonal: mockSwitchToPersonal
    });

    render(
      <BrowserRouter>
        <BusinessAccountSwitcher />
      </BrowserRouter>
    );

    // Click the button to open the dropdown
    fireEvent.click(screen.getByRole('button', { name: /business.switchAccount/i }));

    // Click the personal account option
    fireEvent.click(screen.getByText('Test User'));

    // Check that switchToPersonal was called
    expect(mockSwitchToPersonal).toHaveBeenCalled();
  });
});
