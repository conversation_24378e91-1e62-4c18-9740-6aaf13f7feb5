// Types for the AgreementPreview component

export interface AgreementPreviewProps {
  agreementId: string;
  onSign?: () => void;
  onEdit?: () => void;
  readOnly?: boolean;
}

export interface Agreement {
  id: string;
  title: string;
  type: string;
  status: AgreementStatus;
  createdAt: Date;
  updatedAt?: Date;
  parties: Party[];
  item: AgreementItem;
  terms: AgreementTerms;
  pdfUrl: string;
  version: number;
  annotations: Annotation[];
}

export type AgreementStatus = 'draft' | 'pending_signature' | 'signed' | 'expired' | 'cancelled';

export interface Party {
  id: number;
  name: string;
  role: 'owner' | 'renter';
  signed: boolean;
  signatureDate?: Date;
}

export interface AgreementItem {
  id: number;
  title: string;
  category: string;
}

export interface AgreementTerms {
  startDate: Date;
  endDate: Date;
  price: number;
  deposit: number;
  specialTerms: string;
}

export interface Annotation {
  id: number;
  page: number;
  position: {
    x: number;
    y: number;
  };
  text: string;
  type: 'signature' | 'initial' | 'date';
}

export interface AgreementVersion {
  id: number;
  version: number;
  createdAt: Date;
  createdBy: string;
  status: AgreementStatus;
}

export interface AgreementState {
  agreement: Agreement | null;
  versions: AgreementVersion[];
  loading: boolean;
  error: string | null;
  numPages: number | null;
  pageNumber: number;
  scale: number;
  showAnnotations: boolean;
  activeTab: number;
  helpDialogOpen: boolean;
  helpTopic: string;
  compareDialogOpen: boolean;
  historyDialogOpen: boolean;
}

export type HelpTopic = 'signatures' | 'terms' | 'general';
