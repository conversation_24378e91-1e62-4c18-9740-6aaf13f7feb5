// Helper functions for the AgreementPreview component
import { AgreementStatus, Agreement, AgreementVersion } from './types';
import { STATUS_COLORS } from './constants';

/**
 * Get status color for Material-UI components
 */
export const getStatusColor = (status: AgreementStatus) => {
  return STATUS_COLORS[status] || 'default';
};

/**
 * Format date for display
 */
export const formatDate = (date: Date): string => {
  return new Date(date).toLocaleDateString();
};

/**
 * Calculate total rental days
 */
export const calculateRentalDays = (startDate: Date, endDate: Date): number => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end.getTime() - start.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Calculate total rental cost
 */
export const calculateTotalCost = (pricePerDay: number, startDate: Date, endDate: Date): number => {
  const days = calculateRentalDays(startDate, endDate);
  return pricePerDay * days;
};

/**
 * Check if all parties have signed
 */
export const areAllPartiesSigned = (agreement: Agreement): boolean => {
  return agreement.parties.every(party => party.signed);
};

/**
 * Get party by role
 */
export const getPartyByRole = (agreement: Agreement, role: 'owner' | 'renter') => {
  return agreement.parties.find(party => party.role === role);
};

/**
 * Format status text for display
 */
export const formatStatusText = (status: AgreementStatus): string => {
  return status.replace('_', ' ');
};

/**
 * Check if agreement can be edited
 */
export const canEditAgreement = (status: AgreementStatus): boolean => {
  return status === 'draft';
};

/**
 * Check if agreement can be signed
 */
export const canSignAgreement = (status: AgreementStatus): boolean => {
  return status === 'draft' || status === 'pending_signature';
};

/**
 * Generate mock agreement data
 */
export const generateMockAgreement = (agreementId: string): Agreement => {
  return {
    id: agreementId,
    title: 'Rental Agreement',
    type: 'standard',
    status: 'draft',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    parties: [
      {
        id: 101,
        name: 'John Smith',
        role: 'owner',
        signed: false,
        signatureDate: undefined
      },
      {
        id: 102,
        name: 'Jane Doe',
        role: 'renter',
        signed: false,
        signatureDate: undefined
      }
    ],
    item: {
      id: 1,
      title: 'Mountain Bike',
      category: 'Sports & Recreation'
    },
    terms: {
      startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      endDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days from now
      price: 25.00,
      deposit: 100.00,
      specialTerms: 'Equipment must be returned in the same condition as received.'
    },
    pdfUrl: 'https://example.com/agreements/1.pdf',
    version: 1,
    annotations: [
      {
        id: 1,
        page: 1,
        position: { x: 100, y: 200 },
        text: 'Signature required here',
        type: 'signature'
      },
      {
        id: 2,
        page: 1,
        position: { x: 100, y: 300 },
        text: 'Initial here',
        type: 'initial'
      },
      {
        id: 3,
        page: 2,
        position: { x: 100, y: 150 },
        text: 'Date here',
        type: 'date'
      }
    ]
  };
};

/**
 * Generate mock version history
 */
export const generateMockVersions = (): AgreementVersion[] => {
  return [
    {
      id: 1,
      version: 1,
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      createdBy: 'John Smith',
      status: 'draft'
    }
  ];
};
