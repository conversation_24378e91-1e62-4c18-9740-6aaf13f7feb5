// Constants for the AgreementPreview component
import { AgreementStatus } from './types';

// PDF viewer settings
export const PDF_SETTINGS = {
  minScale: 0.6,
  maxScale: 2.0,
  scaleStep: 0.2,
  defaultScale: 1.0,
  containerHeight: 600,
  pageWidth: 595,
  pageHeight: 842,
} as const;

// Tab indices
export const TAB_INDICES = {
  DETAILS: 0,
  PARTIES: 1,
  HISTORY: 2,
} as const;

// Status color mapping
export const STATUS_COLORS: Record<AgreementStatus, 'info' | 'warning' | 'success' | 'error' | 'default'> = {
  draft: 'info',
  pending_signature: 'warning',
  signed: 'success',
  expired: 'error',
  cancelled: 'default',
};

// Help content topics
export const HELP_TOPICS = {
  SIGNATURES: 'signatures',
  TERMS: 'terms',
  GENERAL: 'general',
} as const;

// Animation durations
export const ANIMATION_DURATIONS = {
  LOADING_DELAY: 1000,
} as const;
