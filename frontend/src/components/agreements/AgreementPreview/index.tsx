// Main AgreementPreview component - refactored for better maintainability
import React, { useState } from 'react';
import { Box, Grid } from '@mui/material';

// Import types
import { AgreementPreviewProps } from './utils/types';

// Import hooks
import { useAgreementData } from './hooks/useAgreementData';
import { usePdfViewer } from './hooks/usePdfViewer';
import { useDialogState } from './hooks/useDialogState';

// Import components
import LoadingState from './components/LoadingState';
import ErrorState from './components/ErrorState';
import AgreementHeader from './components/AgreementHeader';
import PdfViewer from './components/PdfViewer';
import AgreementSidebar from './components/AgreementSidebar';
import HelpDialogs from './components/HelpDialogs';

/**
 * Agreement Preview Component
 * 
 * A comprehensive component for previewing rental agreements with:
 * - PDF viewing with zoom and navigation controls
 * - Annotation display and management
 * - Tabbed sidebar with details, parties, and history
 * - Help dialogs for user guidance
 * - Action buttons for download, print, edit, share
 * - Digital signature workflow
 * 
 * Features:
 * - Responsive design for all screen sizes
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Error handling and retry mechanisms
 * - Loading states and user feedback
 * - Version history and comparison
 * - Interactive PDF annotations
 */
const AgreementPreview: React.FC<AgreementPreviewProps> = ({
  agreementId,
  onSign,
  onEdit,
  readOnly = false,
}) => {
  // Local state for tab management
  const [activeTab, setActiveTab] = useState<number>(0);

  // Custom hooks
  const { agreement, versions, loading, error, retryFetch } = useAgreementData({
    agreementId,
  });

  const {
    numPages,
    pageNumber,
    scale,
    showAnnotations,
    canGoPrevious,
    canGoNext,
    scalePercentage,
    onDocumentLoadSuccess,
    previousPage,
    nextPage,
    zoomIn,
    zoomOut,
    toggleAnnotations,
  } = usePdfViewer();

  const {
    helpDialogOpen,
    helpTopic,
    compareDialogOpen,
    historyDialogOpen,
    openHelpDialog,
    closeHelpDialog,
    openCompareDialog,
    closeCompareDialog,
    openHistoryDialog,
    closeHistoryDialog,
  } = useDialogState();

  // Action handlers
  const handleDownload = () => {
    // In a real implementation, this would download the PDF
    alert('Downloading agreement...');
  };

  const handlePrint = () => {
    // In a real implementation, this would print the PDF
    alert('Printing agreement...');
  };

  const handleShare = () => {
    // In a real implementation, this would share the agreement
    alert('Sharing agreement...');
  };

  const handleCopyLink = () => {
    // In a real implementation, this would copy the agreement link
    alert('Copying agreement link...');
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Render loading state
  if (loading) {
    return <LoadingState />;
  }

  // Render error state
  if (error) {
    return <ErrorState error={error} onRetry={retryFetch} />;
  }

  // Render main content
  if (!agreement) {
    return <ErrorState error="Agreement not found" onRetry={retryFetch} />;
  }

  return (
    <Box>
      {/* Agreement Header */}
      <AgreementHeader
        agreement={agreement}
        readOnly={readOnly}
        onEdit={onEdit}
        onDownload={handleDownload}
        onPrint={handlePrint}
        onShare={handleShare}
        onCopyLink={handleCopyLink}
      />
      
      {/* Main Content */}
      <Grid container spacing={3}>
        {/* PDF Viewer */}
        <Grid item xs={12} md={8}>
          <PdfViewer
            agreement={agreement}
            pageNumber={pageNumber}
            numPages={numPages}
            scale={scale}
            showAnnotations={showAnnotations}
            canGoPrevious={canGoPrevious}
            canGoNext={canGoNext}
            scalePercentage={scalePercentage}
            onPreviousPage={previousPage}
            onNextPage={nextPage}
            onZoomIn={zoomIn}
            onZoomOut={zoomOut}
            onToggleAnnotations={toggleAnnotations}
          />
        </Grid>
        
        {/* Sidebar */}
        <Grid item xs={12} md={4}>
          <AgreementSidebar
            agreement={agreement}
            versions={versions}
            activeTab={activeTab}
            readOnly={readOnly}
            onTabChange={handleTabChange}
            onSign={onSign}
            onOpenCompareDialog={openCompareDialog}
            onOpenHistoryDialog={openHistoryDialog}
            onOpenHelpDialog={openHelpDialog}
          />
        </Grid>
      </Grid>
      
      {/* Help Dialogs */}
      <HelpDialogs
        helpDialogOpen={helpDialogOpen}
        helpTopic={helpTopic}
        compareDialogOpen={compareDialogOpen}
        historyDialogOpen={historyDialogOpen}
        onCloseHelpDialog={closeHelpDialog}
        onCloseCompareDialog={closeCompareDialog}
        onCloseHistoryDialog={closeHistoryDialog}
      />
    </Box>
  );
};

export default AgreementPreview;
