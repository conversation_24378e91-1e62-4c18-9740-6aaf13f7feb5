// Error state component
import React from 'react';
import { Box, Alert, Button } from '@mui/material';

interface ErrorStateProps {
  error: string;
  onRetry: () => void;
}

/**
 * Error state component for agreement preview
 */
const ErrorState: React.FC<ErrorStateProps> = ({ error, onRetry }) => {
  return (
    <Box sx={{ p: 3 }}>
      <Alert 
        severity="error" 
        action={
          <Button color="inherit" size="small" onClick={onRetry}>
            Retry
          </Button>
        }
      >
        {error}
      </Alert>
    </Box>
  );
};

export default ErrorState;
