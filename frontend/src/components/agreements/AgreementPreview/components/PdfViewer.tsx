// PDF viewer component
import React, { useRef } from 'react';
import {
  Paper,
  Box,
  Button,
  Typography,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Visibility,
  VisibilityOff
} from '@mui/icons-material';
import { Agreement } from '../utils/types';
import { PDF_SETTINGS } from '../utils/constants';

interface PdfViewerProps {
  agreement: Agreement;
  pageNumber: number;
  numPages: number | null;
  scale: number;
  showAnnotations: boolean;
  canGoPrevious: boolean;
  canGoNext: boolean;
  scalePercentage: number;
  onPreviousPage: () => void;
  onNextPage: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onToggleAnnotations: () => void;
}

/**
 * PDF viewer component with navigation and zoom controls
 */
const PdfViewer: React.FC<PdfViewerProps> = ({
  agreement,
  pageNumber,
  numPages,
  scale,
  showAnnotations,
  canGoPrevious,
  canGoNext,
  scalePercentage,
  onPreviousPage,
  onNextPage,
  onZoomIn,
  onZoomOut,
  onToggleAnnotations,
}) => {
  const pdfContainerRef = useRef<HTMLDivElement>(null);

  return (
    <Paper sx={{ p: 2, mb: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Button 
            disabled={!canGoPrevious} 
            onClick={onPreviousPage}
          >
            Previous
          </Button>
          <Typography>
            Page {pageNumber} of {numPages || 1}
          </Typography>
          <Button 
            disabled={!canGoNext} 
            onClick={onNextPage}
          >
            Next
          </Button>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Button onClick={onZoomOut}>-</Button>
          <Typography>{scalePercentage}%</Typography>
          <Button onClick={onZoomIn}>+</Button>
          <Tooltip title={showAnnotations ? "Hide Annotations" : "Show Annotations"}>
            <IconButton onClick={onToggleAnnotations}>
              {showAnnotations ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
      
      <Box 
        ref={pdfContainerRef} 
        sx={{ 
          border: '1px solid #ddd', 
          borderRadius: 1, 
          overflow: 'auto', 
          height: PDF_SETTINGS.containerHeight,
          display: 'flex',
          justifyContent: 'center',
          position: 'relative'
        }}
      >
        {/* Mock PDF content - in production this would be a real PDF viewer */}
        <Box sx={{ 
          width: PDF_SETTINGS.pageWidth * scale, 
          height: PDF_SETTINGS.pageHeight * scale, 
          bgcolor: 'white',
          position: 'relative',
          my: 2
        }}>
          <Typography variant="h6" sx={{ textAlign: 'center', mt: 4 }}>
            {agreement.title}
          </Typography>
          
          <Box sx={{ p: 4 }}>
            <Typography variant="body1" paragraph>
              This Agreement is made between the following parties:
            </Typography>
            
            <Typography variant="body1" paragraph>
              <strong>Owner:</strong> {agreement.parties.find(p => p.role === 'owner')?.name}
            </Typography>
            
            <Typography variant="body1" paragraph>
              <strong>Renter:</strong> {agreement.parties.find(p => p.role === 'renter')?.name}
            </Typography>
            
            <Typography variant="body1" paragraph>
              <strong>Item:</strong> {agreement.item.title} ({agreement.item.category})
            </Typography>
            
            <Typography variant="body1" paragraph>
              <strong>Rental Period:</strong> From {new Date(agreement.terms.startDate).toLocaleDateString()} to {new Date(agreement.terms.endDate).toLocaleDateString()}
            </Typography>
            
            <Typography variant="body1" paragraph>
              <strong>Price:</strong> ${agreement.terms.price.toFixed(2)} per day
            </Typography>
            
            <Typography variant="body1" paragraph>
              <strong>Security Deposit:</strong> ${agreement.terms.deposit.toFixed(2)}
            </Typography>
            
            <Typography variant="body1" paragraph>
              <strong>Special Terms:</strong> {agreement.terms.specialTerms}
            </Typography>
            
            <Typography variant="body1" paragraph>
              By signing below, the parties agree to the terms and conditions of this rental agreement.
            </Typography>
            
            <Box sx={{ mt: 8, display: 'flex', justifyContent: 'space-between' }}>
              <Box>
                <Typography variant="body2">Owner Signature:</Typography>
                <Box sx={{ width: 200, height: 50, border: '1px solid #ddd', mt: 1 }} />
                <Typography variant="body2" sx={{ mt: 1 }}>Date:</Typography>
                <Box sx={{ width: 100, height: 30, border: '1px solid #ddd', mt: 1 }} />
              </Box>
              
              <Box>
                <Typography variant="body2">Renter Signature:</Typography>
                <Box sx={{ width: 200, height: 50, border: '1px solid #ddd', mt: 1 }} />
                <Typography variant="body2" sx={{ mt: 1 }}>Date:</Typography>
                <Box sx={{ width: 100, height: 30, border: '1px solid #ddd', mt: 1 }} />
              </Box>
            </Box>
          </Box>
          
          {/* Render annotations if enabled */}
          {showAnnotations && agreement.annotations
            .filter(annotation => annotation.page === pageNumber)
            .map(annotation => (
              <Box
                key={annotation.id}
                sx={{
                  position: 'absolute',
                  left: annotation.position.x * scale,
                  top: annotation.position.y * scale,
                  padding: '4px 8px',
                  backgroundColor: 'rgba(255, 255, 0, 0.3)',
                  border: '1px solid #ffd700',
                  borderRadius: 1,
                  fontSize: 12 * scale,
                  zIndex: 10
                }}
              >
                {annotation.text}
              </Box>
            ))
          }
        </Box>
      </Box>
    </Paper>
  );
};

export default PdfViewer;
