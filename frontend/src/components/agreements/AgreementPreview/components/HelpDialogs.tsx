// Help dialogs component
import React from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogA<PERSON>,
  Button,
  Typography
} from '@mui/material';
import { HELP_TOPICS } from '../utils/constants';

interface HelpDialogsProps {
  helpDialogOpen: boolean;
  helpTopic: string;
  compareDialogOpen: boolean;
  historyDialogOpen: boolean;
  onCloseHelpDialog: () => void;
  onCloseCompareDialog: () => void;
  onCloseHistoryDialog: () => void;
}

/**
 * Help dialogs component for various help topics
 */
const HelpDialogs: React.FC<HelpDialogsProps> = ({
  helpDialogOpen,
  helpTopic,
  compareDialogOpen,
  historyDialogOpen,
  onCloseHelpDialog,
  onCloseCompareDialog,
  onCloseHistoryDialog,
}) => {
  // Get help content based on topic
  const getHelpContent = (topic: string) => {
    switch (topic) {
      case HELP_TOPICS.SIGNATURES:
        return (
          <>
            <Typography variant="h6" gutterBottom>About Signatures</Typography>
            <Typography paragraph>
              Digital signatures are legally binding and equivalent to handwritten signatures. When you sign this agreement, you are indicating your consent to the terms and conditions outlined in the document.
            </Typography>
            <Typography paragraph>
              Each party to the agreement must sign in the designated signature fields. Once all parties have signed, the agreement becomes fully executed and legally binding.
            </Typography>
          </>
        );
      case HELP_TOPICS.TERMS:
        return (
          <>
            <Typography variant="h6" gutterBottom>Understanding Terms</Typography>
            <Typography paragraph>
              The terms of this agreement outline the rights and responsibilities of each party. It's important to read and understand all terms before signing.
            </Typography>
            <Typography paragraph>
              If you have questions about specific terms, you can highlight them and add comments, or contact the other party directly to discuss.
            </Typography>
          </>
        );
      default:
        return (
          <>
            <Typography variant="h6" gutterBottom>Agreement Help</Typography>
            <Typography paragraph>
              This is the agreement preview page. Here you can review the agreement, download it, print it, or sign it electronically.
            </Typography>
            <Typography paragraph>
              Use the navigation controls to move between pages, zoom in or out, and toggle annotations.
            </Typography>
          </>
        );
    }
  };

  return (
    <>
      {/* Help Dialog */}
      <Dialog
        open={helpDialogOpen}
        onClose={onCloseHelpDialog}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Help</DialogTitle>
        <DialogContent>
          {getHelpContent(helpTopic)}
        </DialogContent>
        <DialogActions>
          <Button onClick={onCloseHelpDialog}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* Compare Dialog */}
      <Dialog
        open={compareDialogOpen}
        onClose={onCloseCompareDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Compare Versions</DialogTitle>
        <DialogContent>
          <Typography paragraph>
            No previous versions available for comparison.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={onCloseCompareDialog}>Close</Button>
        </DialogActions>
      </Dialog>
      
      {/* History Dialog */}
      <Dialog
        open={historyDialogOpen}
        onClose={onCloseHistoryDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Agreement History</DialogTitle>
        <DialogContent>
          <Typography paragraph>
            This is the first version of the agreement.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={onCloseHistoryDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default HelpDialogs;
