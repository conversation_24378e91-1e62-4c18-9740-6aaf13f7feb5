// Details tab component
import React from 'react';
import {
  Typography,
  Grid,
  Box,
  Button,
  IconButton
} from '@mui/material';
import {
  Compare,
  History,
  HelpOutline
} from '@mui/icons-material';
import { Agreement } from '../utils/types';
import { formatDate } from '../utils/helpers';

interface DetailsTabProps {
  agreement: Agreement;
  onOpenCompareDialog: () => void;
  onOpenHistoryDialog: () => void;
  onOpenHelpDialog: (topic: string) => void;
}

/**
 * Details tab component showing agreement information
 */
const DetailsTab: React.FC<DetailsTabProps> = ({
  agreement,
  onOpenCompareDialog,
  onOpenHistoryDialog,
  onOpenHelpDialog,
}) => {
  return (
    <>
      <Typography variant="h6" gutterBottom>
        Agreement Details
        <IconButton size="small" onClick={() => onOpenHelpDialog('terms')}>
          <HelpOutline fontSize="small" />
        </IconButton>
      </Typography>
      
      <Grid container spacing={2}>
        <Grid item xs={6}>
          <Typography variant="body2" color="text.secondary">Item</Typography>
          <Typography variant="body1">{agreement.item.title}</Typography>
        </Grid>
        
        <Grid item xs={6}>
          <Typography variant="body2" color="text.secondary">Category</Typography>
          <Typography variant="body1">{agreement.item.category}</Typography>
        </Grid>
        
        <Grid item xs={6}>
          <Typography variant="body2" color="text.secondary">Start Date</Typography>
          <Typography variant="body1">{formatDate(agreement.terms.startDate)}</Typography>
        </Grid>
        
        <Grid item xs={6}>
          <Typography variant="body2" color="text.secondary">End Date</Typography>
          <Typography variant="body1">{formatDate(agreement.terms.endDate)}</Typography>
        </Grid>
        
        <Grid item xs={6}>
          <Typography variant="body2" color="text.secondary">Price</Typography>
          <Typography variant="body1">${agreement.terms.price.toFixed(2)}/day</Typography>
        </Grid>
        
        <Grid item xs={6}>
          <Typography variant="body2" color="text.secondary">Deposit</Typography>
          <Typography variant="body1">${agreement.terms.deposit.toFixed(2)}</Typography>
        </Grid>
        
        <Grid item xs={12}>
          <Typography variant="body2" color="text.secondary">Special Terms</Typography>
          <Typography variant="body1">{agreement.terms.specialTerms}</Typography>
        </Grid>
      </Grid>
      
      <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
        <Button 
          variant="outlined" 
          size="small" 
          startIcon={<Compare />}
          onClick={onOpenCompareDialog}
        >
          Compare Versions
        </Button>
        
        <Button 
          variant="outlined" 
          size="small" 
          startIcon={<History />}
          onClick={onOpenHistoryDialog}
        >
          View History
        </Button>
      </Box>
    </>
  );
};

export default DetailsTab;
