// Agreement sidebar component with tabs
import React from 'react';
import {
  Paper,
  Tabs,
  Tab,
  Box
} from '@mui/material';
import { Agreement, AgreementVersion } from '../utils/types';
import { TAB_INDICES } from '../utils/constants';
import DetailsTab from './DetailsTab';
import PartiesTab from './PartiesTab';
import HistoryTab from './HistoryTab';

interface AgreementSidebarProps {
  agreement: Agreement;
  versions: AgreementVersion[];
  activeTab: number;
  readOnly: boolean;
  onTabChange: (event: React.SyntheticEvent, newValue: number) => void;
  onSign?: () => void;
  onOpenCompareDialog: () => void;
  onOpenHistoryDialog: () => void;
  onOpenHelpDialog: (topic: string) => void;
}

/**
 * Agreement sidebar component with tabbed content
 */
const AgreementSidebar: React.FC<AgreementSidebarProps> = ({
  agreement,
  versions,
  activeTab,
  readOnly,
  onTabChange,
  onSign,
  onOpenCompareDialog,
  onOpenHistoryDialog,
  onOpenHelpDialog,
}) => {
  return (
    <Paper sx={{ mb: 3 }}>
      <Tabs value={activeTab} onChange={onTabChange} variant="fullWidth">
        <Tab label="Details" />
        <Tab label="Parties" />
        <Tab label="History" />
      </Tabs>
      
      <Box sx={{ p: 2 }}>
        {activeTab === TAB_INDICES.DETAILS && (
          <DetailsTab
            agreement={agreement}
            onOpenCompareDialog={onOpenCompareDialog}
            onOpenHistoryDialog={onOpenHistoryDialog}
            onOpenHelpDialog={onOpenHelpDialog}
          />
        )}
        
        {activeTab === TAB_INDICES.PARTIES && (
          <PartiesTab
            agreement={agreement}
            readOnly={readOnly}
            onSign={onSign}
            onOpenHelpDialog={onOpenHelpDialog}
          />
        )}
        
        {activeTab === TAB_INDICES.HISTORY && (
          <HistoryTab
            agreement={agreement}
            versions={versions}
          />
        )}
      </Box>
    </Paper>
  );
};

export default AgreementSidebar;
