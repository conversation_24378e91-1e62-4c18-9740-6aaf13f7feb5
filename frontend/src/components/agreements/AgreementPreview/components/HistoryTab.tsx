// History tab component
import React from 'react';
import {
  Typography,
  Box,
  Chip,
  Button
} from '@mui/material';
import {
  Visibility,
  Compare
} from '@mui/icons-material';
import { Agreement, AgreementVersion } from '../utils/types';
import { getStatusColor, formatDate } from '../utils/helpers';

interface HistoryTabProps {
  agreement: Agreement;
  versions: AgreementVersion[];
}

/**
 * History tab component showing version history
 */
const HistoryTab: React.FC<HistoryTabProps> = ({
  agreement,
  versions,
}) => {
  return (
    <>
      <Typography variant="h6" gutterBottom>
        Version History
      </Typography>
      
      {versions.map((version) => (
        <Box key={version.id} sx={{ mb: 2, p: 2, border: '1px solid #eee', borderRadius: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="subtitle1">Version {version.version}</Typography>
            <Chip 
              label={version.status} 
              color={getStatusColor(version.status)} 
              size="small" 
            />
          </Box>
          
          <Typography variant="body2" color="text.secondary">
            Created on {formatDate(version.createdAt)} by {version.createdBy}
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
            <Button 
              variant="outlined" 
              size="small"
              startIcon={<Visibility />}
            >
              View
            </Button>
            
            {version.version !== agreement.version && (
              <Button 
                variant="outlined" 
                size="small"
                startIcon={<Compare />}
              >
                Compare
              </Button>
            )}
          </Box>
        </Box>
      ))}
    </>
  );
};

export default HistoryTab;
