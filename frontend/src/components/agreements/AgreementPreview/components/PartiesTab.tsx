// Parties tab component
import React from 'react';
import {
  Typo<PERSON>,
  Box,
  Chip,
  Button,
  IconButton
} from '@mui/material';
import {
  CheckCircle,
  HelpOutline
} from '@mui/icons-material';
import { Agreement } from '../utils/types';
import { formatDate, canSignAgreement } from '../utils/helpers';

interface PartiesTabProps {
  agreement: Agreement;
  readOnly: boolean;
  onSign?: () => void;
  onOpenHelpDialog: (topic: string) => void;
}

/**
 * Parties tab component showing signature status
 */
const PartiesTab: React.FC<PartiesTabProps> = ({
  agreement,
  readOnly,
  onSign,
  onOpenHelpDialog,
}) => {
  return (
    <>
      <Typography variant="h6" gutterBottom>
        Parties
        <IconButton size="small" onClick={() => onOpenHelpDialog('signatures')}>
          <HelpOutline fontSize="small" />
        </IconButton>
      </Typography>
      
      {agreement.parties.map((party) => (
        <Box key={party.id} sx={{ mb: 2, p: 2, border: '1px solid #eee', borderRadius: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="subtitle1">{party.name}</Typography>
            <Chip 
              label={party.role} 
              size="small" 
              color={party.role === 'owner' ? 'primary' : 'secondary'} 
            />
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
              Signature Status:
            </Typography>
            {party.signed ? (
              <Chip 
                icon={<CheckCircle />} 
                label={`Signed on ${party.signatureDate ? formatDate(party.signatureDate) : 'Unknown'}`} 
                color="success" 
                size="small" 
              />
            ) : (
              <Chip 
                label="Not signed" 
                variant="outlined" 
                size="small" 
              />
            )}
          </Box>
        </Box>
      ))}
      
      {!readOnly && canSignAgreement(agreement.status) && onSign && (
        <Button 
          variant="contained" 
          color="primary" 
          fullWidth 
          onClick={onSign}
          sx={{ mt: 2 }}
        >
          Sign Agreement
        </Button>
      )}
    </>
  );
};

export default PartiesTab;
