// Agreement header component
import React from 'react';
import {
  Paper,
  Grid,
  Typography,
  Box,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Download,
  Print,
  Edit,
  Share,
  ContentCopy
} from '@mui/icons-material';
import { Agreement } from '../utils/types';
import { getStatusColor, formatStatusText, formatDate } from '../utils/helpers';

interface AgreementHeaderProps {
  agreement: Agreement;
  readOnly: boolean;
  onEdit?: () => void;
  onDownload: () => void;
  onPrint: () => void;
  onShare: () => void;
  onCopyLink: () => void;
}

/**
 * Agreement header component with title, status, and action buttons
 */
const AgreementHeader: React.FC<AgreementHeaderProps> = ({
  agreement,
  readOnly,
  onEdit,
  onDownload,
  onPrint,
  onShare,
  onCopyLink,
}) => {
  return (
    <Paper sx={{ p: 3, mb: 3 }}>
      <Grid container spacing={2} alignItems="center">
        <Grid item xs={12} sm={8}>
          <Typography variant="h5" gutterBottom>
            {agreement.title}
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1, mb: 1 }}>
            <Chip 
              label={agreement.type} 
              color="primary" 
              size="small" 
            />
            <Chip 
              label={formatStatusText(agreement.status)} 
              color={getStatusColor(agreement.status)} 
              size="small" 
            />
            <Chip 
              label={`Version ${agreement.version}`} 
              variant="outlined" 
              size="small" 
            />
          </Box>
          <Typography variant="body2" color="text.secondary">
            Created: {formatDate(agreement.createdAt)}
            {agreement.updatedAt && ` • Updated: ${formatDate(agreement.updatedAt)}`}
          </Typography>
        </Grid>
        
        <Grid item xs={12} sm={4} sx={{ display: 'flex', justifyContent: { xs: 'flex-start', sm: 'flex-end' }, gap: 1 }}>
          <Tooltip title="Download">
            <IconButton onClick={onDownload}>
              <Download />
            </IconButton>
          </Tooltip>
          <Tooltip title="Print">
            <IconButton onClick={onPrint}>
              <Print />
            </IconButton>
          </Tooltip>
          {!readOnly && onEdit && (
            <Tooltip title="Edit">
              <IconButton onClick={onEdit}>
                <Edit />
              </IconButton>
            </Tooltip>
          )}
          <Tooltip title="Share">
            <IconButton onClick={onShare}>
              <Share />
            </IconButton>
          </Tooltip>
          <Tooltip title="Copy Link">
            <IconButton onClick={onCopyLink}>
              <ContentCopy />
            </IconButton>
          </Tooltip>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default AgreementHeader;
