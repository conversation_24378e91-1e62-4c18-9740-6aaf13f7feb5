// Custom hook for managing dialog states
import { useState, useCallback } from 'react';
import { HelpTopic } from '../utils/types';

/**
 * Custom hook for managing dialog states
 */
export const useDialogState = () => {
  const [helpDialogOpen, setHelpDialogOpen] = useState<boolean>(false);
  const [helpTopic, setHelpTopic] = useState<string>('');
  const [compareDialogOpen, setCompareDialogOpen] = useState<boolean>(false);
  const [historyDialogOpen, setHistoryDialogOpen] = useState<boolean>(false);

  // Help dialog handlers
  const openHelpDialog = useCallback((topic: HelpTopic) => {
    setHelpTopic(topic);
    setHelpDialogOpen(true);
  }, []);

  const closeHelpDialog = useCallback(() => {
    setHelpDialogOpen(false);
    setHelpTopic('');
  }, []);

  // Compare dialog handlers
  const openCompareDialog = useCallback(() => {
    setCompareDialogOpen(true);
  }, []);

  const closeCompareDialog = useCallback(() => {
    setCompareDialogOpen(false);
  }, []);

  // History dialog handlers
  const openHistoryDialog = useCallback(() => {
    setHistoryDialogOpen(true);
  }, []);

  const closeHistoryDialog = useCallback(() => {
    setHistoryDialogOpen(false);
  }, []);

  // Close all dialogs
  const closeAllDialogs = useCallback(() => {
    setHelpDialogOpen(false);
    setCompareDialogOpen(false);
    setHistoryDialogOpen(false);
    setHelpTopic('');
  }, []);

  return {
    // State
    helpDialogOpen,
    helpTopic,
    compareDialogOpen,
    historyDialogOpen,
    
    // Actions
    openHelpDialog,
    closeHelpDialog,
    openCompareDialog,
    closeCompareDialog,
    openHistoryDialog,
    closeHistoryDialog,
    closeAllDialogs,
  };
};
