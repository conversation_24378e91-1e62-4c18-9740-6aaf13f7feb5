// Custom hook for managing agreement data
import { useState, useEffect } from 'react';
import { Agreement, AgreementVersion } from '../utils/types';
import { generateMockAgreement, generateMockVersions } from '../utils/helpers';
import { ANIMATION_DURATIONS } from '../utils/constants';

interface UseAgreementDataProps {
  agreementId: string;
}

/**
 * Custom hook for fetching and managing agreement data
 */
export const useAgreementData = ({ agreementId }: UseAgreementDataProps) => {
  const [agreement, setAgreement] = useState<Agreement | null>(null);
  const [versions, setVersions] = useState<AgreementVersion[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAgreement = async () => {
      setLoading(true);
      setError(null);

      try {
        // In a real implementation, this would fetch from the API
        // const response = await axios.get(`${API_BASE_URL}/api/v1/agreements/${agreementId}`);
        // setAgreement(response.data);
        
        // Mock data for development
        setTimeout(() => {
          const mockAgreement = generateMockAgreement(agreementId);
          const mockVersions = generateMockVersions();
          
          setAgreement(mockAgreement);
          setVersions(mockVersions);
          setLoading(false);
        }, ANIMATION_DURATIONS.LOADING_DELAY);
      } catch (err) {
        console.error('Error fetching agreement:', err);
        setError('Failed to load agreement. Please try again later.');
        setLoading(false);
      }
    };
    
    fetchAgreement();
  }, [agreementId]);

  const retryFetch = () => {
    setError(null);
    setLoading(true);
    // Re-trigger the effect by updating a dependency
    setTimeout(() => {
      const mockAgreement = generateMockAgreement(agreementId);
      const mockVersions = generateMockVersions();
      
      setAgreement(mockAgreement);
      setVersions(mockVersions);
      setLoading(false);
    }, ANIMATION_DURATIONS.LOADING_DELAY);
  };

  return {
    agreement,
    versions,
    loading,
    error,
    retryFetch,
  };
};
