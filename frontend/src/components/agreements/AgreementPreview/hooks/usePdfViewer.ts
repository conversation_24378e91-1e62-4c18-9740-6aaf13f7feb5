// Custom hook for managing PDF viewer state
import { useState, useCallback } from 'react';
import { PDF_SETTINGS } from '../utils/constants';

/**
 * Custom hook for managing PDF viewer functionality
 */
export const usePdfViewer = () => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(PDF_SETTINGS.defaultScale);
  const [showAnnotations, setShowAnnotations] = useState<boolean>(true);

  // Handle document load success
  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  }, []);

  // Handle page change
  const changePage = useCallback((offset: number) => {
    setPageNumber(prevPageNumber => {
      const newPageNumber = prevPageNumber + offset;
      return Math.min(Math.max(1, newPageNumber), numPages || 1);
    });
  }, [numPages]);

  // Handle previous page
  const previousPage = useCallback(() => {
    changePage(-1);
  }, [changePage]);

  // Handle next page
  const nextPage = useCallback(() => {
    changePage(1);
  }, [changePage]);

  // Handle zoom in
  const zoomIn = useCallback(() => {
    setScale(prevScale => Math.min(prevScale + PDF_SETTINGS.scaleStep, PDF_SETTINGS.maxScale));
  }, []);

  // Handle zoom out
  const zoomOut = useCallback(() => {
    setScale(prevScale => Math.max(prevScale - PDF_SETTINGS.scaleStep, PDF_SETTINGS.minScale));
  }, []);

  // Handle toggle annotations
  const toggleAnnotations = useCallback(() => {
    setShowAnnotations(prevShowAnnotations => !prevShowAnnotations);
  }, []);

  // Reset viewer state
  const resetViewer = useCallback(() => {
    setPageNumber(1);
    setScale(PDF_SETTINGS.defaultScale);
    setShowAnnotations(true);
  }, []);

  return {
    // State
    numPages,
    pageNumber,
    scale,
    showAnnotations,
    
    // Actions
    onDocumentLoadSuccess,
    previousPage,
    nextPage,
    zoomIn,
    zoomOut,
    toggleAnnotations,
    resetViewer,
    
    // Computed values
    canGoPrevious: pageNumber > 1,
    canGoNext: pageNumber < (numPages || 1),
    scalePercentage: Math.round(scale * 100),
  };
};
