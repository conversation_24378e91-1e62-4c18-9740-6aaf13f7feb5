import React, { useEffect, useRef, useState } from 'react';
import { PerformanceMetric } from '../../utils/performanceMonitoring';

interface PerformanceVisualizationProps {
  metrics: PerformanceMetric[];
  type: string;
  width?: number;
  height?: number;
}

/**
 * Performance Visualization Component
 *
 * Renders a visualization of performance metrics using HTML Canvas.
 * Shows a bar chart of metric durations with interactive tooltips.
 * Includes accessibility features for screen readers.
 */
const PerformanceVisualization: React.FC<PerformanceVisualizationProps> = ({
  metrics,
  type,
  width = 800,
  height = 300
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [accessibleDescription, setAccessibleDescription] = useState<string>('');

  // Filter metrics by type
  const filteredMetrics = type === 'all'
    ? metrics
    : metrics.filter(metric => metric.type === type);

  // Sort metrics by timestamp
  const sortedMetrics = [...filteredMetrics].sort((a, b) => a.timestamp - b.timestamp);

  // Generate accessible description for screen readers
  useEffect(() => {
    if (sortedMetrics.length === 0) {
      setAccessibleDescription('No performance metrics available.');
      return;
    }

    const maxDuration = Math.max(...sortedMetrics.map(m => m.duration));
    const avgDuration = sortedMetrics.reduce((sum, m) => sum + m.duration, 0) / sortedMetrics.length;

    const metricTypes = [...new Set(sortedMetrics.map(m => m.type))];
    const typeBreakdown = metricTypes.map(t => {
      const typeMetrics = sortedMetrics.filter(m => m.type === t);
      const count = typeMetrics.length;
      const avgTypeDuration = typeMetrics.reduce((sum, m) => sum + m.duration, 0) / count;
      return `${count} ${t} metrics with average duration of ${avgTypeDuration.toFixed(2)} milliseconds`;
    }).join('; ');

    const description = `
      Performance visualization showing ${sortedMetrics.length} metrics of type ${type === 'all' ? 'all types' : type}.
      Average duration is ${avgDuration.toFixed(2)} milliseconds, with maximum of ${maxDuration.toFixed(2)} milliseconds.
      Breakdown by type: ${typeBreakdown}.
    `;

    setAccessibleDescription(description);
  }, [sortedMetrics, type]);

  // Draw the visualization
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // If no metrics, show a message
    if (sortedMetrics.length === 0) {
      ctx.font = '16px Arial';
      ctx.fillStyle = '#666';
      ctx.textAlign = 'center';
      ctx.fillText('No metrics available', width / 2, height / 2);
      return;
    }

    // Set up dimensions
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    // Find max duration for scaling
    const maxDuration = Math.max(...sortedMetrics.map(m => m.duration));

    // Draw axes
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.strokeStyle = '#ccc';
    ctx.stroke();

    // Draw y-axis labels
    ctx.font = '12px Arial';
    ctx.fillStyle = '#666';
    ctx.textAlign = 'right';

    const yAxisSteps = 5;
    for (let i = 0; i <= yAxisSteps; i++) {
      const y = height - padding - (i / yAxisSteps) * chartHeight;
      const value = (i / yAxisSteps) * maxDuration;

      ctx.fillText(`${value.toFixed(1)} ms`, padding - 5, y + 4);

      // Draw horizontal grid line
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.strokeStyle = '#eee';
      ctx.stroke();
    }

    // Draw bars
    const barWidth = Math.min(30, chartWidth / sortedMetrics.length - 2);

    sortedMetrics.forEach((metric, index) => {
      const x = padding + (index / sortedMetrics.length) * chartWidth;
      const barHeight = (metric.duration / maxDuration) * chartHeight;
      const y = height - padding - barHeight;

      // Draw bar
      ctx.fillStyle = getColorForMetricType(metric.type);
      ctx.fillRect(x, y, barWidth, barHeight);

      // Store metric data for tooltip
      metric.metadata = {
        ...metric.metadata,
        _visualization: {
          x,
          y,
          width: barWidth,
          height: barHeight
        }
      };

      // Draw x-axis label (timestamp) for every 5th bar or if few bars
      if (index % 5 === 0 || sortedMetrics.length < 10) {
        const time = new Date(metric.timestamp).toLocaleTimeString();
        ctx.save();
        ctx.translate(x + barWidth / 2, height - padding + 5);
        ctx.rotate(Math.PI / 4);
        ctx.font = '10px Arial';
        ctx.fillStyle = '#666';
        ctx.textAlign = 'left';
        ctx.fillText(time, 0, 0);
        ctx.restore();
      }
    });

    // Draw title
    ctx.font = 'bold 14px Arial';
    ctx.fillStyle = '#333';
    ctx.textAlign = 'center';
    ctx.fillText(`${type === 'all' ? 'All' : type} Performance Metrics`, width / 2, 20);

    // Draw legend
    const metricTypes = [...new Set(sortedMetrics.map(m => m.type))];
    const legendX = width - padding - 150;
    const legendY = padding;

    ctx.font = '12px Arial';
    ctx.textAlign = 'left';

    metricTypes.forEach((type, index) => {
      const y = legendY + index * 20;

      // Draw color box
      ctx.fillStyle = getColorForMetricType(type);
      ctx.fillRect(legendX, y, 15, 15);

      // Draw type name
      ctx.fillStyle = '#333';
      ctx.fillText(type, legendX + 20, y + 12);
    });

  }, [sortedMetrics, type, width, height]);

  // Handle mouse move for tooltips
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    const tooltip = tooltipRef.current;
    if (!canvas || !tooltip) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Check if mouse is over a bar
    let hoveredMetric: PerformanceMetric | null = null;

    for (const metric of sortedMetrics) {
      const vizData = metric.metadata?._visualization;
      if (!vizData) continue;

      if (
        x >= vizData.x &&
        x <= vizData.x + vizData.width &&
        y >= vizData.y &&
        y <= vizData.y + vizData.height
      ) {
        hoveredMetric = metric;
        break;
      }
    }

    // Show or hide tooltip
    if (hoveredMetric) {
      tooltip.style.display = 'block';
      tooltip.style.left = `${e.clientX + 10}px`;
      tooltip.style.top = `${e.clientY + 10}px`;

      // Format tooltip content
      const time = new Date(hoveredMetric.timestamp).toLocaleTimeString();

      tooltip.innerHTML = `
        <div class="font-bold">${hoveredMetric.name}</div>
        <div>Type: ${hoveredMetric.type}</div>
        <div>Duration: ${hoveredMetric.duration.toFixed(2)} ms</div>
        <div>Time: ${time}</div>
      `;
    } else {
      tooltip.style.display = 'none';
    }
  };

  // Handle mouse leave
  const handleMouseLeave = () => {
    const tooltip = tooltipRef.current;
    if (tooltip) {
      tooltip.style.display = 'none';
    }
  };

  // Get color for metric type
  const getColorForMetricType = (type: string): string => {
    const colorMap: Record<string, string> = {
      'component-render': '#3b82f6', // blue
      'api-call': '#10b981', // green
      'page-load': '#f59e0b', // amber
      'interaction': '#8b5cf6', // purple
      'animation': '#ec4899', // pink
      'resource-load': '#6366f1', // indigo
    };

    return colorMap[type] || '#9ca3af'; // gray as default
  };

  return (
    <div className="relative">
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className="border border-gray-200 rounded-lg"
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        role="img"
        aria-label={`${type === 'all' ? 'All' : type} Performance Metrics Chart`}
        tabIndex={0}
        aria-describedby="performance-viz-description"
      />

      <div
        id="performance-viz-description"
        className="sr-only"
        aria-live="polite"
      >
        {accessibleDescription}
      </div>

      <div
        ref={tooltipRef}
        className="absolute hidden bg-white p-2 rounded shadow-md border border-gray-200 text-sm z-10"
        style={{ pointerEvents: 'none', display: 'none' }}
        aria-hidden="true"
      />
    </div>
  );
};

export default PerformanceVisualization;
