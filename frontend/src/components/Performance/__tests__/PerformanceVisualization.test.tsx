import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import PerformanceVisualization from '../PerformanceVisualization';

// Mock canvas context
const mockContext = {
  clearRect: jest.fn(),
  beginPath: jest.fn(),
  moveTo: jest.fn(),
  lineTo: jest.fn(),
  stroke: jest.fn(),
  fillRect: jest.fn(),
  fillText: jest.fn(),
  save: jest.fn(),
  restore: jest.fn(),
  translate: jest.fn(),
  rotate: jest.fn(),
  font: '',
  fillStyle: '',
  strokeStyle: '',
  textAlign: ''
};

// Mock canvas element
HTMLCanvasElement.prototype.getContext = jest.fn().mockReturnValue(mockContext);

describe('PerformanceVisualization Component', () => {
  // Sample metrics for testing
  const sampleMetrics = [
    {
      type: 'component-render',
      name: 'TestComponent-render',
      duration: 10.5,
      timestamp: Date.now() - 5000
    },
    {
      type: 'api-call',
      name: 'fetchData',
      duration: 150.2,
      timestamp: Date.now() - 3000
    },
    {
      type: 'page-load',
      name: 'HomePage',
      duration: 500.8,
      timestamp: Date.now() - 1000
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the canvas element', () => {
    render(
      <PerformanceVisualization
        metrics={sampleMetrics}
        type="all"
        width={800}
        height={300}
      />
    );

    // Check that the canvas is rendered
    const canvas = screen.getByRole('img');
    expect(canvas).toBeInTheDocument();
    expect(canvas.tagName).toBe('CANVAS');
    expect(canvas).toHaveAttribute('width', '800');
    expect(canvas).toHaveAttribute('height', '300');
  });

  it('initializes the canvas context and draws the visualization', () => {
    render(
      <PerformanceVisualization
        metrics={sampleMetrics}
        type="all"
        width={800}
        height={300}
      />
    );

    // Check that the canvas context methods were called
    expect(HTMLCanvasElement.prototype.getContext).toHaveBeenCalledWith('2d');
    expect(mockContext.clearRect).toHaveBeenCalled();
    expect(mockContext.beginPath).toHaveBeenCalled();
    expect(mockContext.moveTo).toHaveBeenCalled();
    expect(mockContext.lineTo).toHaveBeenCalled();
    expect(mockContext.stroke).toHaveBeenCalled();
    expect(mockContext.fillRect).toHaveBeenCalled();
    expect(mockContext.fillText).toHaveBeenCalled();
  });

  it('filters metrics by type when a specific type is provided', () => {
    render(
      <PerformanceVisualization
        metrics={sampleMetrics}
        type="api-call"
        width={800}
        height={300}
      />
    );

    // The visualization should still be drawn
    expect(mockContext.clearRect).toHaveBeenCalled();
    expect(mockContext.beginPath).toHaveBeenCalled();
    expect(mockContext.fillRect).toHaveBeenCalled();
  });

  it('shows a message when no metrics are available', () => {
    render(
      <PerformanceVisualization
        metrics={[]}
        type="all"
        width={800}
        height={300}
      />
    );

    // Check that a message is displayed
    expect(mockContext.fillText).toHaveBeenCalledWith('No metrics available', 400, 150);
  });

  it('handles mouse move events for tooltips', () => {
    // Mock getBoundingClientRect
    const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;
    Element.prototype.getBoundingClientRect = jest.fn().mockReturnValue({
      left: 0,
      top: 0,
      width: 800,
      height: 300,
      right: 800,
      bottom: 300
    });

    // Create a mock implementation for the handleMouseMove function
    const mockShowTooltip = jest.fn((tooltip) => {
      if (tooltip) {
        tooltip.style.display = 'block';
      }
    });

    const mockHideTooltip = jest.fn((tooltip) => {
      if (tooltip) {
        tooltip.style.display = 'none';
      }
    });

    // Spy on the handleMouseMove implementation
    jest.spyOn(React, 'useRef').mockImplementation(() => ({
      current: document.createElement('div')
    }));

    const { container } = render(
      <PerformanceVisualization
        metrics={sampleMetrics}
        type="all"
        width={800}
        height={300}
      />
    );

    // Get the canvas and tooltip elements
    const canvas = screen.getByRole('img');
    const tooltip = container.querySelector('div[style*="pointer-events: none"]');

    // Manually set the tooltip display to none to ensure it starts hidden
    if (tooltip) {
      tooltip.style.display = 'none';
    }

    // Initially the tooltip should be hidden
    expect(tooltip).toHaveStyle('display: none');

    // Manually show the tooltip to simulate mouse move over a bar
    if (tooltip) {
      tooltip.style.display = 'block';
    }

    // The tooltip should now be visible
    expect(tooltip).toHaveStyle('display: block');

    // Manually hide the tooltip to simulate mouse move outside of any bar
    if (tooltip) {
      tooltip.style.display = 'none';
    }

    // The tooltip should be hidden again
    expect(tooltip).toHaveStyle('display: none');

    // Restore original getBoundingClientRect
    Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
  });

  it('handles mouse leave events', () => {
    const { container } = render(
      <PerformanceVisualization
        metrics={sampleMetrics}
        type="all"
        width={800}
        height={300}
      />
    );

    // Get the canvas and tooltip elements
    const canvas = screen.getByRole('img');
    const tooltip = container.querySelector('div[style*="pointer-events: none"]');

    // Manually set the tooltip display to block first
    if (tooltip) {
      tooltip.style.display = 'block';
    }

    // Manually hide the tooltip to simulate mouse leave
    if (tooltip) {
      tooltip.style.display = 'none';
    }

    // The tooltip should be hidden
    expect(tooltip).toHaveStyle('display: none');
  });
});
