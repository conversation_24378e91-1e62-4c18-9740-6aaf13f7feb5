import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import PerformanceDashboard from '../PerformanceDashboard';
import * as performanceMonitoring from '../../../utils/performanceMonitoring';

// Mock the performance monitoring utilities
jest.mock('../../../utils/performanceMonitoring', () => ({
  getMetrics: jest.fn(),
  initPerformanceMonitoring: jest.fn(),
  PerformanceMetricType: {
    'component-render': 'component-render',
    'api-call': 'api-call',
    'page-load': 'page-load',
    'interaction': 'interaction',
    'animation': 'animation',
    'resource-load': 'resource-load'
  }
}));

// Mock the PerformanceVisualization component
jest.mock('../PerformanceVisualization', () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="performance-visualization" />)
}));

describe('PerformanceDashboard Component', () => {
  // Sample metrics for testing
  const sampleMetrics = [
    {
      type: 'component-render',
      name: 'TestComponent-render',
      duration: 10.5,
      timestamp: Date.now() - 5000
    },
    {
      type: 'api-call',
      name: 'fetchData',
      duration: 150.2,
      timestamp: Date.now() - 3000
    },
    {
      type: 'page-load',
      name: 'HomePage',
      duration: 500.8,
      timestamp: Date.now() - 1000
    }
  ];
  
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock getMetrics to return sample data
    (performanceMonitoring.getMetrics as jest.Mock).mockReturnValue(sampleMetrics);
  });
  
  it('renders the dashboard with metrics', () => {
    render(<PerformanceDashboard />);
    
    // Check that the title is rendered
    expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    
    // Check that the metrics table is rendered
    expect(screen.getByText('Type')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Duration')).toBeInTheDocument();
    
    // Check that the sample metrics are rendered
    expect(screen.getByText('TestComponent-render')).toBeInTheDocument();
    expect(screen.getByText('fetchData')).toBeInTheDocument();
    expect(screen.getByText('HomePage')).toBeInTheDocument();
  });
  
  it('filters metrics by type when a type is selected', () => {
    render(<PerformanceDashboard />);
    
    // Initially all metrics should be visible
    expect(screen.getByText('TestComponent-render')).toBeInTheDocument();
    expect(screen.getByText('fetchData')).toBeInTheDocument();
    expect(screen.getByText('HomePage')).toBeInTheDocument();
    
    // Select a specific type
    const typeSelect = screen.getByLabelText('Metric Type');
    fireEvent.change(typeSelect, { target: { value: 'api-call' } });
    
    // Now only the API call metric should be visible
    expect(screen.queryByText('TestComponent-render')).not.toBeInTheDocument();
    expect(screen.getByText('fetchData')).toBeInTheDocument();
    expect(screen.queryByText('HomePage')).not.toBeInTheDocument();
  });
  
  it('refreshes metrics when the refresh button is clicked', () => {
    render(<PerformanceDashboard />);
    
    // Click the refresh button
    const refreshButton = screen.getByText('Refresh Now');
    fireEvent.click(refreshButton);
    
    // Check that getMetrics was called again
    expect(performanceMonitoring.getMetrics).toHaveBeenCalledTimes(2);
  });
  
  it('changes the refresh interval when input is changed', () => {
    render(<PerformanceDashboard />);
    
    // Change the refresh interval
    const refreshIntervalInput = screen.getByLabelText('Refresh Interval (ms)');
    fireEvent.change(refreshIntervalInput, { target: { value: '10000' } });
    
    // Check that the input value was updated
    expect(refreshIntervalInput).toHaveValue(10000);
  });
  
  it('toggles auto-refresh when checkbox is clicked', () => {
    render(<PerformanceDashboard />);
    
    // Auto-refresh should be enabled by default
    const autoRefreshCheckbox = screen.getByLabelText('Auto Refresh');
    expect(autoRefreshCheckbox).toBeChecked();
    
    // Disable auto-refresh
    fireEvent.click(autoRefreshCheckbox);
    
    // Check that auto-refresh is now disabled
    expect(autoRefreshCheckbox).not.toBeChecked();
  });
  
  it('renders the performance visualization component', () => {
    render(<PerformanceDashboard />);
    
    // Check that the visualization component is rendered
    expect(screen.getByTestId('performance-visualization')).toBeInTheDocument();
  });
  
  it('displays summary cards for each metric type', () => {
    render(<PerformanceDashboard />);
    
    // Check that summary cards are rendered for each type
    expect(screen.getByText('component-render')).toBeInTheDocument();
    expect(screen.getByText('api-call')).toBeInTheDocument();
    expect(screen.getByText('page-load')).toBeInTheDocument();
    
    // Check that summary statistics are displayed
    expect(screen.getAllByText('Count')).toHaveLength(3);
    expect(screen.getAllByText('Average')).toHaveLength(3);
    expect(screen.getAllByText('Min')).toHaveLength(3);
    expect(screen.getAllByText('Max')).toHaveLength(3);
  });
});
