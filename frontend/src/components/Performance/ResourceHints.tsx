import React from 'react';
import { Helmet } from 'react-helmet-async';

interface ResourceHintsProps {
  preconnect?: string[];
  prefetch?: string[];
  preload?: Array<{
    href: string;
    as: 'script' | 'style' | 'image' | 'font' | 'fetch';
    type?: string;
    crossOrigin?: 'anonymous' | 'use-credentials';
  }>;
  preloadFonts?: Array<{
    href: string;
    type?: string;
    crossOrigin?: 'anonymous' | 'use-credentials';
  }>;
  preloadCriticalImages?: string[];
  preloadCriticalStyles?: string[];
  preloadCriticalScripts?: string[];
}

/**
 * ResourceHints Component
 * 
 * A component for adding resource hints to improve page load performance.
 * This component uses react-helmet to add link tags to the document head.
 * 
 * @example
 * <ResourceHints
 *   preconnect={['https://api.rentup.com', 'https://images.rentup.com']}
 *   preloadFonts={[{ href: '/fonts/inter.woff2', type: 'font/woff2' }]}
 *   preloadCriticalImages={['/images/hero.jpg']}
 * />
 */
export const ResourceHints: React.FC<ResourceHintsProps> = ({
  preconnect = [],
  prefetch = [],
  preload = [],
  preloadFonts = [],
  preloadCriticalImages = [],
  preloadCriticalStyles = [],
  preloadCriticalScripts = [],
}) => {
  // Prepare font preloads
  const fontPreloads = preloadFonts.map(font => ({
    href: font.href,
    as: 'font' as const,
    type: font.type || 'font/woff2',
    crossOrigin: font.crossOrigin || 'anonymous',
  }));
  
  // Prepare image preloads
  const imagePreloads = preloadCriticalImages.map(image => ({
    href: image,
    as: 'image' as const,
  }));
  
  // Prepare style preloads
  const stylePreloads = preloadCriticalStyles.map(style => ({
    href: style,
    as: 'style' as const,
  }));
  
  // Prepare script preloads
  const scriptPreloads = preloadCriticalScripts.map(script => ({
    href: script,
    as: 'script' as const,
  }));
  
  // Combine all preloads
  const allPreloads = [
    ...preload,
    ...fontPreloads,
    ...imagePreloads,
    ...stylePreloads,
    ...scriptPreloads,
  ];
  
  return (
    <Helmet>
      {/* Preconnect hints */}
      {preconnect.map((url, index) => (
        <link key={`preconnect-${index}`} rel="preconnect" href={url} crossOrigin="anonymous" />
      ))}
      
      {/* DNS prefetch hints */}
      {preconnect.map((url, index) => (
        <link key={`dns-prefetch-${index}`} rel="dns-prefetch" href={url} />
      ))}
      
      {/* Prefetch hints */}
      {prefetch.map((url, index) => (
        <link key={`prefetch-${index}`} rel="prefetch" href={url} />
      ))}
      
      {/* Preload hints */}
      {allPreloads.map((item, index) => (
        <link 
          key={`preload-${index}`} 
          rel="preload" 
          href={item.href} 
          as={item.as}
          type={item.type}
          crossOrigin={item.crossOrigin}
        />
      ))}
    </Helmet>
  );
};

export default ResourceHints;
