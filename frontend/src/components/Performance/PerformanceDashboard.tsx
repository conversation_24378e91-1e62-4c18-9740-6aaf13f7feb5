import React, { useState, useEffect } from 'react';
import { getMetrics, PerformanceMetric, PerformanceMetricType } from '../../utils/performanceMonitoring';
import { initPerformanceMonitoring } from '../../utils/performanceMonitoring';
import PerformanceVisualization from './PerformanceVisualization';

// Initialize performance monitoring when this component is imported
initPerformanceMonitoring();

interface MetricSummary {
  count: number;
  average: number;
  min: number;
  max: number;
  p95: number;
}

interface MetricsByType {
  [key: string]: {
    metrics: PerformanceMetric[];
    summary: MetricSummary;
  };
}

/**
 * Performance Dashboard Component
 *
 * Displays performance metrics collected by the performance monitoring system.
 * Shows metrics grouped by type with summary statistics and visualizations.
 */
const PerformanceDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [metricsByType, setMetricsByType] = useState<MetricsByType>({});
  const [selectedType, setSelectedType] = useState<PerformanceMetricType | 'all'>('all');
  const [refreshInterval, setRefreshInterval] = useState<number>(5000);
  const [isAutoRefresh, setIsAutoRefresh] = useState<boolean>(true);

  // Fetch metrics on mount and when refresh interval changes
  useEffect(() => {
    const fetchMetrics = () => {
      const allMetrics = getMetrics();
      setMetrics(allMetrics);

      // Group metrics by type
      const byType: MetricsByType = {};

      allMetrics.forEach(metric => {
        if (!byType[metric.type]) {
          byType[metric.type] = {
            metrics: [],
            summary: {
              count: 0,
              average: 0,
              min: 0,
              max: 0,
              p95: 0
            }
          };
        }

        byType[metric.type].metrics.push(metric);
      });

      // Calculate summary statistics for each type
      Object.keys(byType).forEach(type => {
        const typeMetrics = byType[type].metrics;
        const durations = typeMetrics.map(m => m.duration).sort((a, b) => a - b);

        byType[type].summary = {
          count: durations.length,
          average: durations.reduce((sum, val) => sum + val, 0) / durations.length,
          min: durations[0],
          max: durations[durations.length - 1],
          p95: durations[Math.floor(durations.length * 0.95)]
        };
      });

      setMetricsByType(byType);
    };

    // Initial fetch
    fetchMetrics();

    // Set up interval for auto-refresh
    let intervalId: number | undefined;

    if (isAutoRefresh) {
      intervalId = window.setInterval(fetchMetrics, refreshInterval);
    }

    // Clean up interval on unmount
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [refreshInterval, isAutoRefresh]);

  // Filter metrics by selected type
  const filteredMetrics = selectedType === 'all'
    ? metrics
    : metrics.filter(metric => metric.type === selectedType);

  // Get unique metric types
  const metricTypes = Object.keys(metricsByType) as PerformanceMetricType[];

  // Format duration in ms
  const formatDuration = (duration: number): string => {
    return `${duration.toFixed(2)} ms`;
  };

  // Format timestamp
  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-4">Performance Dashboard</h2>

      {/* Controls */}
      <div className="flex flex-wrap gap-4 mb-6">
        <div>
          <label htmlFor="metric-type" className="block text-sm font-medium text-gray-700 mb-1">
            Metric Type
          </label>
          <select
            id="metric-type"
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value as PerformanceMetricType | 'all')}
          >
            <option value="all">All Types</option>
            {metricTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="refresh-interval" className="block text-sm font-medium text-gray-700 mb-1">
            Refresh Interval (ms)
          </label>
          <input
            id="refresh-interval"
            type="number"
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            value={refreshInterval}
            onChange={(e) => setRefreshInterval(Number(e.target.value))}
            min={1000}
            step={1000}
          />
        </div>

        <div className="flex items-end">
          <label className="inline-flex items-center">
            <input
              type="checkbox"
              className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              checked={isAutoRefresh}
              onChange={(e) => setIsAutoRefresh(e.target.checked)}
            />
            <span className="ml-2 text-sm text-gray-700">Auto Refresh</span>
          </label>
        </div>

        <div className="flex items-end">
          <button
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            onClick={() => setMetrics(getMetrics())}
          >
            Refresh Now
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {Object.entries(metricsByType).map(([type, { summary }]) => (
          <div
            key={type}
            className={`p-4 rounded-lg shadow ${selectedType === type ? 'bg-blue-50 border-2 border-blue-500' : 'bg-gray-50'}`}
            onClick={() => setSelectedType(type as PerformanceMetricType)}
            role="button"
            tabIndex={0}
          >
            <h3 className="text-lg font-semibold mb-2">{type}</h3>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <p className="text-sm text-gray-500">Count</p>
                <p className="text-lg font-medium">{summary.count}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Average</p>
                <p className="text-lg font-medium">{formatDuration(summary.average)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Min</p>
                <p className="text-lg font-medium">{formatDuration(summary.min)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Max</p>
                <p className="text-lg font-medium">{formatDuration(summary.max)}</p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Visualization */}
      <div className="mb-6 bg-white p-4 rounded-lg shadow-md">
        <h3 className="text-lg font-semibold mb-4">Performance Visualization</h3>
        <div className="w-full overflow-x-auto">
          <PerformanceVisualization
            metrics={filteredMetrics}
            type={selectedType}
            width={800}
            height={300}
          />
        </div>
      </div>

      {/* Metrics Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Duration
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Timestamp
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Metadata
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredMetrics.length === 0 ? (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                  No metrics available
                </td>
              </tr>
            ) : (
              filteredMetrics.map((metric, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {metric.type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {metric.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDuration(metric.duration)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatTimestamp(metric.timestamp)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {metric.metadata ? (
                      <details>
                        <summary>View Metadata</summary>
                        <pre className="mt-2 text-xs bg-gray-100 p-2 rounded">
                          {JSON.stringify(metric.metadata, null, 2)}
                        </pre>
                      </details>
                    ) : (
                      'None'
                    )}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PerformanceDashboard;
