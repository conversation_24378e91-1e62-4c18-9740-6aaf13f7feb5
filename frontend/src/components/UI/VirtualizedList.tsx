import React, { useRef, useState, useEffect, useCallback } from 'react';
import { useInView } from 'react-intersection-observer';

interface VirtualizedListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  itemHeight: number | ((item: T, index: number) => number);
  overscan?: number;
  className?: string;
  onEndReached?: () => void;
  endReachedThreshold?: number;
  scrollToIndex?: number;
  keyExtractor?: (item: T, index: number) => string;
  testId?: string;
}

/**
 * VirtualizedList Component
 * 
 * A performant list component that only renders items that are visible in the viewport.
 * This significantly improves performance for long lists by reducing DOM nodes.
 * 
 * Features:
 * - Only renders visible items
 * - Supports variable item heights
 * - Supports scroll to index
 * - Supports end reached callback for infinite loading
 * - Maintains scroll position when items change
 */
function VirtualizedList<T>({
  items,
  renderItem,
  itemHeight,
  overscan = 5,
  className = '',
  onEndReached,
  endReachedThreshold = 0.8,
  scrollToIndex,
  keyExtractor = (_, index) => index.toString(),
  testId = 'virtualized-list',
}: VirtualizedListProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });
  const [totalHeight, setTotalHeight] = useState(0);
  const [itemPositions, setItemPositions] = useState<number[]>([]);
  const previousScrollTop = useRef(0);
  const previousItems = useRef(items);
  
  // End reached detection
  const { ref: endRef } = useInView({
    threshold: endReachedThreshold,
    onChange: (inView) => {
      if (inView && onEndReached) {
        onEndReached();
      }
    },
  });
  
  // Calculate item positions and total height
  useEffect(() => {
    const positions: number[] = [];
    let currentPosition = 0;
    
    items.forEach((item, index) => {
      positions[index] = currentPosition;
      const height = typeof itemHeight === 'function' ? itemHeight(item, index) : itemHeight;
      currentPosition += height;
    });
    
    setItemPositions(positions);
    setTotalHeight(currentPosition);
  }, [items, itemHeight]);
  
  // Calculate visible range based on scroll position
  const calculateVisibleRange = useCallback(() => {
    if (!containerRef.current) return;
    
    const { scrollTop, clientHeight } = containerRef.current;
    const viewportTop = scrollTop;
    const viewportBottom = scrollTop + clientHeight;
    
    // Find the first visible item
    let startIndex = 0;
    while (
      startIndex < items.length &&
      (itemPositions[startIndex + 1] === undefined || itemPositions[startIndex + 1] < viewportTop)
    ) {
      startIndex++;
    }
    
    // Apply overscan to start index
    startIndex = Math.max(0, startIndex - overscan);
    
    // Find the last visible item
    let endIndex = startIndex;
    while (
      endIndex < items.length &&
      itemPositions[endIndex] < viewportBottom
    ) {
      endIndex++;
    }
    
    // Apply overscan to end index
    endIndex = Math.min(items.length - 1, endIndex + overscan);
    
    setVisibleRange({ start: startIndex, end: endIndex });
    previousScrollTop.current = scrollTop;
  }, [items.length, itemPositions, overscan]);
  
  // Handle scroll events
  const handleScroll = useCallback(() => {
    window.requestAnimationFrame(calculateVisibleRange);
  }, [calculateVisibleRange]);
  
  // Set up scroll event listener
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;
    
    container.addEventListener('scroll', handleScroll);
    window.addEventListener('resize', calculateVisibleRange);
    
    // Initial calculation
    calculateVisibleRange();
    
    return () => {
      container.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', calculateVisibleRange);
    };
  }, [calculateVisibleRange, handleScroll]);
  
  // Maintain scroll position when items change
  useEffect(() => {
    if (items !== previousItems.current && containerRef.current) {
      containerRef.current.scrollTop = previousScrollTop.current;
      previousItems.current = items;
    }
  }, [items]);
  
  // Scroll to index
  useEffect(() => {
    if (scrollToIndex !== undefined && containerRef.current && itemPositions[scrollToIndex] !== undefined) {
      containerRef.current.scrollTop = itemPositions[scrollToIndex];
    }
  }, [scrollToIndex, itemPositions]);
  
  // Render only visible items
  const visibleItems = items.slice(visibleRange.start, visibleRange.end + 1);
  
  return (
    <div
      ref={containerRef}
      className={`overflow-auto ${className}`}
      data-testid={testId}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {visibleItems.map((item, index) => {
          const actualIndex = visibleRange.start + index;
          const top = itemPositions[actualIndex];
          const height = typeof itemHeight === 'function' 
            ? itemHeight(item, actualIndex) 
            : itemHeight;
          
          return (
            <div
              key={keyExtractor(item, actualIndex)}
              style={{
                position: 'absolute',
                top,
                height,
                width: '100%',
              }}
              data-testid={`${testId}-item-${actualIndex}`}
            >
              {renderItem(item, actualIndex)}
            </div>
          );
        })}
        
        {/* End reached detector */}
        <div
          ref={endRef}
          style={{
            position: 'absolute',
            bottom: 0,
            width: '100%',
            height: '20px',
          }}
          data-testid={`${testId}-end-detector`}
        />
      </div>
    </div>
  );
}

export default VirtualizedList;
