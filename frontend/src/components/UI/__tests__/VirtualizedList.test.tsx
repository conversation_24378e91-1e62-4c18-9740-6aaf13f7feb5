import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import VirtualizedList from '../VirtualizedList';

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
});
window.IntersectionObserver = mockIntersectionObserver;

describe('VirtualizedList Component', () => {
  // Create test data
  const items = Array.from({ length: 1000 }, (_, i) => ({ id: i, name: `Item ${i}` }));
  
  // Mock element dimensions and scroll behavior
  const mockClientHeight = 500;
  const mockScrollTop = 0;
  
  // Mock element ref
  const mockRef = {
    current: {
      clientHeight: mockClientHeight,
      scrollTop: mockScrollTop,
      scrollHeight: 10000,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn()
    }
  };
  
  // Mock useRef to return our mock ref
  jest.spyOn(React, 'useRef').mockReturnValue(mockRef);
  
  // Mock window.requestAnimationFrame
  const originalRequestAnimationFrame = window.requestAnimationFrame;
  beforeEach(() => {
    window.requestAnimationFrame = jest.fn().mockImplementation(cb => cb());
  });
  
  afterEach(() => {
    window.requestAnimationFrame = originalRequestAnimationFrame;
    jest.clearAllMocks();
  });
  
  it('renders only visible items', () => {
    const renderItem = jest.fn((item) => (
      <div data-testid={`item-${item.id}`}>{item.name}</div>
    ));
    
    render(
      <VirtualizedList
        items={items}
        renderItem={renderItem}
        itemHeight={50}
        testId="virtualized-list"
      />
    );
    
    // Check that the container is rendered
    const container = screen.getByTestId('virtualized-list');
    expect(container).toBeInTheDocument();
    
    // Check that renderItem was called a limited number of times
    // With overscan of 5 (default) and container height of 500px,
    // we should render about 20 items (500/50 + 2*5)
    expect(renderItem).toHaveBeenCalledTimes(20);
  });
  
  it('supports variable item heights', () => {
    const itemHeight = jest.fn((item) => (item.id % 2 === 0 ? 50 : 100));
    
    const renderItem = jest.fn((item) => (
      <div data-testid={`item-${item.id}`}>{item.name}</div>
    ));
    
    render(
      <VirtualizedList
        items={items}
        renderItem={renderItem}
        itemHeight={itemHeight}
        testId="virtualized-list"
      />
    );
    
    // Check that itemHeight was called for each item
    expect(itemHeight).toHaveBeenCalled();
    
    // Check that the container has the correct height
    const container = screen.getByTestId('virtualized-list');
    const innerContainer = container.firstChild as HTMLElement;
    
    // The total height should be the sum of all item heights
    // But we can't check the exact value because the mock doesn't actually set the height
    expect(innerContainer).toBeInTheDocument();
  });
  
  it('calls onEndReached when scrolling to the end', () => {
    const onEndReached = jest.fn();
    
    render(
      <VirtualizedList
        items={items}
        renderItem={(item) => <div>{item.name}</div>}
        itemHeight={50}
        onEndReached={onEndReached}
        testId="virtualized-list"
      />
    );
    
    // Get the IntersectionObserver callback
    const [observerCallback] = mockIntersectionObserver.mock.calls[0];
    
    // Simulate intersection with the end detector
    observerCallback([{ isIntersecting: true }]);
    
    // Check that onEndReached was called
    expect(onEndReached).toHaveBeenCalled();
  });
  
  it('supports scrollToIndex prop', () => {
    const scrollToIndex = 50;
    
    render(
      <VirtualizedList
        items={items}
        renderItem={(item) => <div>{item.name}</div>}
        itemHeight={50}
        scrollToIndex={scrollToIndex}
        testId="virtualized-list"
      />
    );
    
    // Check that scrollTop was set to the correct value
    // In a real component, this would scroll to item 50
    // But in our mock, we can't actually check this
    expect(mockRef.current).toBeDefined();
  });
  
  it('supports keyExtractor prop', () => {
    const keyExtractor = jest.fn((item) => `custom-key-${item.id}`);
    
    render(
      <VirtualizedList
        items={items}
        renderItem={(item) => <div>{item.name}</div>}
        itemHeight={50}
        keyExtractor={keyExtractor}
        testId="virtualized-list"
      />
    );
    
    // Check that keyExtractor was called
    expect(keyExtractor).toHaveBeenCalled();
  });
  
  it('recalculates visible range when items change', () => {
    const { rerender } = render(
      <VirtualizedList
        items={items}
        renderItem={(item) => <div>{item.name}</div>}
        itemHeight={50}
        testId="virtualized-list"
      />
    );
    
    // Rerender with different items
    const newItems = items.slice(0, 500);
    rerender(
      <VirtualizedList
        items={newItems}
        renderItem={(item) => <div>{item.name}</div>}
        itemHeight={50}
        testId="virtualized-list"
      />
    );
    
    // Check that the container is still rendered
    const container = screen.getByTestId('virtualized-list');
    expect(container).toBeInTheDocument();
  });
  
  it('handles scroll events', () => {
    render(
      <VirtualizedList
        items={items}
        renderItem={(item) => <div>{item.name}</div>}
        itemHeight={50}
        testId="virtualized-list"
      />
    );
    
    // Check that addEventListener was called for scroll events
    expect(mockRef.current.addEventListener).toHaveBeenCalledWith('scroll', expect.any(Function));
    
    // Get the scroll handler
    const scrollHandler = mockRef.current.addEventListener.mock.calls[0][1];
    
    // Simulate a scroll event
    scrollHandler();
    
    // Check that requestAnimationFrame was called
    expect(window.requestAnimationFrame).toHaveBeenCalled();
  });
});
