import React from 'react';
import { render, screen } from '@testing-library/react';
import ResponsiveImage from '../ResponsiveImage';
import * as imageOptimization from '../../../utils/imageOptimization';

// Mock the image optimization utilities
jest.mock('../../../utils/imageOptimization', () => ({
  generateSrcSet: jest.fn().mockReturnValue('image-400.jpg 400w, image-800.jpg 800w'),
  getBestImageFormat: jest.fn().mockResolvedValue('webp'),
  createBlurPlaceholder: jest.fn().mockResolvedValue('blur-placeholder.jpg')
}));

describe('ResponsiveImage Component', () => {
  const defaultProps = {
    sources: [
      { src: 'image-400.jpg', width: 400 },
      { src: 'image-800.jpg', width: 800 }
    ],
    alt: 'Test image',
    className: 'test-class',
    sizes: '100vw',
    loading: 'lazy' as const,
    decoding: 'async' as const,
    fetchPriority: 'auto' as const
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with default props', () => {
    render(<ResponsiveImage {...defaultProps} />);
    
    const img = screen.getByAltText('Test image');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('loading', 'lazy');
    expect(img).toHaveAttribute('decoding', 'async');
    
    // Check if the container has the provided class
    const container = img.parentElement;
    expect(container).toHaveClass('test-class');
  });

  it('generates srcset from sources', () => {
    render(<ResponsiveImage {...defaultProps} />);
    
    expect(imageOptimization.generateSrcSet).toHaveBeenCalledWith(
      expect.arrayContaining([
        expect.objectContaining({ src: 'image-400.jpg', width: 400 }),
        expect.objectContaining({ src: 'image-800.jpg', width: 800 })
      ])
    );
    
    const img = screen.getByAltText('Test image');
    expect(img).toHaveAttribute('srcSet', 'image-400.jpg 400w, image-800.jpg 800w');
  });

  it('uses the largest source as default src', () => {
    render(<ResponsiveImage {...defaultProps} />);
    
    const img = screen.getByAltText('Test image');
    expect(img).toHaveAttribute('src', 'image-800.jpg');
  });

  it('applies sizes attribute', () => {
    render(<ResponsiveImage {...defaultProps} sizes="50vw" />);
    
    const img = screen.getByAltText('Test image');
    expect(img).toHaveAttribute('sizes', '50vw');
  });

  it('applies width and height if provided', () => {
    render(<ResponsiveImage {...defaultProps} width={400} height={300} />);
    
    const img = screen.getByAltText('Test image');
    expect(img).toHaveAttribute('width', '400');
    expect(img).toHaveAttribute('height', '300');
  });

  it('creates picture element with source elements for different formats', async () => {
    // Mock getBestImageFormat to return 'avif'
    (imageOptimization.getBestImageFormat as jest.Mock).mockResolvedValue('avif');
    
    render(<ResponsiveImage {...defaultProps} />);
    
    // Wait for useEffect to complete
    await screen.findByAltText('Test image');
    
    // Re-render to trigger the effect of format change
    render(<ResponsiveImage {...defaultProps} />);
    
    // Check for picture element
    const picture = screen.getByAltText('Test image').closest('picture');
    expect(picture).toBeInTheDocument();
    
    // Check for source elements
    const sources = picture?.querySelectorAll('source');
    expect(sources?.length).toBeGreaterThan(0);
  });

  it('shows placeholder while loading', async () => {
    render(<ResponsiveImage {...defaultProps} />);
    
    // Wait for useEffect to complete
    await screen.findByAltText('Test image');
    
    // Check for placeholder
    const placeholder = screen.getByAltText('Test image').parentElement?.querySelector('[aria-hidden="true"]');
    expect(placeholder).toBeInTheDocument();
  });

  it('applies transition classes for loading state', async () => {
    render(<ResponsiveImage {...defaultProps} />);
    
    // Get the image
    const img = await screen.findByAltText('Test image');
    
    // Initially the image should have opacity-0 class
    expect(img).toHaveClass('opacity-0');
    
    // Simulate image load
    img.dispatchEvent(new Event('load'));
    
    // After loading, the image should have opacity-100 class
    expect(img).toHaveClass('opacity-100');
  });
});
