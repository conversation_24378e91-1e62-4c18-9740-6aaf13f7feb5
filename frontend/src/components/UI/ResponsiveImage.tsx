import React, { useState, useEffect } from 'react';
import { 
  ResponsiveImageProps, 
  generateSrcSet, 
  getBestImageFormat,
  createBlurPlaceholder
} from '../../utils/imageOptimization';

/**
 * ResponsiveImage Component
 * 
 * A component for rendering responsive images with modern best practices:
 * - Responsive sizing with srcset and sizes
 * - Modern formats (WebP, AVIF) with fallbacks
 * - Lazy loading
 * - Blur-up loading effect
 * - Accessibility attributes
 */
const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  sources,
  alt,
  className = '',
  sizes = '100vw',
  loading = 'lazy',
  decoding = 'async',
  fetchPriority = 'auto',
  width,
  height,
}) => {
  const [loaded, setLoaded] = useState(false);
  const [placeholder, setPlaceholder] = useState<string | null>(null);
  const [bestFormat, setBestFormat] = useState<'avif' | 'webp' | 'jpeg'>('jpeg');
  
  // Sort sources by width for proper srcset
  const sortedSources = [...sources].sort((a, b) => a.width - b.width);
  
  // Get the largest source for the default src
  const defaultSource = sortedSources[sortedSources.length - 1];
  
  // Generate srcset
  const srcSet = generateSrcSet(sortedSources);
  
  // Determine best format and create placeholder
  useEffect(() => {
    const init = async () => {
      // Get best supported format
      const format = await getBestImageFormat();
      setBestFormat(format);
      
      // Create blur placeholder
      if (loading === 'lazy') {
        const blurPlaceholder = await createBlurPlaceholder(defaultSource.src);
        setPlaceholder(blurPlaceholder);
      }
    };
    
    init();
  }, [defaultSource.src, loading]);
  
  // Filter sources by format
  const getSourcesByFormat = (format: 'avif' | 'webp' | 'jpeg' | 'png') => {
    return sortedSources.filter(source => !source.format || source.format === format);
  };
  
  // Handle image load
  const handleLoad = () => {
    setLoaded(true);
  };
  
  return (
    <div className={`relative overflow-hidden ${className}`} style={{ width, height }}>
      {/* Placeholder while loading */}
      {placeholder && !loaded && (
        <div 
          className="absolute inset-0 bg-cover bg-center blur-sm transition-opacity duration-300"
          style={{ 
            backgroundImage: `url(${placeholder})`,
            opacity: loaded ? 0 : 1,
          }}
          aria-hidden="true"
        />
      )}
      
      <picture>
        {/* AVIF sources */}
        {bestFormat === 'avif' && (
          <source
            type="image/avif"
            srcSet={generateSrcSet(getSourcesByFormat('avif'))}
            sizes={sizes}
          />
        )}
        
        {/* WebP sources */}
        {(bestFormat === 'avif' || bestFormat === 'webp') && (
          <source
            type="image/webp"
            srcSet={generateSrcSet(getSourcesByFormat('webp'))}
            sizes={sizes}
          />
        )}
        
        {/* JPEG/PNG sources */}
        <img
          src={defaultSource.src}
          srcSet={srcSet}
          sizes={sizes}
          alt={alt}
          loading={loading}
          decoding={decoding}
          fetchPriority={fetchPriority}
          onLoad={handleLoad}
          className={`w-full h-full object-cover transition-opacity duration-300 ${loaded ? 'opacity-100' : 'opacity-0'}`}
          width={width}
          height={height}
        />
      </picture>
    </div>
  );
};

export default ResponsiveImage;
