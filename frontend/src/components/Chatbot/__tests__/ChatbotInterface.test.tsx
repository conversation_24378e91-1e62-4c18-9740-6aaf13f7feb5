import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ChatbotInterface from '../ChatbotInterface';
import '@testing-library/jest-dom';

// Mock uuid
jest.mock('uuid', () => ({
  v4: () => 'test-uuid'
}));

// Mock axios
jest.mock('axios', () => ({
  post: jest.fn(() => Promise.resolve({
    data: {
      message: 'Test response',
      intent: 'general',
      suggestions: [
        { id: 'suggestion_1', text: 'Suggestion 1' },
        { id: 'suggestion_2', text: 'Suggestion 2' },
        { id: 'suggestion_3', text: 'Suggestion 3' }
      ],
      timestamp: new Date().toISOString()
    }
  }))
}));

describe('ChatbotInterface', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('renders the initial state correctly', () => {
    render(<ChatbotInterface initialMessage="Hello, how can I help you?" />);
    
    // Check for initial message
    expect(screen.getByText('Hello, how can I help you?')).toBeInTheDocument();
    
    // Check for input field
    expect(screen.getByPlaceholderText('Type your message...')).toBeInTheDocument();
  });

  it('allows user to send a message', async () => {
    render(<ChatbotInterface />);
    
    // Type a message
    const input = screen.getByPlaceholderText('Type your message...');
    await userEvent.type(input, 'Hello, I need help');
    
    // Send the message
    const sendButton = screen.getByRole('button', { name: '' }); // The send button has an SVG icon
    await userEvent.click(sendButton);
    
    // Check that the user message appears
    expect(screen.getByText('Hello, I need help')).toBeInTheDocument();
    
    // Check for loading indicator
    expect(screen.getByRole('status')).toBeInTheDocument();
    
    // Wait for response
    await waitFor(() => {
      expect(screen.getByText('Test response')).toBeInTheDocument();
    });
  });

  it('displays suggestions and allows clicking them', async () => {
    const axios = require('axios');
    axios.post.mockResolvedValueOnce({
      data: {
        message: 'Test response',
        intent: 'general',
        suggestions: [
          { id: 'suggestion_1', text: 'How do I rent an item?' },
          { id: 'suggestion_2', text: 'What are the fees?' },
          { id: 'suggestion_3', text: 'How do I contact support?' }
        ],
        timestamp: new Date().toISOString()
      }
    });
    
    render(<ChatbotInterface />);
    
    // Type and send a message
    const input = screen.getByPlaceholderText('Type your message...');
    await userEvent.type(input, 'Hello');
    const sendButton = screen.getByRole('button', { name: '' });
    await userEvent.click(sendButton);
    
    // Wait for suggestions to appear
    await waitFor(() => {
      expect(screen.getByText('How do I rent an item?')).toBeInTheDocument();
    });
    
    // Click a suggestion
    const suggestion = screen.getByText('What are the fees?');
    await userEvent.click(suggestion);
    
    // Check that the suggestion text is now in the input field
    const updatedInput = screen.getByPlaceholderText('Type your message...');
    expect(updatedInput).toHaveValue('What are the fees?');
  });

  it('can be minimized and maximized', () => {
    render(<ChatbotInterface />);
    
    // Find and click the minimize button
    const minimizeButton = screen.getByRole('button', { name: '' }); // The minimize button has an SVG icon
    fireEvent.click(minimizeButton);
    
    // Check that the chat body is not visible
    expect(screen.queryByPlaceholderText('Type your message...')).not.toBeInTheDocument();
    
    // Click again to maximize
    fireEvent.click(minimizeButton);
    
    // Check that the chat body is visible again
    expect(screen.getByPlaceholderText('Type your message...')).toBeInTheDocument();
  });

  it('can be closed', () => {
    const onCloseMock = jest.fn();
    render(<ChatbotInterface onClose={onCloseMock} />);
    
    // Find and click the close button
    const closeButton = screen.getAllByRole('button', { name: '' })[1]; // The close button is the second button with an SVG icon
    fireEvent.click(closeButton);
    
    // Check that onClose was called
    expect(onCloseMock).toHaveBeenCalled();
  });

  it('handles API errors gracefully', async () => {
    const axios = require('axios');
    axios.post.mockRejectedValueOnce(new Error('API Error'));
    
    render(<ChatbotInterface />);
    
    // Type and send a message
    const input = screen.getByPlaceholderText('Type your message...');
    await userEvent.type(input, 'Hello');
    const sendButton = screen.getByRole('button', { name: '' });
    await userEvent.click(sendButton);
    
    // Wait for error message
    await waitFor(() => {
      expect(screen.getByText(/Sorry, I encountered an error/)).toBeInTheDocument();
    });
  });
});
