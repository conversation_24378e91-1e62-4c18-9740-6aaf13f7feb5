import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import ChatbotButton from '../ChatbotButton';
import '@testing-library/jest-dom';

// Mock the ChatbotInterface component
jest.mock('../ChatbotInterface', () => {
  return function MockChatbotInterface({ onClose }: { onClose?: () => void }) {
    return (
      <div data-testid="chatbot-interface">
        Mock Chatbot Interface
        <button onClick={onClose}>Close</button>
      </div>
    );
  };
});

describe('ChatbotButton', () => {
  it('renders the floating action buttons when closed', () => {
    render(<ChatbotButton />);
    
    // Check that all buttons are rendered
    expect(screen.getByTitle('Get support')).toBeInTheDocument();
    expect(screen.getByTitle('Give feedback')).toBeInTheDocument();
    expect(screen.getByTitle('Report a dispute')).toBeInTheDocument();
    expect(screen.getByTitle('Chat with us')).toBeInTheDocument();
  });

  it('opens the chatbot interface when a button is clicked', () => {
    render(<ChatbotButton />);
    
    // Click the support button
    fireEvent.click(screen.getByTitle('Get support'));
    
    // Check that the chatbot interface is rendered
    expect(screen.getByTestId('chatbot-interface')).toBeInTheDocument();
    
    // Check that the floating buttons are hidden
    expect(screen.queryByTitle('Get support')).not.toBeInTheDocument();
  });

  it('closes the chatbot interface when the close button is clicked', () => {
    render(<ChatbotButton />);
    
    // Open the chatbot
    fireEvent.click(screen.getByTitle('Get support'));
    
    // Check that the chatbot interface is rendered
    expect(screen.getByTestId('chatbot-interface')).toBeInTheDocument();
    
    // Close the chatbot
    fireEvent.click(screen.getByText('Close'));
    
    // Check that the chatbot interface is removed
    expect(screen.queryByTestId('chatbot-interface')).not.toBeInTheDocument();
    
    // Check that the floating buttons are visible again
    expect(screen.getByTitle('Get support')).toBeInTheDocument();
  });

  it('passes the userId prop to the ChatbotInterface', () => {
    // Create a spy on the ChatbotInterface mock
    const ChatbotInterface = require('../ChatbotInterface');
    const spy = jest.spyOn(ChatbotInterface, 'default');
    
    // Render with userId
    render(<ChatbotButton userId="user123" />);
    
    // Open the chatbot
    fireEvent.click(screen.getByTitle('Get support'));
    
    // Check that userId was passed to ChatbotInterface
    expect(spy).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user123',
        chatType: 'support'
      }),
      expect.anything()
    );
    
    // Clean up
    spy.mockRestore();
  });

  it('sets the correct chat type based on the button clicked', () => {
    render(<ChatbotButton />);
    
    // Test support button
    fireEvent.click(screen.getByTitle('Get support'));
    expect(screen.getByTestId('chatbot-interface')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Close'));
    
    // Test feedback button
    fireEvent.click(screen.getByTitle('Give feedback'));
    const ChatbotInterface = require('../ChatbotInterface');
    expect(ChatbotInterface.default).toHaveBeenLastCalledWith(
      expect.objectContaining({
        chatType: 'feedback'
      }),
      expect.anything()
    );
    fireEvent.click(screen.getByText('Close'));
    
    // Test dispute button
    fireEvent.click(screen.getByTitle('Report a dispute'));
    expect(ChatbotInterface.default).toHaveBeenLastCalledWith(
      expect.objectContaining({
        chatType: 'dispute'
      }),
      expect.anything()
    );
    fireEvent.click(screen.getByText('Close'));
    
    // Test general chat button
    fireEvent.click(screen.getByTitle('Chat with us'));
    expect(ChatbotInterface.default).toHaveBeenLastCalledWith(
      expect.objectContaining({
        chatType: 'general'
      }),
      expect.anything()
    );
  });

  it('applies custom className prop', () => {
    render(<ChatbotButton className="custom-class" />);
    
    // Check that the custom class is applied to the container
    const container = screen.getByTitle('Get support').closest('div')?.parentElement;
    expect(container).toHaveClass('custom-class');
  });
});
