# RentUp Chatbot Components

The RentUp Chatbot components provide a user-friendly interface for interacting with the AI-powered chatbot. These components enable users to get support, provide feedback, resolve disputes, and ask general questions about the RentUp platform.

## Features

- **Floating Cha<PERSON> <PERSON><PERSON>**: Easy access to the chatbot from any page
- **Minimizable Interface**: Compact design that can be minimized when not in use
- **Conversation History**: Maintains the full conversation history
- **Suggested Responses**: Provides clickable suggestions for common follow-up questions
- **Multiple Chat Types**: Support for different conversation types (support, feedback, dispute, general)
- **Loading Indicators**: Visual feedback during message processing
- **Error Handling**: Graceful handling of API errors

## Components

The Chatbot module consists of the following components:

1. **ChatbotInterface**: The main component that handles the chat functionality
2. **ChatbotButton**: A floating action button that opens the chatbot interface

## Usage

### Adding the Chatbot Button

```tsx
import { ChatbotButton } from '../components/Chatbot';

// Add the button to your layout
const Layout = () => (
  <div>
    <Header />
    <main>{children}</main>
    <Footer />
    <ChatbotButton userId={user?.id} />
  </div>
);
```

### Using the Chatbot Interface Directly

```tsx
import { ChatbotInterface } from '../components/Chatbot';

const SupportPage = () => (
  <div className="container mx-auto px-4 py-8">
    <h1 className="text-3xl font-bold mb-4">Customer Support</h1>
    <ChatbotInterface 
      chatType="support" 
      initialMessage="Hi there! How can I help you with your RentUp experience today?"
      userId={user?.id} 
    />
  </div>
);
```

## Props

### ChatbotInterface

| Prop | Type | Description |
|------|------|-------------|
| initialMessage | string (optional) | The initial message from the chatbot |
| chatType | 'support' \| 'feedback' \| 'dispute' \| 'general' (optional) | The type of chat |
| userId | string (optional) | The ID of the current user |
| onClose | function (optional) | Callback function when the chatbot is closed |
| className | string (optional) | Additional CSS classes |

### ChatbotButton

| Prop | Type | Description |
|------|------|-------------|
| userId | string (optional) | The ID of the current user |
| className | string (optional) | Additional CSS classes |

## Chat Types

The chatbot supports four different types of conversations:

1. **Support**: For customer support inquiries
2. **Feedback**: For collecting user feedback
3. **Dispute**: For handling disputes and issues
4. **General**: For general questions about RentUp

Each chat type has its own initial message and suggested responses.

## API Integration

The chatbot components integrate with the RentUp backend API:

- **POST /api/v1/chatbot/chat**: Sends user messages and receives responses
- **POST /api/v1/chatbot/feedback**: Submits feedback on chatbot responses

## Testing

Run the chatbot component tests with:

```bash
npm test -- --testPathPattern=Chatbot
```

## Styling

The chatbot components use Tailwind CSS for styling and are designed to be responsive and accessible. The color scheme follows the RentUp design system, using the primary color for user messages and accents.

## Future Improvements

- Add support for file attachments
- Implement voice input and output
- Add support for rich message formatting
- Implement typing indicators
- Add support for user ratings on individual messages
- Implement chat transcript export
- Add support for agent handoff to human support
