import React, { useState } from 'react';
import ChatbotInterface from './ChatbotInterface';

interface ChatbotButtonProps {
  userId?: string;
  className?: string;
}

const ChatbotButton: React.FC<ChatbotButtonProps> = ({ userId, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [chatType, setChatType] = useState<'support' | 'feedback' | 'dispute' | 'general'>('general');
  
  // Toggle chatbot
  const toggleChatbot = (type: 'support' | 'feedback' | 'dispute' | 'general' = 'general') => {
    setChatType(type);
    setIsOpen(!isOpen);
  };
  
  return (
    <>
      {/* Floating action button */}
      <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
        {!isOpen && (
          <div className="flex flex-col items-end space-y-2">
            {/* Support button */}
            <button
              onClick={() => toggleChatbot('support')}
              className="bg-blue-500 text-white p-3 rounded-full shadow-lg hover:bg-blue-600 transition-colors"
              aria-label="Get support"
              title="Get support"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192l-3.536 3.536M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </button>
            
            {/* Feedback button */}
            <button
              onClick={() => toggleChatbot('feedback')}
              className="bg-green-500 text-white p-3 rounded-full shadow-lg hover:bg-green-600 transition-colors"
              aria-label="Give feedback"
              title="Give feedback"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
            
            {/* Dispute button */}
            <button
              onClick={() => toggleChatbot('dispute')}
              className="bg-red-500 text-white p-3 rounded-full shadow-lg hover:bg-red-600 transition-colors"
              aria-label="Report a dispute"
              title="Report a dispute"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </button>
            
            {/* General chat button */}
            <button
              onClick={() => toggleChatbot('general')}
              className="bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary-dark transition-colors"
              aria-label="Chat with us"
              title="Chat with us"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
            </button>
          </div>
        )}
      </div>
      
      {/* Chatbot interface */}
      {isOpen && (
        <ChatbotInterface
          chatType={chatType}
          userId={userId}
          onClose={() => setIsOpen(false)}
          initialMessage={
            chatType === 'support'
              ? "Hi there! How can I help you today? If you need support with a specific issue, please provide details so I can assist you better."
              : chatType === 'feedback'
              ? "We value your feedback! Please share your thoughts, suggestions, or experiences with RentUp."
              : chatType === 'dispute'
              ? "I'm here to help with any disputes or issues you're experiencing. Please provide details about your concern, including any relevant booking information."
              : "Hi there! How can I help you today? Feel free to ask any questions about RentUp."
          }
        />
      )}
    </>
  );
};

export default ChatbotButton;
