import React, { useState, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

// Types
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

interface ChatSuggestion {
  id: string;
  text: string;
}

interface ChatbotProps {
  initialMessage?: string;
  chatType?: 'support' | 'feedback' | 'dispute' | 'general';
  userId?: string;
  onClose?: () => void;
  className?: string;
}

const ChatbotInterface: React.FC<ChatbotProps> = ({
  initialMessage = "Hi there! How can I help you today?",
  chatType = 'general',
  userId,
  onClose,
  className = ''
}) => {
  // State
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState('');
  const [suggestions, setSuggestions] = useState<ChatSuggestion[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Initialize chat session
  useEffect(() => {
    const newSessionId = uuidv4();
    setSessionId(newSessionId);

    // Add initial assistant message
    const initialAssistantMessage: ChatMessage = {
      id: uuidv4(),
      role: 'assistant',
      content: initialMessage,
      timestamp: new Date().toISOString()
    };

    setMessages([initialAssistantMessage]);

    // Set initial suggestions based on chat type
    setInitialSuggestions(chatType);

    // Set chat as open
    setIsOpen(true);
  }, [initialMessage, chatType]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Focus input when chat is opened
  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, isMinimized]);

  // Set initial suggestions based on chat type
  const setInitialSuggestions = (type: string) => {
    switch (type) {
      case 'support':
        setSuggestions([
          { id: 'support_1', text: 'I need help with a booking' },
          { id: 'support_2', text: 'How do I contact customer support?' },
          { id: 'support_3', text: 'I have a problem with my account' }
        ]);
        break;
      case 'feedback':
        setSuggestions([
          { id: 'feedback_1', text: 'I want to provide feedback' },
          { id: 'feedback_2', text: 'I have a suggestion for improvement' },
          { id: 'feedback_3', text: 'I found a bug on the website' }
        ]);
        break;
      case 'dispute':
        setSuggestions([
          { id: 'dispute_1', text: 'I need to report an issue with a rental' },
          { id: 'dispute_2', text: 'The item I received was damaged' },
          { id: 'dispute_3', text: 'I need a refund for my booking' }
        ]);
        break;
      default:
        setSuggestions([
          { id: 'general_1', text: 'How do I list an item for rent?' },
          { id: 'general_2', text: 'How does the booking process work?' },
          { id: 'general_3', text: 'What safety measures do you have in place?' }
        ]);
    }
  };

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Handle sending a message
  const handleSendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (!input.trim()) return;

    // Add user message to chat
    const userMessage: ChatMessage = {
      id: uuidv4(),
      role: 'user',
      content: input,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // Prepare request payload
      const payload = {
        user_id: userId,
        session_id: sessionId,
        messages: [...messages, userMessage].map(msg => ({
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp
        })),
        context: {
          chat_type: chatType
        }
      };

      // Send request to API
      const response = await axios.post('/api/v1/chatbot/chat', payload);

      // Add assistant response to chat
      const assistantMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: response.data.message,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Update suggestions
      if (response.data.suggestions && response.data.suggestions.length > 0) {
        setSuggestions(response.data.suggestions);
      }
    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message
      const errorMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again later.',
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion: ChatSuggestion) => {
    setInput(suggestion.text);
    inputRef.current?.focus();
  };

  // Toggle chat minimized state
  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  // Close chat
  const handleClose = () => {
    setIsOpen(false);
    if (onClose) onClose();
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // If chat is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div
      className={`fixed bottom-4 right-4 bg-white rounded-lg shadow-lg overflow-hidden transition-all duration-300 ${
        isMinimized ? 'w-72 h-16' : 'w-96 h-[500px]'
      } ${className}`}
    >
      {/* Chat header */}
      <div className="bg-primary text-white p-3 flex justify-between items-center">
        <h3 className="font-semibold">RentUp Assistant</h3>
        <div className="flex space-x-2">
          <button
            onClick={toggleMinimize}
            className="text-white hover:text-gray-200 focus:outline-none"
          >
            {isMinimized ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            )}
          </button>
          <button
            onClick={handleClose}
            className="text-white hover:text-gray-200 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>

      {/* Chat body - only show when not minimized */}
      {!isMinimized && (
        <>
          {/* Messages */}
          <div className="p-3 h-[350px] overflow-y-auto">
            {messages.map(message => (
              <div
                key={message.id}
                className={`mb-3 ${message.role === 'user' ? 'text-right' : ''}`}
              >
                <div
                  className={`inline-block max-w-[80%] p-3 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-primary text-white rounded-br-none'
                      : 'bg-gray-100 text-gray-800 rounded-bl-none'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {formatTimestamp(message.timestamp)}
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start mb-3">
                <div className="bg-gray-100 p-3 rounded-lg rounded-bl-none">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-100"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-200"></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Suggestions */}
          {suggestions.length > 0 && (
            <div className="px-3 py-2 border-t border-gray-200">
              <div className="flex flex-wrap gap-2">
                {suggestions.map(suggestion => (
                  <button
                    key={suggestion.id}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-800 px-3 py-1 rounded-full transition-colors"
                  >
                    {suggestion.text}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Message input */}
          <form onSubmit={handleSendMessage} className="p-3 border-t border-gray-200">
            <div className="flex">
              <textarea
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Type your message..."
                className="flex-1 border border-gray-300 rounded-l-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                rows={1}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
              />
              <button
                type="submit"
                disabled={isLoading || !input.trim()}
                className="bg-primary text-white px-4 py-2 rounded-r-lg hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </form>
        </>
      )}
    </div>
  );
};

export default ChatbotInterface;
