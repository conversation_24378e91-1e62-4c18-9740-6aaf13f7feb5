import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { availableLanguages, changeLanguage, getCurrentLanguage } from '../../i18n/i18n';
import usePerformance from '../../hooks/usePerformance';

/**
 * LanguageSelector Component
 * 
 * A dropdown component that allows users to select their preferred language.
 * Displays language name and flag emoji.
 */
const LanguageSelector: React.FC = () => {
  const { t } = useTranslation();
  const { measureEventHandler } = usePerformance('LanguageSelector');
  
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const currentLanguage = getCurrentLanguage();
  
  // Find the current language object
  const currentLangObj = availableLanguages.find(lang => lang.code === currentLanguage) || availableLanguages[0];
  
  // Handle language change
  const handleLanguageChange = measureEventHandler('languageChange')((langCode: string) => {
    changeLanguage(langCode);
    setIsOpen(false);
  });
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Toggle dropdown
  const toggleDropdown = measureEventHandler('toggleDropdown')(() => {
    setIsOpen(!isOpen);
  });
  
  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary focus:outline-none"
        onClick={toggleDropdown}
        aria-expanded={isOpen}
        aria-haspopup="true"
        aria-label={t('common.language')}
      >
        <span className="text-lg" aria-hidden="true">{currentLangObj.flag}</span>
        <span className="hidden md:inline-block">{currentLangObj.name}</span>
        <svg
          className={`h-5 w-5 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>
      
      {/* Dropdown menu */}
      {isOpen && (
        <div
          className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="language-menu"
        >
          <div className="py-1" role="none">
            {availableLanguages.map((language) => (
              <button
                key={language.code}
                className={`w-full text-left px-4 py-2 text-sm ${
                  language.code === currentLanguage
                    ? 'bg-gray-100 text-primary font-medium'
                    : 'text-gray-700 hover:bg-gray-50'
                } flex items-center space-x-2`}
                role="menuitem"
                onClick={() => handleLanguageChange(language.code)}
              >
                <span className="text-lg" aria-hidden="true">{language.flag}</span>
                <span>{language.name}</span>
                {language.code === currentLanguage && (
                  <svg
                    className="ml-auto h-5 w-5 text-primary"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
