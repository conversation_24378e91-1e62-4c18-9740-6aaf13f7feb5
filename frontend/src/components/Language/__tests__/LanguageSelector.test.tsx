import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import LanguageSelector from '../LanguageSelector';
import * as i18n from '../../../i18n/i18n';

// Mock the i18n module
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    i18n: {
      changeLanguage: jest.fn(),
      language: 'en'
    }
  })
}));

// Mock the i18n functions
jest.mock('../../../i18n/i18n', () => ({
  availableLanguages: [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'es', name: 'Espa<PERSON><PERSON>', flag: '🇪🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' }
  ],
  changeLanguage: jest.fn(),
  getCurrentLanguage: jest.fn().mockReturnValue('en'),
  isRTL: jest.fn().mockReturnValue(false)
}));

// Mock the usePerformance hook
jest.mock('../../../hooks/usePerformance', () => () => ({
  measureEventHandler: (name: string) => (fn: any) => fn,
  measureFunction: (name: string) => (fn: any) => fn,
  measureApiCall: (name: string) => (fn: any) => fn
}));

describe('LanguageSelector Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  it('renders the language selector with current language', () => {
    render(<LanguageSelector />);
    
    // Check that the current language is displayed
    expect(screen.getByText('English')).toBeInTheDocument();
    expect(screen.getByText('🇺🇸')).toBeInTheDocument();
  });
  
  it('opens the dropdown when clicked', () => {
    render(<LanguageSelector />);
    
    // Initially, the dropdown should be closed
    expect(screen.queryByText('Español')).not.toBeInTheDocument();
    
    // Click the language selector button
    fireEvent.click(screen.getByRole('button'));
    
    // Now the dropdown should be open
    expect(screen.getByText('Español')).toBeInTheDocument();
    expect(screen.getByText('Français')).toBeInTheDocument();
  });
  
  it('changes the language when a language option is clicked', () => {
    render(<LanguageSelector />);
    
    // Open the dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Click on the Spanish option
    fireEvent.click(screen.getByText('Español'));
    
    // Check that changeLanguage was called with the correct language code
    expect(i18n.changeLanguage).toHaveBeenCalledWith('es');
    
    // The dropdown should be closed after selection
    expect(screen.queryByText('Español')).not.toBeInTheDocument();
  });
  
  it('closes the dropdown when clicking outside', () => {
    render(
      <div>
        <LanguageSelector />
        <div data-testid="outside">Outside</div>
      </div>
    );
    
    // Open the dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Check that the dropdown is open
    expect(screen.getByText('Español')).toBeInTheDocument();
    
    // Click outside the dropdown
    fireEvent.mouseDown(screen.getByTestId('outside'));
    
    // The dropdown should be closed
    expect(screen.queryByText('Español')).not.toBeInTheDocument();
  });
  
  it('shows the current language as selected in the dropdown', () => {
    render(<LanguageSelector />);
    
    // Open the dropdown
    fireEvent.click(screen.getByRole('button'));
    
    // Check that the current language has the selected class
    const englishOption = screen.getByText('English').closest('button');
    expect(englishOption).toHaveClass('bg-gray-100');
    
    // Other languages should not have the selected class
    const spanishOption = screen.getByText('Español').closest('button');
    expect(spanishOption).not.toHaveClass('bg-gray-100');
  });
  
  it('renders only the flag on mobile', () => {
    // Mock window.innerWidth to simulate mobile view
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500
    });
    
    render(<LanguageSelector />);
    
    // The flag should be visible
    expect(screen.getByText('🇺🇸')).toBeInTheDocument();
    
    // The language name should be hidden on mobile
    const languageName = screen.getByText('English');
    expect(languageName).toHaveClass('hidden');
  });
});
