import React from 'react';
import {
  Box,
  Button,
  CircularProgress
} from '@mui/material';
import {
  Assessment,
  Category,
  Flag
} from '@mui/icons-material';

/**
 * BatchProcessingControls Component
 * 
 * Provides controls for batch processing operations including
 * quality assessment, categorization, and content moderation.
 */
interface BatchProcessingControlsProps {
  batchProcessing: boolean;
  onBatchProcess: (type: 'quality' | 'categorize' | 'moderate') => void;
}

export const BatchProcessingControls: React.FC<BatchProcessingControlsProps> = ({
  batchProcessing,
  onBatchProcess
}) => {
  return (
    <Box sx={{ mb: 3, display: 'flex', gap: 2, alignItems: 'center' }}>
      {/* Quality Assessment Button */}
      <Button 
        variant="outlined" 
        startIcon={<Assessment />}
        onClick={() => onBatchProcess('quality')}
        disabled={batchProcessing}
        size="medium"
      >
        Batch Quality Assessment
      </Button>
      
      {/* Categorization Button */}
      <Button 
        variant="outlined" 
        startIcon={<Category />}
        onClick={() => onBatchProcess('categorize')}
        disabled={batchProcessing}
        size="medium"
      >
        Batch Categorization
      </Button>
      
      {/* Moderation Button */}
      <Button 
        variant="outlined" 
        startIcon={<Flag />}
        onClick={() => onBatchProcess('moderate')}
        disabled={batchProcessing}
        size="medium"
      >
        Batch Moderation
      </Button>
      
      {/* Loading Indicator */}
      {batchProcessing && (
        <CircularProgress size={24} sx={{ ml: 2 }} />
      )}
    </Box>
  );
};
