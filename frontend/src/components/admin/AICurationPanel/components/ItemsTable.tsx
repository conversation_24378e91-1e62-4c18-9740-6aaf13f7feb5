import React, { useState } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Chip,
  Tabs,
  Tab,
  CircularProgress
} from '@mui/material';
import {
  Assessment,
  Category,
  Flag
} from '@mui/icons-material';
import { CurationItem } from '../utils/types';

/**
 * ItemsTable Component
 * 
 * Displays items in a table format with tabs for different views
 * and action buttons for quality, category, and moderation operations.
 */
interface ItemsTableProps {
  items: CurationItem[];
  loading: boolean;
  onOpenDialog: (item: CurationItem, type: 'quality' | 'category' | 'moderation') => void;
}

export const ItemsTable: React.FC<ItemsTableProps> = ({
  items,
  loading,
  onOpenDialog
}) => {
  const [activeTab, setActiveTab] = useState(0);
  
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  // Filter items based on active tab
  const getFilteredItems = () => {
    switch (activeTab) {
      case 1: // Needs Review
        return items.filter(item => item.status === 'pending');
      case 2: // Low Quality (mock filter)
        return items.filter(item => Math.random() > 0.7); // Mock low quality filter
      default: // All Items
        return items;
    }
  };
  
  const filteredItems = getFilteredItems();
  
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  return (
    <Box>
      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="All Items" />
          <Tab label="Needs Review" />
          <Tab label="Low Quality" />
        </Tabs>
      </Box>
      
      {/* Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>Title</TableCell>
              <TableCell>Category</TableCell>
              <TableCell>Status</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredItems.length > 0 ? (
              filteredItems.map((item) => (
                <TableRow key={item.id} hover>
                  <TableCell>{item.id}</TableCell>
                  <TableCell>
                    <Box sx={{ fontWeight: 'medium' }}>
                      {item.title}
                    </Box>
                  </TableCell>
                  <TableCell>{item.category}</TableCell>
                  <TableCell>
                    <Chip 
                      label={item.status} 
                      color={item.status === 'active' ? 'success' : 'warning'}
                      size="small"
                      sx={{ textTransform: 'capitalize' }}
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                      <Button 
                        size="small" 
                        startIcon={<Assessment />}
                        onClick={() => onOpenDialog(item, 'quality')}
                        variant="outlined"
                        color="primary"
                      >
                        Quality
                      </Button>
                      <Button 
                        size="small" 
                        startIcon={<Category />}
                        onClick={() => onOpenDialog(item, 'category')}
                        variant="outlined"
                        color="secondary"
                      >
                        Category
                      </Button>
                      <Button 
                        size="small" 
                        startIcon={<Flag />}
                        onClick={() => onOpenDialog(item, 'moderation')}
                        variant="outlined"
                        color="warning"
                      >
                        Moderate
                      </Button>
                    </Box>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} align="center" sx={{ py: 4 }}>
                  No items found matching the current filters.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};
