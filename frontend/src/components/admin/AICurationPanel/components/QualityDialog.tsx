import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  LinearProgress,
  Grid,
  CircularProgress
} from '@mui/material';
import { CurationItem, QualityData } from '../utils/types';

/**
 * QualityDialog Component
 * 
 * Displays quality assessment results for an item including
 * overall quality score, detailed metrics, and improvement suggestions.
 */
interface QualityDialogProps {
  open: boolean;
  item: CurationItem | null;
  data: QualityData | null;
  loading: boolean;
  onClose: () => void;
}

export const QualityDialog: React.FC<QualityDialogProps> = ({
  open,
  item,
  data,
  loading,
  onClose
}) => {
  const renderContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }
    
    if (!data) {
      return (
        <Typography>No quality data available.</Typography>
      );
    }
    
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Quality Assessment
        </Typography>
        
        {/* Overall Quality Score */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Overall Quality: {data.quality_score.toFixed(2)} ({data.quality_level})
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={data.quality_score * 100} 
            color={
              data.quality_level === 'high' ? 'success' : 
              data.quality_level === 'medium' ? 'warning' : 'error'
            }
            sx={{ height: 10, borderRadius: 5, mt: 1 }}
          />
        </Box>
        
        {/* Quality Metrics */}
        <Typography variant="subtitle1" gutterBottom>
          Quality Metrics
        </Typography>
        
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={4}>
            <Typography variant="body2" gutterBottom>
              Completeness
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={data.metrics.completeness * 100} 
              sx={{ height: 8, borderRadius: 5, mt: 1 }}
            />
            <Typography variant="body2" align="right" sx={{ mt: 0.5 }}>
              {(data.metrics.completeness * 100).toFixed(0)}%
            </Typography>
          </Grid>
          
          <Grid item xs={4}>
            <Typography variant="body2" gutterBottom>
              Clarity
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={data.metrics.clarity * 100} 
              sx={{ height: 8, borderRadius: 5, mt: 1 }}
            />
            <Typography variant="body2" align="right" sx={{ mt: 0.5 }}>
              {(data.metrics.clarity * 100).toFixed(0)}%
            </Typography>
          </Grid>
          
          <Grid item xs={4}>
            <Typography variant="body2" gutterBottom>
              Relevance
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={data.metrics.relevance * 100} 
              sx={{ height: 8, borderRadius: 5, mt: 1 }}
            />
            <Typography variant="body2" align="right" sx={{ mt: 0.5 }}>
              {(data.metrics.relevance * 100).toFixed(0)}%
            </Typography>
          </Grid>
        </Grid>
        
        {/* Improvement Suggestions */}
        <Typography variant="subtitle1" gutterBottom>
          Improvement Suggestions
        </Typography>
        
        {data.suggestions.length > 0 ? (
          <Box component="ul" sx={{ pl: 2, mt: 1 }}>
            {data.suggestions.map((suggestion, index) => (
              <Box component="li" key={index} sx={{ mb: 1 }}>
                <Typography variant="body2">{suggestion}</Typography>
              </Box>
            ))}
          </Box>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No suggestions available. This item meets quality standards.
          </Typography>
        )}
      </Box>
    );
  };
  
  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {item && `Quality Assessment - ${item.title}`}
      </DialogTitle>
      <DialogContent dividers>
        {renderContent()}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};
