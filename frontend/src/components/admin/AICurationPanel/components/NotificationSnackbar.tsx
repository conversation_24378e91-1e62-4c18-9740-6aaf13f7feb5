import React from 'react';
import { Snackbar, Alert } from '@mui/material';

/**
 * NotificationSnackbar Component
 * 
 * Displays notification messages using Material-UI Snackbar.
 * Supports different severity levels (success, error, info, warning).
 */
interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

interface NotificationSnackbarProps {
  snackbar: SnackbarState;
  onClose: () => void;
}

export const NotificationSnackbar: React.FC<NotificationSnackbarProps> = ({
  snackbar,
  onClose
}) => {
  return (
    <Snackbar
      open={snackbar.open}
      autoHideDuration={6000}
      onClose={onClose}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
    >
      <Alert 
        onClose={onClose} 
        severity={snackbar.severity}
        variant="filled"
        sx={{ width: '100%' }}
      >
        {snackbar.message}
      </Alert>
    </Snackbar>
  );
};
