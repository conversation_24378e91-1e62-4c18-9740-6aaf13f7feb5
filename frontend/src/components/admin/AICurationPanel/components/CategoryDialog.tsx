import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  CircularProgress
} from '@mui/material';
import { CurationItem, CategoryData } from '../utils/types';

/**
 * CategoryDialog Component
 * 
 * Displays category suggestions for an item with confidence scores
 * and allows applying suggested categories.
 */
interface CategoryDialogProps {
  open: boolean;
  item: CurationItem | null;
  data: CategoryData | null;
  loading: boolean;
  onClose: () => void;
}

export const CategoryDialog: React.FC<CategoryDialogProps> = ({
  open,
  item,
  data,
  loading,
  onClose
}) => {
  const handleApplyCategory = (categoryId: number, categoryName: string) => {
    // In a real implementation, this would update the item's category
    console.log(`Applying category ${categoryName} (ID: ${categoryId}) to item ${item?.id}`);
    
    // Show success feedback
    // This would typically be handled by a parent component or hook
  };
  
  const renderContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }
    
    if (!data || !data.categories.length) {
      return (
        <Typography>No category suggestions available.</Typography>
      );
    }
    
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Category Suggestions
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          AI-generated category suggestions based on item content analysis.
          Higher confidence scores indicate better matches.
        </Typography>
        
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Category</TableCell>
                <TableCell align="right">Confidence</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {data.categories.map((category) => (
                <TableRow key={category.category_id} hover>
                  <TableCell>
                    <Typography variant="body2" fontWeight="medium">
                      {category.category_name}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Box sx={{ minWidth: 120 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="body2">
                          {(category.confidence * 100).toFixed(0)}%
                        </Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={category.confidence * 100} 
                        color={
                          category.confidence > 0.8 ? 'success' :
                          category.confidence > 0.6 ? 'primary' :
                          category.confidence > 0.4 ? 'warning' : 'error'
                        }
                        sx={{ height: 6, borderRadius: 3 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    <Button 
                      size="small" 
                      variant="outlined"
                      color="primary"
                      onClick={() => handleApplyCategory(category.category_id, category.category_name)}
                      disabled={category.confidence < 0.3} // Disable low confidence suggestions
                    >
                      Apply
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        {data.categories.length > 0 && (
          <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
            Tip: Categories with confidence below 30% are disabled to prevent misclassification.
          </Typography>
        )}
      </Box>
    );
  };
  
  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {item && `Category Suggestions - ${item.title}`}
      </DialogTitle>
      <DialogContent dividers>
        {renderContent()}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};
