import React from 'react';
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import { Search } from '@mui/icons-material';

/**
 * FilterControls Component
 * 
 * Provides search and filtering controls for the AI curation panel.
 * Includes search by title, category filter, and status filter.
 */
interface FilterControlsProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filterCategory: string;
  setFilterCategory: (category: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
}

export const FilterControls: React.FC<FilterControlsProps> = ({
  searchQuery,
  setSearchQuery,
  filterCategory,
  setFilterCategory,
  filterStatus,
  setFilterStatus
}) => {
  const categories = [
    'All Categories',
    'Sports & Recreation',
    'Electronics & Technology',
    'Real Estate & Spaces',
    'Tools & Equipment',
    'Fashion & Accessories'
  ];
  
  const statuses = [
    'All Status',
    'Active',
    'Pending'
  ];
  
  return (
    <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
      {/* Search Input */}
      <TextField
        label="Search Items"
        variant="outlined"
        size="small"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        sx={{ flexGrow: 1 }}
        InputProps={{
          startAdornment: <Search sx={{ color: 'action.active', mr: 1 }} />,
        }}
        placeholder="Search by item title..."
      />
      
      {/* Category Filter */}
      <FormControl size="small" sx={{ minWidth: 150 }}>
        <InputLabel>Category</InputLabel>
        <Select
          value={filterCategory}
          label="Category"
          onChange={(e) => setFilterCategory(e.target.value)}
        >
          {categories.map((category) => (
            <MenuItem 
              key={category} 
              value={category === 'All Categories' ? 'all' : category}
            >
              {category}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      
      {/* Status Filter */}
      <FormControl size="small" sx={{ minWidth: 120 }}>
        <InputLabel>Status</InputLabel>
        <Select
          value={filterStatus}
          label="Status"
          onChange={(e) => setFilterStatus(e.target.value)}
        >
          {statuses.map((status) => (
            <MenuItem 
              key={status} 
              value={status === 'All Status' ? 'all' : status.toLowerCase()}
            >
              {status}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Box>
  );
};
