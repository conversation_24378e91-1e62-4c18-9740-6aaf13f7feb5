import React from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  Grid,
  LinearProgress,
  CircularProgress
} from '@mui/material';
import { CurationItem, ModerationData } from '../utils/types';

/**
 * ModerationDialog Component
 * 
 * Displays content moderation results for an item including
 * moderation scores for different categories and approval/rejection actions.
 */
interface ModerationDialogProps {
  open: boolean;
  item: CurationItem | null;
  data: ModerationData | null;
  loading: boolean;
  onClose: () => void;
}

export const ModerationDialog: React.FC<ModerationDialogProps> = ({
  open,
  item,
  data,
  loading,
  onClose
}) => {
  const handleApproveContent = () => {
    // In a real implementation, this would approve the content
    console.log(`Approving content for item ${item?.id}`);
    onClose();
  };
  
  const handleRejectContent = () => {
    // In a real implementation, this would reject the content
    console.log(`Rejecting content for item ${item?.id}`);
    onClose();
  };
  
  const getModerationColor = (score: number) => {
    if (score > 0.7) return 'error';
    if (score > 0.4) return 'warning';
    return 'success';
  };
  
  const renderContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }
    
    if (!data) {
      return (
        <Typography>No moderation data available.</Typography>
      );
    }
    
    return (
      <Box>
        <Typography variant="h6" gutterBottom>
          Content Moderation
        </Typography>
        
        {/* Moderation Status Alert */}
        <Box sx={{ mb: 3 }}>
          <Alert 
            severity={data.needs_review ? "warning" : "success"}
            sx={{ mb: 2 }}
          >
            {data.needs_review 
              ? `This item needs review. Primary flag: ${data.primary_flag}` 
              : "This item passed automatic moderation checks."}
          </Alert>
        </Box>
        
        {/* Moderation Scores */}
        <Typography variant="subtitle1" gutterBottom>
          Moderation Scores
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Higher scores indicate higher risk in each category.
        </Typography>
        
        <Grid container spacing={2}>
          {Object.entries(data.moderation_scores).map(([category, score]) => (
            <Grid item xs={6} key={category}>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                  <Typography 
                    variant="body2" 
                    sx={{ textTransform: 'capitalize', fontWeight: 'medium' }}
                  >
                    {category}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {(score * 100).toFixed(0)}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={score * 100} 
                  color={getModerationColor(score)}
                  sx={{ height: 8, borderRadius: 4 }}
                />
                <Typography 
                  variant="caption" 
                  color={
                    score > 0.7 ? 'error.main' :
                    score > 0.4 ? 'warning.main' : 'success.main'
                  }
                  sx={{ mt: 0.5, display: 'block' }}
                >
                  {score > 0.7 ? 'High Risk' :
                   score > 0.4 ? 'Medium Risk' : 'Low Risk'}
                </Typography>
              </Box>
            </Grid>
          ))}
        </Grid>
        
        {/* Action Buttons for Items Needing Review */}
        {data.needs_review && (
          <Box sx={{ mt: 4, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="subtitle2" gutterBottom>
              Moderation Actions
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              This item requires manual review. Choose an action below:
            </Typography>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button 
                variant="contained" 
                color="success"
                onClick={handleApproveContent}
                size="small"
              >
                Approve Content
              </Button>
              <Button 
                variant="contained" 
                color="error"
                onClick={handleRejectContent}
                size="small"
              >
                Reject Content
              </Button>
            </Box>
          </Box>
        )}
      </Box>
    );
  };
  
  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        {item && `Content Moderation - ${item.title}`}
      </DialogTitle>
      <DialogContent dividers>
        {renderContent()}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};
