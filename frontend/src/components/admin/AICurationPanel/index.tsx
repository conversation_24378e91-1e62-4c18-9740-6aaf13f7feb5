import React from 'react';
import { Box, Typography, Alert } from '@mui/material';
import { FilterControls } from './components/FilterControls';
import { BatchProcessingControls } from './components/BatchProcessingControls';
import { ItemsTable } from './components/ItemsTable';
import { QualityDialog } from './components/QualityDialog';
import { CategoryDialog } from './components/CategoryDialog';
import { ModerationDialog } from './components/ModerationDialog';
import { NotificationSnackbar } from './components/NotificationSnackbar';
import { useCurationData } from './hooks/useCurationData';
import { useFilterControls } from './hooks/useFilterControls';
import { useBatchProcessing } from './hooks/useBatchProcessing';
import { useDialogManagement } from './hooks/useDialogManagement';
import { useNotifications } from './hooks/useNotifications';

/**
 * AICurationPanel Component
 * 
 * Main component for AI curation management in the admin dashboard.
 * Provides quality assessment, categorization, and content moderation tools.
 */
const AICurationPanel: React.FC = () => {
  // Custom hooks for data and state management
  const { items, loading: itemsLoading, error: itemsError } = useCurationData();
  
  const {
    searchQuery,
    setSearchQuery,
    filterCategory,
    setFilterCategory,
    filterStatus,
    setFilterStatus,
    filteredItems
  } = useFilterControls(items);
  
  const {
    batchProcessing,
    batchResults,
    setBatchResults,
    handleBatchProcess
  } = useBatchProcessing();
  
  const {
    dialogOpen,
    dialogType,
    selectedItem,
    qualityData,
    categoryData,
    moderationData,
    loading: dialogLoading,
    handleOpenDialog,
    handleCloseDialog
  } = useDialogManagement();
  
  const {
    snackbar,
    showNotification,
    handleCloseSnackbar
  } = useNotifications();
  
  // Show error state if items failed to load
  if (itemsError) {
    return (
      <Box>
        <Typography variant="h5" gutterBottom>
          AI Curation Management
        </Typography>
        <Alert severity="error" sx={{ mt: 2 }}>
          {itemsError}
        </Alert>
      </Box>
    );
  }
  
  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        AI Curation Management
      </Typography>
      
      {/* Filter Controls */}
      <FilterControls
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        filterCategory={filterCategory}
        setFilterCategory={setFilterCategory}
        filterStatus={filterStatus}
        setFilterStatus={setFilterStatus}
      />
      
      {/* Batch Processing Controls */}
      <BatchProcessingControls
        batchProcessing={batchProcessing}
        onBatchProcess={handleBatchProcess}
      />
      
      {/* Batch Results */}
      {batchResults && (
        <Alert 
          severity="info" 
          sx={{ mb: 3 }}
          onClose={() => setBatchResults(null)}
        >
          Processed {batchResults.processed} items for {batchResults.type}. 
          Success: {batchResults.success}, 
          Failed: {batchResults.processed - batchResults.success}
        </Alert>
      )}
      
      {/* Items Table */}
      <ItemsTable
        items={filteredItems}
        loading={itemsLoading}
        onOpenDialog={handleOpenDialog}
      />
      
      {/* Quality Assessment Dialog */}
      <QualityDialog
        open={dialogOpen && dialogType === 'quality'}
        item={selectedItem}
        data={qualityData}
        loading={dialogLoading}
        onClose={handleCloseDialog}
      />
      
      {/* Category Suggestion Dialog */}
      <CategoryDialog
        open={dialogOpen && dialogType === 'category'}
        item={selectedItem}
        data={categoryData}
        loading={dialogLoading}
        onClose={handleCloseDialog}
      />
      
      {/* Content Moderation Dialog */}
      <ModerationDialog
        open={dialogOpen && dialogType === 'moderation'}
        item={selectedItem}
        data={moderationData}
        loading={dialogLoading}
        onClose={handleCloseDialog}
      />
      
      {/* Notification Snackbar */}
      <NotificationSnackbar
        snackbar={snackbar}
        onClose={handleCloseSnackbar}
      />
    </Box>
  );
};

export default AICurationPanel;
