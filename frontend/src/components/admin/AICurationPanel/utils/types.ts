/**
 * TypeScript interfaces and types for the AICurationPanel component
 */

/**
 * Curation item interface
 */
export interface CurationItem {
  id: number;
  title: string;
  category: string;
  status: 'active' | 'pending' | 'rejected' | 'archived';
  description?: string;
  imageUrl?: string;
  createdAt?: string;
  updatedAt?: string;
  ownerId?: number;
  ownerName?: string;
}

/**
 * Quality assessment data interface
 */
export interface QualityData {
  item_id: number;
  quality_score: number;
  quality_level: 'high' | 'medium' | 'low';
  metrics: {
    completeness: number;
    clarity: number;
    relevance: number;
  };
  suggestions: string[];
  last_assessed?: string;
}

/**
 * Category suggestion interface
 */
export interface CategorySuggestion {
  category_id: number;
  category_name: string;
  confidence: number;
}

/**
 * Category data interface
 */
export interface CategoryData {
  item_id: number;
  categories: CategorySuggestion[];
  current_category?: string;
  last_categorized?: string;
}

/**
 * Moderation scores interface
 */
export interface ModerationScores {
  spam: number;
  offensive: number;
  misleading: number;
  prohibited: number;
  irrelevant: number;
}

/**
 * Moderation data interface
 */
export interface ModerationData {
  item_id: number;
  needs_review: boolean;
  moderation_scores: ModerationScores;
  primary_flag: string;
  last_moderated?: string;
  moderator_notes?: string;
}

/**
 * Batch processing results interface
 */
export interface BatchResults {
  type: 'quality' | 'categorize' | 'moderate';
  processed: number;
  success: number;
  failed: number;
  errors: string[];
  started_at?: string;
  completed_at?: string;
}

/**
 * Filter state interface
 */
export interface FilterState {
  searchQuery: string;
  category: string;
  status: string;
}

/**
 * Dialog state interface
 */
export interface DialogState {
  open: boolean;
  type: 'quality' | 'category' | 'moderation' | '';
  item: CurationItem | null;
  loading: boolean;
}

/**
 * Snackbar notification interface
 */
export interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

/**
 * API response wrapper interface
 */
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

/**
 * Pagination interface
 */
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

/**
 * Curation statistics interface
 */
export interface CurationStats {
  total_items: number;
  pending_review: number;
  low_quality: number;
  needs_categorization: number;
  flagged_content: number;
  processed_today: number;
}

/**
 * Component props interfaces
 */
export interface FilterControlsProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  filterCategory: string;
  setFilterCategory: (category: string) => void;
  filterStatus: string;
  setFilterStatus: (status: string) => void;
}

export interface BatchProcessingControlsProps {
  batchProcessing: boolean;
  onBatchProcess: (type: 'quality' | 'categorize' | 'moderate') => void;
}

export interface ItemsTableProps {
  items: CurationItem[];
  loading: boolean;
  onOpenDialog: (item: CurationItem, type: 'quality' | 'category' | 'moderation') => void;
}

export interface QualityDialogProps {
  open: boolean;
  item: CurationItem | null;
  data: QualityData | null;
  loading: boolean;
  onClose: () => void;
}

export interface CategoryDialogProps {
  open: boolean;
  item: CurationItem | null;
  data: CategoryData | null;
  loading: boolean;
  onClose: () => void;
}

export interface ModerationDialogProps {
  open: boolean;
  item: CurationItem | null;
  data: ModerationData | null;
  loading: boolean;
  onClose: () => void;
}

export interface NotificationSnackbarProps {
  snackbar: SnackbarState;
  onClose: () => void;
}
