/**
 * Constants for the AI Curation Panel
 */

/**
 * Available item categories
 */
export const ITEM_CATEGORIES = [
  'Sports & Recreation',
  'Electronics & Technology',
  'Real Estate & Spaces',
  'Tools & Equipment',
  'Fashion & Accessories',
  'Furniture & Home',
  'Automotive & Transportation',
  'Books & Media',
  'Health & Beauty',
  'Garden & Outdoor'
] as const;

/**
 * Item status options
 */
export const ITEM_STATUSES = [
  'active',
  'pending',
  'rejected',
  'archived'
] as const;

/**
 * Quality levels
 */
export const QUALITY_LEVELS = [
  'high',
  'medium',
  'low'
] as const;

/**
 * Moderation categories
 */
export const MODERATION_CATEGORIES = [
  'spam',
  'offensive',
  'misleading',
  'prohibited',
  'irrelevant'
] as const;

/**
 * Batch processing types
 */
export const BATCH_PROCESSING_TYPES = [
  'quality',
  'categorize',
  'moderate'
] as const;

/**
 * Quality score thresholds
 */
export const QUALITY_THRESHOLDS = {
  HIGH: 0.8,
  MEDIUM: 0.6,
  LOW: 0.4
} as const;

/**
 * Moderation score thresholds
 */
export const MODERATION_THRESHOLDS = {
  HIGH_RISK: 0.7,
  MEDIUM_RISK: 0.4,
  LOW_RISK: 0.2
} as const;

/**
 * Category confidence thresholds
 */
export const CONFIDENCE_THRESHOLDS = {
  HIGH: 0.8,
  MEDIUM: 0.6,
  LOW: 0.4,
  MINIMUM: 0.3
} as const;

/**
 * Default pagination settings
 */
export const PAGINATION_DEFAULTS = {
  PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100
} as const;

/**
 * API endpoints
 */
export const API_ENDPOINTS = {
  ITEMS: '/api/v1/curation/items',
  QUALITY: '/api/v1/curation/quality',
  CATEGORIZE: '/api/v1/curation/categorize',
  MODERATE: '/api/v1/curation/moderate',
  BATCH_QUALITY: '/api/v1/curation/batch/quality',
  BATCH_CATEGORIZE: '/api/v1/curation/batch/categorize',
  BATCH_MODERATE: '/api/v1/curation/batch/moderate'
} as const;

/**
 * Dialog types
 */
export const DIALOG_TYPES = [
  'quality',
  'category',
  'moderation'
] as const;

/**
 * Notification severities
 */
export const NOTIFICATION_SEVERITIES = [
  'success',
  'error',
  'info',
  'warning'
] as const;

/**
 * Table column definitions
 */
export const TABLE_COLUMNS = [
  { id: 'id', label: 'ID', minWidth: 70 },
  { id: 'title', label: 'Title', minWidth: 200 },
  { id: 'category', label: 'Category', minWidth: 150 },
  { id: 'status', label: 'Status', minWidth: 100 },
  { id: 'actions', label: 'Actions', minWidth: 200, align: 'center' as const }
] as const;

/**
 * Filter options
 */
export const FILTER_OPTIONS = {
  CATEGORIES: ['all', ...ITEM_CATEGORIES],
  STATUSES: ['all', ...ITEM_STATUSES]
} as const;

/**
 * Color mappings for different states
 */
export const COLOR_MAPPINGS = {
  QUALITY_LEVELS: {
    high: 'success',
    medium: 'warning',
    low: 'error'
  },
  ITEM_STATUSES: {
    active: 'success',
    pending: 'warning',
    rejected: 'error',
    archived: 'default'
  },
  CONFIDENCE_LEVELS: {
    high: 'success',
    medium: 'primary',
    low: 'warning',
    veryLow: 'error'
  }
} as const;

/**
 * Default mock data settings
 */
export const MOCK_DATA_SETTINGS = {
  ITEMS_COUNT: 8,
  QUALITY_SCORE_RANGE: [0.6, 1.0],
  CONFIDENCE_RANGE: [0.4, 1.0],
  MODERATION_SCORE_RANGE: [0.0, 0.8],
  PROCESSING_DELAY: 1000,
  BATCH_PROCESSING_DELAY: 2000
} as const;

/**
 * UI configuration
 */
export const UI_CONFIG = {
  SNACKBAR_AUTO_HIDE_DURATION: 6000,
  DIALOG_MAX_WIDTH: 'md' as const,
  TABLE_ROWS_PER_PAGE: 10,
  PROGRESS_BAR_HEIGHT: 8,
  LARGE_PROGRESS_BAR_HEIGHT: 10
} as const;
