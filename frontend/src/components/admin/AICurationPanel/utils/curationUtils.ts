import { CurationItem, QualityData, ModerationData, CategoryData } from './types';

/**
 * Utility functions for AI curation operations
 */

/**
 * Formats quality score for display
 */
export const formatQualityScore = (score: number): string => {
  return (score * 100).toFixed(0) + '%';
};

/**
 * Gets quality level color based on score
 */
export const getQualityLevelColor = (level: string): 'success' | 'warning' | 'error' => {
  switch (level) {
    case 'high':
      return 'success';
    case 'medium':
      return 'warning';
    case 'low':
      return 'error';
    default:
      return 'warning';
  }
};

/**
 * Gets moderation risk level based on score
 */
export const getModerationRiskLevel = (score: number): 'low' | 'medium' | 'high' => {
  if (score > 0.7) return 'high';
  if (score > 0.4) return 'medium';
  return 'low';
};

/**
 * Gets moderation risk color
 */
export const getModerationRiskColor = (score: number): 'success' | 'warning' | 'error' => {
  const risk = getModerationRiskLevel(score);
  switch (risk) {
    case 'low':
      return 'success';
    case 'medium':
      return 'warning';
    case 'high':
      return 'error';
  }
};

/**
 * Formats confidence score for display
 */
export const formatConfidenceScore = (confidence: number): string => {
  return (confidence * 100).toFixed(0) + '%';
};

/**
 * Gets confidence level color
 */
export const getConfidenceLevelColor = (confidence: number): 'success' | 'primary' | 'warning' | 'error' => {
  if (confidence > 0.8) return 'success';
  if (confidence > 0.6) return 'primary';
  if (confidence > 0.4) return 'warning';
  return 'error';
};

/**
 * Filters items by quality level
 */
export const filterItemsByQuality = (
  items: CurationItem[], 
  qualityData: Record<number, QualityData>,
  level: 'high' | 'medium' | 'low'
): CurationItem[] => {
  return items.filter(item => {
    const quality = qualityData[item.id];
    return quality && quality.quality_level === level;
  });
};

/**
 * Filters items that need review
 */
export const filterItemsNeedingReview = (
  items: CurationItem[],
  moderationData: Record<number, ModerationData>
): CurationItem[] => {
  return items.filter(item => {
    const moderation = moderationData[item.id];
    return moderation && moderation.needs_review;
  });
};

/**
 * Gets items with low confidence categories
 */
export const getItemsWithLowConfidenceCategories = (
  items: CurationItem[],
  categoryData: Record<number, CategoryData>,
  threshold: number = 0.6
): CurationItem[] => {
  return items.filter(item => {
    const categories = categoryData[item.id];
    if (!categories || !categories.categories.length) return false;
    
    const highestConfidence = Math.max(...categories.categories.map(c => c.confidence));
    return highestConfidence < threshold;
  });
};

/**
 * Calculates overall curation score for an item
 */
export const calculateCurationScore = (
  qualityData?: QualityData,
  moderationData?: ModerationData,
  categoryData?: CategoryData
): number => {
  let score = 0;
  let factors = 0;
  
  // Quality factor (40% weight)
  if (qualityData) {
    score += qualityData.quality_score * 0.4;
    factors += 0.4;
  }
  
  // Moderation factor (30% weight) - inverse of risk
  if (moderationData) {
    const maxModerationScore = Math.max(...Object.values(moderationData.moderation_scores));
    const moderationScore = 1 - maxModerationScore; // Inverse risk
    score += moderationScore * 0.3;
    factors += 0.3;
  }
  
  // Category confidence factor (30% weight)
  if (categoryData && categoryData.categories.length > 0) {
    const highestConfidence = Math.max(...categoryData.categories.map(c => c.confidence));
    score += highestConfidence * 0.3;
    factors += 0.3;
  }
  
  return factors > 0 ? score / factors : 0;
};

/**
 * Generates curation recommendations for an item
 */
export const generateCurationRecommendations = (
  item: CurationItem,
  qualityData?: QualityData,
  moderationData?: ModerationData,
  categoryData?: CategoryData
): string[] => {
  const recommendations: string[] = [];
  
  // Quality recommendations
  if (qualityData) {
    if (qualityData.quality_score < 0.7) {
      recommendations.push('Improve item quality by following the suggestions provided');
    }
    
    if (qualityData.metrics.completeness < 0.8) {
      recommendations.push('Add more complete item information');
    }
    
    if (qualityData.metrics.clarity < 0.7) {
      recommendations.push('Improve description clarity and readability');
    }
  }
  
  // Moderation recommendations
  if (moderationData && moderationData.needs_review) {
    recommendations.push(`Review content for ${moderationData.primary_flag} issues`);
  }
  
  // Category recommendations
  if (categoryData && categoryData.categories.length > 0) {
    const highestConfidence = Math.max(...categoryData.categories.map(c => c.confidence));
    if (highestConfidence < 0.6) {
      recommendations.push('Review and confirm item category assignment');
    }
  }
  
  return recommendations;
};

/**
 * Exports curation data to CSV format
 */
export const exportCurationDataToCSV = (
  items: CurationItem[],
  qualityData: Record<number, QualityData>,
  moderationData: Record<number, ModerationData>,
  categoryData: Record<number, CategoryData>
): string => {
  const headers = [
    'ID',
    'Title',
    'Category',
    'Status',
    'Quality Score',
    'Quality Level',
    'Needs Review',
    'Primary Flag',
    'Category Confidence'
  ];
  
  const rows = items.map(item => {
    const quality = qualityData[item.id];
    const moderation = moderationData[item.id];
    const category = categoryData[item.id];
    
    const highestCategoryConfidence = category && category.categories.length > 0
      ? Math.max(...category.categories.map(c => c.confidence))
      : 0;
    
    return [
      item.id,
      `"${item.title}"`,
      `"${item.category}"`,
      item.status,
      quality ? quality.quality_score.toFixed(2) : 'N/A',
      quality ? quality.quality_level : 'N/A',
      moderation ? (moderation.needs_review ? 'Yes' : 'No') : 'N/A',
      moderation ? moderation.primary_flag : 'N/A',
      highestCategoryConfidence.toFixed(2)
    ];
  });
  
  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
};
