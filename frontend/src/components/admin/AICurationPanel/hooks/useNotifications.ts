import { useState } from 'react';

/**
 * Snackbar state interface
 */
interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

/**
 * useNotifications Hook
 * 
 * Manages notification state and provides functions to show notifications
 * with different severity levels.
 */
export const useNotifications = () => {
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info'
  });
  
  // Show notification with specified message and severity
  const showNotification = (
    message: string, 
    severity: 'success' | 'error' | 'info' | 'warning' = 'info'
  ) => {
    setSnackbar({
      open: true,
      message,
      severity
    });
  };
  
  // Show success notification
  const showSuccess = (message: string) => {
    showNotification(message, 'success');
  };
  
  // Show error notification
  const showError = (message: string) => {
    showNotification(message, 'error');
  };
  
  // Show info notification
  const showInfo = (message: string) => {
    showNotification(message, 'info');
  };
  
  // Show warning notification
  const showWarning = (message: string) => {
    showNotification(message, 'warning');
  };
  
  // Close notification
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };
  
  return {
    snackbar,
    showNotification,
    showSuccess,
    showError,
    showInfo,
    showWarning,
    handleCloseSnackbar
  };
};
