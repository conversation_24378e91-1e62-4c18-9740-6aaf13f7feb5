import { useState, useMemo } from 'react';
import { CurationItem } from '../utils/types';

/**
 * useFilterControls Hook
 * 
 * Manages filter state and provides filtered items based on
 * search query, category, and status filters.
 */
export const useFilterControls = (items: CurationItem[]) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  
  // Filter items based on current filter state
  const filteredItems = useMemo(() => {
    return items.filter(item => {
      // Search filter - check title (case insensitive)
      const matchesSearch = searchQuery === '' || 
        item.title.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Category filter
      const matchesCategory = filterCategory === 'all' || 
        item.category === filterCategory;
      
      // Status filter
      const matchesStatus = filterStatus === 'all' || 
        item.status === filterStatus;
      
      return matchesSearch && matchesCategory && matchesStatus;
    });
  }, [items, searchQuery, filterCategory, filterStatus]);
  
  // Reset all filters
  const resetFilters = () => {
    setSearchQuery('');
    setFilterCategory('all');
    setFilterStatus('all');
  };
  
  // Get filter summary
  const getFilterSummary = () => {
    const activeFilters = [];
    
    if (searchQuery) {
      activeFilters.push(`Search: "${searchQuery}"`);
    }
    
    if (filterCategory !== 'all') {
      activeFilters.push(`Category: ${filterCategory}`);
    }
    
    if (filterStatus !== 'all') {
      activeFilters.push(`Status: ${filterStatus}`);
    }
    
    return {
      hasActiveFilters: activeFilters.length > 0,
      activeFilters,
      totalItems: items.length,
      filteredCount: filteredItems.length
    };
  };
  
  return {
    // Filter state
    searchQuery,
    setSearchQuery,
    filterCategory,
    setFilterCategory,
    filterStatus,
    setFilterStatus,
    
    // Filtered results
    filteredItems,
    
    // Utility functions
    resetFilters,
    getFilterSummary
  };
};
