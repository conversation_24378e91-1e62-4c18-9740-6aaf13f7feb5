import { useState, useEffect } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '../../../../config';
import { CurationItem } from '../utils/types';

/**
 * Mock data for development
 */
const MOCK_ITEMS: CurationItem[] = [
  { id: 1, title: 'Mountain Bike', category: 'Sports & Recreation', status: 'active' },
  { id: 2, title: 'Professional Camera', category: 'Electronics & Technology', status: 'pending' },
  { id: 3, title: 'Apartment in Downtown', category: 'Real Estate & Spaces', status: 'active' },
  { id: 4, title: 'Power Drill', category: 'Tools & Equipment', status: 'active' },
  { id: 5, title: 'Designer Dress', category: 'Fashion & Accessories', status: 'pending' },
  { id: 6, title: 'Gaming Laptop', category: 'Electronics & Technology', status: 'active' },
  { id: 7, title: 'Yoga Mat', category: 'Sports & Recreation', status: 'active' },
  { id: 8, title: 'Office Chair', category: 'Furniture & Home', status: 'pending' },
];

/**
 * useCurationData Hook
 * 
 * Manages fetching and state management for curation items.
 * Provides loading states and error handling.
 */
export const useCurationData = () => {
  const [items, setItems] = useState<CurationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch items from API
  const fetchItems = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would fetch from the API
      // const response = await axios.get(`${API_BASE_URL}/api/v1/curation/items`);
      // setItems(response.data);
      
      // Mock data for development
      setTimeout(() => {
        setItems(MOCK_ITEMS);
        setLoading(false);
      }, 1000);
    } catch (err) {
      console.error('Error fetching curation items:', err);
      setError('Failed to load items. Please try again later.');
      setLoading(false);
    }
  };
  
  // Fetch items on mount
  useEffect(() => {
    fetchItems();
  }, []);
  
  // Refresh items
  const refreshItems = () => {
    fetchItems();
  };
  
  // Update item status
  const updateItemStatus = (itemId: number, newStatus: string) => {
    setItems(prevItems => 
      prevItems.map(item => 
        item.id === itemId 
          ? { ...item, status: newStatus }
          : item
      )
    );
  };
  
  // Update item category
  const updateItemCategory = (itemId: number, newCategory: string) => {
    setItems(prevItems => 
      prevItems.map(item => 
        item.id === itemId 
          ? { ...item, category: newCategory }
          : item
      )
    );
  };
  
  return {
    items,
    loading,
    error,
    refreshItems,
    updateItemStatus,
    updateItemCategory
  };
};
