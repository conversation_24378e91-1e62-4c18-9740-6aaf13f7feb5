import { useState } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '../../../../config';
import { CurationItem, QualityData, CategoryData, ModerationData } from '../utils/types';

/**
 * useDialogManagement Hook
 * 
 * Manages dialog state and data fetching for quality, category, and moderation dialogs.
 * Handles opening/closing dialogs and fetching relevant data for each dialog type.
 */
export const useDialogManagement = () => {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState<'quality' | 'category' | 'moderation' | ''>('');
  const [selectedItem, setSelectedItem] = useState<CurationItem | null>(null);
  const [loading, setLoading] = useState(false);
  
  // Dialog data states
  const [qualityData, setQualityData] = useState<QualityData | null>(null);
  const [categoryData, setCategoryData] = useState<CategoryData | null>(null);
  const [moderationData, setModerationData] = useState<ModerationData | null>(null);
  
  // Open dialog and fetch data
  const handleOpenDialog = (item: CurationItem, type: 'quality' | 'category' | 'moderation') => {
    setSelectedItem(item);
    setDialogType(type);
    setDialogOpen(true);
    
    // Clear previous data
    setQualityData(null);
    setCategoryData(null);
    setModerationData(null);
    
    // Fetch data based on dialog type
    switch (type) {
      case 'quality':
        fetchQualityData(item.id);
        break;
      case 'category':
        fetchCategoryData(item.id);
        break;
      case 'moderation':
        fetchModerationData(item.id);
        break;
    }
  };
  
  // Close dialog and reset state
  const handleCloseDialog = () => {
    setDialogOpen(false);
    setSelectedItem(null);
    setDialogType('');
    setQualityData(null);
    setCategoryData(null);
    setModerationData(null);
  };
  
  // Fetch quality data for an item
  const fetchQualityData = async (itemId: number) => {
    setLoading(true);
    try {
      // In a real implementation, this would fetch from the API
      // const response = await axios.get(`${API_BASE_URL}/api/v1/curation/quality/${itemId}`);
      // setQualityData(response.data);
      
      // Mock data for development
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setQualityData({
        item_id: itemId,
        quality_score: Math.random() * 0.4 + 0.6, // Random score between 0.6-1.0
        quality_level: Math.random() > 0.5 ? 'high' : 'medium',
        metrics: {
          completeness: Math.random() * 0.3 + 0.7,
          clarity: Math.random() * 0.4 + 0.6,
          relevance: Math.random() * 0.3 + 0.7
        },
        suggestions: [
          'Add more detailed description',
          'Include more high-quality images',
          'Specify more features or specifications'
        ].slice(0, Math.floor(Math.random() * 3) + 1)
      });
    } catch (error) {
      console.error('Error fetching quality data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch category data for an item
  const fetchCategoryData = async (itemId: number) => {
    setLoading(true);
    try {
      // In a real implementation, this would fetch from the API
      // const response = await axios.get(`${API_BASE_URL}/api/v1/curation/categorize/${itemId}`);
      // setCategoryData(response.data);
      
      // Mock data for development
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const categories = [
        'Sports & Recreation',
        'Electronics & Technology',
        'Real Estate & Spaces',
        'Tools & Equipment',
        'Fashion & Accessories',
        'Furniture & Home'
      ];
      
      setCategoryData({
        item_id: itemId,
        categories: categories
          .sort(() => Math.random() - 0.5)
          .slice(0, 3)
          .map((name, index) => ({
            category_id: index + 1,
            category_name: name,
            confidence: Math.random() * 0.6 + 0.4 // Random confidence 0.4-1.0
          }))
          .sort((a, b) => b.confidence - a.confidence) // Sort by confidence desc
      });
    } catch (error) {
      console.error('Error fetching category data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch moderation data for an item
  const fetchModerationData = async (itemId: number) => {
    setLoading(true);
    try {
      // In a real implementation, this would fetch from the API
      // const response = await axios.get(`${API_BASE_URL}/api/v1/curation/moderate/${itemId}`);
      // setModerationData(response.data);
      
      // Mock data for development
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const needsReview = Math.random() > 0.7;
      const scores = {
        spam: Math.random() * 0.5,
        offensive: Math.random() * 0.3,
        misleading: Math.random() * 0.6,
        prohibited: Math.random() * 0.2,
        irrelevant: Math.random() * 0.4
      };
      
      setModerationData({
        item_id: itemId,
        needs_review: needsReview,
        moderation_scores: scores,
        primary_flag: Object.entries(scores).reduce((a, b) => scores[a[0]] > scores[b[0]] ? a : b)[0]
      });
    } catch (error) {
      console.error('Error fetching moderation data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return {
    dialogOpen,
    dialogType,
    selectedItem,
    loading,
    qualityData,
    categoryData,
    moderationData,
    handleOpenDialog,
    handleCloseDialog
  };
};
