// Types for the RentPlanner component

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

export interface RentalItem {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  imageUrl: string;
  selected: boolean;
}

export interface RentalPlan {
  id: string;
  name: string;
  description: string;
  items: RentalItem[];
  totalPrice: number;
  startDate?: string;
  endDate?: string;
}

export interface RentPlannerProps {
  userId?: string;
  onClose?: () => void;
  className?: string;
}

export type PlannerStep = 'chat' | 'items' | 'review' | 'complete';
