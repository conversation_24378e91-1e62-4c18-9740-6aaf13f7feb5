// Mock data generators for the RentPlanner component
import { RentalItem } from './types';
import { generateId } from './formatters';

// Sample categories
const categories = [
  'Furniture',
  'Electronics',
  'Appliances',
  'Tools',
  'Outdoor',
  'Sports',
  'Party',
];

// Sample items by category
const itemsByCategory: Record<string, string[]> = {
  Furniture: [
    'Sofa',
    'Dining Table',
    'Coffee Table',
    'Bed Frame',
    'Desk',
    'Bookshelf',
    'Armchair',
    'Dresser',
  ],
  Electronics: [
    'TV',
    'Laptop',
    'Projector',
    'Speaker System',
    'Gaming Console',
    'Camera',
    'Drone',
  ],
  Appliances: [
    'Refrigerator',
    'Washing Machine',
    'Microwave',
    'Blender',
    'Coffee Maker',
    'Vacuum Cleaner',
  ],
  Tools: [
    'Power Drill',
    'Circular Saw',
    'Ladder',
    'Pressure Washer',
    'Lawn Mower',
    'Chainsaw',
  ],
  Outdoor: [
    'Tent',
    'Grill',
    'Patio Furniture',
    'Hammock',
    'Canopy',
    'Fire Pit',
  ],
  Sports: [
    'Bicycle',
    'Kayak',
    'Snowboard',
    'Golf Clubs',
    'Tennis Racket',
    'Surfboard',
  ],
  Party: [
    'Karaoke Machine',
    'Bounce House',
    'Popcorn Machine',
    'Cotton Candy Maker',
    'Photo Booth',
    'Disco Ball',
  ],
};

// Sample descriptions
const descriptions = [
  'Perfect for any occasion',
  'High-quality and reliable',
  'Like new condition',
  'Recently serviced and maintained',
  'Top-rated by other renters',
  'Includes all accessories',
  'Easy to use and set up',
];

/**
 * Generate a random price between min and max
 * @param min - Minimum price
 * @param max - Maximum price
 * @returns A random price
 */
const randomPrice = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1) + min);
};

/**
 * Generate a mock rental item
 * @param category - Optional category for the item
 * @returns A mock rental item
 */
export const generateMockItem = (category?: string): RentalItem => {
  const itemCategory = category || categories[Math.floor(Math.random() * categories.length)];
  const itemNames = itemsByCategory[itemCategory];
  const itemName = itemNames[Math.floor(Math.random() * itemNames.length)];
  const description = descriptions[Math.floor(Math.random() * descriptions.length)];
  
  return {
    id: generateId(),
    name: itemName,
    description,
    category: itemCategory,
    price: randomPrice(10, 200),
    imageUrl: `https://source.unsplash.com/random/300x200/?${itemName.toLowerCase().replace(' ', '-')}`,
    selected: false,
  };
};

/**
 * Generate a list of mock rental items
 * @param count - Number of items to generate
 * @returns An array of mock rental items
 */
export const generateMockItems = (count: number): RentalItem[] => {
  return Array.from({ length: count }, () => generateMockItem());
};

/**
 * Generate a list of mock rental items based on a query
 * @param query - Search query
 * @param count - Number of items to generate
 * @returns An array of mock rental items
 */
export const generateMockItemsForQuery = (query: string, count: number): RentalItem[] => {
  // Find categories that match the query
  const matchingCategories = categories.filter(category => 
    category.toLowerCase().includes(query.toLowerCase())
  );
  
  if (matchingCategories.length > 0) {
    // Generate items from matching categories
    return Array.from({ length: count }, () => 
      generateMockItem(matchingCategories[Math.floor(Math.random() * matchingCategories.length)])
    );
  }
  
  // If no matching categories, generate random items
  return generateMockItems(count);
};
