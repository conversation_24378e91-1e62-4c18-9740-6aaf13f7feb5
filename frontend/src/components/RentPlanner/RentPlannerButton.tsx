import React, { useState } from 'react';
import RentPlannerInterface from './RentPlannerInterface';

interface RentPlannerButtonProps {
  userId?: string;
  className?: string;
}

const RentPlannerButton: React.FC<RentPlannerButtonProps> = ({ userId, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  // Toggle planner
  const togglePlanner = () => {
    setIsOpen(!isOpen);
  };
  
  return (
    <>
      {/* Floating action button */}
      <div className={`fixed bottom-20 right-4 z-50 ${className}`}>
        {!isOpen && (
          <button
            onClick={togglePlanner}
            className="bg-primary text-white p-3 rounded-full shadow-lg hover:bg-primary-dark transition-colors flex items-center justify-center"
            aria-label="Open Rent Planner"
            title="Plan your rentals"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
          </button>
        )}
      </div>
      
      {/* Rent Planner Modal */}
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75" onClick={() => setIsOpen(false)}></div>
            </div>
            
            {/* Modal panel */}
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-500 focus:outline-none"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              
              <RentPlannerInterface 
                userId={userId} 
                onClose={() => setIsOpen(false)}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default RentPlannerButton;
