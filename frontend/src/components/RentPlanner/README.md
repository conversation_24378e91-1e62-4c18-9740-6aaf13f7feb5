# RentUp Rent Planner

The RentUp Rent Planner is an AI-powered tool that helps users plan their rental needs for various events and occasions. It provides a conversational interface for users to describe their needs, then recommends relevant items and helps them create a comprehensive rental plan.

## Features

- **Conversational Planning**: Chat-based interface for describing rental needs
- **AI Recommendations**: Intelligent item recommendations based on the user's needs
- **Plan Creation**: Step-by-step process for creating a rental plan
- **Item Selection**: Visual interface for selecting recommended items
- **Plan Review**: Detailed review of the rental plan before completion
- **Checkout Integration**: Seamless transition to the checkout process

## Components

The Rent Planner consists of the following components:

1. **RentPlannerInterface**: The main component that handles the planning process
2. **RentPlannerButton**: A floating action button that opens the planner in a modal
3. **RentPlannerPage**: A dedicated page for the planner

## Usage

### Adding the Rent Planner Button

```tsx
import { RentPlannerButton } from '../components/RentPlanner';

// Add the button to your layout
const Layout = () => (
  <div>
    <Header />
    <main>{children}</main>
    <Footer />
    <RentPlannerButton userId={user?.id} />
  </div>
);
```

### Using the Rent Planner Page

```tsx
import { RentPlannerInterface } from '../components/RentPlanner';

const RentPlannerPage = () => (
  <div className="container mx-auto px-4 py-8">
    <h1 className="text-3xl font-bold mb-4">Rent Planner</h1>
    <RentPlannerInterface userId={user?.id} />
  </div>
);
```

## Planning Process

The Rent Planner guides users through a 4-step process:

1. **Chat**: Users describe their rental needs in a conversational interface
2. **Items**: Users select from recommended items and provide plan details
3. **Review**: Users review their rental plan before completion
4. **Complete**: Users receive confirmation and can proceed to checkout

### Chat Step

In the chat step, users describe what they're planning (e.g., wedding, camping trip, home renovation). The AI analyzes their needs and generates relevant item recommendations.

### Items Step

In the items step, users:
- Name their rental plan
- Add a description
- Set start and end dates
- Select items from the recommendations

### Review Step

In the review step, users see a summary of their plan, including:
- Plan name and description
- Start and end dates
- Selected items
- Total price

### Complete Step

In the complete step, users receive confirmation that their plan has been created and can:
- Proceed to checkout
- Save the plan for later
- Create another plan

## Props

### RentPlannerInterface

| Prop | Type | Description |
|------|------|-------------|
| userId | string (optional) | The ID of the current user |
| onClose | function (optional) | Callback function when the planner is closed |
| className | string (optional) | Additional CSS classes |

### RentPlannerButton

| Prop | Type | Description |
|------|------|-------------|
| userId | string (optional) | The ID of the current user |
| className | string (optional) | Additional CSS classes |

## Testing

Run the Rent Planner tests with:

```bash
npm test -- --testPathPattern=RentPlanner
```

## Future Improvements

- Add support for custom item requests
- Implement plan saving and loading
- Add integration with user preferences
- Implement more sophisticated recommendation algorithms
- Add support for location-based recommendations
- Implement plan sharing functionality
