import React, { useState, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

// Types
interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

interface RentalItem {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  imageUrl: string;
  selected: boolean;
}

interface RentalPlan {
  id: string;
  name: string;
  description: string;
  items: RentalItem[];
  totalPrice: number;
  startDate?: string;
  endDate?: string;
}

interface RentPlannerProps {
  userId?: string;
  onClose?: () => void;
  className?: string;
}

const RentPlannerInterface: React.FC<RentPlannerProps> = ({
  userId,
  onClose,
  className = ''
}) => {
  // State
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState('');
  const [rentalPlan, setRentalPlan] = useState<RentalPlan | null>(null);
  const [suggestedItems, setSuggestedItems] = useState<RentalItem[]>([]);
  const [planName, setPlanName] = useState('');
  const [planDescription, setPlanDescription] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [step, setStep] = useState<'chat' | 'items' | 'review' | 'complete'>('chat');

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  // Initialize chat session
  useEffect(() => {
    const newSessionId = uuidv4();
    setSessionId(newSessionId);

    // Add initial assistant message
    const initialAssistantMessage: ChatMessage = {
      id: uuidv4(),
      role: 'assistant',
      content: "Hi there! I'm your RentUp Planner. I can help you plan your rental needs for any event or occasion. What are you planning for? (e.g., wedding, camping trip, house renovation, etc.)",
      timestamp: new Date().toISOString()
    };

    setMessages([initialAssistantMessage]);
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Handle sending a message
  const handleSendMessage = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (!input.trim()) return;

    // Add user message to chat
    const userMessage: ChatMessage = {
      id: uuidv4(),
      role: 'user',
      content: input,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    try {
      // In a real implementation, this would call the AI recommendation agent
      // For now, we'll simulate a response

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Generate a response based on the user's message
      let responseContent = '';
      let mockItems: RentalItem[] = [];

      // Check if the message contains keywords related to events
      const message = input.toLowerCase();

      if (message.includes('wedding')) {
        responseContent = "Great! Planning a wedding is exciting. Based on your needs, I recommend the following items for your wedding:";
        mockItems = generateWeddingItems();
      } else if (message.includes('camping') || message.includes('outdoor')) {
        responseContent = "Perfect! For your camping trip, here are some essential items you might want to consider:";
        mockItems = generateCampingItems();
      } else if (message.includes('party') || message.includes('birthday')) {
        responseContent = "Sounds fun! For your party, I recommend the following items:";
        mockItems = generatePartyItems();
      } else if (message.includes('renovation') || message.includes('diy') || message.includes('home')) {
        responseContent = "For your home renovation project, these tools and equipment would be helpful:";
        mockItems = generateRenovationItems();
      } else {
        responseContent = "Based on what you've told me, here are some items that might be useful for your needs:";
        mockItems = generateGeneralItems();
      }

      // Add assistant response to chat
      const assistantMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: responseContent,
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Set suggested items
      setSuggestedItems(mockItems);

      // Move to items step
      setStep('items');

    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message
      const errorMessage: ChatMessage = {
        id: uuidv4(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again later.',
        timestamp: new Date().toISOString()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle item selection
  const toggleItemSelection = (itemId: string) => {
    setSuggestedItems(prev =>
      prev.map(item =>
        item.id === itemId
          ? { ...item, selected: !item.selected }
          : item
      )
    );
  };

  // Calculate total price
  const calculateTotalPrice = () => {
    return suggestedItems
      .filter(item => item.selected)
      .reduce((total, item) => total + item.price, 0);
  };

  // Move to review step
  const handleReviewPlan = () => {
    // Create rental plan
    const selectedItems = suggestedItems.filter(item => item.selected);

    if (selectedItems.length === 0) {
      alert('Please select at least one item for your rental plan.');
      return;
    }

    const newPlan: RentalPlan = {
      id: uuidv4(),
      name: planName || 'My Rental Plan',
      description: planDescription || 'Custom rental plan',
      items: selectedItems,
      totalPrice: calculateTotalPrice(),
      startDate: startDate || undefined,
      endDate: endDate || undefined
    };

    setRentalPlan(newPlan);
    setStep('review');
  };

  // Complete the plan
  const handleCompletePlan = () => {
    // In a real implementation, this would save the plan to the database
    setStep('complete');
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  // Generate mock items for different categories
  const generateWeddingItems = (): RentalItem[] => {
    return [
      {
        id: uuidv4(),
        name: 'Wedding Venue',
        description: 'Elegant venue for your ceremony and reception',
        category: 'Venue',
        price: 2500,
        imageUrl: 'https://images.unsplash.com/photo-1519225421980-715cb0215aed',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Catering Service',
        description: 'Full-service catering for up to 100 guests',
        category: 'Food',
        price: 3500,
        imageUrl: 'https://images.unsplash.com/photo-1555244162-803834f70033',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Professional Photographer',
        description: 'Experienced wedding photographer for 8 hours',
        category: 'Photography',
        price: 1800,
        imageUrl: 'https://images.unsplash.com/photo-1537633552985-df8429e8048b',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'DJ & Sound System',
        description: 'Professional DJ with sound equipment',
        category: 'Entertainment',
        price: 1200,
        imageUrl: 'https://images.unsplash.com/photo-1571266028243-5a8d7c3c1c19',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Decoration Package',
        description: 'Complete decoration setup for ceremony and reception',
        category: 'Decor',
        price: 1500,
        imageUrl: 'https://images.unsplash.com/photo-1519225421980-715cb0215aed',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Wedding Tent',
        description: 'Large tent for outdoor wedding (100 guests)',
        category: 'Equipment',
        price: 800,
        imageUrl: 'https://images.unsplash.com/photo-1519225421980-715cb0215aed',
        selected: false
      }
    ];
  };

  const generateCampingItems = (): RentalItem[] => {
    return [
      {
        id: uuidv4(),
        name: '4-Person Tent',
        description: 'Spacious tent with rainfly and vestibule',
        category: 'Shelter',
        price: 45,
        imageUrl: 'https://images.unsplash.com/photo-1504280390367-361c6d9f38f4',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Sleeping Bag',
        description: 'All-season sleeping bag rated for 20°F',
        category: 'Sleep',
        price: 25,
        imageUrl: 'https://images.unsplash.com/photo-1504280390367-361c6d9f38f4',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Camping Stove',
        description: 'Portable propane camping stove with 2 burners',
        category: 'Cooking',
        price: 35,
        imageUrl: 'https://images.unsplash.com/photo-1504280390367-361c6d9f38f4',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Cooler',
        description: '50-quart cooler with 5-day ice retention',
        category: 'Food',
        price: 30,
        imageUrl: 'https://images.unsplash.com/photo-1504280390367-361c6d9f38f4',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Camping Chairs',
        description: 'Set of 4 comfortable folding chairs',
        category: 'Furniture',
        price: 40,
        imageUrl: 'https://images.unsplash.com/photo-1504280390367-361c6d9f38f4',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Portable Grill',
        description: 'Compact propane grill for outdoor cooking',
        category: 'Cooking',
        price: 50,
        imageUrl: 'https://images.unsplash.com/photo-1504280390367-361c6d9f38f4',
        selected: false
      }
    ];
  };

  const generatePartyItems = (): RentalItem[] => {
    return [
      {
        id: uuidv4(),
        name: 'Sound System',
        description: 'Professional sound system with speakers and mixer',
        category: 'Entertainment',
        price: 150,
        imageUrl: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Party Tent',
        description: '20x20 tent for outdoor parties',
        category: 'Shelter',
        price: 120,
        imageUrl: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Folding Tables',
        description: 'Set of 5 folding tables (6ft each)',
        category: 'Furniture',
        price: 75,
        imageUrl: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Folding Chairs',
        description: 'Set of 20 folding chairs',
        category: 'Furniture',
        price: 60,
        imageUrl: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Lighting Package',
        description: 'Party lights, strobes, and disco ball',
        category: 'Lighting',
        price: 85,
        imageUrl: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Karaoke Machine',
        description: 'Complete karaoke system with microphones',
        category: 'Entertainment',
        price: 95,
        imageUrl: 'https://images.unsplash.com/photo-1470225620780-dba8ba36b745',
        selected: false
      }
    ];
  };

  const generateRenovationItems = (): RentalItem[] => {
    return [
      {
        id: uuidv4(),
        name: 'Power Drill',
        description: 'Cordless power drill with battery and charger',
        category: 'Tools',
        price: 35,
        imageUrl: 'https://images.unsplash.com/photo-1581147036324-c47a03a31afd',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Ladder',
        description: '8-foot aluminum ladder',
        category: 'Equipment',
        price: 40,
        imageUrl: 'https://images.unsplash.com/photo-1581147036324-c47a03a31afd',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Paint Sprayer',
        description: 'Professional paint sprayer for large surfaces',
        category: 'Painting',
        price: 65,
        imageUrl: 'https://images.unsplash.com/photo-1581147036324-c47a03a31afd',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Tile Cutter',
        description: 'Manual tile cutter for ceramic tiles',
        category: 'Tools',
        price: 45,
        imageUrl: 'https://images.unsplash.com/photo-1581147036324-c47a03a31afd',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Pressure Washer',
        description: 'Electric pressure washer (2000 PSI)',
        category: 'Cleaning',
        price: 55,
        imageUrl: 'https://images.unsplash.com/photo-1581147036324-c47a03a31afd',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Circular Saw',
        description: '7.25-inch circular saw with blade',
        category: 'Tools',
        price: 40,
        imageUrl: 'https://images.unsplash.com/photo-1581147036324-c47a03a31afd',
        selected: false
      }
    ];
  };

  const generateGeneralItems = (): RentalItem[] => {
    return [
      {
        id: uuidv4(),
        name: 'Digital Camera',
        description: 'Professional DSLR camera with lens',
        category: 'Electronics',
        price: 75,
        imageUrl: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Projector',
        description: 'HD projector with screen',
        category: 'Electronics',
        price: 65,
        imageUrl: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Bicycle',
        description: 'Mountain bike with helmet',
        category: 'Sports',
        price: 35,
        imageUrl: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Kayak',
        description: 'Single-person kayak with paddle',
        category: 'Water Sports',
        price: 50,
        imageUrl: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Lawn Mower',
        description: 'Gas-powered lawn mower',
        category: 'Garden',
        price: 45,
        imageUrl: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32',
        selected: false
      },
      {
        id: uuidv4(),
        name: 'Moving Truck',
        description: '15-foot moving truck with ramp',
        category: 'Transportation',
        price: 120,
        imageUrl: 'https://images.unsplash.com/photo-1516035069371-29a1b244cc32',
        selected: false
      }
    ];
  };

  // Render the component based on the current step
  return (
    <div className={`bg-white rounded-lg shadow-lg overflow-hidden ${className}`}>
      {/* Header */}
      <div className="bg-primary text-white p-4">
        <h2 className="text-xl font-semibold">RentUp Planner</h2>
        <p className="text-sm">Plan your rental needs with our AI assistant</p>
      </div>

      {/* Content based on current step */}
      {step === 'chat' && (
        <div className="p-4">
          {/* Chat messages */}
          <div className="h-80 overflow-y-auto mb-4 p-2 border border-gray-200 rounded-lg">
            {messages.map(message => (
              <div
                key={message.id}
                className={`mb-3 ${message.role === 'user' ? 'text-right' : ''}`}
              >
                <div
                  className={`inline-block max-w-[80%] p-3 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-primary text-white rounded-br-none'
                      : 'bg-gray-100 text-gray-800 rounded-bl-none'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {formatTimestamp(message.timestamp)}
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start mb-3">
                <div className="bg-gray-100 p-3 rounded-lg rounded-bl-none">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-100"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-200"></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Message input */}
          <form onSubmit={handleSendMessage} className="mt-4">
            <div className="flex">
              <textarea
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                placeholder="Describe what you're planning (e.g., wedding, camping trip, etc.)"
                className="flex-1 border border-gray-300 rounded-l-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                rows={2}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
              />
              <button
                type="submit"
                disabled={isLoading || !input.trim()}
                className="bg-primary text-white px-4 py-2 rounded-r-lg hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Items selection step */}
      {step === 'items' && (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-4">Select items for your rental plan</h3>

          {/* Plan details */}
          <div className="mb-4">
            <div className="mb-2">
              <label htmlFor="planName" className="block text-sm font-medium text-gray-700">Plan Name</label>
              <input
                type="text"
                id="planName"
                value={planName}
                onChange={(e) => setPlanName(e.target.value)}
                placeholder="e.g., My Wedding Rental Plan"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 mt-1 focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>

            <div className="mb-2">
              <label htmlFor="planDescription" className="block text-sm font-medium text-gray-700">Description</label>
              <textarea
                id="planDescription"
                value={planDescription}
                onChange={(e) => setPlanDescription(e.target.value)}
                placeholder="Brief description of your rental plan"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 mt-1 focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                rows={2}
              />
            </div>

            <div className="grid grid-cols-2 gap-4 mb-2">
              <div>
                <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">Start Date</label>
                <input
                  type="date"
                  id="startDate"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 mt-1 focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              <div>
                <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">End Date</label>
                <input
                  type="date"
                  id="endDate"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 mt-1 focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
            </div>
          </div>

          {/* Items grid */}
          <div className="mb-4">
            <h4 className="text-md font-medium mb-2">Recommended Items</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-80 overflow-y-auto p-2">
              {suggestedItems.map(item => (
                <div
                  key={item.id}
                  className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                    item.selected ? 'border-primary bg-primary bg-opacity-10' : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => toggleItemSelection(item.id)}
                >
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-12 w-12 bg-gray-200 rounded-md overflow-hidden">
                      <img
                        src={item.imageUrl}
                        alt={item.name}
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = 'https://via.placeholder.com/100?text=No+Image';
                        }}
                      />
                    </div>
                    <div className="ml-3 flex-1">
                      <h5 className="text-sm font-medium">{item.name}</h5>
                      <p className="text-xs text-gray-500">{item.category}</p>
                      <p className="text-sm font-semibold text-primary">{formatPrice(item.price)}</p>
                    </div>
                    <div className="ml-2">
                      <input
                        type="checkbox"
                        checked={item.selected}
                        onChange={() => toggleItemSelection(item.id)}
                        className="h-5 w-5 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Total and actions */}
          <div className="flex items-center justify-between border-t border-gray-200 pt-4">
            <div>
              <p className="text-sm text-gray-500">Total Price:</p>
              <p className="text-lg font-semibold">{formatPrice(calculateTotalPrice())}</p>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => setStep('chat')}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary"
              >
                Back
              </button>

              <button
                onClick={handleReviewPlan}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary"
              >
                Review Plan
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Review step */}
      {step === 'review' && rentalPlan && (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-4">Review Your Rental Plan</h3>

          {/* Plan summary */}
          <div className="mb-4 p-4 border border-gray-200 rounded-lg">
            <h4 className="text-md font-medium">{rentalPlan.name}</h4>
            <p className="text-sm text-gray-600 mb-2">{rentalPlan.description}</p>

            <div className="grid grid-cols-2 gap-2 text-sm mb-3">
              <div>
                <span className="text-gray-500">Start Date:</span>
                <span className="ml-2 font-medium">{rentalPlan.startDate || 'Not specified'}</span>
              </div>
              <div>
                <span className="text-gray-500">End Date:</span>
                <span className="ml-2 font-medium">{rentalPlan.endDate || 'Not specified'}</span>
              </div>
              <div>
                <span className="text-gray-500">Total Items:</span>
                <span className="ml-2 font-medium">{rentalPlan.items.length}</span>
              </div>
              <div>
                <span className="text-gray-500">Total Price:</span>
                <span className="ml-2 font-medium">{formatPrice(rentalPlan.totalPrice)}</span>
              </div>
            </div>
          </div>

          {/* Selected items */}
          <div className="mb-4">
            <h4 className="text-md font-medium mb-2">Selected Items</h4>
            <div className="max-h-80 overflow-y-auto">
              {rentalPlan.items.map(item => (
                <div key={item.id} className="flex items-center p-3 border-b border-gray-200 last:border-b-0">
                  <div className="flex-shrink-0 h-12 w-12 bg-gray-200 rounded-md overflow-hidden">
                    <img
                      src={item.imageUrl}
                      alt={item.name}
                      className="h-full w-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://via.placeholder.com/100?text=No+Image';
                      }}
                    />
                  </div>
                  <div className="ml-3 flex-1">
                    <h5 className="text-sm font-medium">{item.name}</h5>
                    <p className="text-xs text-gray-500">{item.category}</p>
                  </div>
                  <div className="text-sm font-semibold text-primary">
                    {formatPrice(item.price)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between border-t border-gray-200 pt-4">
            <button
              onClick={() => setStep('items')}
              className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary"
            >
              Back to Items
            </button>

            <button
              onClick={handleCompletePlan}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary"
            >
              Complete Plan
            </button>
          </div>
        </div>
      )}

      {/* Complete step */}
      {step === 'complete' && rentalPlan && (
        <div className="p-4 text-center">
          <div className="mb-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>

          <h3 className="text-lg font-semibold mb-2">Rental Plan Created!</h3>
          <p className="text-gray-600 mb-6">Your rental plan has been created successfully. You can now proceed to checkout or save it for later.</p>

          <div className="flex flex-col space-y-3">
            <button
              className="w-full px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary"
            >
              Proceed to Checkout
            </button>

            <button
              className="w-full px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary"
            >
              Save for Later
            </button>

            <button
              onClick={() => {
                setStep('chat');
                setMessages([{
                  id: uuidv4(),
                  role: 'assistant',
                  content: "Hi there! I'm your RentUp Planner. I can help you plan your rental needs for any event or occasion. What are you planning for?",
                  timestamp: new Date().toISOString()
                }]);
                setSuggestedItems([]);
                setRentalPlan(null);
                setPlanName('');
                setPlanDescription('');
                setStartDate('');
                setEndDate('');
              }}
              className="w-full px-4 py-2 text-gray-500 hover:text-gray-700 focus:outline-none"
            >
              Create Another Plan
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default RentPlannerInterface;