// Main RentPlanner component
import React from 'react';
import { RentPlannerProps } from './utils/types';
import useChatMessages from './hooks/useChatMessages';
import useRentalPlan from './hooks/useRentalPlan';
import ChatInterface from './components/ChatInterface';
import ItemSelection from './components/ItemSelection';
import PlanReview from './components/PlanReview';
import PlanComplete from './components/PlanComplete';

/**
 * RentPlanner component
 * 
 * A multi-step interface for creating rental plans with AI assistance.
 */
const RentPlanner: React.FC<RentPlannerProps> = ({
  userId,
  onClose,
  className = '',
}) => {
  // Chat messages state and functions
  const { 
    messages, 
    isTyping, 
    addUserMessage 
  } = useChatMessages();
  
  // Rental plan state and functions
  const {
    currentStep,
    availableItems,
    searchQuery,
    isLoading,
    plan,
    startDate,
    endDate,
    searchItems,
    toggleItemSelection,
    addSelectedItemsToPlan,
    removeItemFromPlan,
    updatePlanDetails,
    updateRentalDates,
    goToNextStep,
    goToPreviousStep,
    resetPlanner,
  } = useRentalPlan();
  
  // Render the appropriate step
  const renderStep = () => {
    switch (currentStep) {
      case 'chat':
        return (
          <ChatInterface
            messages={messages}
            isTyping={isTyping}
            onSendMessage={addUserMessage}
            onContinue={goToNextStep}
          />
        );
      
      case 'items':
        return (
          <ItemSelection
            availableItems={availableItems}
            searchQuery={searchQuery}
            isLoading={isLoading}
            onSearchChange={searchItems}
            onToggleSelection={toggleItemSelection}
            onAddSelectedItems={addSelectedItemsToPlan}
            onContinue={goToNextStep}
            onBack={goToPreviousStep}
          />
        );
      
      case 'review':
        return (
          <PlanReview
            plan={plan}
            startDate={startDate}
            endDate={endDate}
            onUpdatePlanDetails={updatePlanDetails}
            onUpdateDates={updateRentalDates}
            onRemoveItem={removeItemFromPlan}
            onContinue={goToNextStep}
            onBack={goToPreviousStep}
          />
        );
      
      case 'complete':
        return (
          <PlanComplete
            plan={plan}
            onStartOver={resetPlanner}
            onClose={onClose}
          />
        );
      
      default:
        return null;
    }
  };
  
  // Render step indicator
  const renderStepIndicator = () => {
    const steps = [
      { id: 'chat', label: 'Chat' },
      { id: 'items', label: 'Select Items' },
      { id: 'review', label: 'Review Plan' },
      { id: 'complete', label: 'Complete' },
    ];
    
    return (
      <div className="flex items-center justify-center py-4 border-b border-gray-200">
        {steps.map((step, index) => {
          const isActive = step.id === currentStep;
          const isCompleted = steps.findIndex(s => s.id === currentStep) > index;
          
          return (
            <React.Fragment key={step.id}>
              {index > 0 && (
                <div 
                  className={`w-12 h-0.5 mx-1 ${
                    isCompleted ? 'bg-blue-500' : 'bg-gray-300'
                  }`}
                />
              )}
              
              <div className="flex flex-col items-center">
                <div 
                  className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm
                    ${isActive 
                      ? 'bg-blue-500 text-white' 
                      : isCompleted 
                        ? 'bg-blue-100 text-blue-500 border border-blue-500' 
                        : 'bg-gray-200 text-gray-500'
                    }
                  `}
                >
                  {isCompleted ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    index + 1
                  )}
                </div>
                <span 
                  className={`
                    text-xs mt-1
                    ${isActive ? 'text-blue-500 font-medium' : 'text-gray-500'}
                  `}
                >
                  {step.label}
                </span>
              </div>
            </React.Fragment>
          );
        })}
      </div>
    );
  };
  
  return (
    <div 
      className={`flex flex-col bg-white rounded-lg shadow-lg overflow-hidden h-full ${className}`}
      data-testid="rent-planner"
    >
      {renderStepIndicator()}
      {renderStep()}
    </div>
  );
};

export default RentPlanner;
