// Plan review component for the RentPlanner
import React, { useState } from 'react';
import { RentalPlan } from '../utils/types';
import { formatPrice, formatDate } from '../utils/formatters';

interface PlanReviewProps {
  plan: RentalPlan;
  startDate?: string;
  endDate?: string;
  onUpdatePlanDetails: (name: string, description: string) => void;
  onUpdateDates: (startDate?: string, endDate?: string) => void;
  onRemoveItem: (itemId: string) => void;
  onContinue: () => void;
  onBack: () => void;
}

/**
 * Plan review component for the RentPlanner
 */
const PlanReview: React.FC<PlanReviewProps> = ({
  plan,
  startDate,
  endDate,
  onUpdatePlanDetails,
  onUpdateDates,
  onRemoveItem,
  onContinue,
  onBack,
}) => {
  const [planName, setPlanName] = useState(plan.name);
  const [planDescription, setPlanDescription] = useState(plan.description);
  const [localStartDate, setLocalStartDate] = useState(startDate || '');
  const [localEndDate, setLocalEndDate] = useState(endDate || '');
  
  // Handle plan details update
  const handleDetailsUpdate = () => {
    onUpdatePlanDetails(planName, planDescription);
  };
  
  // Handle dates update
  const handleDatesUpdate = () => {
    onUpdateDates(localStartDate || undefined, localEndDate || undefined);
  };
  
  // Calculate rental duration in days
  const calculateDuration = (): number => {
    if (!localStartDate || !localEndDate) return 0;
    
    const start = new Date(localStartDate);
    const end = new Date(localEndDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };
  
  const rentalDuration = calculateDuration();
  const totalPrice = plan.totalPrice * (rentalDuration || 1);
  
  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold mb-4">Review Your Rental Plan</h2>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
          <h3 className="text-lg font-semibold mb-2">Plan Details</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Plan Name
              </label>
              <input
                type="text"
                value={planName}
                onChange={(e) => setPlanName(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                data-testid="plan-name-input"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <input
                type="text"
                value={planDescription}
                onChange={(e) => setPlanDescription(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                data-testid="plan-description-input"
              />
            </div>
          </div>
          
          <button
            onClick={handleDetailsUpdate}
            className="text-blue-500 hover:text-blue-700 focus:outline-none text-sm"
            data-testid="update-details-button"
          >
            Update Details
          </button>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
          <h3 className="text-lg font-semibold mb-2">Rental Dates</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                value={localStartDate}
                onChange={(e) => setLocalStartDate(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                data-testid="start-date-input"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                value={localEndDate}
                onChange={(e) => setLocalEndDate(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                min={localStartDate}
                data-testid="end-date-input"
              />
            </div>
          </div>
          
          {localStartDate && localEndDate && (
            <div className="text-sm text-gray-600 mb-2">
              Duration: {rentalDuration} day{rentalDuration !== 1 ? 's' : ''}
            </div>
          )}
          
          <button
            onClick={handleDatesUpdate}
            className="text-blue-500 hover:text-blue-700 focus:outline-none text-sm"
            data-testid="update-dates-button"
          >
            Update Dates
          </button>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
          <h3 className="text-lg font-semibold mb-2">Selected Items</h3>
          
          {plan.items.length === 0 ? (
            <div className="text-center text-gray-500 py-4">
              No items selected yet. Go back to add items.
            </div>
          ) : (
            <div className="space-y-4">
              {plan.items.map(item => (
                <div 
                  key={item.id} 
                  className="flex items-center border-b border-gray-100 pb-4 last:border-b-0 last:pb-0"
                  data-testid={`review-item-${item.id}`}
                >
                  <div className="w-16 h-16 rounded overflow-hidden flex-shrink-0">
                    <img
                      src={item.imageUrl}
                      alt={item.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="ml-4 flex-1">
                    <div className="flex justify-between">
                      <h4 className="font-medium">{item.name}</h4>
                      <span className="text-green-600">{formatPrice(item.price)}</span>
                    </div>
                    <p className="text-sm text-gray-600">{item.description}</p>
                    <div className="mt-1">
                      <span className="inline-block bg-gray-200 rounded-full px-2 py-0.5 text-xs font-semibold text-gray-700">
                        {item.category}
                      </span>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => onRemoveItem(item.id)}
                    className="ml-2 text-red-500 hover:text-red-700 focus:outline-none"
                    aria-label={`Remove ${item.name}`}
                    data-testid={`remove-item-${item.id}`}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <h3 className="text-lg font-semibold mb-2">Summary</h3>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Items:</span>
              <span>{plan.items.length}</span>
            </div>
            
            <div className="flex justify-between">
              <span>Daily Rate:</span>
              <span>{formatPrice(plan.totalPrice)}</span>
            </div>
            
            {rentalDuration > 0 && (
              <div className="flex justify-between">
                <span>Duration:</span>
                <span>{rentalDuration} day{rentalDuration !== 1 ? 's' : ''}</span>
              </div>
            )}
            
            <div className="border-t border-gray-200 pt-2 mt-2">
              <div className="flex justify-between font-semibold">
                <span>Total:</span>
                <span>{formatPrice(totalPrice)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="border-t border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <button
            onClick={onBack}
            className="text-blue-500 hover:text-blue-700 focus:outline-none"
            data-testid="back-button"
          >
            ← Back to Items
          </button>
          
          <button
            onClick={onContinue}
            className="bg-green-500 text-white rounded-lg px-6 py-2 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
            disabled={plan.items.length === 0}
            data-testid="complete-button"
          >
            Complete Plan
          </button>
        </div>
      </div>
    </div>
  );
};

export default PlanReview;
