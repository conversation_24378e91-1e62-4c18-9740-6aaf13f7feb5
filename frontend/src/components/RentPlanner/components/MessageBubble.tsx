// Message bubble component for the chat interface
import React from 'react';
import { ChatMessage } from '../utils/types';
import { formatTimestamp } from '../utils/formatters';

interface MessageBubbleProps {
  message: ChatMessage;
}

/**
 * Message bubble component for the chat interface
 */
const MessageBubble: React.FC<MessageBubbleProps> = ({ message }) => {
  const isUser = message.role === 'user';
  
  return (
    <div 
      className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}
      data-testid={`message-${message.id}`}
    >
      <div 
        className={`
          max-w-[80%] rounded-lg px-4 py-2 
          ${isUser 
            ? 'bg-blue-500 text-white rounded-br-none' 
            : 'bg-gray-200 text-gray-800 rounded-bl-none'
          }
        `}
      >
        <div className="text-sm">{message.content}</div>
        <div 
          className={`text-xs mt-1 ${isUser ? 'text-blue-100' : 'text-gray-500'}`}
        >
          {formatTimestamp(message.timestamp)}
        </div>
      </div>
    </div>
  );
};

export default MessageBubble;
