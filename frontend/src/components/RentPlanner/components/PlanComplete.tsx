// Plan complete component for the RentPlanner
import React from 'react';
import { RentalPlan } from '../utils/types';
import { formatPrice, formatDate } from '../utils/formatters';

interface PlanCompleteProps {
  plan: RentalPlan;
  onStartOver: () => void;
  onClose?: () => void;
}

/**
 * Plan complete component for the RentPlanner
 */
const PlanComplete: React.FC<PlanCompleteProps> = ({
  plan,
  onStartOver,
  onClose,
}) => {
  // Calculate rental duration in days
  const calculateDuration = (): number => {
    if (!plan.startDate || !plan.endDate) return 0;
    
    const start = new Date(plan.startDate);
    const end = new Date(plan.endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };
  
  const rentalDuration = calculateDuration();
  const totalPrice = plan.totalPrice * (rentalDuration || 1);
  
  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold mb-1">Plan Completed!</h2>
        <p className="text-gray-600">Your rental plan has been created successfully.</p>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center text-green-700 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            <span className="font-semibold">Success!</span>
          </div>
          <p className="text-green-700">
            Your rental plan has been created and saved. You can view and manage your plans in your account dashboard.
          </p>
        </div>
        
        <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
          <h3 className="text-lg font-semibold mb-2">Plan Summary</h3>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-700">Plan Details</h4>
              <div className="mt-1 space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600">Name:</span>
                  <span>{plan.name}</span>
                </div>
                {plan.description && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Description:</span>
                    <span>{plan.description}</span>
                  </div>
                )}
              </div>
            </div>
            
            {(plan.startDate || plan.endDate) && (
              <div>
                <h4 className="font-medium text-gray-700">Rental Period</h4>
                <div className="mt-1 space-y-1">
                  {plan.startDate && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Start Date:</span>
                      <span>{formatDate(plan.startDate)}</span>
                    </div>
                  )}
                  {plan.endDate && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">End Date:</span>
                      <span>{formatDate(plan.endDate)}</span>
                    </div>
                  )}
                  {rentalDuration > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Duration:</span>
                      <span>{rentalDuration} day{rentalDuration !== 1 ? 's' : ''}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
            
            <div>
              <h4 className="font-medium text-gray-700">Items ({plan.items.length})</h4>
              <div className="mt-2 space-y-2">
                {plan.items.map(item => (
                  <div 
                    key={item.id} 
                    className="flex items-center border-b border-gray-100 pb-2 last:border-b-0 last:pb-0"
                  >
                    <div className="w-12 h-12 rounded overflow-hidden flex-shrink-0">
                      <img
                        src={item.imageUrl}
                        alt={item.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    
                    <div className="ml-3 flex-1">
                      <div className="flex justify-between">
                        <h5 className="font-medium text-sm">{item.name}</h5>
                        <span className="text-green-600 text-sm">{formatPrice(item.price)}</span>
                      </div>
                      <div className="mt-0.5">
                        <span className="inline-block bg-gray-200 rounded-full px-2 py-0.5 text-xs font-semibold text-gray-700">
                          {item.category}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-700">Pricing</h4>
              <div className="mt-1 space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-600">Daily Rate:</span>
                  <span>{formatPrice(plan.totalPrice)}</span>
                </div>
                {rentalDuration > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Duration:</span>
                    <span>{rentalDuration} day{rentalDuration !== 1 ? 's' : ''}</span>
                  </div>
                )}
                <div className="flex justify-between font-semibold pt-1 border-t border-gray-100">
                  <span>Total:</span>
                  <span>{formatPrice(totalPrice)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 mt-0.5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <div className="text-blue-700">
              <p className="font-medium mb-1">What's Next?</p>
              <ul className="list-disc list-inside text-sm space-y-1">
                <li>You'll receive a confirmation email with your plan details.</li>
                <li>The rental provider will contact you to confirm availability.</li>
                <li>You can manage your rental plans in your account dashboard.</li>
                <li>Need help? Contact our support <NAME_EMAIL></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <div className="border-t border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <button
            onClick={onStartOver}
            className="text-blue-500 hover:text-blue-700 focus:outline-none"
            data-testid="start-over-button"
          >
            Start a New Plan
          </button>
          
          {onClose && (
            <button
              onClick={onClose}
              className="bg-gray-500 text-white rounded-lg px-6 py-2 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
              data-testid="close-button"
            >
              Close
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PlanComplete;
