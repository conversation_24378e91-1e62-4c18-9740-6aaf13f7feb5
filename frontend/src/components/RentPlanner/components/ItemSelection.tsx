// Item selection component for the RentPlanner
import React, { useState } from 'react';
import { RentalItem } from '../utils/types';
import { formatPrice } from '../utils/formatters';

interface ItemSelectionProps {
  availableItems: RentalItem[];
  searchQuery: string;
  isLoading: boolean;
  onSearchChange: (query: string) => void;
  onToggleSelection: (itemId: string) => void;
  onAddSelectedItems: () => void;
  onContinue: () => void;
  onBack: () => void;
}

/**
 * Item selection component for the RentPlanner
 */
const ItemSelection: React.FC<ItemSelectionProps> = ({
  availableItems,
  searchQuery,
  isLoading,
  onSearchChange,
  onToggleSelection,
  onAddSelectedItems,
  onContinue,
  onBack,
}) => {
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSearchQuery(e.target.value);
  };
  
  // Handle search form submission
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearchChange(localSearchQuery);
  };
  
  // Count selected items
  const selectedCount = availableItems.filter(item => item.selected).length;
  
  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-xl font-semibold mb-4">Select Items to Rent</h2>
        
        <form onSubmit={handleSearchSubmit} className="flex space-x-2 mb-4">
          <input
            type="text"
            value={localSearchQuery}
            onChange={handleSearchChange}
            placeholder="Search for items..."
            className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            data-testid="search-input"
          />
          <button
            type="submit"
            className="bg-blue-500 text-white rounded-lg px-4 py-2 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            data-testid="search-button"
          >
            Search
          </button>
        </form>
      </div>
      
      <div className="flex-1 overflow-y-auto p-4">
        {isLoading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : availableItems.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            No items found. Try a different search term.
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableItems.map(item => (
              <div
                key={item.id}
                className={`
                  border rounded-lg overflow-hidden transition-all
                  ${item.selected ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-200 hover:border-blue-300'}
                `}
                onClick={() => onToggleSelection(item.id)}
                data-testid={`item-${item.id}`}
              >
                <div className="relative h-40">
                  <img
                    src={item.imageUrl}
                    alt={item.name}
                    className="w-full h-full object-cover"
                  />
                  {item.selected && (
                    <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="p-4">
                  <div className="flex justify-between items-start">
                    <h3 className="font-semibold">{item.name}</h3>
                    <span className="text-green-600 font-medium">{formatPrice(item.price)}</span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                  <div className="mt-2">
                    <span className="inline-block bg-gray-200 rounded-full px-2 py-1 text-xs font-semibold text-gray-700">
                      {item.category}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="border-t border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <button
            onClick={onBack}
            className="text-blue-500 hover:text-blue-700 focus:outline-none"
            data-testid="back-button"
          >
            ← Back to Chat
          </button>
          
          <div className="flex space-x-2">
            <button
              onClick={onAddSelectedItems}
              className="bg-blue-500 text-white rounded-lg px-4 py-2 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              disabled={selectedCount === 0}
              data-testid="add-selected-button"
            >
              Add Selected ({selectedCount})
            </button>
            
            <button
              onClick={onContinue}
              className="bg-green-500 text-white rounded-lg px-4 py-2 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500"
              data-testid="continue-button"
            >
              Continue to Review
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ItemSelection;
