import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import RentPlannerButton from '../RentPlannerButton';
import '@testing-library/jest-dom';

// Mock the RentPlannerInterface component
jest.mock('../RentPlannerInterface', () => {
  return function MockRentPlannerInterface({ onClose }: { onClose?: () => void }) {
    return (
      <div data-testid="rent-planner-interface">
        Mock Rent Planner Interface
        <button onClick={onClose}>Close</button>
      </div>
    );
  };
});

describe('RentPlannerButton', () => {
  it('renders the floating action button when closed', () => {
    render(<RentPlannerButton />);
    
    // Check that the button is rendered
    expect(screen.getByTitle('Plan your rentals')).toBeInTheDocument();
  });

  it('opens the rent planner modal when the button is clicked', () => {
    render(<RentPlannerButton />);
    
    // Click the button
    fireEvent.click(screen.getByTitle('Plan your rentals'));
    
    // Check that the modal is rendered
    expect(screen.getByTestId('rent-planner-interface')).toBeInTheDocument();
    
    // Check that the button is hidden
    expect(screen.queryByTitle('Plan your rentals')).not.toBeInTheDocument();
  });

  it('closes the rent planner modal when the close button is clicked', () => {
    render(<RentPlannerButton />);
    
    // Open the modal
    fireEvent.click(screen.getByTitle('Plan your rentals'));
    
    // Check that the modal is rendered
    expect(screen.getByTestId('rent-planner-interface')).toBeInTheDocument();
    
    // Close the modal
    fireEvent.click(screen.getByText('Close'));
    
    // Check that the modal is removed
    expect(screen.queryByTestId('rent-planner-interface')).not.toBeInTheDocument();
    
    // Check that the button is visible again
    expect(screen.getByTitle('Plan your rentals')).toBeInTheDocument();
  });

  it('closes the modal when clicking the overlay', () => {
    render(<RentPlannerButton />);
    
    // Open the modal
    fireEvent.click(screen.getByTitle('Plan your rentals'));
    
    // Check that the modal is rendered
    expect(screen.getByTestId('rent-planner-interface')).toBeInTheDocument();
    
    // Click the overlay (the div with the background overlay class)
    const overlay = screen.getByRole('dialog').querySelector('.bg-gray-500');
    if (overlay) {
      fireEvent.click(overlay);
    }
    
    // Check that the modal is removed
    expect(screen.queryByTestId('rent-planner-interface')).not.toBeInTheDocument();
  });

  it('passes the userId prop to the RentPlannerInterface', () => {
    // Create a spy on the RentPlannerInterface mock
    const RentPlannerInterface = require('../RentPlannerInterface');
    const spy = jest.spyOn(RentPlannerInterface, 'default');
    
    // Render with userId
    render(<RentPlannerButton userId="user123" />);
    
    // Open the modal
    fireEvent.click(screen.getByTitle('Plan your rentals'));
    
    // Check that userId was passed to RentPlannerInterface
    expect(spy).toHaveBeenCalledWith(
      expect.objectContaining({
        userId: 'user123'
      }),
      expect.anything()
    );
    
    // Clean up
    spy.mockRestore();
  });

  it('applies custom className prop', () => {
    render(<RentPlannerButton className="custom-class" />);
    
    // Check that the custom class is applied to the container
    const container = screen.getByTitle('Plan your rentals').closest('div');
    expect(container).toHaveClass('custom-class');
  });
});
