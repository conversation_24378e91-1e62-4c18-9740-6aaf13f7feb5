import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import RentPlannerInterface from '../RentPlannerInterface';
import '@testing-library/jest-dom';

// Mock uuid
jest.mock('uuid', () => ({
  v4: () => 'test-uuid'
}));

// Mock axios
jest.mock('axios', () => ({
  post: jest.fn(() => Promise.resolve({
    data: {
      message: 'Test response',
      suggestions: [
        { id: 'suggestion_1', text: 'Suggestion 1' },
        { id: 'suggestion_2', text: 'Suggestion 2' },
        { id: 'suggestion_3', text: 'Suggestion 3' }
      ]
    }
  }))
}));

describe('RentPlannerInterface', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('renders the initial state correctly', () => {
    render(<RentPlannerInterface />);
    
    // Check for header
    expect(screen.getByText('RentUp Planner')).toBeInTheDocument();
    
    // Check for initial message
    expect(screen.getByText(/I'm your RentUp Planner/)).toBeInTheDocument();
    
    // Check for input field
    expect(screen.getByPlaceholderText(/Describe what you're planning/)).toBeInTheDocument();
  });

  it('allows user to send a message', async () => {
    render(<RentPlannerInterface />);
    
    // Type a message
    const input = screen.getByPlaceholderText(/Describe what you're planning/);
    await userEvent.type(input, 'I need to plan a wedding');
    
    // Send the message
    const sendButton = screen.getByRole('button', { name: '' }); // The send button has an SVG icon
    await userEvent.click(sendButton);
    
    // Check that the user message appears
    expect(screen.getByText('I need to plan a wedding')).toBeInTheDocument();
    
    // Check for loading indicator
    expect(screen.getByRole('status')).toBeInTheDocument();
    
    // Wait for response
    await waitFor(() => {
      expect(screen.getByText('Test response')).toBeInTheDocument();
    });
  });

  it('transitions to items selection step', async () => {
    render(<RentPlannerInterface />);
    
    // Type a message about a wedding
    const input = screen.getByPlaceholderText(/Describe what you're planning/);
    await userEvent.type(input, 'I need to plan a wedding');
    
    // Send the message
    const sendButton = screen.getByRole('button', { name: '' });
    await userEvent.click(sendButton);
    
    // Wait for transition to items step
    await waitFor(() => {
      expect(screen.getByText('Select items for your rental plan')).toBeInTheDocument();
    });
    
    // Check for plan details inputs
    expect(screen.getByLabelText('Plan Name')).toBeInTheDocument();
    expect(screen.getByLabelText('Description')).toBeInTheDocument();
    expect(screen.getByLabelText('Start Date')).toBeInTheDocument();
    expect(screen.getByLabelText('End Date')).toBeInTheDocument();
    
    // Check for items grid
    expect(screen.getByText('Recommended Items')).toBeInTheDocument();
  });

  it('allows selecting items and reviewing the plan', async () => {
    render(<RentPlannerInterface />);
    
    // Type a message about a wedding
    const input = screen.getByPlaceholderText(/Describe what you're planning/);
    await userEvent.type(input, 'I need to plan a wedding');
    
    // Send the message
    const sendButton = screen.getByRole('button', { name: '' });
    await userEvent.click(sendButton);
    
    // Wait for transition to items step
    await waitFor(() => {
      expect(screen.getByText('Select items for your rental plan')).toBeInTheDocument();
    });
    
    // Fill in plan details
    await userEvent.type(screen.getByLabelText('Plan Name'), 'My Wedding Plan');
    await userEvent.type(screen.getByLabelText('Description'), 'Wedding in June');
    
    // Select an item (first checkbox)
    const firstCheckbox = screen.getAllByRole('checkbox')[0];
    await userEvent.click(firstCheckbox);
    
    // Click Review Plan button
    const reviewButton = screen.getByText('Review Plan');
    await userEvent.click(reviewButton);
    
    // Check for review step
    await waitFor(() => {
      expect(screen.getByText('Review Your Rental Plan')).toBeInTheDocument();
    });
    
    // Check plan details are displayed
    expect(screen.getByText('My Wedding Plan')).toBeInTheDocument();
    expect(screen.getByText('Wedding in June')).toBeInTheDocument();
    
    // Check for Complete Plan button
    expect(screen.getByText('Complete Plan')).toBeInTheDocument();
  });

  it('completes the plan and shows success message', async () => {
    render(<RentPlannerInterface />);
    
    // Type a message about a wedding
    const input = screen.getByPlaceholderText(/Describe what you're planning/);
    await userEvent.type(input, 'I need to plan a wedding');
    
    // Send the message
    const sendButton = screen.getByRole('button', { name: '' });
    await userEvent.click(sendButton);
    
    // Wait for transition to items step
    await waitFor(() => {
      expect(screen.getByText('Select items for your rental plan')).toBeInTheDocument();
    });
    
    // Fill in plan details
    await userEvent.type(screen.getByLabelText('Plan Name'), 'My Wedding Plan');
    
    // Select an item (first checkbox)
    const firstCheckbox = screen.getAllByRole('checkbox')[0];
    await userEvent.click(firstCheckbox);
    
    // Click Review Plan button
    const reviewButton = screen.getByText('Review Plan');
    await userEvent.click(reviewButton);
    
    // Wait for review step
    await waitFor(() => {
      expect(screen.getByText('Review Your Rental Plan')).toBeInTheDocument();
    });
    
    // Click Complete Plan button
    const completeButton = screen.getByText('Complete Plan');
    await userEvent.click(completeButton);
    
    // Check for success message
    await waitFor(() => {
      expect(screen.getByText('Rental Plan Created!')).toBeInTheDocument();
    });
    
    // Check for action buttons
    expect(screen.getByText('Proceed to Checkout')).toBeInTheDocument();
    expect(screen.getByText('Save for Later')).toBeInTheDocument();
    expect(screen.getByText('Create Another Plan')).toBeInTheDocument();
  });
});
