// Custom hook for managing chat messages
import { useState, useCallback } from 'react';
import { ChatMessage } from '../utils/types';
import { generateId } from '../utils/formatters';

// Initial welcome message from the assistant
const initialMessages: ChatMessage[] = [
  {
    id: generateId(),
    role: 'assistant',
    content: 'Hello! I\'m your rental planning assistant. Tell me what you\'re looking to rent, and I\'ll help you create a plan.',
    timestamp: new Date().toISOString(),
  },
];

/**
 * Custom hook for managing chat messages
 * @returns Chat messages state and functions
 */
export const useChatMessages = () => {
  const [messages, setMessages] = useState<ChatMessage[]>(initialMessages);
  const [isTyping, setIsTyping] = useState(false);

  /**
   * Add a user message to the chat
   * @param content - Message content
   */
  const addUserMessage = useCallback((content: string) => {
    if (!content.trim()) return;

    const newMessage: ChatMessage = {
      id: generateId(),
      role: 'user',
      content,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, newMessage]);
    setIsTyping(true);

    // Simulate assistant response after a delay
    setTimeout(() => {
      addAssistantResponse(content);
    }, 1000);
  }, []);

  /**
   * Add an assistant response to the chat
   * @param userMessage - The user message that triggered the response
   */
  const addAssistantResponse = useCallback((userMessage: string) => {
    // In a real application, this would call an API to get a response
    // For now, we'll use a simple mock response
    let response = '';

    if (userMessage.toLowerCase().includes('furniture')) {
      response = 'I can help you find furniture to rent. What specific pieces are you looking for? For example, sofas, tables, chairs, etc.';
    } else if (userMessage.toLowerCase().includes('party')) {
      response = 'Planning a party? I can help you find equipment like speakers, lighting, tables, chairs, and more.';
    } else if (userMessage.toLowerCase().includes('outdoor') || userMessage.toLowerCase().includes('camping')) {
      response = 'Looking for outdoor or camping gear? I can help you find tents, grills, patio furniture, and more.';
    } else if (userMessage.toLowerCase().includes('price') || userMessage.toLowerCase().includes('cost')) {
      response = 'Our rental prices vary based on the items and duration. Once we've selected some items, I can show you the total cost.';
    } else if (userMessage.toLowerCase().includes('duration') || userMessage.toLowerCase().includes('how long')) {
      response = 'You can rent items for as short as a day or as long as several months. We're flexible with rental durations.';
    } else {
      response = 'I can help you find items to rent based on your needs. Could you provide more details about what you're looking for?';
    }

    const newMessage: ChatMessage = {
      id: generateId(),
      role: 'assistant',
      content: response,
      timestamp: new Date().toISOString(),
    };

    setMessages(prev => [...prev, newMessage]);
    setIsTyping(false);
  }, []);

  return {
    messages,
    isTyping,
    addUserMessage,
  };
};

export default useChatMessages;
