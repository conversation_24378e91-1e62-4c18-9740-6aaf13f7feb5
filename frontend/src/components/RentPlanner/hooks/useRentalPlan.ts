// Custom hook for managing the rental plan
import { useState, useCallback, useEffect } from 'react';
import { RentalItem, RentalPlan, PlannerStep } from '../utils/types';
import { generateId } from '../utils/formatters';
import { generateMockItems, generateMockItemsForQuery } from '../utils/mockItemGenerators';

/**
 * Custom hook for managing the rental plan
 * @returns Rental plan state and functions
 */
export const useRentalPlan = () => {
  // Current step in the planning process
  const [currentStep, setCurrentStep] = useState<PlannerStep>('chat');
  
  // Available items to choose from
  const [availableItems, setAvailableItems] = useState<RentalItem[]>([]);
  
  // Search query for filtering items
  const [searchQuery, setSearchQuery] = useState('');
  
  // Loading state for items
  const [isLoading, setIsLoading] = useState(false);
  
  // The current rental plan
  const [plan, setPlan] = useState<RentalPlan>({
    id: generateId(),
    name: 'My Rental Plan',
    description: '',
    items: [],
    totalPrice: 0,
  });

  // Dates for the rental
  const [startDate, setStartDate] = useState<string | undefined>(undefined);
  const [endDate, setEndDate] = useState<string | undefined>(undefined);

  // Load initial items
  useEffect(() => {
    setIsLoading(true);
    // Simulate API call delay
    setTimeout(() => {
      setAvailableItems(generateMockItems(12));
      setIsLoading(false);
    }, 1000);
  }, []);

  // Search for items
  const searchItems = useCallback((query: string) => {
    setSearchQuery(query);
    setIsLoading(true);
    
    // Simulate API call delay
    setTimeout(() => {
      if (query.trim() === '') {
        setAvailableItems(generateMockItems(12));
      } else {
        setAvailableItems(generateMockItemsForQuery(query, 8));
      }
      setIsLoading(false);
    }, 800);
  }, []);

  // Toggle item selection
  const toggleItemSelection = useCallback((itemId: string) => {
    setAvailableItems(prevItems => 
      prevItems.map(item => 
        item.id === itemId ? { ...item, selected: !item.selected } : item
      )
    );
  }, []);

  // Add selected items to the plan
  const addSelectedItemsToPlan = useCallback(() => {
    const selectedItems = availableItems.filter(item => item.selected);
    
    if (selectedItems.length === 0) return;
    
    setPlan(prevPlan => {
      const updatedItems = [...prevPlan.items];
      
      // Add new items that aren't already in the plan
      selectedItems.forEach(item => {
        if (!updatedItems.some(existingItem => existingItem.id === item.id)) {
          updatedItems.push(item);
        }
      });
      
      // Calculate new total price
      const newTotalPrice = updatedItems.reduce((sum, item) => sum + item.price, 0);
      
      return {
        ...prevPlan,
        items: updatedItems,
        totalPrice: newTotalPrice,
      };
    });
    
    // Clear selections
    setAvailableItems(prevItems => 
      prevItems.map(item => ({ ...item, selected: false }))
    );
  }, [availableItems]);

  // Remove an item from the plan
  const removeItemFromPlan = useCallback((itemId: string) => {
    setPlan(prevPlan => {
      const updatedItems = prevPlan.items.filter(item => item.id !== itemId);
      const newTotalPrice = updatedItems.reduce((sum, item) => sum + item.price, 0);
      
      return {
        ...prevPlan,
        items: updatedItems,
        totalPrice: newTotalPrice,
      };
    });
  }, []);

  // Update plan details
  const updatePlanDetails = useCallback((name: string, description: string) => {
    setPlan(prevPlan => ({
      ...prevPlan,
      name,
      description,
    }));
  }, []);

  // Update rental dates
  const updateRentalDates = useCallback((start?: string, end?: string) => {
    setStartDate(start);
    setEndDate(end);
    
    setPlan(prevPlan => ({
      ...prevPlan,
      startDate: start,
      endDate: end,
    }));
  }, []);

  // Navigate to the next step
  const goToNextStep = useCallback(() => {
    setCurrentStep(prevStep => {
      switch (prevStep) {
        case 'chat':
          return 'items';
        case 'items':
          return 'review';
        case 'review':
          return 'complete';
        default:
          return prevStep;
      }
    });
  }, []);

  // Navigate to the previous step
  const goToPreviousStep = useCallback(() => {
    setCurrentStep(prevStep => {
      switch (prevStep) {
        case 'items':
          return 'chat';
        case 'review':
          return 'items';
        case 'complete':
          return 'review';
        default:
          return prevStep;
      }
    });
  }, []);

  // Reset the planner
  const resetPlanner = useCallback(() => {
    setCurrentStep('chat');
    setPlan({
      id: generateId(),
      name: 'My Rental Plan',
      description: '',
      items: [],
      totalPrice: 0,
    });
    setStartDate(undefined);
    setEndDate(undefined);
    setSearchQuery('');
    setAvailableItems(generateMockItems(12));
  }, []);

  return {
    currentStep,
    availableItems,
    searchQuery,
    isLoading,
    plan,
    startDate,
    endDate,
    searchItems,
    toggleItemSelection,
    addSelectedItemsToPlan,
    removeItemFromPlan,
    updatePlanDetails,
    updateRentalDates,
    goToNextStep,
    goToPreviousStep,
    resetPlanner,
  };
};

export default useRentalPlan;
