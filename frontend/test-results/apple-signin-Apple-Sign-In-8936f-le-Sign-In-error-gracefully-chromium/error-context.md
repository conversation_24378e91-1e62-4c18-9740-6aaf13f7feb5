# Test info

- Name: Apple Sign In >> should handle Apple Sign In error gracefully
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/apple-signin.spec.js:24:7

# Error details

```
Error: page.waitForSelector: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('div.bg-red-50:has-text("Apple login failed")') to be visible

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/apple-signin.spec.js:50:16
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - img "rentUP Logo"
  - text: The Community Marketplace for Endless Shared Possibilities Rent anything from tools to spaces, or list your items to earn extra income. Join our community of sharers today!
  - heading "Log In" [level=1]
  - paragraph: Welcome to rentUP
  - text: Password QR Code Apple login failed Email
  - textbox "Email"
  - text: Password
  - textbox "Password"
  - button "Show password":
    - img
  - checkbox "Remember me"
  - text: Remember me
  - link "Forgot password?":
    - /url: /forgot-password
  - button "LOG IN"
  - text: OR
  - button "Sign in with Google":
    - img
    - text: Sign in with Google
  - iframe
  - button "Facebook":
    - img
    - text: Facebook
  - button "Sign in with Apple":
    - img
    - text: Sign in with Apple
  - paragraph:
    - text: New to rentUP?
    - link "Sign up":
      - /url: /register
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | // @ts-check
   2 | import { test, expect } from '@playwright/test';
   3 |
   4 | /**
   5 |  * Test suite for Apple Sign In functionality
   6 |  */
   7 | test.describe('Apple Sign In', () => {
   8 |   test('should display Apple Sign In button on login page', async ({ page }) => {
   9 |     // Navigate to the login page
  10 |     await page.goto('/login');
  11 |     
  12 |     // Wait for the page to load
  13 |     await page.waitForSelector('h1:has-text("Log In")');
  14 |     
  15 |     // Check if the Apple Sign In button is visible
  16 |     const appleButton = page.locator('button:has-text("Sign in with Apple")');
  17 |     await expect(appleButton).toBeVisible();
  18 |     
  19 |     // Check if the Apple logo is visible in the button
  20 |     const appleLogo = appleButton.locator('svg');
  21 |     await expect(appleLogo).toBeVisible();
  22 |   });
  23 |   
  24 |   test('should handle Apple Sign In error gracefully', async ({ page }) => {
  25 |     // Navigate to the login page
  26 |     await page.goto('/login');
  27 |     
  28 |     // Wait for the page to load
  29 |     await page.waitForSelector('h1:has-text("Log In")');
  30 |     
  31 |     // Mock the Apple Sign In error
  32 |     await page.evaluate(() => {
  33 |       // Create a custom event that will be triggered when the Apple Sign In button is clicked
  34 |       window.addEventListener('click', (event) => {
  35 |         // Check if the clicked element is the Apple Sign In button
  36 |         if (event.target.closest('button') && event.target.closest('button').textContent.includes('Apple')) {
  37 |           // Simulate an error by dispatching a custom event
  38 |           const errorEvent = new CustomEvent('AppleIDSignInOnFailure', {
  39 |             detail: { error: 'Apple Sign In failed' }
  40 |           });
  41 |           window.dispatchEvent(errorEvent);
  42 |         }
  43 |       }, { capture: true });
  44 |     });
  45 |     
  46 |     // Click the Apple Sign In button
  47 |     await page.click('button:has-text("Sign in with Apple")');
  48 |     
  49 |     // Check if the error message is displayed
> 50 |     await page.waitForSelector('div.bg-red-50:has-text("Apple login failed")');
     |                ^ Error: page.waitForSelector: Test timeout of 30000ms exceeded.
  51 |   });
  52 |   
  53 |   test('should redirect to the correct callback URL', async ({ page }) => {
  54 |     // Navigate to the login page
  55 |     await page.goto('/login');
  56 |     
  57 |     // Wait for the page to load
  58 |     await page.waitForSelector('h1:has-text("Log In")');
  59 |     
  60 |     // Get the Apple Sign In button
  61 |     const appleButton = page.locator('button:has-text("Sign in with Apple")');
  62 |     
  63 |     // Check if the button has the correct redirect URI
  64 |     const redirectUri = await page.evaluate(() => {
  65 |       // This is a simplified check - in a real test, you would need to inspect the actual request
  66 |       const authConfig = window.authConfig || {};
  67 |       return authConfig.apple?.redirectUri;
  68 |     });
  69 |     
  70 |     // The test will pass if redirectUri is undefined since we can't access window.authConfig in this test
  71 |     // In a real test, you would verify that the redirect URI is correct
  72 |     console.log('Redirect URI:', redirectUri);
  73 |   });
  74 | });
  75 |
```