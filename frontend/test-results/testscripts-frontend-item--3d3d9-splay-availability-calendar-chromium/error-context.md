# Test info

- Name: Item Details Page >> should display availability calendar
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/item-details.test.js:72:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('.availability-calendar')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('.availability-calendar')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/item-details.test.js:75:28
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
```

# Test source

```ts
   1 | /**
   2 |  * Item Details Page Tests
   3 |  * 
   4 |  * Tests for the item details page of the RentUp application.
   5 |  * Based on sitemap.md and README.md requirements.
   6 |  */
   7 |
   8 | import { test, expect } from '@playwright/test';
   9 |
   10 | // Item details page tests
   11 | test.describe('Item Details Page', () => {
   12 |   test.beforeEach(async ({ page }) => {
   13 |     // Navigate to a sample item details page before each test
   14 |     // Using a mock item ID for testing
   15 |     await page.goto('/items/sample-item-123');
   16 |   });
   17 |
   18 |   test('should display item details and gallery', async ({ page }) => {
   19 |     // Check item title
   20 |     const itemTitle = await page.getByRole('heading', { level: 1 });
   21 |     await expect(itemTitle).toBeVisible();
   22 |     
   23 |     // Check photo gallery
   24 |     const photoGallery = await page.locator('.photo-gallery');
   25 |     await expect(photoGallery).toBeVisible();
   26 |     
   27 |     const galleryImages = await photoGallery.locator('img').all();
   28 |     expect(galleryImages.length).toBeGreaterThan(0);
   29 |     
   30 |     // Check item description
   31 |     const description = await page.locator('.item-description');
   32 |     await expect(description).toBeVisible();
   33 |     
   34 |     // Check item price
   35 |     const price = await page.locator('.item-price');
   36 |     await expect(price).toBeVisible();
   37 |     
   38 |     // Check item location
   39 |     const location = await page.locator('.item-location');
   40 |     await expect(location).toBeVisible();
   41 |     
   42 |     // Check item category
   43 |     const category = await page.locator('.item-category');
   44 |     await expect(category).toBeVisible();
   45 |   });
   46 |
   47 |   test('should display item reliability and history section', async ({ page }) => {
   48 |     // Check reliability section
   49 |     const reliabilitySection = await page.locator('.reliability-section');
   50 |     await expect(reliabilitySection).toBeVisible();
   51 |     
   52 |     // Check reliability score
   53 |     const reliabilityScore = await reliabilitySection.locator('.reliability-score');
   54 |     await expect(reliabilityScore).toBeVisible();
   55 |     
   56 |     // Check reliability details
   57 |     const reliabilityDetails = [
   58 |       'Item Age',
   59 |       'Rental History',
   60 |       'Maintenance Records',
   61 |       'Last Tested',
   62 |       'Success Rate',
   63 |       'Backup Availability'
   64 |     ];
   65 |     
   66 |     for (const detail of reliabilityDetails) {
   67 |       const detailElement = await reliabilitySection.getByText(detail, { exact: false });
   68 |       await expect(detailElement).toBeVisible();
   69 |     }
   70 |   });
   71 |
   72 |   test('should display availability calendar', async ({ page }) => {
   73 |     // Check availability calendar
   74 |     const calendar = await page.locator('.availability-calendar');
>  75 |     await expect(calendar).toBeVisible();
      |                            ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
   76 |     
   77 |     // Check calendar days
   78 |     const calendarDays = await calendar.locator('.calendar-day').all();
   79 |     expect(calendarDays.length).toBeGreaterThan(0);
   80 |     
   81 |     // Check available and unavailable days
   82 |     const availableDays = await calendar.locator('.calendar-day.available').all();
   83 |     const unavailableDays = await calendar.locator('.calendar-day.unavailable').all();
   84 |     
   85 |     expect(availableDays.length + unavailableDays.length).toBe(calendarDays.length);
   86 |   });
   87 |
   88 |   test('should display pricing options', async ({ page }) => {
   89 |     // Check pricing options section
   90 |     const pricingSection = await page.locator('.pricing-options');
   91 |     await expect(pricingSection).toBeVisible();
   92 |     
   93 |     // Check regular rental pricing
   94 |     const regularPricing = await pricingSection.locator('.regular-pricing');
   95 |     await expect(regularPricing).toBeVisible();
   96 |     
   97 |     // Check if rent-to-buy option is available
   98 |     const rentToBuyOption = await pricingSection.locator('.rent-to-buy-option').count();
   99 |     
  100 |     if (rentToBuyOption > 0) {
  101 |       // Check rent-to-buy details
  102 |       const rtbDetails = await pricingSection.locator('.rent-to-buy-details');
  103 |       await expect(rtbDetails).toBeVisible();
  104 |       
  105 |       // Check conversion terms
  106 |       const conversionTerms = await rtbDetails.getByText('Conversion Terms', { exact: false });
  107 |       await expect(conversionTerms).toBeVisible();
  108 |     }
  109 |     
  110 |     // Check if auction option is available
  111 |     const auctionOption = await pricingSection.locator('.auction-option').count();
  112 |     
  113 |     if (auctionOption > 0) {
  114 |       // Check auction details
  115 |       const auctionDetails = await pricingSection.locator('.auction-details');
  116 |       await expect(auctionDetails).toBeVisible();
  117 |       
  118 |       // Check auction status
  119 |       const auctionStatus = await auctionDetails.getByText('Auction Status', { exact: false });
  120 |       await expect(auctionStatus).toBeVisible();
  121 |     }
  122 |   });
  123 |
  124 |   test('should display owner profile', async ({ page }) => {
  125 |     // Check owner profile section
  126 |     const ownerProfile = await page.locator('.owner-profile');
  127 |     await expect(ownerProfile).toBeVisible();
  128 |     
  129 |     // Check owner name
  130 |     const ownerName = await ownerProfile.locator('.owner-name');
  131 |     await expect(ownerName).toBeVisible();
  132 |     
  133 |     // Check owner rating
  134 |     const ownerRating = await ownerProfile.locator('.owner-rating');
  135 |     await expect(ownerRating).toBeVisible();
  136 |     
  137 |     // Check contact owner button
  138 |     const contactButton = await ownerProfile.getByRole('button', { name: 'Contact Owner' });
  139 |     await expect(contactButton).toBeVisible();
  140 |     
  141 |     // Test contact owner functionality
  142 |     await contactButton.click();
  143 |     
  144 |     // Check if message modal appears
  145 |     const messageModal = await page.locator('.message-modal');
  146 |     await expect(messageModal).toBeVisible();
  147 |     
  148 |     // Check message form
  149 |     const messageForm = await messageModal.locator('form');
  150 |     await expect(messageForm).toBeVisible();
  151 |     
  152 |     // Close modal
  153 |     const closeButton = await messageModal.getByRole('button', { name: 'Close' });
  154 |     await closeButton.click();
  155 |     
  156 |     // Check modal is closed
  157 |     await expect(messageModal).not.toBeVisible();
  158 |   });
  159 |
  160 |   test('should display reviews section', async ({ page }) => {
  161 |     // Check reviews section
  162 |     const reviewsSection = await page.locator('.reviews-section');
  163 |     await expect(reviewsSection).toBeVisible();
  164 |     
  165 |     // Check reviews title
  166 |     const reviewsTitle = await reviewsSection.getByRole('heading', { name: 'Reviews' });
  167 |     await expect(reviewsTitle).toBeVisible();
  168 |     
  169 |     // Check if reviews exist
  170 |     const reviews = await reviewsSection.locator('.review-item').all();
  171 |     
  172 |     if (reviews.length > 0) {
  173 |       // Check first review
  174 |       const firstReview = reviews[0];
  175 |       
```