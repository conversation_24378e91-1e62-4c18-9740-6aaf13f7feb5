# Test info

- Name: Responsive Design >> home page should adapt to mobile viewport
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/responsive.test.js:72:7

# Error details

```
Error: expect(received).not.toBeNull()

Matcher error: this matcher must not have an expected argument

Expected has type:  string
Expected has value: "Mobile menu button should exist on mobile viewport"
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/responsive.test.js:81:34
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - button "common.language"
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - button "Toggle mobile menu": Open menu
  - search "Mobile site search":
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Search for items": Search
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, <PERSON><PERSON>e Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: 81 • Very Good Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 4 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: 92 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 4 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: New 83 • Very Good Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day
  - img
  - text: "Eastside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: 94 • Excellent Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day
  - img
  - text: "Northside • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | /**
   2 |  * Enhanced Responsive Design Tests
   3 |  *
   4 |  * Comprehensive tests for the responsive design features of the RentUp application.
   5 |  * Tests adaptively check for responsive behavior across multiple viewport sizes.
   6 |  * Based on sitemap.md and README.md requirements.
   7 |  */
   8 |
   9 | import { test, expect } from '@playwright/test';
   10 |
   11 | // Define viewport sizes for testing
   12 | const viewports = {
   13 |   mobile: { width: 375, height: 667 },
   14 |   smallTablet: { width: 600, height: 800 },
   15 |   tablet: { width: 768, height: 1024 },
   16 |   desktop: { width: 1280, height: 800 },
   17 |   largeDesktop: { width: 1920, height: 1080 }
   18 | };
   19 |
   20 | // Helper function to check if an element is visible and get its dimensions
   21 | async function getVisibleElementDimensions(page, selector) {
   22 |   const element = await page.locator(selector).first();
   23 |   const isVisible = await element.isVisible();
   24 |
   25 |   if (!isVisible) {
   26 |     return { isVisible, width: 0, height: 0 };
   27 |   }
   28 |
   29 |   const boundingBox = await element.boundingBox();
   30 |   return {
   31 |     isVisible,
   32 |     width: boundingBox ? boundingBox.width : 0,
   33 |     height: boundingBox ? boundingBox.height : 0
   34 |   };
   35 | }
   36 |
   37 | // Helper function to check if element exists
   38 | async function elementExists(page, selector) {
   39 |   const count = await page.locator(selector).count();
   40 |   return count > 0;
   41 | }
   42 |
   43 | // Helper function to find mobile menu button with various possible selectors
   44 | async function findMobileMenuButton(page) {
   45 |   const possibleSelectors = [
   46 |     'button[aria-label*="menu" i]',
   47 |     'button[aria-label*="navigation" i]',
   48 |     'button.mobile-menu-button',
   49 |     '[class*="mobile-menu-button"]',
   50 |     'button:has(svg):visible',
   51 |     'header button:visible'
   52 |   ];
   53 |
   54 |   for (const selector of possibleSelectors) {
   55 |     const button = await page.locator(selector).first();
   56 |     if (await button.count() > 0 && await button.isVisible()) {
   57 |       return button;
   58 |     }
   59 |   }
   60 |
   61 |   return null;
   62 | }
   63 |
   64 | // Responsive design tests
   65 | test.describe('Responsive Design', () => {
   66 |   test.beforeEach(async ({ page }) => {
   67 |     // Wait for network to be idle after navigation
   68 |     await page.goto('/');
   69 |     await page.waitForLoadState('networkidle');
   70 |   });
   71 |
   72 |   test('home page should adapt to mobile viewport', async ({ page }) => {
   73 |     // Set viewport to mobile size
   74 |     await page.setViewportSize(viewports.mobile);
   75 |     await page.waitForTimeout(500); // Wait for responsive changes
   76 |
   77 |     // Try to find mobile menu button with various possible selectors
   78 |     const mobileMenuButton = await findMobileMenuButton(page);
   79 |
   80 |     // Check if we found a mobile menu button
>  81 |     expect(mobileMenuButton).not.toBeNull('Mobile menu button should exist on mobile viewport');
      |                                  ^ Error: expect(received).not.toBeNull()
   82 |
   83 |     if (mobileMenuButton) {
   84 |       // Click the mobile menu button
   85 |       await mobileMenuButton.click();
   86 |       await page.waitForTimeout(300); // Wait for animation
   87 |
   88 |       // Check for any visible navigation links after clicking
   89 |       const visibleLinks = await page.locator('a:visible').all();
   90 |       expect(visibleLinks.length).toBeGreaterThan(3, 'Mobile menu should show navigation links when opened');
   91 |     }
   92 |
   93 |     // Check for responsive layout of grid items (category cards, featured items, etc.)
   94 |     // Look for any grid container
   95 |     const gridContainers = await page.locator('.grid, [class*="grid-cols"]').all();
   96 |
   97 |     if (gridContainers.length > 0) {
   98 |       // Check the first grid container's children
   99 |       const firstGrid = gridContainers[0];
  100 |       const gridItems = await firstGrid.locator(':scope > *').all();
  101 |
  102 |       if (gridItems.length >= 2) {
  103 |         const firstItem = gridItems[0];
  104 |         const secondItem = gridItems[1];
  105 |
  106 |         const firstItemBounds = await firstItem.boundingBox();
  107 |         const secondItemBounds = await secondItem.boundingBox();
  108 |
  109 |         // On mobile, items should typically be stacked (one per row)
  110 |         // We check if the second item is below the first one
  111 |         const isStacked = secondItemBounds.y >= (firstItemBounds.y + firstItemBounds.height - 10);
  112 |
  113 |         // We're being flexible here - some designs might have 2 columns even on mobile
  114 |         // So we check either they're stacked OR they're side by side with appropriate width
  115 |         const isSideBySide = Math.abs(secondItemBounds.y - firstItemBounds.y) < 10 &&
  116 |                             firstItemBounds.width < (viewports.mobile.width * 0.6);
  117 |
  118 |         expect(isStacked || isSideBySide).toBe(true,
  119 |           'Grid items should either be stacked or have appropriate width on mobile');
  120 |       }
  121 |     }
  122 |
  123 |     // Check if text is properly sized for mobile
  124 |     const headings = await page.locator('h1, h2, h3').all();
  125 |     if (headings.length > 0) {
  126 |       for (const heading of headings) {
  127 |         const fontSize = await heading.evaluate(el => parseFloat(window.getComputedStyle(el).fontSize));
  128 |         expect(fontSize).toBeLessThanOrEqual(32, 'Headings should have appropriate font size on mobile');
  129 |       }
  130 |     }
  131 |   });
  132 |
  133 |   test('home page should be responsive on tablet', async ({ page }) => {
  134 |     // Set viewport to tablet size
  135 |     await page.setViewportSize(viewports.tablet);
  136 |
  137 |     // Navigate to the home page
  138 |     await page.goto('/');
  139 |
  140 |     // Check if desktop navigation is visible
  141 |     const desktopNav = await page.locator('.desktop-nav');
  142 |     await expect(desktopNav).toBeVisible();
  143 |
  144 |     // Check if mobile menu button is not visible
  145 |     const mobileMenuButton = await page.locator('.mobile-menu-button').count();
  146 |     expect(mobileMenuButton).toBe(0);
  147 |
  148 |     // Check if category cards are in a grid
  149 |     const categoryCards = await page.locator('.category-card').all();
  150 |     if (categoryCards.length > 2) {
  151 |       const firstCard = categoryCards[0];
  152 |       const secondCard = categoryCards[1];
  153 |       const thirdCard = categoryCards[2];
  154 |
  155 |       const firstCardBounds = await firstCard.boundingBox();
  156 |       const secondCardBounds = await secondCard.boundingBox();
  157 |       const thirdCardBounds = await thirdCard.boundingBox();
  158 |
  159 |       // Check if first and second cards are on the same row
  160 |       expect(Math.abs(firstCardBounds.y - secondCardBounds.y)).toBeLessThan(10);
  161 |
  162 |       // Check if third card is either on the same row or below
  163 |       if (categoryCards.length > 3) {
  164 |         const fourthCard = categoryCards[3];
  165 |         const fourthCardBounds = await fourthCard.boundingBox();
  166 |
  167 |         // Check if third and fourth cards are on the same row
  168 |         expect(Math.abs(thirdCardBounds.y - fourthCardBounds.y)).toBeLessThan(10);
  169 |       }
  170 |     }
  171 |   });
  172 |
  173 |   test('home page should be responsive on desktop', async ({ page }) => {
  174 |     // Set viewport to desktop size
  175 |     await page.setViewportSize(viewports.desktop);
  176 |
  177 |     // Navigate to the home page
  178 |     await page.goto('/');
  179 |
  180 |     // Check if desktop navigation is visible
  181 |     const desktopNav = await page.locator('.desktop-nav');
```