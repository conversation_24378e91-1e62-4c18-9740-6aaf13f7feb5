# Test info

- Name: Responsive Design >> search page should be responsive on desktop
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/responsive.test.js:243:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('.filter-panel')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('.filter-panel')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/responsive.test.js:252:31
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - heading "Search" [level=1]
  - paragraph: Find the perfect items to rent using our advanced search capabilities.
  - heading "Multi-Modal Search" [level=2]
  - text: Search by Text
  - textbox "Search by Text"
  - text: Search by Image Upload Image Or Enter Image URL
  - textbox "Or Enter Image URL"
  - text: "Text Weight: 0.7"
  - 'slider "Text Weight: 0.7"': "0.7"
  - text: "Image Weight: 0.3"
  - 'slider "Image Weight: 0.3"': "0.3"
  - button "Search"
  - heading "Search Tips" [level=2]
  - list:
    - listitem: Use specific keywords for better results (e.g., "DSLR camera" instead of just "camera")
    - listitem: Upload a clear image of the item you're looking for
    - listitem: Adjust the weights to prioritize text or image matching
    - listitem: Try different combinations of text and images for more options
  - heading "Popular Categories" [level=2]
  - link "📱 Electronics":
    - /url: /search/results?category=electronics
  - link "🏡 Home & Garden":
    - /url: /search/results?category=home-%26-garden
  - link "🏀 Sports":
    - /url: /search/results?category=sports
  - link "🚗 Vehicles":
    - /url: /search/results?category=vehicles
  - link "👕 Clothing":
    - /url: /search/results?category=clothing
  - link "🔧 Tools":
    - /url: /search/results?category=tools
  - link "📷 Photography":
    - /url: /search/results?category=photography
  - link "🎸 Music":
    - /url: /search/results?category=music
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
  152 |       const secondCard = categoryCards[1];
  153 |       const thirdCard = categoryCards[2];
  154 |
  155 |       const firstCardBounds = await firstCard.boundingBox();
  156 |       const secondCardBounds = await secondCard.boundingBox();
  157 |       const thirdCardBounds = await thirdCard.boundingBox();
  158 |
  159 |       // Check if first and second cards are on the same row
  160 |       expect(Math.abs(firstCardBounds.y - secondCardBounds.y)).toBeLessThan(10);
  161 |
  162 |       // Check if third card is either on the same row or below
  163 |       if (categoryCards.length > 3) {
  164 |         const fourthCard = categoryCards[3];
  165 |         const fourthCardBounds = await fourthCard.boundingBox();
  166 |
  167 |         // Check if third and fourth cards are on the same row
  168 |         expect(Math.abs(thirdCardBounds.y - fourthCardBounds.y)).toBeLessThan(10);
  169 |       }
  170 |     }
  171 |   });
  172 |
  173 |   test('home page should be responsive on desktop', async ({ page }) => {
  174 |     // Set viewport to desktop size
  175 |     await page.setViewportSize(viewports.desktop);
  176 |
  177 |     // Navigate to the home page
  178 |     await page.goto('/');
  179 |
  180 |     // Check if desktop navigation is visible
  181 |     const desktopNav = await page.locator('.desktop-nav');
  182 |     await expect(desktopNav).toBeVisible();
  183 |
  184 |     // Check if all navigation links are visible
  185 |     const navLinks = await desktopNav.locator('a').all();
  186 |     expect(navLinks.length).toBeGreaterThan(3);
  187 |
  188 |     // Check if category cards are in a grid with multiple items per row
  189 |     const categoryCards = await page.locator('.category-card').all();
  190 |     if (categoryCards.length > 3) {
  191 |       const firstCard = categoryCards[0];
  192 |       const secondCard = categoryCards[1];
  193 |       const thirdCard = categoryCards[2];
  194 |       const fourthCard = categoryCards[3];
  195 |
  196 |       const firstCardBounds = await firstCard.boundingBox();
  197 |       const secondCardBounds = await secondCard.boundingBox();
  198 |       const thirdCardBounds = await thirdCard.boundingBox();
  199 |       const fourthCardBounds = await fourthCard.boundingBox();
  200 |
  201 |       // Check if first and second cards are on the same row
  202 |       expect(Math.abs(firstCardBounds.y - secondCardBounds.y)).toBeLessThan(10);
  203 |
  204 |       // Check if third and fourth cards are on the same row
  205 |       expect(Math.abs(thirdCardBounds.y - fourthCardBounds.y)).toBeLessThan(10);
  206 |     }
  207 |   });
  208 |
  209 |   test('search page should be responsive on mobile', async ({ page }) => {
  210 |     // Set viewport to mobile size
  211 |     await page.setViewportSize(viewports.mobile);
  212 |
  213 |     // Navigate to the search page
  214 |     await page.goto('/search');
  215 |
  216 |     // Check if filter button is visible
  217 |     const filterButton = await page.locator('.filter-button');
  218 |     await expect(filterButton).toBeVisible();
  219 |
  220 |     // Check if filters are hidden by default
  221 |     const filterPanel = await page.locator('.filter-panel');
  222 |     await expect(filterPanel).not.toBeVisible();
  223 |
  224 |     // Open filters
  225 |     await filterButton.click();
  226 |
  227 |     // Check if filters are now visible
  228 |     await expect(filterPanel).toBeVisible();
  229 |
  230 |     // Check if search results are stacked
  231 |     const searchResults = await page.locator('.item-card').all();
  232 |     if (searchResults.length > 1) {
  233 |       const firstResult = searchResults[0];
  234 |       const secondResult = searchResults[1];
  235 |
  236 |       const firstResultBounds = await firstResult.boundingBox();
  237 |       const secondResultBounds = await secondResult.boundingBox();
  238 |
  239 |       expect(secondResultBounds.y).toBeGreaterThan(firstResultBounds.y + firstResultBounds.height - 10);
  240 |     }
  241 |   });
  242 |
  243 |   test('search page should be responsive on desktop', async ({ page }) => {
  244 |     // Set viewport to desktop size
  245 |     await page.setViewportSize(viewports.desktop);
  246 |
  247 |     // Navigate to the search page
  248 |     await page.goto('/search');
  249 |
  250 |     // Check if filter panel is visible by default
  251 |     const filterPanel = await page.locator('.filter-panel');
> 252 |     await expect(filterPanel).toBeVisible();
      |                               ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  253 |
  254 |     // Check if filter button is not visible
  255 |     const filterButton = await page.locator('.filter-button').count();
  256 |     expect(filterButton).toBe(0);
  257 |
  258 |     // Check if search results are in a grid
  259 |     const searchResults = await page.locator('.item-card').all();
  260 |     if (searchResults.length > 2) {
  261 |       const firstResult = searchResults[0];
  262 |       const secondResult = searchResults[1];
  263 |       const thirdResult = searchResults[2];
  264 |
  265 |       const firstResultBounds = await firstResult.boundingBox();
  266 |       const secondResultBounds = await secondResult.boundingBox();
  267 |       const thirdResultBounds = await thirdResult.boundingBox();
  268 |
  269 |       // Check if first and second results are on the same row
  270 |       const sameRow = Math.abs(firstResultBounds.y - secondResultBounds.y) < 10;
  271 |
  272 |       // If not on the same row, check if they're in a 2-column layout
  273 |       if (!sameRow) {
  274 |         expect(Math.abs(firstResultBounds.x - thirdResultBounds.x)).toBeLessThan(10);
  275 |       } else {
  276 |         // If on the same row, check if they're in a 3+ column layout
  277 |         if (searchResults.length > 3) {
  278 |           const fourthResult = searchResults[3];
  279 |           const fourthResultBounds = await fourthResult.boundingBox();
  280 |
  281 |           expect(Math.abs(thirdResultBounds.y - fourthResultBounds.y)).toBeLessThan(10);
  282 |         }
  283 |       }
  284 |     }
  285 |   });
  286 |
  287 |   test('item details page should be responsive on mobile', async ({ page }) => {
  288 |     // Set viewport to mobile size
  289 |     await page.setViewportSize(viewports.mobile);
  290 |
  291 |     // Navigate to a sample item details page
  292 |     await page.goto('/items/sample-item-123');
  293 |
  294 |     // Check if gallery is properly sized
  295 |     const gallery = await page.locator('.photo-gallery');
  296 |     const galleryWidth = await gallery.evaluate(el => el.clientWidth);
  297 |     expect(galleryWidth).toBeLessThan(viewports.mobile.width);
  298 |
  299 |     // Check if thumbnail navigation is horizontal scrolling
  300 |     const thumbnails = await page.locator('.gallery-thumbnails');
  301 |     const thumbnailsOverflow = await thumbnails.evaluate(el => window.getComputedStyle(el).overflowX);
  302 |     expect(['auto', 'scroll'].includes(thumbnailsOverflow)).toBe(true);
  303 |
  304 |     // Check if item details are stacked
  305 |     const itemTitle = await page.getByRole('heading', { level: 1 });
  306 |     const itemPrice = await page.locator('.item-price');
  307 |
  308 |     const titleBounds = await itemTitle.boundingBox();
  309 |     const priceBounds = await itemPrice.boundingBox();
  310 |
  311 |     expect(priceBounds.y).toBeGreaterThan(titleBounds.y);
  312 |   });
  313 |
  314 |   test('item details page should be responsive on desktop', async ({ page }) => {
  315 |     // Set viewport to desktop size
  316 |     await page.setViewportSize(viewports.desktop);
  317 |
  318 |     // Navigate to a sample item details page
  319 |     await page.goto('/items/sample-item-123');
  320 |
  321 |     // Check if gallery and details are side by side
  322 |     const gallery = await page.locator('.photo-gallery');
  323 |     const details = await page.locator('.item-details');
  324 |
  325 |     const galleryBounds = await gallery.boundingBox();
  326 |     const detailsBounds = await details.boundingBox();
  327 |
  328 |     // Check if gallery is on the left and details on the right
  329 |     expect(galleryBounds.x + galleryBounds.width).toBeLessThanOrEqual(detailsBounds.x + 20);
  330 |
  331 |     // Check if they are roughly on the same vertical position
  332 |     expect(Math.abs(galleryBounds.y - detailsBounds.y)).toBeLessThan(50);
  333 |   });
  334 |
  335 |   test('user dashboard should be responsive on mobile', async ({ page }) => {
  336 |     // Login first
  337 |     await page.goto('/login');
  338 |
  339 |     await page.fill('input[name="email"]', '<EMAIL>');
  340 |     await page.fill('input[name="password"]', 'Password123!');
  341 |
  342 |     await page.click('button[type="submit"]');
  343 |
  344 |     // Wait for dashboard to load
  345 |     await page.waitForURL('/dashboard');
  346 |
  347 |     // Set viewport to mobile size
  348 |     await page.setViewportSize(viewports.mobile);
  349 |
  350 |     // Check if sidebar is hidden by default
  351 |     const sidebar = await page.locator('.dashboard-sidebar');
  352 |     await expect(sidebar).not.toBeVisible();
```