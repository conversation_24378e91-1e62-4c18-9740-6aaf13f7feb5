# Test info

- Name: Basic Tests >> should have critical UI components
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/basic.spec.js:104:7

# Error details

```
Error: expect(received).not.toBeNull()

Matcher error: this matcher must not have an expected argument

Expected has type:  string
Expected has value: "Header or navigation should be present"
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/basic.spec.js:121:29
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: Rent-to-Buy New 91 • Excellent Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day RTB
  - img
  - text: "Downtown • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: Rent-to-Buy New 99 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day RTB
  - img
  - text: "Westside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: 99 • Excellent Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day
  - img
  - text: "Eastside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: 95 • Excellent Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day
  - img
  - text: "Northside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   21 |       }
   22 |     } catch (error) {
   23 |       console.log(`Error finding element with selector "${selector}": ${error.message}`);
   24 |     }
   25 |   }
   26 |
   27 |   return null;
   28 | }
   29 |
   30 | test.describe('Basic Tests', () => {
   31 |   test.beforeEach(async ({ page }) => {
   32 |     // Navigate to the home page before each test with a longer timeout
   33 |     await page.goto('/', { timeout: 60000 });
   34 |
   35 |     // Wait for the page to be somewhat stable
   36 |     try {
   37 |       await page.waitForLoadState('domcontentloaded', { timeout: 30000 });
   38 |       console.log('Page loaded: domcontentloaded state reached');
   39 |
   40 |       // Try to wait for network idle, but don't fail if it times out
   41 |       try {
   42 |         await page.waitForLoadState('networkidle', { timeout: 30000 });
   43 |         console.log('Page fully loaded: networkidle state reached');
   44 |       } catch (error) {
   45 |         console.log('Network idle timeout, but continuing anyway');
   46 |       }
   47 |     } catch (error) {
   48 |       console.log('Page load timeout, but continuing anyway');
   49 |     }
   50 |
   51 |     // Take a screenshot at the beginning of each test for debugging
   52 |     await page.screenshot({ path: `test-results/before-${test.info().title.replace(/\s+/g, '-')}.png` });
   53 |   });
   54 |
   55 |   test('should load the application without errors', async ({ page }) => {
   56 |     // Check if the page loads without errors
   57 |     const body = await page.locator('body');
   58 |     await expect(body).toBeVisible({ timeout: 30000 });
   59 |
   60 |     // Check if the document has content
   61 |     const content = await page.content();
   62 |     expect(content.length).toBeGreaterThan(0, 'Page should have content');
   63 |
   64 |     // Check for root element
   65 |     const rootSelectors = ['#root', '[id="root"]', 'body > div', 'div', 'main', 'section'];
   66 |     const root = await findElement(page, rootSelectors);
   67 |
   68 |     // Use soft assertion so test continues even if this fails
   69 |     expect.soft(root).not.toBeNull('Root element should be present');
   70 |
   71 |     // Take a screenshot for debugging
   72 |     await page.screenshot({ path: 'test-results/application-loaded.png' });
   73 |
   74 |     // Always pass this test if we got this far
   75 |     expect(true).toBe(true);
   76 |   });
   77 |
   78 |   test('should have proper HTML structure', async ({ page }) => {
   79 |     // Check for HTML lang attribute
   80 |     const html = await page.locator('html');
   81 |     const langAttribute = await html.getAttribute('lang');
   82 |
   83 |     // Use soft assertion so test continues even if this fails
   84 |     expect.soft(langAttribute).toBeTruthy('HTML should have a lang attribute');
   85 |
   86 |     // Check for title
   87 |     const title = await page.title();
   88 |     expect.soft(title.length).toBeGreaterThan(0, 'Page should have a title');
   89 |
   90 |     // Check for basic HTML elements
   91 |     const head = await page.locator('head');
   92 |     const body = await page.locator('body');
   93 |
   94 |     await expect.soft(head).toBeDefined('Head should be defined');
   95 |     await expect.soft(body).toBeVisible({ timeout: 30000 });
   96 |
   97 |     // Take a screenshot for debugging
   98 |     await page.screenshot({ path: 'test-results/html-structure.png' });
   99 |
  100 |     // Always pass this test if we got this far
  101 |     expect(true).toBe(true);
  102 |   });
  103 |
  104 |   test('should have critical UI components', async ({ page }) => {
  105 |     // Check for header using multiple selector strategies
  106 |     const headerSelectors = [
  107 |       'header',
  108 |       '[role="banner"]',
  109 |       '.header',
  110 |       'nav',
  111 |       '.navbar',
  112 |       'div:first-child',
  113 |       'body > div',
  114 |       'body > header',
  115 |       'body > nav',
  116 |       'body > div > header',
  117 |       'body > div > nav'
  118 |     ];
  119 |
  120 |     const header = await findElement(page, headerSelectors);
> 121 |     expect.soft(header).not.toBeNull('Header or navigation should be present');
      |                             ^ Error: expect(received).not.toBeNull()
  122 |
  123 |     // Check for logo or brand name
  124 |     const logoSelectors = [
  125 |       '.logo',
  126 |       'img[alt*="logo" i]',
  127 |       'a:has-text("rentUP")',
  128 |       'a:has-text("rent")',
  129 |       'a:has-text("Rent")',
  130 |       'a:first-child',
  131 |       'img',
  132 |       'svg',
  133 |       'h1',
  134 |       'h2',
  135 |       '.brand',
  136 |       '[class*="logo" i]',
  137 |       '[class*="brand" i]'
  138 |     ];
  139 |
  140 |     const logo = await findElement(page, logoSelectors);
  141 |     expect.soft(logo).not.toBeNull('Logo or brand name should be present');
  142 |
  143 |     // Check for any clickable elements
  144 |     const clickableSelectors = [
  145 |       'a', 'button', '[role="button"]',
  146 |       '.btn', '.button', '[type="button"]',
  147 |       '[type="submit"]', '[aria-role="button"]'
  148 |     ];
  149 |
  150 |     const clickable = await findElement(page, clickableSelectors);
  151 |     expect.soft(clickable).not.toBeNull('At least one clickable element should be present');
  152 |
  153 |     // Take a screenshot for debugging
  154 |     await page.screenshot({ path: 'test-results/ui-components.png' });
  155 |
  156 |     // Always pass this test if we got this far
  157 |     expect(true).toBe(true);
  158 |   });
  159 |
  160 |   test('should have search functionality', async ({ page }) => {
  161 |     // Check for search input using multiple selector strategies
  162 |     const searchInputSelectors = [
  163 |       'input[type="search"]',
  164 |       'input[type="text"]',
  165 |       'input[placeholder*="search" i]',
  166 |       'input[placeholder*="find" i]',
  167 |       'input[placeholder*="rent" i]',
  168 |       'input[placeholder*="look" i]',
  169 |       'input[placeholder*="what" i]',
  170 |       'input[aria-label*="search" i]',
  171 |       'input',
  172 |       '[role="searchbox"]',
  173 |       '[class*="search" i]',
  174 |       'form input'
  175 |     ];
  176 |
  177 |     const searchInput = await findElement(page, searchInputSelectors);
  178 |
  179 |     // If search input doesn't exist, don't fail the test
  180 |     if (!searchInput) {
  181 |       console.log('Search input not found, but continuing anyway');
  182 |       // Take a screenshot for debugging
  183 |       await page.screenshot({ path: 'test-results/no-search-input.png' });
  184 |       // Pass the test anyway
  185 |       expect(true).toBe(true);
  186 |       return;
  187 |     }
  188 |
  189 |     // Try to interact with the search input
  190 |     try {
  191 |       await expect(searchInput).toBeVisible({ timeout: 10000 });
  192 |
  193 |       // Test search functionality
  194 |       await searchInput.fill('test');
  195 |       const inputValue = await searchInput.inputValue();
  196 |       expect.soft(inputValue).toBe('test', 'Search input should accept text');
  197 |
  198 |       // Take a screenshot after filling the search input
  199 |       await page.screenshot({ path: 'test-results/search-input-filled.png' });
  200 |     } catch (error) {
  201 |       console.log(`Error interacting with search input: ${error.message}`);
  202 |       // Take a screenshot for debugging
  203 |       await page.screenshot({ path: 'test-results/search-input-error.png' });
  204 |     }
  205 |
  206 |     // Always pass this test if we got this far
  207 |     expect(true).toBe(true);
  208 |   });
  209 |
  210 |   test('should be responsive', async ({ page }) => {
  211 |     try {
  212 |       // Test mobile viewport
  213 |       await page.setViewportSize({ width: 375, height: 667 });
  214 |       await page.waitForTimeout(1000); // Wait longer for responsive changes
  215 |
  216 |       // Take a screenshot in mobile viewport
  217 |       await page.screenshot({ path: 'test-results/mobile-viewport.png' });
  218 |
  219 |       // Check that the page is still visible on mobile
  220 |       const bodyMobile = await page.locator('body');
  221 |       await expect.soft(bodyMobile).toBeVisible({ timeout: 10000 });
```