# Test info

- Name: should have proper focus management
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/visualization-a11y.spec.ts:53:5

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toHaveAttribute(expected)

Locator: getByRole('tab', { name: 'Preference Visualization' })
Expected string: "true"
Received: <element(s) not found>
Call log:
  - expect.toHaveAttribute with timeout 5000ms
  - waiting for getBy<PERSON>ole('tab', { name: 'Preference Visualization' })

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/visualization-a11y.spec.ts:89:32
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - heading "AI Recommendation Insights" [level=1]
  - paragraph: Explore how our AI recommendation system works to provide you with personalized suggestions.
  - heading "Sign In to See Your Personalized Insights" [level=2]
  - paragraph: To view your personalized recommendation insights, please sign in to your account. This will allow us to show you how your preferences influence the recommendations you receive.
  - link "Sign In":
    - /url: /login
  - heading "How Our Recommendations Work" [level=2]
  - paragraph: Our AI recommendation system uses a combination of collaborative filtering and content-based approaches to provide personalized recommendations tailored to your preferences.
  - list:
    - listitem:
      - strong: "Preference Tracking:"
      - text: We analyze your interactions with items, including views, likes, and rentals.
    - listitem:
      - strong: "Item Embeddings:"
      - text: We create mathematical representations of items based on their features and relationships.
    - listitem:
      - strong: "Contextual Factors:"
      - text: We consider factors like location, season, and time of day.
    - listitem:
      - strong: "Collaborative Signals:"
      - text: We identify patterns among users with similar preferences.
  - heading "Privacy and Your Data" [level=2]
  - paragraph: "We take your privacy seriously. Here's how we handle your data in our recommendation system:"
  - list:
    - listitem:
      - strong: "Data Control:"
      - text: You can view and manage your preference data at any time.
    - listitem:
      - strong: "Anonymized Processing:"
      - text: Your data is anonymized during processing.
    - listitem:
      - strong: "No Third-Party Sharing:"
      - text: We never share your preference data with third parties.
    - listitem:
      - strong: "Transparency:"
      - text: The visualizations on this page show exactly how your data influences recommendations.
  - link "Learn more about our privacy policy":
    - /url: /privacy-policy
  - heading "Frequently Asked Questions" [level=2]
  - heading "How can I improve my recommendations?" [level=3]
  - paragraph: The more you interact with items on RentUp, the better our recommendations become. Like items you're interested in, create favorites, and browse categories you enjoy.
  - heading "Can I reset my recommendation preferences?" [level=3]
  - paragraph: Yes, you can reset your recommendation preferences in your account settings. This will clear your preference data and start fresh.
  - heading "Why am I seeing certain recommendations?" [level=3]
  - paragraph: The visualizations on this page help explain why you're seeing certain recommendations. Your preferences, browsing history, and similar users all influence what you see.
  - heading "How often are recommendations updated?" [level=3]
  - paragraph: Your recommendations are updated in real-time as you interact with the platform. The system continuously learns from your behavior to improve suggestions.
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import AxeBuilder from '@axe-core/playwright';
   3 |
   4 | // Using individual tests instead of describe blocks to match the project configuration
   5 | test('should not have any automatically detectable accessibility issues', async ({ page }) => {
   6 |   // Navigate to the visualization page
   7 |   await page.goto('/visualization');
   8 |
   9 |   // Run the accessibility scan
   10 |   const accessibilityScanResults = await new AxeBuilder({ page })
   11 |     .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
   12 |     .analyze();
   13 |
   14 |   // Assert that there are no violations
   15 |   expect(accessibilityScanResults.violations).toEqual([]);
   16 | });
   17 |
   18 | test('should have proper heading structure', async ({ page }) => {
   19 |   // Navigate to the visualization page
   20 |   await page.goto('/visualization');
   21 |
   22 |   // Check that there is an h1 heading
   23 |   const h1Elements = await page.locator('h1').count();
   24 |   expect(h1Elements).toBe(1);
   25 |
   26 |   // Check that h2 headings come after h1
   27 |   const h2Elements = await page.locator('h2').all();
   28 |   for (const h2 of h2Elements) {
   29 |     const h2Text = await h2.textContent();
   30 |     expect(h2Text).toBeTruthy();
   31 |   }
   32 | });
   33 |
   34 | test('should have sufficient color contrast', async ({ page }) => {
   35 |   // Navigate to the visualization page
   36 |   await page.goto('/visualization');
   37 |
   38 |   // Run the accessibility scan specifically for color contrast
   39 |   const accessibilityScanResults = await new AxeBuilder({ page })
   40 |     .withTags(['wcag2aa'])
   41 |     .options({
   42 |       runOnly: {
   43 |         type: 'rule',
   44 |         values: ['color-contrast'],
   45 |       },
   46 |     })
   47 |     .analyze();
   48 |
   49 |   // Assert that there are no color contrast violations
   50 |   expect(accessibilityScanResults.violations).toEqual([]);
   51 | });
   52 |
   53 | test('should have proper focus management', async ({ page }) => {
   54 |   // Navigate to the visualization page
   55 |   await page.goto('/visualization');
   56 |
   57 |   // Mock authentication
   58 |   await page.evaluate(() => {
   59 |     localStorage.setItem('auth', JSON.stringify({
   60 |       user: { id: 'user-123', name: 'Test User' },
   61 |       token: 'fake-token',
   62 |       isAuthenticated: true
   63 |     }));
   64 |   });
   65 |
   66 |   // Reload the page to apply the authentication
   67 |   await page.reload();
   68 |
   69 |   // Tab through the page and check that focus is visible
   70 |   await page.keyboard.press('Tab');
   71 |
   72 |   // Check that the first tab button is focused
   73 |   const focusedElement = await page.evaluate(() => {
   74 |     const activeElement = document.activeElement;
   75 |     return activeElement ? activeElement.tagName : null;
   76 |   });
   77 |
   78 |   expect(focusedElement).not.toBeNull();
   79 |
   80 |   // Tab to the first visualization tab
   81 |   await page.keyboard.press('Tab');
   82 |   await page.keyboard.press('Tab');
   83 |
   84 |   // Check that we can activate the tab with the keyboard
   85 |   await page.keyboard.press('Enter');
   86 |
   87 |   // Check that the tab is selected
   88 |   const preferencesTab = page.getByRole('tab', { name: 'Preference Visualization' });
>  89 |   await expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
      |                                ^ Error: Timed out 5000ms waiting for expect(locator).toHaveAttribute(expected)
   90 |
   91 |   // Tab to the second visualization tab
   92 |   await page.keyboard.press('Tab');
   93 |
   94 |   // Check that we can activate the tab with the keyboard
   95 |   await page.keyboard.press('Enter');
   96 |
   97 |   // Check that the tab is selected
   98 |   const embeddingsTab = page.getByRole('tab', { name: 'Embedding Visualization' });
   99 |   await expect(embeddingsTab).toHaveAttribute('aria-selected', 'true');
  100 | });
  101 |
  102 | test('should have proper ARIA attributes', async ({ page }) => {
  103 |   // Navigate to the visualization page
  104 |   await page.goto('/visualization');
  105 |
  106 |   // Mock authentication
  107 |   await page.evaluate(() => {
  108 |     localStorage.setItem('auth', JSON.stringify({
  109 |       user: { id: 'user-123', name: 'Test User' },
  110 |       token: 'fake-token',
  111 |       isAuthenticated: true
  112 |     }));
  113 |   });
  114 |
  115 |   // Reload the page to apply the authentication
  116 |   await page.reload();
  117 |
  118 |   // Check that the tabs have proper ARIA attributes
  119 |   const tablist = page.getByRole('tablist');
  120 |   await expect(tablist).toBeVisible();
  121 |
  122 |   const preferencesTab = page.getByRole('tab', { name: 'Preference Visualization' });
  123 |   await expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
  124 |
  125 |   const embeddingsTab = page.getByRole('tab', { name: 'Embedding Visualization' });
  126 |   await expect(embeddingsTab).toHaveAttribute('aria-selected', 'false');
  127 |
  128 |   // Check that the tabpanels have proper ARIA attributes
  129 |   const preferencesPanel = page.getByRole('tabpanel');
  130 |   await expect(preferencesPanel).toBeVisible();
  131 |   await expect(preferencesPanel).toHaveAttribute('aria-labelledby', await preferencesTab.getAttribute('id'));
  132 | });
  133 |
```