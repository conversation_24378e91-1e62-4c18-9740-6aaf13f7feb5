# Test info

- Name: Accessibility >> registration page should be accessible
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/accessibility.test.js:313:7

# Error details

```
Error: expect(received).toEqual(expected) // deep equality

- Expected  -   1
+ Received  + 299

- Array []
+ Array [
+   Object {
+     "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds",
+     "help": "Elements must meet minimum color contrast ratio thresholds",
+     "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/color-contrast?application=playwright",
+     "id": "color-contrast",
+     "impact": "serious",
+     "nodes": Array [
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/auctions\" data-discover=\"true\">navigation.auctions</a>",
+                 "target": Array [
+                   "a[href$=\"auctions\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/auctions\" data-discover=\"true\">navigation.auctions</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"auctions\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/visualization\" data-discover=\"true\">navigation.aiInsights</a>",
+                 "target": Array [
+                   "a[href$=\"visualization\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/visualization\" data-discover=\"true\">navigation.aiInsights</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"visualization\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/design-system\" data-discover=\"true\">navigation.designSystem</a>",
+                 "target": Array [
+                   "a[href$=\"design-system\"]:nth-child(6)",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/design-system\" data-discover=\"true\">navigation.designSystem</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"design-system\"]:nth-child(6)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/responsive-design-system\" data-discover=\"true\">navigation.responsiveDesign</a>",
+                 "target": Array [
+                   "a[href$=\"responsive-design-system\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/responsive-design-system\" data-discover=\"true\">navigation.responsiveDesign</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"responsive-design-system\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "10.5pt (14px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"text-primary-700 hover:text-primary-800 underline\" href=\"/terms\" data-discover=\"true\">Terms of Service</a>",
+                 "target": Array [
+                   ".text-primary-700.hover\\:text-primary-800[href$=\"terms\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"text-primary-700 hover:text-primary-800 underline\" href=\"/terms\" data-discover=\"true\">Terms of Service</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".text-primary-700.hover\\:text-primary-800[href$=\"terms\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "10.5pt (14px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"text-primary-700 hover:text-primary-800 underline\" href=\"/privacy\" data-discover=\"true\">Privacy Policy</a>",
+                 "target": Array [
+                   ".text-primary-700.hover\\:text-primary-800[href$=\"privacy\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"text-primary-700 hover:text-primary-800 underline\" href=\"/privacy\" data-discover=\"true\">Privacy Policy</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".text-primary-700.hover\\:text-primary-800[href$=\"privacy\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"text-primary-700 hover:text-primary-800 font-semibold underline\" href=\"/login\" data-discover=\"true\">Sign in</a>",
+                 "target": Array [
+                   ".text-primary-700.hover\\:text-primary-800[href$=\"login\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"text-primary-700 hover:text-primary-800 font-semibold underline\" href=\"/login\" data-discover=\"true\">Sign in</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".text-primary-700.hover\\:text-primary-800[href$=\"login\"]",
+         ],
+       },
+     ],
+     "tags": Array [
+       "cat.color",
+       "wcag2aa",
+       "wcag143",
+       "TTv5",
+       "TT13.c",
+       "EN-301-549",
+       "EN-*******",
+       "ACT",
+     ],
+   },
+   Object {
+     "description": "Ensure the order of headings is semantically correct",
+     "help": "Heading levels should only increase by one",
+     "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/heading-order?application=playwright",
+     "id": "heading-order",
+     "impact": "moderate",
+     "nodes": Array [
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": null,
+             "id": "heading-order",
+             "impact": "moderate",
+             "message": "Heading order invalid",
+             "relatedNodes": Array [],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Heading order invalid",
+         "html": "<h3 class=\"text-2xl font-semibold text-black mb-2\">Join Our Community</h3>",
+         "impact": "moderate",
+         "none": Array [],
+         "target": Array [
+           "h3",
+         ],
+       },
+     ],
+     "tags": Array [
+       "cat.semantics",
+       "best-practice",
+     ],
+   },
+ ]
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/accessibility.test.js:321:49
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - heading "Create Account" [level=1]
  - paragraph: Join RentUp and start renting or listing items
  - text: Full Name
  - textbox "Full Name"
  - text: Email Address
  - textbox "Email Address"
  - text: Password
  - textbox "Password"
  - text: Confirm Password
  - textbox "Confirm Password"
  - checkbox "I agree to the Terms of Service and Privacy Policy"
  - text: I agree to the
  - link "Terms of Service":
    - /url: /terms
  - text: and
  - link "Privacy Policy":
    - /url: /privacy
  - button "Create Account"
  - paragraph:
    - text: Already have an account?
    - link "Sign in":
      - /url: /login
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
  221 |       // Get all focusable elements
  222 |       const focusableElements = await page.locator('a, button, input, select, textarea, [tabindex]:not([tabindex="-1"])').all();
  223 |
  224 |       // Check at least a few elements
  225 |       const samplesToCheck = Math.min(5, focusableElements.length);
  226 |
  227 |       for (let i = 0; i < samplesToCheck; i++) {
  228 |         const element = focusableElements[i];
  229 |
  230 |         // Focus the element
  231 |         await element.focus();
  232 |
  233 |         // Check if focus style is applied
  234 |         const hasFocusStyle = await element.evaluate((el) => {
  235 |           const styles = window.getComputedStyle(el);
  236 |           return styles.outlineWidth !== '0px' ||
  237 |                  styles.boxShadow !== 'none' ||
  238 |                  el.classList.contains('focus') ||
  239 |                  el.classList.contains('focus-visible') ||
  240 |                  el.hasAttribute('data-focused');
  241 |         });
  242 |
  243 |         expect(hasFocusStyle).toBe(true, 'Focusable elements should have visible focus indicators');
  244 |       }
  245 |     });
  246 |
  247 |     test('should have skip link for keyboard users', async ({ page }) => {
  248 |       await page.goto('/');
  249 |       await page.waitForLoadState('networkidle');
  250 |
  251 |       // Check for skip link
  252 |       const skipLink = await page.locator('a[href="#main-content"], a[href="#main"], a[href="#content"]');
  253 |       const skipLinkCount = await skipLink.count();
  254 |
  255 |       // Skip link might be visually hidden but should exist in the DOM
  256 |       expect(skipLinkCount).toBeGreaterThan(0, 'Page should have a skip link for keyboard users');
  257 |
  258 |       if (skipLinkCount > 0) {
  259 |         // Focus the skip link (it should become visible)
  260 |         await skipLink.focus();
  261 |
  262 |         // Check if it's now visible
  263 |         const isVisible = await skipLink.isVisible();
  264 |         expect(isVisible).toBe(true, 'Skip link should become visible when focused');
  265 |       }
  266 |     });
  267 |   });
  268 |
  269 |   test.describe('Form Accessibility', () => {
  270 |     test('should have accessible form controls', async ({ page }) => {
  271 |       // Navigate to a page with forms (login or search)
  272 |       await page.goto('/login');
  273 |       await page.waitForLoadState('networkidle');
  274 |
  275 |       // Check if form exists
  276 |       const form = await page.locator('form');
  277 |       const formCount = await form.count();
  278 |
  279 |       if (formCount > 0) {
  280 |         // Check form inputs for labels
  281 |         const inputs = await page.locator('input:not([type="hidden"]), select, textarea').all();
  282 |
  283 |         for (const input of inputs) {
  284 |           // Check for associated label or aria-label
  285 |           const inputId = await input.getAttribute('id');
  286 |           let hasAccessibleLabel = false;
  287 |
  288 |           if (inputId) {
  289 |             // Check for label with matching 'for' attribute
  290 |             const labelCount = await page.locator(`label[for="${inputId}"]`).count();
  291 |             hasAccessibleLabel = labelCount > 0;
  292 |           }
  293 |
  294 |           // Check for aria-label or aria-labelledby if no label element
  295 |           if (!hasAccessibleLabel) {
  296 |             const ariaLabel = await input.getAttribute('aria-label');
  297 |             const ariaLabelledBy = await input.getAttribute('aria-labelledby');
  298 |             hasAccessibleLabel = !!(ariaLabel || ariaLabelledBy);
  299 |           }
  300 |
  301 |           // Check for placeholder as fallback (not ideal for accessibility)
  302 |           if (!hasAccessibleLabel) {
  303 |             const placeholder = await input.getAttribute('placeholder');
  304 |             hasAccessibleLabel = !!placeholder;
  305 |           }
  306 |
  307 |           expect(hasAccessibleLabel).toBe(true, 'Form controls should have accessible labels');
  308 |         }
  309 |       }
  310 |     });
  311 |   });
  312 |
  313 |   test('registration page should be accessible', async ({ page }) => {
  314 |     // Navigate to the registration page
  315 |     await page.goto('/register');
  316 |
  317 |     // Run axe accessibility tests
  318 |     const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
  319 |
  320 |     // Check for violations
> 321 |     expect(accessibilityScanResults.violations).toEqual([]);
      |                                                 ^ Error: expect(received).toEqual(expected) // deep equality
  322 |   });
  323 |
  324 |   test('should support keyboard navigation', async ({ page }) => {
  325 |     // Navigate to the home page
  326 |     await page.goto('/');
  327 |
  328 |     // Focus on the first focusable element (usually the logo or skip link)
  329 |     await page.keyboard.press('Tab');
  330 |
  331 |     // Check if an element has focus
  332 |     const focusedElement = await page.evaluate(() => document.activeElement.tagName);
  333 |     expect(focusedElement).not.toBe('BODY');
  334 |
  335 |     // Navigate through main navigation using keyboard
  336 |     for (let i = 0; i < 5; i++) {
  337 |       await page.keyboard.press('Tab');
  338 |     }
  339 |
  340 |     // Check if navigation link has focus
  341 |     const navLinkFocused = await page.evaluate(() => {
  342 |       const active = document.activeElement;
  343 |       return active.tagName === 'A' && active.closest('nav') !== null;
  344 |     });
  345 |
  346 |     expect(navLinkFocused).toBe(true);
  347 |
  348 |     // Navigate to search input
  349 |     for (let i = 0; i < 10; i++) {
  350 |       await page.keyboard.press('Tab');
  351 |
  352 |       // Check if search input has focus
  353 |       const isSearchFocused = await page.evaluate(() => {
  354 |         return document.activeElement.getAttribute('placeholder')?.includes('rent');
  355 |       });
  356 |
  357 |       if (isSearchFocused) {
  358 |         break;
  359 |       }
  360 |     }
  361 |
  362 |     // Type in search input
  363 |     await page.keyboard.type('test search');
  364 |
  365 |     // Navigate to search button
  366 |     await page.keyboard.press('Tab');
  367 |
  368 |     // Check if search button has focus
  369 |     const searchButtonFocused = await page.evaluate(() => {
  370 |       const active = document.activeElement;
  371 |       return active.tagName === 'BUTTON' && active.textContent?.includes('Search');
  372 |     });
  373 |
  374 |     expect(searchButtonFocused).toBe(true);
  375 |
  376 |     // Activate search button
  377 |     await page.keyboard.press('Enter');
  378 |
  379 |     // Should navigate to search page
  380 |     await expect(page).toHaveURL(/\/search\?q=test\+search/);
  381 |   });
  382 |
  383 |   test('interactive elements should have proper ARIA attributes', async ({ page }) => {
  384 |     // Navigate to the home page
  385 |     await page.goto('/');
  386 |
  387 |     // Check buttons have proper attributes
  388 |     const buttons = await page.locator('button, [role="button"]').all();
  389 |
  390 |     for (const button of buttons) {
  391 |       // Check for aria-label or accessible name
  392 |       const hasAccessibleName = await button.evaluate((el) => {
  393 |         return el.textContent?.trim() !== '' ||
  394 |                el.getAttribute('aria-label') !== null ||
  395 |                el.getAttribute('title') !== null;
  396 |       });
  397 |
  398 |       expect(hasAccessibleName).toBe(true);
  399 |     }
  400 |
  401 |     // Check form inputs have proper labels
  402 |     const inputs = await page.locator('input:not([type="hidden"]), select, textarea').all();
  403 |
  404 |     for (const input of inputs) {
  405 |       // Check for associated label or aria-label
  406 |       const hasLabel = await input.evaluate((el) => {
  407 |         const id = el.getAttribute('id');
  408 |         return id && document.querySelector(`label[for="${id}"]`) !== null ||
  409 |                el.getAttribute('aria-label') !== null ||
  410 |                el.getAttribute('aria-labelledby') !== null;
  411 |       });
  412 |
  413 |       expect(hasLabel).toBe(true);
  414 |     }
  415 |   });
  416 |
  417 |   test('images should have alt text', async ({ page }) => {
  418 |     // Navigate to the home page
  419 |     await page.goto('/');
  420 |
  421 |     // Check all images have alt text
```