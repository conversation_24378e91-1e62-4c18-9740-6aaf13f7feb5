# Test info

- Name: Agreement Components >> should be able to change pen color and thickness
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/agreement-components.spec.js:68:7

# Error details

```
Error: page.waitForSelector: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('h2:has-text("Sign Agreement")') to be visible

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/agreement-components.spec.js:73:16
```

# Page snapshot

```yaml
- img
- heading "Something went wrong" [level=2]
- paragraph: We're sorry, but an error occurred while rendering this page. Our team has been notified.
- button "Reload Page"
- link "Go to Home":
  - /url: /
```

# Test source

```ts
   1 | // @ts-check
   2 | import { test, expect } from '@playwright/test';
   3 |
   4 | test.describe('Agreement Components', () => {
   5 |   test('should render the signature capture component', async ({ page }) => {
   6 |     // Navigate to the agreement sign page
   7 |     await page.goto('/agreements/agreement-1/sign');
   8 |     
   9 |     // Wait for the page to load
   10 |     await page.waitForSelector('h2:has-text("Sign Agreement")');
   11 |     
   12 |     // Check if signature canvas is rendered
   13 |     const signatureCanvas = await page.locator('canvas').first();
   14 |     await expect(signatureCanvas).toBeVisible();
   15 |     
   16 |     // Check if pen color options are rendered
   17 |     const colorButtons = await page.locator('button[title]').filter({ hasText: '' });
   18 |     expect(await colorButtons.count()).toBeGreaterThan(0);
   19 |     
   20 |     // Check if pen thickness options are rendered
   21 |     const thicknessButtons = await page.locator('button').filter({ hasText: /^[1-9]$/ });
   22 |     expect(await thicknessButtons.count()).toBeGreaterThan(0);
   23 |     
   24 |     // Check if clear and confirm buttons are rendered
   25 |     await expect(page.locator('button:has-text("Clear")')).toBeVisible();
   26 |     await expect(page.locator('button:has-text("Confirm Signature")')).toBeVisible();
   27 |   });
   28 |
   29 |   test('should be able to draw on the signature canvas', async ({ page }) => {
   30 |     // Navigate to the agreement sign page
   31 |     await page.goto('/agreements/agreement-1/sign');
   32 |     
   33 |     // Wait for the page to load
   34 |     await page.waitForSelector('h2:has-text("Sign Agreement")');
   35 |     
   36 |     // Get the signature canvas
   37 |     const signatureCanvas = await page.locator('canvas').first();
   38 |     
   39 |     // Get the bounding box of the canvas
   40 |     const boundingBox = await signatureCanvas.boundingBox();
   41 |     if (!boundingBox) {
   42 |       throw new Error('Canvas bounding box not found');
   43 |     }
   44 |     
   45 |     // Draw a signature on the canvas
   46 |     await page.mouse.move(
   47 |       boundingBox.x + boundingBox.width / 4,
   48 |       boundingBox.y + boundingBox.height / 2
   49 |     );
   50 |     await page.mouse.down();
   51 |     await page.mouse.move(
   52 |       boundingBox.x + boundingBox.width / 2,
   53 |       boundingBox.y + boundingBox.height / 4,
   54 |       { steps: 5 }
   55 |     );
   56 |     await page.mouse.move(
   57 |       boundingBox.x + boundingBox.width * 3 / 4,
   58 |       boundingBox.y + boundingBox.height / 2,
   59 |       { steps: 5 }
   60 |     );
   61 |     await page.mouse.up();
   62 |     
   63 |     // Check if the confirm button is enabled after drawing
   64 |     const confirmButton = await page.locator('button:has-text("Confirm Signature")');
   65 |     await expect(confirmButton).not.toBeDisabled();
   66 |   });
   67 |
   68 |   test('should be able to change pen color and thickness', async ({ page }) => {
   69 |     // Navigate to the agreement sign page
   70 |     await page.goto('/agreements/agreement-1/sign');
   71 |     
   72 |     // Wait for the page to load
>  73 |     await page.waitForSelector('h2:has-text("Sign Agreement")');
      |                ^ Error: page.waitForSelector: Test timeout of 30000ms exceeded.
   74 |     
   75 |     // Get the blue color button and click it
   76 |     const blueColorButton = await page.locator('button[title="Blue"]');
   77 |     await blueColorButton.click();
   78 |     
   79 |     // Get the thickness button for 5px and click it
   80 |     const thicknessButton = await page.locator('button:has-text("5")');
   81 |     await thicknessButton.click();
   82 |     
   83 |     // Check if the buttons have the selected state
   84 |     await expect(blueColorButton).toHaveClass(/ring-2/);
   85 |     await expect(thicknessButton).toHaveClass(/bg-primary/);
   86 |   });
   87 |
   88 |   test('should be able to clear the signature', async ({ page }) => {
   89 |     // Navigate to the agreement sign page
   90 |     await page.goto('/agreements/agreement-1/sign');
   91 |     
   92 |     // Wait for the page to load
   93 |     await page.waitForSelector('h2:has-text("Sign Agreement")');
   94 |     
   95 |     // Get the signature canvas
   96 |     const signatureCanvas = await page.locator('canvas').first();
   97 |     
   98 |     // Get the bounding box of the canvas
   99 |     const boundingBox = await signatureCanvas.boundingBox();
  100 |     if (!boundingBox) {
  101 |       throw new Error('Canvas bounding box not found');
  102 |     }
  103 |     
  104 |     // Draw a signature on the canvas
  105 |     await page.mouse.move(
  106 |       boundingBox.x + boundingBox.width / 4,
  107 |       boundingBox.y + boundingBox.height / 2
  108 |     );
  109 |     await page.mouse.down();
  110 |     await page.mouse.move(
  111 |       boundingBox.x + boundingBox.width * 3 / 4,
  112 |       boundingBox.y + boundingBox.height / 2,
  113 |       { steps: 5 }
  114 |     );
  115 |     await page.mouse.up();
  116 |     
  117 |     // Check if the confirm button is enabled after drawing
  118 |     const confirmButton = await page.locator('button:has-text("Confirm Signature")');
  119 |     await expect(confirmButton).not.toBeDisabled();
  120 |     
  121 |     // Click the clear button
  122 |     await page.locator('button:has-text("Clear")').click();
  123 |     
  124 |     // Check if the confirm button is disabled after clearing
  125 |     await expect(confirmButton).toBeDisabled();
  126 |   });
  127 |
  128 |   test('should be able to submit the signature', async ({ page }) => {
  129 |     // Navigate to the agreement sign page
  130 |     await page.goto('/agreements/agreement-1/sign');
  131 |     
  132 |     // Wait for the page to load
  133 |     await page.waitForSelector('h2:has-text("Sign Agreement")');
  134 |     
  135 |     // Get the signature canvas
  136 |     const signatureCanvas = await page.locator('canvas').first();
  137 |     
  138 |     // Get the bounding box of the canvas
  139 |     const boundingBox = await signatureCanvas.boundingBox();
  140 |     if (!boundingBox) {
  141 |       throw new Error('Canvas bounding box not found');
  142 |     }
  143 |     
  144 |     // Draw a signature on the canvas
  145 |     await page.mouse.move(
  146 |       boundingBox.x + boundingBox.width / 4,
  147 |       boundingBox.y + boundingBox.height / 2
  148 |     );
  149 |     await page.mouse.down();
  150 |     await page.mouse.move(
  151 |       boundingBox.x + boundingBox.width * 3 / 4,
  152 |       boundingBox.y + boundingBox.height / 2,
  153 |       { steps: 5 }
  154 |     );
  155 |     await page.mouse.up();
  156 |     
  157 |     // Check the agreement checkbox
  158 |     await page.locator('input[type="checkbox"]').check();
  159 |     
  160 |     // Click the confirm signature button
  161 |     await page.locator('button:has-text("Confirm Signature")').click();
  162 |     
  163 |     // Click the submit button
  164 |     await page.locator('button:has-text("Sign Agreement")').click();
  165 |     
  166 |     // Check if the success message is shown
  167 |     await page.waitForSelector('text=Agreement signed successfully');
  168 |   });
  169 | });
  170 |
```