# Test info

- Name: Responsive Design >> user dashboard should be responsive on desktop
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/responsive.test.js:377:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/responsive.test.js:387:16
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - img "rentUP Logo"
  - text: The Community Marketplace for Endless Shared Possibilities Rent anything from tools to spaces, or list your items to earn extra income. Join our community of sharers today!
  - heading "Log In" [level=1]
  - paragraph: Welcome to rentUP
  - text: Password QR Code Email
  - textbox "Email": <EMAIL>
  - text: Password
  - textbox "Password": Password123!
  - button "Show password":
    - img
  - checkbox "Remember me"
  - text: Remember me
  - link "Forgot password?":
    - /url: /forgot-password
  - button "LOG IN"
  - text: OR
  - button "Sign in with Google":
    - img
    - text: Sign in with Google
  - iframe
  - button "Facebook":
    - img
    - text: Facebook
  - button "Sign in with Apple":
    - img
    - text: Sign in with Apple
  - paragraph:
    - text: New to rentUP?
    - link "Sign up":
      - /url: /register
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
  287 |   test('item details page should be responsive on mobile', async ({ page }) => {
  288 |     // Set viewport to mobile size
  289 |     await page.setViewportSize(viewports.mobile);
  290 |
  291 |     // Navigate to a sample item details page
  292 |     await page.goto('/items/sample-item-123');
  293 |
  294 |     // Check if gallery is properly sized
  295 |     const gallery = await page.locator('.photo-gallery');
  296 |     const galleryWidth = await gallery.evaluate(el => el.clientWidth);
  297 |     expect(galleryWidth).toBeLessThan(viewports.mobile.width);
  298 |
  299 |     // Check if thumbnail navigation is horizontal scrolling
  300 |     const thumbnails = await page.locator('.gallery-thumbnails');
  301 |     const thumbnailsOverflow = await thumbnails.evaluate(el => window.getComputedStyle(el).overflowX);
  302 |     expect(['auto', 'scroll'].includes(thumbnailsOverflow)).toBe(true);
  303 |
  304 |     // Check if item details are stacked
  305 |     const itemTitle = await page.getByRole('heading', { level: 1 });
  306 |     const itemPrice = await page.locator('.item-price');
  307 |
  308 |     const titleBounds = await itemTitle.boundingBox();
  309 |     const priceBounds = await itemPrice.boundingBox();
  310 |
  311 |     expect(priceBounds.y).toBeGreaterThan(titleBounds.y);
  312 |   });
  313 |
  314 |   test('item details page should be responsive on desktop', async ({ page }) => {
  315 |     // Set viewport to desktop size
  316 |     await page.setViewportSize(viewports.desktop);
  317 |
  318 |     // Navigate to a sample item details page
  319 |     await page.goto('/items/sample-item-123');
  320 |
  321 |     // Check if gallery and details are side by side
  322 |     const gallery = await page.locator('.photo-gallery');
  323 |     const details = await page.locator('.item-details');
  324 |
  325 |     const galleryBounds = await gallery.boundingBox();
  326 |     const detailsBounds = await details.boundingBox();
  327 |
  328 |     // Check if gallery is on the left and details on the right
  329 |     expect(galleryBounds.x + galleryBounds.width).toBeLessThanOrEqual(detailsBounds.x + 20);
  330 |
  331 |     // Check if they are roughly on the same vertical position
  332 |     expect(Math.abs(galleryBounds.y - detailsBounds.y)).toBeLessThan(50);
  333 |   });
  334 |
  335 |   test('user dashboard should be responsive on mobile', async ({ page }) => {
  336 |     // Login first
  337 |     await page.goto('/login');
  338 |
  339 |     await page.fill('input[name="email"]', '<EMAIL>');
  340 |     await page.fill('input[name="password"]', 'Password123!');
  341 |
  342 |     await page.click('button[type="submit"]');
  343 |
  344 |     // Wait for dashboard to load
  345 |     await page.waitForURL('/dashboard');
  346 |
  347 |     // Set viewport to mobile size
  348 |     await page.setViewportSize(viewports.mobile);
  349 |
  350 |     // Check if sidebar is hidden by default
  351 |     const sidebar = await page.locator('.dashboard-sidebar');
  352 |     await expect(sidebar).not.toBeVisible();
  353 |
  354 |     // Check if sidebar toggle button is visible
  355 |     const sidebarToggle = await page.locator('.sidebar-toggle');
  356 |     await expect(sidebarToggle).toBeVisible();
  357 |
  358 |     // Open sidebar
  359 |     await sidebarToggle.click();
  360 |
  361 |     // Check if sidebar is now visible
  362 |     await expect(sidebar).toBeVisible();
  363 |
  364 |     // Check if dashboard cards are stacked
  365 |     const dashboardCards = await page.locator('.dashboard-card').all();
  366 |     if (dashboardCards.length > 1) {
  367 |       const firstCard = dashboardCards[0];
  368 |       const secondCard = dashboardCards[1];
  369 |
  370 |       const firstCardBounds = await firstCard.boundingBox();
  371 |       const secondCardBounds = await secondCard.boundingBox();
  372 |
  373 |       expect(secondCardBounds.y).toBeGreaterThan(firstCardBounds.y + firstCardBounds.height - 10);
  374 |     }
  375 |   });
  376 |
  377 |   test('user dashboard should be responsive on desktop', async ({ page }) => {
  378 |     // Login first
  379 |     await page.goto('/login');
  380 |
  381 |     await page.fill('input[name="email"]', '<EMAIL>');
  382 |     await page.fill('input[name="password"]', 'Password123!');
  383 |
  384 |     await page.click('button[type="submit"]');
  385 |
  386 |     // Wait for dashboard to load
> 387 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
  388 |
  389 |     // Set viewport to desktop size
  390 |     await page.setViewportSize(viewports.desktop);
  391 |
  392 |     // Check if sidebar is visible by default
  393 |     const sidebar = await page.locator('.dashboard-sidebar');
  394 |     await expect(sidebar).toBeVisible();
  395 |
  396 |     // Check if sidebar toggle button is not visible
  397 |     const sidebarToggle = await page.locator('.sidebar-toggle').count();
  398 |     expect(sidebarToggle).toBe(0);
  399 |
  400 |     // Check if dashboard cards are in a grid
  401 |     const dashboardCards = await page.locator('.dashboard-card').all();
  402 |     if (dashboardCards.length > 2) {
  403 |       const firstCard = dashboardCards[0];
  404 |       const secondCard = dashboardCards[1];
  405 |
  406 |       const firstCardBounds = await firstCard.boundingBox();
  407 |       const secondCardBounds = await secondCard.boundingBox();
  408 |
  409 |       // Check if cards are side by side
  410 |       expect(Math.abs(firstCardBounds.y - secondCardBounds.y)).toBeLessThan(10);
  411 |     }
  412 |   });
  413 |
  414 |   test('typography should be responsive', async ({ page }) => {
  415 |     // Test on mobile
  416 |     await page.setViewportSize(viewports.mobile);
  417 |     await page.goto('/');
  418 |
  419 |     const mobileHeading = await page.getByRole('heading', { level: 1 });
  420 |     const mobileFontSize = await mobileHeading.evaluate(el => parseInt(window.getComputedStyle(el).fontSize));
  421 |
  422 |     // Test on desktop
  423 |     await page.setViewportSize(viewports.desktop);
  424 |     await page.goto('/');
  425 |
  426 |     const desktopHeading = await page.getByRole('heading', { level: 1 });
  427 |     const desktopFontSize = await desktopHeading.evaluate(el => parseInt(window.getComputedStyle(el).fontSize));
  428 |
  429 |     // Desktop heading should be larger than mobile heading
  430 |     expect(desktopFontSize).toBeGreaterThan(mobileFontSize);
  431 |   });
  432 |
  433 |   test('images should be responsive', async ({ page }) => {
  434 |     // Test on mobile
  435 |     await page.setViewportSize(viewports.mobile);
  436 |     await page.goto('/');
  437 |
  438 |     const mobileImages = await page.locator('img').all();
  439 |
  440 |     for (const img of mobileImages) {
  441 |       const hasSrcset = await img.evaluate(el => el.hasAttribute('srcset') || el.parentElement.style.backgroundImage !== '');
  442 |       const hasResponsiveClass = await img.evaluate(el => {
  443 |         return el.classList.contains('img-fluid') ||
  444 |                el.classList.contains('responsive-img') ||
  445 |                window.getComputedStyle(el).maxWidth === '100%';
  446 |       });
  447 |
  448 |       expect(hasSrcset || hasResponsiveClass).toBe(true);
  449 |     }
  450 |   });
  451 | });
  452 |
```