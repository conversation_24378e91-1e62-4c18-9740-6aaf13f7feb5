# Test info

- Name: Home Page >> should display hero section with search functionality
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/home.test.js:47:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('input[placeholder*="rent" i]') resolved to 2 elements:
    1) <input value="" type="search" id="desktop-search" aria-label="Search for items to rent" placeholder="Search for items to rent..." class="w-full h-full py-2 px-4 pr-10 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-inner"/> aka getByRole('searchbox', { name: 'Search for items to rent' })
    2) <input value="" type="search" id="mobile-search" aria-label="Search for items to rent" placeholder="Search for items to rent..." class="w-full py-2 px-4 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"/> aka getByLabel('Mobile site search').getByPlaceholder('Search for items to rent...')

Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('input[placeholder*="rent" i]')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/home.test.js:79:33
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: 84 • Very Good Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 4 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: New 81 • Very Good Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: New 89 • Very Good Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day
  - img
  - text: "Eastside • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: New 83 • Very Good Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day
  - img
  - text: "Northside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | /**
   2 |  * Home Page Tests
   3 |  *
   4 |  * These tests verify the functionality of the home page using the Page Object Model pattern.
   5 |  * This approach improves maintainability, reusability, and readability of tests.
   6 |  * 
   7 |  * This file consolidates functionality from:
   8 |  * - home.spec.ts
   9 |  * - home-simple.spec.js
   10 |  * - home-pom.spec.js
   11 |  */
   12 |
   13 | import { test, expect } from '@playwright/test';
   14 | import { HomePage } from './page-objects/HomePage';
   15 |
   16 | // Utility function to find an element using multiple selector strategies
   17 | async function findElement(page, selectors) {
   18 |   const selectorArray = Array.isArray(selectors) ? selectors : [selectors];
   19 |
   20 |   for (const selector of selectorArray) {
   21 |     try {
   22 |       const element = page.locator(selector);
   23 |       const count = await element.count();
   24 |
   25 |       if (count === 1) {
   26 |         return element;
   27 |       } else if (count > 1) {
   28 |         // If multiple elements match, return the first one
   29 |         return element.first();
   30 |       }
   31 |     } catch (error) {
   32 |       console.log(`Error finding element with selector "${selector}": ${error.message}`);
   33 |     }
   34 |   }
   35 |
   36 |   return null;
   37 | }
   38 |
   39 | test.describe('Home Page', () => {
   40 |   let homePage;
   41 |
   42 |   test.beforeEach(async ({ page }) => {
   43 |     homePage = new HomePage(page);
   44 |     await homePage.goto();
   45 |   });
   46 |
   47 |   test('should display hero section with search functionality', async ({ page }) => {
   48 |     // Check hero title using Page Object Model
   49 |     const heroTitle = await homePage.getHeroTitle();
   50 |     
   51 |     if (heroTitle) {
   52 |       await expect(heroTitle).toBeVisible({ timeout: 5000 });
   53 |       console.log('Hero title is visible');
   54 |     } else {
   55 |       // Fallback to direct selectors if POM method fails
   56 |       const heroTitleSelectors = [
   57 |         'h1:has-text("Community Marketplace")',
   58 |         'h1:has-text("Sharing Possibilities")',
   59 |         'h1:has-text("rentUP")',
   60 |         'h1',
   61 |         '[class*="hero"] h1',
   62 |         '[class*="hero"] [class*="title"]'
   63 |       ];
   64 |
   65 |       const fallbackHeroTitle = await findElement(page, heroTitleSelectors);
   66 |       
   67 |       if (fallbackHeroTitle) {
   68 |         await expect(fallbackHeroTitle).toBeVisible();
   69 |         console.log('Hero title found using fallback selectors');
   70 |       } else {
   71 |         console.log('Hero title not found, but this is not critical');
   72 |       }
   73 |     }
   74 |     
   75 |     // Check search input and button using Page Object Model
   76 |     const searchInput = await homePage.getSearchInput();
   77 |     
   78 |     if (searchInput) {
>  79 |       await expect(searchInput).toBeVisible({ timeout: 5000 });
      |                                 ^ Error: expect.toBeVisible: Error: strict mode violation: locator('input[placeholder*="rent" i]') resolved to 2 elements:
   80 |       console.log('Search input is visible');
   81 |       
   82 |       // Test search functionality
   83 |       await searchInput.fill('test item');
   84 |       expect(await searchInput.inputValue()).toBe('test item', 'Search input should accept text');
   85 |       
   86 |       // Check search button
   87 |       const searchButton = await homePage.getSearchButton();
   88 |       
   89 |       if (searchButton) {
   90 |         await expect(searchButton).toBeVisible({ timeout: 5000 });
   91 |         console.log('Search button is visible');
   92 |       } else {
   93 |         console.log('Search button not found, but this is not critical');
   94 |       }
   95 |     } else {
   96 |       // Fallback to direct selectors if POM method fails
   97 |       const searchInputSelectors = [
   98 |         'input[placeholder*="rent" i]',
   99 |         'input[placeholder*="search" i]',
  100 |         'input[type="search"]',
  101 |         'input[type="text"]',
  102 |         '[class*="search"] input'
  103 |       ];
  104 |
  105 |       const fallbackSearchInput = await findElement(page, searchInputSelectors);
  106 |       
  107 |       if (fallbackSearchInput) {
  108 |         await expect(fallbackSearchInput).toBeVisible();
  109 |         console.log('Search input found using fallback selectors');
  110 |         
  111 |         // Test search functionality
  112 |         await fallbackSearchInput.fill('test item');
  113 |         expect(await fallbackSearchInput.inputValue()).toBe('test item');
  114 |       } else {
  115 |         console.log('Search input not found, but this is not critical');
  116 |       }
  117 |     }
  118 |   });
  119 |
  120 |   test('should display category showcase section if implemented', async ({ page }) => {
  121 |     // Check category title using Page Object Model
  122 |     const categoryTitle = await homePage.findElement(homePage.selectors.categoryTitle);
  123 |     
  124 |     if (categoryTitle) {
  125 |       await expect(categoryTitle).toBeVisible({ timeout: 5000 });
  126 |       console.log('Category title is visible');
  127 |       
  128 |       // Check category cards
  129 |       const categoryCards = await homePage.getCategoryCards();
  130 |       
  131 |       if (categoryCards) {
  132 |         console.log('Category cards are present');
  133 |       } else {
  134 |         console.log('Category cards not found, but this is not critical');
  135 |       }
  136 |     } else {
  137 |       // Fallback to direct selectors if POM method fails
  138 |       const categoryTitleSelectors = [
  139 |         'h2:has-text("Browse by Category")',
  140 |         'h2:has-text("Categories")',
  141 |         'h2:has-text("Explore Categories")',
  142 |         '[class*="category"] h2',
  143 |         'h2'
  144 |       ];
  145 |
  146 |       const fallbackCategoryTitle = await findElement(page, categoryTitleSelectors);
  147 |       
  148 |       if (fallbackCategoryTitle) {
  149 |         await expect(fallbackCategoryTitle).toBeVisible();
  150 |         console.log('Category title found using fallback selectors');
  151 |         
  152 |         // Check category cards using multiple selector strategies
  153 |         const categoryCardSelectors = [
  154 |           '[class*="category-card"]',
  155 |           '[class*="category"] a',
  156 |           '[class*="category"] div',
  157 |           'a[href*="category"]',
  158 |           'a[href*="search"]'
  159 |         ];
  160 |
  161 |         const fallbackCategoryCards = await findElement(page, categoryCardSelectors);
  162 |         
  163 |         if (fallbackCategoryCards) {
  164 |           console.log('Category cards found using fallback selectors');
  165 |         } else {
  166 |           console.log('Category cards not found, but this is not critical');
  167 |         }
  168 |       } else {
  169 |         console.log('Category section not found, might not be implemented yet');
  170 |         test.skip();
  171 |       }
  172 |     }
  173 |   });
  174 |
  175 |   test('should display featured items section if implemented', async ({ page }) => {
  176 |     // Check featured items title using Page Object Model
  177 |     const featuredTitle = await homePage.findElement(homePage.selectors.featuredTitle);
  178 |     
  179 |     if (featuredTitle) {
```