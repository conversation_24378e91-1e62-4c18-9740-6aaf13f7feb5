# Test info

- Name: Accessibility >> Automated Accessibility Scans >> login page should be accessible
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/accessibility.test.js:84:9

# Error details

```
Error: 
      Rule: color-contrast (serious impact)
      Description: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
      Help: Elements must meet minimum color contrast ratio thresholds
      Elements: 8 elements affected
      First element: <a class="py-3 text-black hover:text-primary transition-colors font-medium" href="/auctions" data-discover="true">navigation.auctions</a>
    

      Rule: heading-order (moderate impact)
      Description: Ensure the order of headings is semantically correct
      Help: Heading levels should only increase by one
      Elements: 1 elements affected
      First element: <h3 class="text-2xl font-semibold text-black mb-2">Join Our Community</h3>
    

expect(received).toEqual(expected) // deep equality

- Expected  -   1
+ Received  + 334

- Array []
+ Array [
+   Object {
+     "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds",
+     "help": "Elements must meet minimum color contrast ratio thresholds",
+     "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/color-contrast?application=playwright",
+     "id": "color-contrast",
+     "impact": "serious",
+     "nodes": Array [
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/auctions\" data-discover=\"true\">navigation.auctions</a>",
+                 "target": Array [
+                   "a[href$=\"auctions\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/auctions\" data-discover=\"true\">navigation.auctions</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"auctions\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/visualization\" data-discover=\"true\">navigation.aiInsights</a>",
+                 "target": Array [
+                   "a[href$=\"visualization\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/visualization\" data-discover=\"true\">navigation.aiInsights</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"visualization\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/design-system\" data-discover=\"true\">navigation.designSystem</a>",
+                 "target": Array [
+                   "a[href$=\"design-system\"]:nth-child(6)",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/design-system\" data-discover=\"true\">navigation.designSystem</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"design-system\"]:nth-child(6)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/responsive-design-system\" data-discover=\"true\">navigation.responsiveDesign</a>",
+                 "target": Array [
+                   "a[href$=\"responsive-design-system\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/responsive-design-system\" data-discover=\"true\">navigation.responsiveDesign</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"responsive-design-system\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "10.5pt (14px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"forgot-password\" href=\"/forgot-password\" data-discover=\"true\">Forgot password?</a>",
+                 "target": Array [
+                   ".forgot-password",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"forgot-password\" href=\"/forgot-password\" data-discover=\"true\">Forgot password?</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".forgot-password",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#3b82f6",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "11.3pt (15px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #ffffff, background color: #3b82f6, font size: 11.3pt (15px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<button type=\"submit\" class=\"login-button\">LOG IN</button>",
+                 "target": Array [
+                   ".login-button",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #ffffff, background color: #3b82f6, font size: 11.3pt (15px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<button type=\"submit\" class=\"login-button\">LOG IN</button>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".login-button",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#1877f2",
+               "contrastRatio": 4.23,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "10.5pt (14px)",
+               "fontWeight": "bold",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.23 (foreground color: #ffffff, background color: #1877f2, font size: 10.5pt (14px), font weight: bold). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<button type=\"button\" class=\"w-full flex justify-center items-center bg-[#1877F2] text-white py-2 px-4 rounded-md hover:bg-[#166FE5] transition-colors\" style=\"height: 40px; font-size: 14px; font-weight: bold;\">",
+                 "target": Array [
+                   ".bg-\\[\\#1877F2\\]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.23 (foreground color: #ffffff, background color: #1877f2, font size: 10.5pt (14px), font weight: bold). Expected contrast ratio of 4.5:1",
+         "html": "<div class=\"flex items-center\">",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".bg-\\[\\#1877F2\\] > .items-center.flex",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "13.5pt (18px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 13.5pt (18px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"text-primary hover:underline font-medium\" href=\"/register\" data-discover=\"true\">Sign up</a>",
+                 "target": Array [
+                   ".hover\\:underline",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 13.5pt (18px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"text-primary hover:underline font-medium\" href=\"/register\" data-discover=\"true\">Sign up</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".hover\\:underline",
+         ],
+       },
+     ],
+     "tags": Array [
+       "cat.color",
+       "wcag2aa",
+       "wcag143",
+       "TTv5",
+       "TT13.c",
+       "EN-301-549",
+       "EN-*******",
+       "ACT",
+     ],
+   },
+   Object {
+     "description": "Ensure the order of headings is semantically correct",
+     "help": "Heading levels should only increase by one",
+     "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/heading-order?application=playwright",
+     "id": "heading-order",
+     "impact": "moderate",
+     "nodes": Array [
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": null,
+             "id": "heading-order",
+             "impact": "moderate",
+             "message": "Heading order invalid",
+             "relatedNodes": Array [],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Heading order invalid",
+         "html": "<h3 class=\"text-2xl font-semibold text-black mb-2\">Join Our Community</h3>",
+         "impact": "moderate",
+         "none": Array [],
+         "target": Array [
+           "h3",
+         ],
+       },
+     ],
+     "tags": Array [
+       "cat.semantics",
+       "best-practice",
+     ],
+   },
+ ]
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/accessibility.test.js:86:72
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - img "rentUP Logo"
  - text: The Community Marketplace for Endless Shared Possibilities Rent anything from tools to spaces, or list your items to earn extra income. Join our community of sharers today!
  - heading "Log In" [level=1]
  - paragraph: Welcome to rentUP
  - text: Password QR Code Email
  - textbox "Email"
  - text: Password
  - textbox "Password"
  - button "Show password":
    - img
  - checkbox "Remember me"
  - text: Remember me
  - link "Forgot password?":
    - /url: /forgot-password
  - button "LOG IN"
  - text: OR
  - button "Sign in with Google":
    - img
    - text: Sign in with Google
  - iframe
  - button "Facebook":
    - img
    - text: Facebook
  - button "Sign in with Apple":
    - img
    - text: Sign in with Apple
  - paragraph:
    - text: New to rentUP?
    - link "Sign up":
      - /url: /register
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | /**
   2 |  * Enhanced Accessibility Tests
   3 |  *
   4 |  * Comprehensive tests for the accessibility features of the RentUp application.
   5 |  * Based on WCAG 2.1 AA standards, sitemap.md and README.md requirements.
   6 |  *
   7 |  * These tests check for:
   8 |  * - Automated accessibility violations using axe-core
   9 |  * - Proper ARIA attributes and roles
   10 |  * - Keyboard navigation
   11 |  * - Color contrast
   12 |  * - Focus management
   13 |  * - Screen reader support
   14 |  */
   15 |
   16 | import { test, expect } from '@playwright/test';
   17 | import AxeBuilder from '@axe-core/playwright';
   18 |
   19 | // Helper function to format accessibility violations for better readability
   20 | function formatViolations(violations) {
   21 |   if (violations.length === 0) return 'No violations found';
   22 |
   23 |   return violations.map(violation => {
   24 |     return `
   25 |       Rule: ${violation.id} (${violation.impact} impact)
   26 |       Description: ${violation.description}
   27 |       Help: ${violation.help}
   28 |       Elements: ${violation.nodes.length} elements affected
   29 |       First element: ${violation.nodes[0]?.html || 'N/A'}
   30 |     `;
   31 |   }).join('\n');
   32 | }
   33 |
   34 | // Helper function to run accessibility tests on a page
   35 | async function runAccessibilityTests(page, url, options = {}) {
   36 |   // Navigate to the page
   37 |   await page.goto(url);
   38 |   await page.waitForLoadState('networkidle');
   39 |
   40 |   // Configure axe builder with options
   41 |   let axeBuilder = new AxeBuilder({ page });
   42 |
   43 |   // Add specific rules if provided
   44 |   if (options.rules) {
   45 |     axeBuilder = axeBuilder.include(options.rules);
   46 |   }
   47 |
   48 |   // Add specific context if provided
   49 |   if (options.context) {
   50 |     axeBuilder = axeBuilder.include(options.context);
   51 |   }
   52 |
   53 |   // Run axe accessibility tests
   54 |   const accessibilityScanResults = await axeBuilder.analyze();
   55 |
   56 |   // Log detailed information about violations for debugging
   57 |   if (accessibilityScanResults.violations.length > 0) {
   58 |     console.log(`Accessibility violations on ${url}:\n${formatViolations(accessibilityScanResults.violations)}`);
   59 |   }
   60 |
   61 |   // Return the results
   62 |   return accessibilityScanResults;
   63 | }
   64 |
   65 | // Accessibility tests
   66 | test.describe('Accessibility', () => {
   67 |   test.describe('Automated Accessibility Scans', () => {
   68 |     test('home page should be accessible', async ({ page }) => {
   69 |       const results = await runAccessibilityTests(page, '/');
   70 |       expect(results.violations, formatViolations(results.violations)).toEqual([]);
   71 |     });
   72 |
   73 |     test('search page should be accessible', async ({ page }) => {
   74 |       const results = await runAccessibilityTests(page, '/search');
   75 |       expect(results.violations, formatViolations(results.violations)).toEqual([]);
   76 |     });
   77 |
   78 |     test('item details page should be accessible', async ({ page }) => {
   79 |       // Try to use a real item ID if possible, fallback to sample
   80 |       const results = await runAccessibilityTests(page, '/items/sample-item-123');
   81 |       expect(results.violations, formatViolations(results.violations)).toEqual([]);
   82 |     });
   83 |
   84 |     test('login page should be accessible', async ({ page }) => {
   85 |       const results = await runAccessibilityTests(page, '/login');
>  86 |       expect(results.violations, formatViolations(results.violations)).toEqual([]);
      |                                                                        ^ Error: 
   87 |     });
   88 |   });
   89 |
   90 |   test.describe('ARIA and Semantic HTML', () => {
   91 |     test('should use proper heading structure', async ({ page }) => {
   92 |       await page.goto('/');
   93 |       await page.waitForLoadState('networkidle');
   94 |
   95 |       // Get all headings
   96 |       const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
   97 |
   98 |       // Check that we have at least one heading
   99 |       expect(headings.length).toBeGreaterThan(0, 'Page should have at least one heading');
  100 |
  101 |       // Get heading levels
  102 |       const headingLevels = await Promise.all(
  103 |         headings.map(async (heading) => {
  104 |           const tagName = await heading.evaluate(el => el.tagName);
  105 |           return parseInt(tagName.substring(1));
  106 |         })
  107 |       );
  108 |
  109 |       // Check if heading levels are properly nested (no skipping levels)
  110 |       let previousLevel = 0;
  111 |       let hasProperStructure = true;
  112 |       let firstViolation = '';
  113 |
  114 |       for (let i = 0; i < headingLevels.length; i++) {
  115 |         const level = headingLevels[i];
  116 |         if (level > previousLevel + 1) {
  117 |           hasProperStructure = false;
  118 |           const headingText = await headings[i].textContent();
  119 |           firstViolation = `Heading level jumped from h${previousLevel || '(none)'} to h${level} with text: "${headingText}"`;
  120 |           break;
  121 |         }
  122 |         previousLevel = level === 1 ? 1 : previousLevel;
  123 |       }
  124 |
  125 |       expect(hasProperStructure).toBe(true, firstViolation || 'Heading levels should not skip (e.g., h1 to h3)');
  126 |     });
  127 |
  128 |     test('should have proper landmark regions', async ({ page }) => {
  129 |       await page.goto('/');
  130 |       await page.waitForLoadState('networkidle');
  131 |
  132 |       // Check for header landmark
  133 |       const header = await page.locator('header, [role="banner"]');
  134 |       await expect(header).toBeVisible('Page should have a header landmark');
  135 |
  136 |       // Check for main landmark
  137 |       const main = await page.locator('main, [role="main"]');
  138 |       await expect(main).toBeVisible('Page should have a main landmark');
  139 |
  140 |       // Check for footer landmark
  141 |       const footer = await page.locator('footer, [role="contentinfo"]');
  142 |       await expect(footer).toBeVisible('Page should have a footer landmark');
  143 |
  144 |       // Check for navigation landmark
  145 |       const nav = await page.locator('nav, [role="navigation"]');
  146 |       await expect(nav).toBeVisible('Page should have a navigation landmark');
  147 |     });
  148 |
  149 |     test('should have proper document language', async ({ page }) => {
  150 |       await page.goto('/');
  151 |       await page.waitForLoadState('networkidle');
  152 |
  153 |       // Check for lang attribute on html element
  154 |       const html = await page.locator('html');
  155 |       const lang = await html.getAttribute('lang');
  156 |
  157 |       expect(lang).toBeTruthy('HTML element should have a lang attribute');
  158 |       expect(lang.length).toBeGreaterThanOrEqual(2, 'Lang attribute should be at least 2 characters');
  159 |     });
  160 |
  161 |     test('should have proper ARIA attributes on interactive elements', async ({ page }) => {
  162 |       await page.goto('/');
  163 |       await page.waitForLoadState('networkidle');
  164 |
  165 |       // Check buttons for proper ARIA attributes
  166 |       const buttons = await page.locator('button:visible, [role="button"]:visible').all();
  167 |
  168 |       for (const button of buttons) {
  169 |         // Check if button has accessible name
  170 |         const hasText = (await button.textContent())?.trim().length > 0;
  171 |         const hasAriaLabel = await button.getAttribute('aria-label');
  172 |         const hasTitle = await button.getAttribute('title');
  173 |
  174 |         const hasAccessibleName = hasText || hasAriaLabel || hasTitle;
  175 |         expect(hasAccessibleName).toBe(true, 'Button should have an accessible name');
  176 |
  177 |         // Check if button with aria-expanded has proper value
  178 |         const hasAriaExpanded = await button.getAttribute('aria-expanded');
  179 |         if (hasAriaExpanded !== null) {
  180 |           expect(['true', 'false']).toContain(hasAriaExpanded, 'aria-expanded should be "true" or "false"');
  181 |         }
  182 |       }
  183 |     });
  184 |   });
  185 |
  186 |   test.describe('Keyboard Navigation and Focus Management', () => {
```