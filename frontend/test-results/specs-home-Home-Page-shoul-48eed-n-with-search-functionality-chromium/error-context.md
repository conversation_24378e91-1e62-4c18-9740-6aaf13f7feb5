# Test info

- Name: Home Page >> should display hero section with search functionality
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/specs/home.spec.js:19:7

# Error details

```
Error: Hero title should be present

expect(received).not.toBeNull()

Received: null
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/specs/home.spec.js:22:59
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: 91 • Excellent Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: 91 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: New 94 • Excellent Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day
  - img
  - text: "Eastside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: Rent-to-Buy 82 • Very Good Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day RTB
  - img
  - text: "Northside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | /**
   2 |  * Home Page Tests
   3 |  * 
   4 |  * These tests verify the functionality of the home page.
   5 |  * They use the Page Object Model pattern for better organization and maintainability.
   6 |  */
   7 |
   8 | import { test, expect } from '@playwright/test';
   9 | import { HomePage } from '../page-objects/HomePage';
   10 |
   11 | test.describe('Home Page', () => {
   12 |   let homePage;
   13 |
   14 |   test.beforeEach(async ({ page }) => {
   15 |     homePage = new HomePage(page);
   16 |     await homePage.goto();
   17 |   });
   18 |
   19 |   test('should display hero section with search functionality', async () => {
   20 |     // Check hero title
   21 |     const heroTitle = await homePage.getHeroTitle();
>  22 |     expect(heroTitle, 'Hero title should be present').not.toBeNull();
      |                                                           ^ Error: Hero title should be present
   23 |     
   24 |     if (heroTitle) {
   25 |       await expect(heroTitle).toBeVisible({ timeout: 5000 });
   26 |     }
   27 |     
   28 |     // Check search input and button
   29 |     const searchInput = await homePage.getSearchInput();
   30 |     expect(searchInput, 'Search input should be present').not.toBeNull();
   31 |     
   32 |     const searchButton = await homePage.getSearchButton();
   33 |     expect(searchButton, 'Search button should be present').not.toBeNull();
   34 |     
   35 |     // Test search functionality if both input and button exist
   36 |     if (searchInput && searchButton) {
   37 |       // Fill the search input
   38 |       await searchInput.fill('test item');
   39 |       
   40 |       // Verify the input value
   41 |       expect(await searchInput.inputValue()).toBe('test item', 'Search input should accept text');
   42 |       
   43 |       // Click the search button
   44 |       await searchButton.click();
   45 |       
   46 |       // Check if we navigated to the search page
   47 |       const url = homePage.page.url();
   48 |       
   49 |       // We don't want to fail the test if the search functionality isn't fully implemented yet
   50 |       // So we just log the result
   51 |       if (url.includes('/search')) {
   52 |         console.log('Successfully navigated to search page');
   53 |       } else {
   54 |         console.log('Search navigation not implemented yet');
   55 |       }
   56 |     }
   57 |   });
   58 |
   59 |   test('should display category showcase section', async () => {
   60 |     // Check category title
   61 |     const categoryTitle = await homePage.findElement(homePage.selectors.categoryTitle);
   62 |     
   63 |     // If category section exists, proceed with testing
   64 |     if (categoryTitle) {
   65 |       await expect(categoryTitle).toBeVisible({ timeout: 5000 });
   66 |       
   67 |       // Check category cards
   68 |       const categoryCards = await homePage.getCategoryCards();
   69 |       expect(categoryCards, 'Category cards should be present').not.toBeNull();
   70 |       
   71 |       // Test category navigation if cards exist
   72 |       if (categoryCards) {
   73 |         const allCards = await categoryCards.all();
   74 |         
   75 |         if (allCards.length > 0) {
   76 |           // We don't want to fail the test if clicking the category doesn't work yet
   77 |           // So we just log the result
   78 |           const navigationSuccessful = await homePage.clickFirstCategory();
   79 |           
   80 |           if (navigationSuccessful) {
   81 |             console.log('Successfully navigated to category page');
   82 |           } else {
   83 |             console.log('Category navigation not implemented yet');
   84 |             
   85 |             // Navigate back to home page for the next test
   86 |             await homePage.goto();
   87 |           }
   88 |         }
   89 |       }
   90 |     } else {
   91 |       console.log('Category section not found, might not be implemented yet');
   92 |     }
   93 |   });
   94 |
   95 |   test('should display featured items section', async () => {
   96 |     // Check featured items title
   97 |     const featuredTitle = await homePage.findElement(homePage.selectors.featuredTitle);
   98 |     
   99 |     // If featured items section exists, proceed with testing
  100 |     if (featuredTitle) {
  101 |       await expect(featuredTitle).toBeVisible({ timeout: 5000 });
  102 |       
  103 |       // Check item cards
  104 |       const itemCards = await homePage.getItemCards();
  105 |       expect(itemCards, 'Item cards should be present').not.toBeNull();
  106 |       
  107 |       // Test item navigation if cards exist
  108 |       if (itemCards) {
  109 |         const allCards = await itemCards.all();
  110 |         
  111 |         if (allCards.length > 0) {
  112 |           // We don't want to fail the test if clicking the item doesn't work yet
  113 |           // So we just log the result
  114 |           const navigationSuccessful = await homePage.clickFirstItem();
  115 |           
  116 |           if (navigationSuccessful) {
  117 |             console.log('Successfully navigated to item details page');
  118 |           } else {
  119 |             console.log('Item navigation not implemented yet');
  120 |             
  121 |             // Navigate back to home page for the next test
  122 |             await homePage.goto();
```