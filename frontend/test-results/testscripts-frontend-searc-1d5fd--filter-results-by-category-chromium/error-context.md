# Test info

- Name: Search Page >> should filter results by category
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/search.test.js:78:7

# Error details

```
Error: locator.click: Test timeout of 30000ms exceeded.
Call log:
  - waiting for getByText('Tools & Equipment')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/search.test.js:81:25
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - heading "Search" [level=1]
  - paragraph: Find the perfect items to rent using our advanced search capabilities.
  - heading "Multi-Modal Search" [level=2]
  - text: Search by Text
  - textbox "Search by Text"
  - text: Search by Image Upload Image Or Enter Image URL
  - textbox "Or Enter Image URL"
  - text: "Text Weight: 0.7"
  - 'slider "Text Weight: 0.7"': "0.7"
  - text: "Image Weight: 0.3"
  - 'slider "Image Weight: 0.3"': "0.3"
  - button "Search"
  - heading "Search Tips" [level=2]
  - list:
    - listitem: Use specific keywords for better results (e.g., "DSLR camera" instead of just "camera")
    - listitem: Upload a clear image of the item you're looking for
    - listitem: Adjust the weights to prioritize text or image matching
    - listitem: Try different combinations of text and images for more options
  - heading "Popular Categories" [level=2]
  - link "📱 Electronics":
    - /url: /search/results?category=electronics
  - link "🏡 Home & Garden":
    - /url: /search/results?category=home-%26-garden
  - link "🏀 Sports":
    - /url: /search/results?category=sports
  - link "🚗 Vehicles":
    - /url: /search/results?category=vehicles
  - link "👕 Clothing":
    - /url: /search/results?category=clothing
  - link "🔧 Tools":
    - /url: /search/results?category=tools
  - link "📷 Photography":
    - /url: /search/results?category=photography
  - link "🎸 Music":
    - /url: /search/results?category=music
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | /**
   2 |  * Search Page Tests
   3 |  * 
   4 |  * Tests for the search functionality and results page of the RentUp application.
   5 |  * Based on sitemap.md and README.md requirements.
   6 |  */
   7 |
   8 | import { test, expect } from '@playwright/test';
   9 |
   10 | // Search page tests
   11 | test.describe('Search Page', () => {
   12 |   test.beforeEach(async ({ page }) => {
   13 |     // Navigate to the search page before each test
   14 |     await page.goto('/search');
   15 |   });
   16 |
   17 |   test('should display search input and filters', async ({ page }) => {
   18 |     // Check search input exists
   19 |     const searchInput = await page.getByPlaceholder('What would you like to rent today?');
   20 |     await expect(searchInput).toBeVisible();
   21 |     
   22 |     // Check search button exists
   23 |     const searchButton = await page.getByRole('button', { name: 'Search' });
   24 |     await expect(searchButton).toBeVisible();
   25 |     
   26 |     // Check filter sections
   27 |     const filterSections = [
   28 |       'Category',
   29 |       'Location',
   30 |       'Price Range',
   31 |       'Listing Type'
   32 |     ];
   33 |     
   34 |     for (const section of filterSections) {
   35 |       const filterSection = await page.getByText(section, { exact: false });
   36 |       await expect(filterSection).toBeVisible();
   37 |     }
   38 |     
   39 |     // Check category filters
   40 |     const categoryFilters = await page.locator('.category-filter').all();
   41 |     expect(categoryFilters.length).toBeGreaterThan(0);
   42 |     
   43 |     // Check price range filter
   44 |     const priceRangeFilter = await page.locator('.price-range-filter');
   45 |     await expect(priceRangeFilter).toBeVisible();
   46 |     
   47 |     // Check listing type filters
   48 |     const listingTypeFilters = await page.locator('.listing-type-filter').all();
   49 |     expect(listingTypeFilters.length).toBeGreaterThan(0);
   50 |   });
   51 |
   52 |   test('should perform search and display results', async ({ page }) => {
   53 |     // Perform search
   54 |     const searchInput = await page.getByPlaceholder('What would you like to rent today?');
   55 |     await searchInput.fill('power drill');
   56 |     
   57 |     const searchButton = await page.getByRole('button', { name: 'Search' });
   58 |     await searchButton.click();
   59 |     
   60 |     // Check URL has search query
   61 |     await expect(page).toHaveURL(/\/search\?q=power\+drill/);
   62 |     
   63 |     // Check search results are displayed
   64 |     const searchResults = await page.locator('.search-results');
   65 |     await expect(searchResults).toBeVisible();
   66 |     
   67 |     // Check search result items
   68 |     const resultItems = await page.locator('.item-card').all();
   69 |     expect(resultItems.length).toBeGreaterThan(0);
   70 |     
   71 |     // Check result item details
   72 |     const firstItem = await page.locator('.item-card').first();
   73 |     await expect(firstItem.locator('.item-name')).toBeVisible();
   74 |     await expect(firstItem.locator('.item-price')).toBeVisible();
   75 |     await expect(firstItem.locator('.item-location')).toBeVisible();
   76 |   });
   77 |
   78 |   test('should filter results by category', async ({ page }) => {
   79 |     // Select a category filter
   80 |     const toolsCategory = await page.getByText('Tools & Equipment', { exact: false });
>  81 |     await toolsCategory.click();
      |                         ^ Error: locator.click: Test timeout of 30000ms exceeded.
   82 |     
   83 |     // Check URL has category filter
   84 |     await expect(page).toHaveURL(/\/search\?category=tools/i);
   85 |     
   86 |     // Check filtered results
   87 |     const searchResults = await page.locator('.search-results');
   88 |     await expect(searchResults).toBeVisible();
   89 |     
   90 |     // Check result items are from the selected category
   91 |     const resultItems = await page.locator('.item-card').all();
   92 |     expect(resultItems.length).toBeGreaterThan(0);
   93 |     
   94 |     // Check category badge on items
   95 |     const categoryBadges = await page.locator('.category-badge').all();
   96 |     for (const badge of categoryBadges) {
   97 |       await expect(badge).toContainText('Tools', { ignoreCase: true });
   98 |     }
   99 |   });
  100 |
  101 |   test('should filter results by price range', async ({ page }) => {
  102 |     // Set price range filter
  103 |     const minPriceInput = await page.locator('input[name="minPrice"]');
  104 |     const maxPriceInput = await page.locator('input[name="maxPrice"]');
  105 |     
  106 |     await minPriceInput.fill('10');
  107 |     await maxPriceInput.fill('50');
  108 |     
  109 |     const applyFilterButton = await page.getByRole('button', { name: 'Apply' });
  110 |     await applyFilterButton.click();
  111 |     
  112 |     // Check URL has price range filter
  113 |     await expect(page).toHaveURL(/\/search\?.*minPrice=10.*maxPrice=50/);
  114 |     
  115 |     // Check filtered results
  116 |     const searchResults = await page.locator('.search-results');
  117 |     await expect(searchResults).toBeVisible();
  118 |     
  119 |     // Check result items are within price range
  120 |     const resultItems = await page.locator('.item-card').all();
  121 |     expect(resultItems.length).toBeGreaterThan(0);
  122 |     
  123 |     // Check prices on items
  124 |     const itemPrices = await page.locator('.item-price').all();
  125 |     for (const price of itemPrices) {
  126 |       const priceText = await price.textContent();
  127 |       const priceValue = parseFloat(priceText.replace(/[^0-9.]/g, ''));
  128 |       expect(priceValue).toBeGreaterThanOrEqual(10);
  129 |       expect(priceValue).toBeLessThanOrEqual(50);
  130 |     }
  131 |   });
  132 |
  133 |   test('should filter results by listing type', async ({ page }) => {
  134 |     // Select rent-to-buy listing type
  135 |     const rentToBuyFilter = await page.getByText('Rent-to-Buy', { exact: false });
  136 |     await rentToBuyFilter.click();
  137 |     
  138 |     // Check URL has listing type filter
  139 |     await expect(page).toHaveURL(/\/search\?.*listingType=rent-to-buy/);
  140 |     
  141 |     // Check filtered results
  142 |     const searchResults = await page.locator('.search-results');
  143 |     await expect(searchResults).toBeVisible();
  144 |     
  145 |     // Check result items are rent-to-buy listings
  146 |     const resultItems = await page.locator('.item-card').all();
  147 |     expect(resultItems.length).toBeGreaterThan(0);
  148 |     
  149 |     // Check rent-to-buy badge on items
  150 |     const rtbBadges = await page.locator('.rtb-badge').all();
  151 |     expect(rtbBadges.length).toBe(resultItems.length);
  152 |   });
  153 |
  154 |   test('should sort search results', async ({ page }) => {
  155 |     // Open sort dropdown
  156 |     const sortDropdown = await page.locator('.sort-dropdown');
  157 |     await sortDropdown.click();
  158 |     
  159 |     // Select price: low to high
  160 |     const priceLowToHigh = await page.getByText('Price: Low to High');
  161 |     await priceLowToHigh.click();
  162 |     
  163 |     // Check URL has sort parameter
  164 |     await expect(page).toHaveURL(/\/search\?.*sort=price_asc/);
  165 |     
  166 |     // Check sorted results
  167 |     const searchResults = await page.locator('.search-results');
  168 |     await expect(searchResults).toBeVisible();
  169 |     
  170 |     // Check prices are in ascending order
  171 |     const itemPrices = await page.locator('.item-price').all();
  172 |     const prices = [];
  173 |     
  174 |     for (const price of itemPrices) {
  175 |       const priceText = await price.textContent();
  176 |       const priceValue = parseFloat(priceText.replace(/[^0-9.]/g, ''));
  177 |       prices.push(priceValue);
  178 |     }
  179 |     
  180 |     // Check if prices are sorted
  181 |     const sortedPrices = [...prices].sort((a, b) => a - b);
```