# Test info

- Name: Home Page >> should display trust section
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/enhanced-home.test.js:226:7

# Error details

```
Error: expect(received).toBe<PERSON><PERSON><PERSON><PERSON><PERSON>(expected)

Expected: > 0
Received:   0
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/enhanced-home.test.js:259:36
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: 92 • Excellent Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 4 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: 98 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: Rent-to-Buy New 82 • Very Good Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day RTB
  - img
  - text: "Eastside • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: Rent-to-Buy 80 • Very Good Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day RTB
  - img
  - text: "Northside • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
  159 |           // This is likely the category section
  160 |           
  161 |           // Check for category cards or items
  162 |           const categoryItems = await section.locator('a, .card, [class*="category"], [class*="card"]').all();
  163 |           expect(categoryItems.length).toBeGreaterThan(0, 'Category section should contain category items');
  164 |           
  165 |           // Check for "View All" or similar link
  166 |           const viewAllLink = await section.locator('a:text("View All"), a:text("See All"), a:text("Browse All")').first();
  167 |           const hasViewAll = await viewAllLink.count() > 0;
  168 |           
  169 |           if (hasViewAll) {
  170 |             await expect(viewAllLink).toBeVisible('View All link should be visible');
  171 |           }
  172 |           
  173 |           return; // We found and verified the section
  174 |         }
  175 |       }
  176 |     } else {
  177 |       // We found the section with our selectors
  178 |       
  179 |       // Check for category cards or items
  180 |       const categoryItems = await categorySection.locator('a, .card, [class*="category"], [class*="card"]').all();
  181 |       expect(categoryItems.length).toBeGreaterThan(0, 'Category section should contain category items');
  182 |       
  183 |       // Check for "View All" or similar link
  184 |       const viewAllLink = await categorySection.locator('a:text("View All"), a:text("See All"), a:text("Browse All")').first();
  185 |       const hasViewAll = await viewAllLink.count() > 0;
  186 |       
  187 |       if (hasViewAll) {
  188 |         await expect(viewAllLink).toBeVisible('View All link should be visible');
  189 |       }
  190 |     }
  191 |   });
  192 |
  193 |   test('should display featured items section', async ({ page }) => {
  194 |     // Find featured items section using multiple strategies
  195 |     const featuredSection = await findElement(page, {
  196 |       selector: 'section:has(h2:text("Featured"), h2:text("Popular"))',
  197 |       ariaLabel: 'Featured items'
  198 |     });
  199 |     
  200 |     // If we can't find it with specific selectors, try a more general approach
  201 |     if (!featuredSection) {
  202 |       // Look for any section that might be the featured items section
  203 |       const sections = await page.locator('section').all();
  204 |       
  205 |       for (const section of sections) {
  206 |         const headingText = await section.locator('h2').textContent();
  207 |         if (headingText && (headingText.toLowerCase().includes('featured') || headingText.toLowerCase().includes('popular'))) {
  208 |           // This is likely the featured items section
  209 |           
  210 |           // Check for item cards
  211 |           const itemCards = await section.locator('a, .card, [class*="item"], [class*="card"]').all();
  212 |           expect(itemCards.length).toBeGreaterThan(0, 'Featured section should contain item cards');
  213 |           
  214 |           return; // We found and verified the section
  215 |         }
  216 |       }
  217 |     } else {
  218 |       // We found the section with our selectors
  219 |       
  220 |       // Check for item cards
  221 |       const itemCards = await featuredSection.locator('a, .card, [class*="item"], [class*="card"]').all();
  222 |       expect(itemCards.length).toBeGreaterThan(0, 'Featured section should contain item cards');
  223 |     }
  224 |   });
  225 |
  226 |   test('should display trust section', async ({ page }) => {
  227 |     // Find trust section using multiple strategies
  228 |     const trustSection = await findElement(page, {
  229 |       selector: 'section:has(h2:text("Trust"), h2:text("Confidence"), h2:text("Safety"))',
  230 |       ariaLabel: 'Trust features'
  231 |     });
  232 |     
  233 |     // If we can't find it with specific selectors, try a more general approach
  234 |     if (!trustSection) {
  235 |       // Look for any section that might be the trust section
  236 |       const sections = await page.locator('section').all();
  237 |       
  238 |       for (const section of sections) {
  239 |         const headingText = await section.locator('h2').textContent();
  240 |         if (headingText && (
  241 |           headingText.toLowerCase().includes('trust') || 
  242 |           headingText.toLowerCase().includes('confidence') ||
  243 |           headingText.toLowerCase().includes('safety')
  244 |         )) {
  245 |           // This is likely the trust section
  246 |           
  247 |           // Check for trust features
  248 |           const trustFeatures = await section.locator('[class*="feature"], [class*="card"], [class*="trust"]').all();
  249 |           expect(trustFeatures.length).toBeGreaterThan(0, 'Trust section should contain trust features');
  250 |           
  251 |           return; // We found and verified the section
  252 |         }
  253 |       }
  254 |     } else {
  255 |       // We found the section with our selectors
  256 |       
  257 |       // Check for trust features
  258 |       const trustFeatures = await trustSection.locator('[class*="feature"], [class*="card"], [class*="trust"]').all();
> 259 |       expect(trustFeatures.length).toBeGreaterThan(0, 'Trust section should contain trust features');
      |                                    ^ Error: expect(received).toBeGreaterThan(expected)
  260 |     }
  261 |   });
  262 |
  263 |   test('should display call-to-action section', async ({ page }) => {
  264 |     // Find CTA section using multiple strategies
  265 |     const ctaSection = await findElement(page, {
  266 |       selector: 'section:has(a:text("Get Started"), a:text("Sign Up"), a:text("Join"))',
  267 |       ariaLabel: 'Call to action'
  268 |     });
  269 |     
  270 |     // If we can't find it with specific selectors, try a more general approach
  271 |     if (!ctaSection) {
  272 |       // Look for any section that might be the CTA section
  273 |       const sections = await page.locator('section').all();
  274 |       
  275 |       for (const section of sections) {
  276 |         // Check if this section has CTA-like buttons
  277 |         const ctaButtons = await section.locator('a:text("Get Started"), a:text("Sign Up"), a:text("Join"), a:text("Learn More")').all();
  278 |         
  279 |         if (ctaButtons.length > 0) {
  280 |           // This is likely the CTA section
  281 |           
  282 |           // Check for heading
  283 |           const heading = await section.locator('h2, h3').first();
  284 |           await expect(heading).toBeVisible('CTA section should have a heading');
  285 |           
  286 |           // Check for CTA buttons
  287 |           expect(ctaButtons.length).toBeGreaterThan(0, 'CTA section should contain call-to-action buttons');
  288 |           
  289 |           return; // We found and verified the section
  290 |         }
  291 |       }
  292 |     } else {
  293 |       // We found the section with our selectors
  294 |       
  295 |       // Check for heading
  296 |       const heading = await ctaSection.locator('h2, h3').first();
  297 |       await expect(heading).toBeVisible('CTA section should have a heading');
  298 |       
  299 |       // Check for CTA buttons
  300 |       const ctaButtons = await ctaSection.locator('a:text("Get Started"), a:text("Sign Up"), a:text("Join"), a:text("Learn More")').all();
  301 |       expect(ctaButtons.length).toBeGreaterThan(0, 'CTA section should contain call-to-action buttons');
  302 |     }
  303 |   });
  304 |
  305 |   test('should be responsive across different viewport sizes', async ({ page }) => {
  306 |     // Test mobile viewport
  307 |     await page.setViewportSize(viewports.mobile);
  308 |     await page.waitForTimeout(500); // Wait for responsive changes
  309 |     
  310 |     // Check for mobile menu button
  311 |     const mobileMenuButton = await findElement(page, {
  312 |       selector: 'button[aria-label*="menu" i], [class*="mobile-menu-button"]',
  313 |       ariaLabel: 'Toggle menu'
  314 |     });
  315 |     
  316 |     if (mobileMenuButton) {
  317 |       await expect(mobileMenuButton).toBeVisible('Mobile menu button should be visible on mobile');
  318 |       
  319 |       // Click mobile menu button
  320 |       await mobileMenuButton.click();
  321 |       await page.waitForTimeout(300); // Wait for animation
  322 |       
  323 |       // Check for visible navigation links
  324 |       const navLinks = await page.locator('a:visible').all();
  325 |       expect(navLinks.length).toBeGreaterThan(3, 'Mobile menu should show navigation links when opened');
  326 |     }
  327 |     
  328 |     // Test tablet viewport
  329 |     await page.setViewportSize(viewports.tablet);
  330 |     await page.waitForTimeout(500); // Wait for responsive changes
  331 |     
  332 |     // Check layout adjustments for tablet
  333 |     const heroSectionTablet = await findElement(page, {
  334 |       selector: 'section:first-child, [class*="hero"]',
  335 |       className: 'hero-section'
  336 |     });
  337 |     
  338 |     if (heroSectionTablet) {
  339 |       const heroHeight = await heroSectionTablet.evaluate(el => el.clientHeight);
  340 |       expect(heroHeight).toBeGreaterThan(300, 'Hero section should have appropriate height on tablet');
  341 |     }
  342 |     
  343 |     // Test desktop viewport
  344 |     await page.setViewportSize(viewports.desktop);
  345 |     await page.waitForTimeout(500); // Wait for responsive changes
  346 |     
  347 |     // Check for desktop navigation
  348 |     const desktopNav = await findElement(page, {
  349 |       selector: 'nav, [class*="desktop-nav"]',
  350 |       className: 'desktop-nav'
  351 |     });
  352 |     
  353 |     if (desktopNav) {
  354 |       await expect(desktopNav).toBeVisible('Desktop navigation should be visible on desktop');
  355 |       
  356 |       // Check navigation links
  357 |       const navLinks = await desktopNav.locator('a').all();
  358 |       expect(navLinks.length).toBeGreaterThan(2, 'Desktop navigation should have multiple links');
  359 |     }
```