# Test info

- Name: Internationalization >> Can change language to German
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/i18n.spec.js:63:3

# Error details

```
Error: locator.click: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('button[aria-label="Language"]')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/i18n.spec.js:66:28
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: WEEKEND SPECIAL
  - heading "Professional DSLR Camera Kit" [level=2]
  - paragraph: 24MP, 4K Video, 3 Lenses Included
  - link "RENT NOW":
    - /url: /search?category=cameras
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "Professional DSLR Camera Kit"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: 82 • Very Good Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: New 80 • Very Good Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: 97 • Excellent Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day
  - img
  - text: "Eastside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: 99 • Excellent Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day
  - img
  - text: "Northside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | // @ts-check
   2 | const { test, expect } = require('@playwright/test');
   3 |
   4 | /**
   5 |  * Test for internationalization features
   6 |  * 
   7 |  * This test verifies that:
   8 |  * 1. The language selector is visible and functional
   9 |  * 2. Changing the language updates the UI text
   10 |  * 3. RTL support works correctly
   11 |  */
   12 | test.describe('Internationalization', () => {
   13 |   test.beforeEach(async ({ page }) => {
   14 |     // Go to the home page
   15 |     await page.goto('/');
   16 |   });
   17 |
   18 |   test('Language selector is visible', async ({ page }) => {
   19 |     // Check that the language selector is visible
   20 |     const languageSelector = page.locator('button[aria-label="Language"]');
   21 |     await expect(languageSelector).toBeVisible();
   22 |   });
   23 |
   24 |   test('Can change language to Spanish', async ({ page }) => {
   25 |     // Open the language selector dropdown
   26 |     const languageSelector = page.locator('button[aria-label="Language"]');
   27 |     await languageSelector.click();
   28 |     
   29 |     // Wait for the dropdown to appear
   30 |     await page.waitForSelector('[role="menu"]');
   31 |     
   32 |     // Click on the Spanish option
   33 |     await page.locator('text=Español').click();
   34 |     
   35 |     // Check that the page content is now in Spanish
   36 |     await expect(page.locator('text=El Mercado Comunitario para Posibilidades Compartidas Infinitas')).toBeVisible();
   37 |     
   38 |     // Check that navigation items are in Spanish
   39 |     await expect(page.locator('text=Explorar todo')).toBeVisible();
   40 |     await expect(page.locator('text=Subastas')).toBeVisible();
   41 |     
   42 |     // Check that the HTML lang attribute is set correctly
   43 |     const htmlLang = await page.evaluate(() => document.documentElement.lang);
   44 |     expect(htmlLang).toBe('es');
   45 |   });
   46 |
   47 |   test('Can change language to French', async ({ page }) => {
   48 |     // Open the language selector dropdown
   49 |     const languageSelector = page.locator('button[aria-label="Language"]');
   50 |     await languageSelector.click();
   51 |     
   52 |     // Wait for the dropdown to appear
   53 |     await page.waitForSelector('[role="menu"]');
   54 |     
   55 |     // Click on the French option
   56 |     await page.locator('text=Français').click();
   57 |     
   58 |     // Check that the HTML lang attribute is set correctly
   59 |     const htmlLang = await page.evaluate(() => document.documentElement.lang);
   60 |     expect(htmlLang).toBe('fr');
   61 |   });
   62 |
   63 |   test('Can change language to German', async ({ page }) => {
   64 |     // Open the language selector dropdown
   65 |     const languageSelector = page.locator('button[aria-label="Language"]');
>  66 |     await languageSelector.click();
      |                            ^ Error: locator.click: Test timeout of 30000ms exceeded.
   67 |     
   68 |     // Wait for the dropdown to appear
   69 |     await page.waitForSelector('[role="menu"]');
   70 |     
   71 |     // Click on the German option
   72 |     await page.locator('text=Deutsch').click();
   73 |     
   74 |     // Check that the HTML lang attribute is set correctly
   75 |     const htmlLang = await page.evaluate(() => document.documentElement.lang);
   76 |     expect(htmlLang).toBe('de');
   77 |   });
   78 |
   79 |   test('Can change language to Chinese', async ({ page }) => {
   80 |     // Open the language selector dropdown
   81 |     const languageSelector = page.locator('button[aria-label="Language"]');
   82 |     await languageSelector.click();
   83 |     
   84 |     // Wait for the dropdown to appear
   85 |     await page.waitForSelector('[role="menu"]');
   86 |     
   87 |     // Click on the Chinese option
   88 |     await page.locator('text=中文').click();
   89 |     
   90 |     // Check that the HTML lang attribute is set correctly
   91 |     const htmlLang = await page.evaluate(() => document.documentElement.lang);
   92 |     expect(htmlLang).toBe('zh');
   93 |   });
   94 |
   95 |   test('Language preference is persisted', async ({ page }) => {
   96 |     // Open the language selector dropdown
   97 |     const languageSelector = page.locator('button[aria-label="Language"]');
   98 |     await languageSelector.click();
   99 |     
  100 |     // Wait for the dropdown to appear
  101 |     await page.waitForSelector('[role="menu"]');
  102 |     
  103 |     // Click on the Spanish option
  104 |     await page.locator('text=Español').click();
  105 |     
  106 |     // Reload the page
  107 |     await page.reload();
  108 |     
  109 |     // Check that the language is still Spanish
  110 |     await expect(page.locator('text=El Mercado Comunitario para Posibilidades Compartidas Infinitas')).toBeVisible();
  111 |     
  112 |     // Check that the HTML lang attribute is still set to Spanish
  113 |     const htmlLang = await page.evaluate(() => document.documentElement.lang);
  114 |     expect(htmlLang).toBe('es');
  115 |   });
  116 |
  117 |   test('RTL support works correctly', async ({ page }) => {
  118 |     // Open the language selector dropdown
  119 |     const languageSelector = page.locator('button[aria-label="Language"]');
  120 |     await languageSelector.click();
  121 |     
  122 |     // Wait for the dropdown to appear
  123 |     await page.waitForSelector('[role="menu"]');
  124 |     
  125 |     // Click on the Arabic option (if available)
  126 |     const arabicOption = page.locator('text=العربية');
  127 |     if (await arabicOption.count() > 0) {
  128 |       await arabicOption.click();
  129 |       
  130 |       // Check that the HTML dir attribute is set to RTL
  131 |       const htmlDir = await page.evaluate(() => document.documentElement.dir);
  132 |       expect(htmlDir).toBe('rtl');
  133 |       
  134 |       // Check that the RTL class is added to the body
  135 |       const hasRtlClass = await page.evaluate(() => document.body.classList.contains('rtl'));
  136 |       expect(hasRtlClass).toBe(true);
  137 |     } else {
  138 |       console.log('Arabic language option not available, skipping RTL test');
  139 |     }
  140 |   });
  141 | });
  142 |
```