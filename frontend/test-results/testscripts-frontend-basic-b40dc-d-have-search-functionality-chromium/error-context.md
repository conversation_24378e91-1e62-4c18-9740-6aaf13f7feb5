# Test info

- Name: Basic Smoke Test >> should have search functionality
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/basic-smoke.test.js:272:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('input[type="search"]') resolved to 2 elements:
    1) <input value="" type="search" id="desktop-search" aria-label="Search for items to rent" placeholder="Search for items to rent..." class="w-full h-full py-2 px-4 pr-10 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-inner"/> aka getByRole('searchbox', { name: 'Search for items to rent' })
    2) <input value="" type="search" id="mobile-search" aria-label="Search for items to rent" placeholder="Search for items to rent..." class="w-full py-2 px-4 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"/> aka getByLabel('Mobile site search').getByPlaceholder('Search for items to rent...')

Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('input[type="search"]')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/basic-smoke.test.js:291:31
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: Rent-to-Buy New 87 • Very Good Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day RTB
  - img
  - text: "Downtown • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: 99 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: Rent-to-Buy 95 • Excellent Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day RTB
  - img
  - text: "Eastside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: Rent-to-Buy New 82 • Very Good Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day RTB
  - img
  - text: "Northside • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
  191 |       console.log('Footer not found, but this is not critical');
  192 |     }
  193 |
  194 |     // Check for navigation links
  195 |     const navLinkSelectors = [
  196 |       'a[href]',
  197 |       'button',
  198 |       '[role="link"]',
  199 |       '[role="button"]'
  200 |     ];
  201 |
  202 |     const navLinks = await findElement(page, navLinkSelectors);
  203 |     expect(navLinks).not.toBeNull('Navigation links should be present');
  204 |
  205 |     // Check for logo or brand name
  206 |     const logoSelectors = [
  207 |       '.logo',
  208 |       'img[alt*="logo" i]',
  209 |       'a:has-text("rentUP")',
  210 |       'a:has-text("rent")',
  211 |       'a:first-child'
  212 |     ];
  213 |
  214 |     const logo = await findElement(page, logoSelectors);
  215 |     expect(logo).not.toBeNull('Logo or brand name should be present');
  216 |   });
  217 |
  218 |   test('should have working navigation links', async ({ page }) => {
  219 |     // Find navigation links using multiple selector strategies
  220 |     const navLinkSelectors = [
  221 |       'a[href]',
  222 |       'button[onclick]',
  223 |       '[role="link"]',
  224 |       '[role="button"]'
  225 |     ];
  226 |
  227 |     // Get all navigation links
  228 |     let navLinks = [];
  229 |
  230 |     for (const selector of navLinkSelectors) {
  231 |       const links = await page.locator(selector).all();
  232 |       if (links.length > 0) {
  233 |         navLinks = links;
  234 |         break; // Found some links, no need to try other selectors
  235 |       }
  236 |     }
  237 |
  238 |     expect(navLinks.length).toBeGreaterThan(0, 'Page should have navigation links');
  239 |
  240 |     // Check for home link using multiple selector strategies
  241 |     const homeLinkSelectors = [
  242 |       'a[href="/"]',
  243 |       'a:has-text("Home")',
  244 |       'a:has-text("rentUP")',
  245 |       'a:has-text("rent")',
  246 |       'a:first-child'
  247 |     ];
  248 |
  249 |     const homeLink = await findElement(page, homeLinkSelectors);
  250 |
  251 |     // Don't fail the test if home link is not found, just log it
  252 |     if (!homeLink) {
  253 |       console.log('Home link not found, but this is not critical');
  254 |     }
  255 |
  256 |     // Check for search link using multiple selector strategies
  257 |     const searchLinkSelectors = [
  258 |       'a[href*="/search"]',
  259 |       'a:has-text("Search")',
  260 |       'a:has(svg)',
  261 |       'button:has(svg)'
  262 |     ];
  263 |
  264 |     const searchLink = await findElement(page, searchLinkSelectors);
  265 |
  266 |     // Don't fail the test if search link is not found, just log it
  267 |     if (!searchLink) {
  268 |       console.log('Search link not found, but this is not critical');
  269 |     }
  270 |   });
  271 |
  272 |   test('should have search functionality', async ({ page }) => {
  273 |     // Check for search input using multiple selector strategies
  274 |     const searchInputSelectors = [
  275 |       'input[type="search"]',
  276 |       'input[type="text"]',
  277 |       'input[placeholder*="search" i]',
  278 |       'input[placeholder*="find" i]',
  279 |       'input[placeholder*="rent" i]'
  280 |     ];
  281 |
  282 |     const searchInput = await findElement(page, searchInputSelectors);
  283 |
  284 |     // Skip test if search input doesn't exist
  285 |     if (!searchInput) {
  286 |       console.log('Search input not found, skipping search functionality test');
  287 |       test.skip();
  288 |       return;
  289 |     }
  290 |
> 291 |     await expect(searchInput).toBeVisible('Search input should be visible');
      |                               ^ Error: expect.toBeVisible: Error: strict mode violation: locator('input[type="search"]') resolved to 2 elements:
  292 |
  293 |     // Test search functionality
  294 |     await searchInput.fill('test');
  295 |     expect(await searchInput.inputValue()).toBe('test', 'Search input should accept text');
  296 |
  297 |     // Check for search button using multiple selector strategies
  298 |     const searchButtonSelectors = [
  299 |       'button:has-text("Search")',
  300 |       'button[type="submit"]',
  301 |       'button:has(svg)',
  302 |       'button.search-button',
  303 |       'button:right-of(input)'
  304 |     ];
  305 |
  306 |     const searchButton = await findElement(page, searchButtonSelectors);
  307 |
  308 |     // If search button exists, test clicking it
  309 |     if (searchButton) {
  310 |       await searchButton.click();
  311 |
  312 |       // Wait for navigation to complete
  313 |       const navigationSuccessful = await waitForNavigation(page, /\/search/);
  314 |
  315 |       // Don't fail the test if navigation failed, just log it
  316 |       if (!navigationSuccessful) {
  317 |         console.log('Search navigation not implemented as expected');
  318 |       }
  319 |     }
  320 |   });
  321 |
  322 |   test('should be responsive', async ({ page }) => {
  323 |     // Use our testResponsive helper function to check responsive behavior
  324 |     const elementSelectors = [
  325 |       'body',
  326 |       '#root',
  327 |       'div'
  328 |     ];
  329 |
  330 |     const responsiveResults = await testResponsive(page, elementSelectors);
  331 |     expect(responsiveResults.mobile).toBe(true, 'Page should be visible on mobile');
  332 |     expect(responsiveResults.desktop).toBe(true, 'Page should be visible on desktop');
  333 |
  334 |     // Check for mobile menu button using multiple selector strategies
  335 |     const mobileMenuSelectors = [
  336 |       'button[aria-label*="menu" i]',
  337 |       '[class*="mobile-menu" i]',
  338 |       'button:has(svg)',
  339 |       'button.hamburger',
  340 |       'button:has(.bar)'
  341 |     ];
  342 |
  343 |     // Set viewport to mobile size
  344 |     await page.setViewportSize({ width: 375, height: 667 });
  345 |     await page.waitForTimeout(500); // Wait for responsive changes
  346 |
  347 |     const mobileMenu = await findElement(page, mobileMenuSelectors);
  348 |
  349 |     // Don't fail the test if mobile menu is not found, just log it
  350 |     if (!mobileMenu) {
  351 |       console.log('Mobile menu not found, but this is not critical');
  352 |     } else {
  353 |       await expect(mobileMenu).toBeVisible('Mobile menu should be visible on mobile');
  354 |
  355 |       // Test desktop viewport - mobile menu should be hidden or different
  356 |       await page.setViewportSize({ width: 1280, height: 800 });
  357 |       await page.waitForTimeout(500); // Wait for responsive changes
  358 |
  359 |       // Check if the mobile menu is still visible on desktop
  360 |       const isMobileMenuVisibleOnDesktop = await mobileMenu.isVisible();
  361 |
  362 |       // Log the result but don't fail the test
  363 |       if (isMobileMenuVisibleOnDesktop) {
  364 |         console.log('Mobile menu is still visible on desktop, might not be fully responsive');
  365 |       }
  366 |     }
  367 |
  368 |     // Reset viewport to desktop size
  369 |     await page.setViewportSize({ width: 1280, height: 800 });
  370 |   });
  371 | });
  372 |
```