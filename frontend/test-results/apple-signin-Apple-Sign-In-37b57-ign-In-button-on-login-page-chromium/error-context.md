# Test info

- Name: Apple Sign In >> should display Apple Sign In button on login page
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/apple-signin.spec.js:8:7

# Error details

```
Error: page.waitForSelector: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('h1:has-text("Log In")') to be visible

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/apple-signin.spec.js:13:16
```

# Page snapshot

```yaml
- img
- heading "Something went wrong" [level=2]
- paragraph: We're sorry, but an error occurred while rendering this page. Our team has been notified.
- button "Reload Page"
- link "Go to Home":
  - /url: /
```

# Test source

```ts
   1 | // @ts-check
   2 | import { test, expect } from '@playwright/test';
   3 |
   4 | /**
   5 |  * Test suite for Apple Sign In functionality
   6 |  */
   7 | test.describe('Apple Sign In', () => {
   8 |   test('should display Apple Sign In button on login page', async ({ page }) => {
   9 |     // Navigate to the login page
  10 |     await page.goto('/login');
  11 |     
  12 |     // Wait for the page to load
> 13 |     await page.waitForSelector('h1:has-text("Log In")');
     |                ^ Error: page.waitForSelector: Test timeout of 30000ms exceeded.
  14 |     
  15 |     // Check if the Apple Sign In button is visible
  16 |     const appleButton = page.locator('button:has-text("Sign in with Apple")');
  17 |     await expect(appleButton).toBeVisible();
  18 |     
  19 |     // Check if the Apple logo is visible in the button
  20 |     const appleLogo = appleButton.locator('svg');
  21 |     await expect(appleLogo).toBeVisible();
  22 |   });
  23 |   
  24 |   test('should handle Apple Sign In error gracefully', async ({ page }) => {
  25 |     // Navigate to the login page
  26 |     await page.goto('/login');
  27 |     
  28 |     // Wait for the page to load
  29 |     await page.waitForSelector('h1:has-text("Log In")');
  30 |     
  31 |     // Mock the Apple Sign In error
  32 |     await page.evaluate(() => {
  33 |       // Create a custom event that will be triggered when the Apple Sign In button is clicked
  34 |       window.addEventListener('click', (event) => {
  35 |         // Check if the clicked element is the Apple Sign In button
  36 |         if (event.target.closest('button') && event.target.closest('button').textContent.includes('Apple')) {
  37 |           // Simulate an error by dispatching a custom event
  38 |           const errorEvent = new CustomEvent('AppleIDSignInOnFailure', {
  39 |             detail: { error: 'Apple Sign In failed' }
  40 |           });
  41 |           window.dispatchEvent(errorEvent);
  42 |         }
  43 |       }, { capture: true });
  44 |     });
  45 |     
  46 |     // Click the Apple Sign In button
  47 |     await page.click('button:has-text("Sign in with Apple")');
  48 |     
  49 |     // Check if the error message is displayed
  50 |     await page.waitForSelector('div.bg-red-50:has-text("Apple login failed")');
  51 |   });
  52 |   
  53 |   test('should redirect to the correct callback URL', async ({ page }) => {
  54 |     // Navigate to the login page
  55 |     await page.goto('/login');
  56 |     
  57 |     // Wait for the page to load
  58 |     await page.waitForSelector('h1:has-text("Log In")');
  59 |     
  60 |     // Get the Apple Sign In button
  61 |     const appleButton = page.locator('button:has-text("Sign in with Apple")');
  62 |     
  63 |     // Check if the button has the correct redirect URI
  64 |     const redirectUri = await page.evaluate(() => {
  65 |       // This is a simplified check - in a real test, you would need to inspect the actual request
  66 |       const authConfig = window.authConfig || {};
  67 |       return authConfig.apple?.redirectUri;
  68 |     });
  69 |     
  70 |     // The test will pass if redirectUri is undefined since we can't access window.authConfig in this test
  71 |     // In a real test, you would verify that the redirect URI is correct
  72 |     console.log('Redirect URI:', redirectUri);
  73 |   });
  74 | });
  75 |
```