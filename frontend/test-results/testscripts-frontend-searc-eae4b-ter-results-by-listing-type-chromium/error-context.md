# Test info

- Name: Search Page >> should filter results by listing type
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/search.test.js:133:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toHaveURL(expected)

Locator: locator(':root')
Expected pattern: /\/search\?.*listingType=rent-to-buy/
Received string:  "http://localhost:5173/rent-to-buy"
Call log:
  - expect.toHaveURL with timeout 5000ms
  - waiting for locator(':root')
    9 × locator resolved to <html lang="en">…</html>
      - unexpected value "http://localhost:5173/rent-to-buy"

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/search.test.js:139:24
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - heading "Rent-to-Buy Hub" [level=1]
  - paragraph: Try before you buy with our flexible Rent-to-Buy program. Apply a portion of your rental payments toward ownership.
  - heading "How Rent-to-Buy Works" [level=2]
  - text: "1"
  - heading "Rent First" [level=3]
  - paragraph: Choose an item with the Rent-to-Buy option and start renting it at the regular daily or monthly rate.
  - text: "2"
  - heading "Build Credit" [level=3]
  - paragraph: A percentage of each rental payment is credited toward the purchase price if you decide to buy.
  - text: "3"
  - heading "Buy or Return" [level=3]
  - paragraph: Choose to purchase the item using your accumulated credit, or simply return it with no obligation.
  - heading "Featured Rent-to-Buy Items" [level=2]
  - img "Professional Camera"
  - heading "Professional Camera" [level=3]
  - paragraph: Canon EOS R5 with 24-70mm lens. Perfect for photography enthusiasts looking to own professional equipment.
  - text: "$75/day Buy: $3500"
  - paragraph: 15% of rental payments go toward purchase
  - paragraph: "Min. rental: 30 days"
  - link "View Details":
    - /url: /items/1
  - img "Electric Scooter"
  - heading "Electric Scooter" [level=3]
  - paragraph: Eco-friendly urban transportation. Try it out before committing to purchase.
  - text: "$25/day Buy: $800"
  - paragraph: 20% of rental payments go toward purchase
  - paragraph: "Min. rental: 14 days"
  - link "View Details":
    - /url: /items/2
  - img "Gaming Laptop"
  - heading "Gaming Laptop" [level=3]
  - paragraph: High-performance gaming laptop with RTX graphics. Test it with your favorite games before buying.
  - text: "$50/day Buy: $1800"
  - paragraph: 25% of rental payments go toward purchase
  - paragraph: "Min. rental: 7 days"
  - link "View Details":
    - /url: /items/3
  - img "Smart Home System"
  - heading "Smart Home System" [level=3]
  - paragraph: Complete smart home package with hub, lights, and sensors. Experience the convenience before investing.
  - text: "$35/day Buy: $1200"
  - paragraph: 20% of rental payments go toward purchase
  - paragraph: "Min. rental: 14 days"
  - link "View Details":
    - /url: /items/4
  - link "View All Rent-to-Buy Items":
    - /url: /search?listingType=rent-to-buy
  - heading "Rent-to-Buy Calculator" [level=2]
  - paragraph: See how much you can save with our Rent-to-Buy program compared to buying outright.
  - paragraph: Our calculator is currently being updated.
  - paragraph: Please check back soon or contact our support team for personalized calculations.
  - heading "Frequently Asked Questions" [level=2]
  - heading "How does Rent-to-Buy work?" [level=3]
  - paragraph: Our Rent-to-Buy program allows you to apply a portion of your rental payments toward the purchase price of the item. You can try the item for as long as you need, and if you decide to buy, a percentage of what you've already paid will count toward ownership.
  - heading "What percentage of my rental payment goes toward purchase?" [level=3]
  - paragraph: The conversion rate varies by item, typically ranging from 15-25% of your rental payments. The specific rate is always clearly displayed on each item's listing.
  - heading "Is there a minimum rental period?" [level=3]
  - paragraph: Yes, most Rent-to-Buy items have a minimum rental period to qualify for the purchase credit. This ensures both renters and owners benefit from the arrangement.
  - heading "Can I return the item if I decide not to buy?" [level=3]
  - paragraph: Absolutely! There's no obligation to purchase. If you decide the item isn't right for you, simply return it at the end of your rental period with no additional fees.
  - heading "Ready to Try Before You Buy?" [level=2]
  - paragraph: Browse our selection of Rent-to-Buy items and start your journey to ownership today.
  - link "Browse RTB Items":
    - /url: /search?listingType=rent-to-buy
  - link "Contact Support":
    - /url: /contact
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   39 |     // Check category filters
   40 |     const categoryFilters = await page.locator('.category-filter').all();
   41 |     expect(categoryFilters.length).toBeGreaterThan(0);
   42 |     
   43 |     // Check price range filter
   44 |     const priceRangeFilter = await page.locator('.price-range-filter');
   45 |     await expect(priceRangeFilter).toBeVisible();
   46 |     
   47 |     // Check listing type filters
   48 |     const listingTypeFilters = await page.locator('.listing-type-filter').all();
   49 |     expect(listingTypeFilters.length).toBeGreaterThan(0);
   50 |   });
   51 |
   52 |   test('should perform search and display results', async ({ page }) => {
   53 |     // Perform search
   54 |     const searchInput = await page.getByPlaceholder('What would you like to rent today?');
   55 |     await searchInput.fill('power drill');
   56 |     
   57 |     const searchButton = await page.getByRole('button', { name: 'Search' });
   58 |     await searchButton.click();
   59 |     
   60 |     // Check URL has search query
   61 |     await expect(page).toHaveURL(/\/search\?q=power\+drill/);
   62 |     
   63 |     // Check search results are displayed
   64 |     const searchResults = await page.locator('.search-results');
   65 |     await expect(searchResults).toBeVisible();
   66 |     
   67 |     // Check search result items
   68 |     const resultItems = await page.locator('.item-card').all();
   69 |     expect(resultItems.length).toBeGreaterThan(0);
   70 |     
   71 |     // Check result item details
   72 |     const firstItem = await page.locator('.item-card').first();
   73 |     await expect(firstItem.locator('.item-name')).toBeVisible();
   74 |     await expect(firstItem.locator('.item-price')).toBeVisible();
   75 |     await expect(firstItem.locator('.item-location')).toBeVisible();
   76 |   });
   77 |
   78 |   test('should filter results by category', async ({ page }) => {
   79 |     // Select a category filter
   80 |     const toolsCategory = await page.getByText('Tools & Equipment', { exact: false });
   81 |     await toolsCategory.click();
   82 |     
   83 |     // Check URL has category filter
   84 |     await expect(page).toHaveURL(/\/search\?category=tools/i);
   85 |     
   86 |     // Check filtered results
   87 |     const searchResults = await page.locator('.search-results');
   88 |     await expect(searchResults).toBeVisible();
   89 |     
   90 |     // Check result items are from the selected category
   91 |     const resultItems = await page.locator('.item-card').all();
   92 |     expect(resultItems.length).toBeGreaterThan(0);
   93 |     
   94 |     // Check category badge on items
   95 |     const categoryBadges = await page.locator('.category-badge').all();
   96 |     for (const badge of categoryBadges) {
   97 |       await expect(badge).toContainText('Tools', { ignoreCase: true });
   98 |     }
   99 |   });
  100 |
  101 |   test('should filter results by price range', async ({ page }) => {
  102 |     // Set price range filter
  103 |     const minPriceInput = await page.locator('input[name="minPrice"]');
  104 |     const maxPriceInput = await page.locator('input[name="maxPrice"]');
  105 |     
  106 |     await minPriceInput.fill('10');
  107 |     await maxPriceInput.fill('50');
  108 |     
  109 |     const applyFilterButton = await page.getByRole('button', { name: 'Apply' });
  110 |     await applyFilterButton.click();
  111 |     
  112 |     // Check URL has price range filter
  113 |     await expect(page).toHaveURL(/\/search\?.*minPrice=10.*maxPrice=50/);
  114 |     
  115 |     // Check filtered results
  116 |     const searchResults = await page.locator('.search-results');
  117 |     await expect(searchResults).toBeVisible();
  118 |     
  119 |     // Check result items are within price range
  120 |     const resultItems = await page.locator('.item-card').all();
  121 |     expect(resultItems.length).toBeGreaterThan(0);
  122 |     
  123 |     // Check prices on items
  124 |     const itemPrices = await page.locator('.item-price').all();
  125 |     for (const price of itemPrices) {
  126 |       const priceText = await price.textContent();
  127 |       const priceValue = parseFloat(priceText.replace(/[^0-9.]/g, ''));
  128 |       expect(priceValue).toBeGreaterThanOrEqual(10);
  129 |       expect(priceValue).toBeLessThanOrEqual(50);
  130 |     }
  131 |   });
  132 |
  133 |   test('should filter results by listing type', async ({ page }) => {
  134 |     // Select rent-to-buy listing type
  135 |     const rentToBuyFilter = await page.getByText('Rent-to-Buy', { exact: false });
  136 |     await rentToBuyFilter.click();
  137 |     
  138 |     // Check URL has listing type filter
> 139 |     await expect(page).toHaveURL(/\/search\?.*listingType=rent-to-buy/);
      |                        ^ Error: Timed out 5000ms waiting for expect(locator).toHaveURL(expected)
  140 |     
  141 |     // Check filtered results
  142 |     const searchResults = await page.locator('.search-results');
  143 |     await expect(searchResults).toBeVisible();
  144 |     
  145 |     // Check result items are rent-to-buy listings
  146 |     const resultItems = await page.locator('.item-card').all();
  147 |     expect(resultItems.length).toBeGreaterThan(0);
  148 |     
  149 |     // Check rent-to-buy badge on items
  150 |     const rtbBadges = await page.locator('.rtb-badge').all();
  151 |     expect(rtbBadges.length).toBe(resultItems.length);
  152 |   });
  153 |
  154 |   test('should sort search results', async ({ page }) => {
  155 |     // Open sort dropdown
  156 |     const sortDropdown = await page.locator('.sort-dropdown');
  157 |     await sortDropdown.click();
  158 |     
  159 |     // Select price: low to high
  160 |     const priceLowToHigh = await page.getByText('Price: Low to High');
  161 |     await priceLowToHigh.click();
  162 |     
  163 |     // Check URL has sort parameter
  164 |     await expect(page).toHaveURL(/\/search\?.*sort=price_asc/);
  165 |     
  166 |     // Check sorted results
  167 |     const searchResults = await page.locator('.search-results');
  168 |     await expect(searchResults).toBeVisible();
  169 |     
  170 |     // Check prices are in ascending order
  171 |     const itemPrices = await page.locator('.item-price').all();
  172 |     const prices = [];
  173 |     
  174 |     for (const price of itemPrices) {
  175 |       const priceText = await price.textContent();
  176 |       const priceValue = parseFloat(priceText.replace(/[^0-9.]/g, ''));
  177 |       prices.push(priceValue);
  178 |     }
  179 |     
  180 |     // Check if prices are sorted
  181 |     const sortedPrices = [...prices].sort((a, b) => a - b);
  182 |     expect(prices).toEqual(sortedPrices);
  183 |   });
  184 |
  185 |   test('should display map view', async ({ page }) => {
  186 |     // Click map view button
  187 |     const mapViewButton = await page.getByRole('button', { name: 'Map View' });
  188 |     await mapViewButton.click();
  189 |     
  190 |     // Check map is displayed
  191 |     const mapContainer = await page.locator('.map-container');
  192 |     await expect(mapContainer).toBeVisible();
  193 |     
  194 |     // Check map markers
  195 |     const mapMarkers = await page.locator('.map-marker').all();
  196 |     expect(mapMarkers.length).toBeGreaterThan(0);
  197 |     
  198 |     // Click a map marker
  199 |     await mapMarkers[0].click();
  200 |     
  201 |     // Check item popup is displayed
  202 |     const itemPopup = await page.locator('.map-item-popup');
  203 |     await expect(itemPopup).toBeVisible();
  204 |     
  205 |     // Check item details in popup
  206 |     await expect(itemPopup.locator('.item-name')).toBeVisible();
  207 |     await expect(itemPopup.locator('.item-price')).toBeVisible();
  208 |     
  209 |     // Click view item button in popup
  210 |     const viewItemButton = await itemPopup.getByRole('link', { name: 'View Item' });
  211 |     await viewItemButton.click();
  212 |     
  213 |     // Should navigate to item details page
  214 |     await expect(page).toHaveURL(/\/items\/[a-zA-Z0-9-]+/);
  215 |   });
  216 | });
  217 |
```