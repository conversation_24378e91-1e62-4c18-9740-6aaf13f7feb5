# Test info

- Name: Authentication >> should allow user login
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/authentication.test.js:96:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('form') resolved to 4 elements:
    1) <form role="search" class="relative flex" aria-label="Site search">…</form> aka getByRole('search', { name: 'Site search' })
    2) <form role="search" class="relative" aria-label="Mobile site search">…</form> aka getByLabel('Mobile site search')
    3) <form>…</form> aka getByText('EmailPasswordRemember')
    4) <form class="flex flex-col sm:flex-row gap-2 max-w-md mx-auto">…</form> aka locator('form').filter({ hasText: 'Subscribe' })

Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('form')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/authentication.test.js:102:29
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - img "rentUP Logo"
  - text: The Community Marketplace for Endless Shared Possibilities Rent anything from tools to spaces, or list your items to earn extra income. Join our community of sharers today!
  - heading "Log In" [level=1]
  - paragraph: Welcome to rentUP
  - text: Password QR Code Email
  - textbox "Email"
  - text: Password
  - textbox "Password"
  - button "Show password":
    - img
  - checkbox "Remember me"
  - text: Remember me
  - link "Forgot password?":
    - /url: /forgot-password
  - button "LOG IN"
  - text: OR
  - button "Sign in with Google":
    - img
    - text: Sign in with Google
  - iframe
  - button "Facebook":
    - img
    - text: Facebook
  - button "Sign in with Apple":
    - img
    - text: Sign in with Apple
  - paragraph:
    - text: New to rentUP?
    - link "Sign up":
      - /url: /register
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   2 |  * Authentication Tests
   3 |  * 
   4 |  * Tests for the authentication functionality of the RentUp application.
   5 |  * Based on sitemap.md and README.md requirements.
   6 |  */
   7 |
   8 | import { test, expect } from '@playwright/test';
   9 |
   10 | // Authentication tests
   11 | test.describe('Authentication', () => {
   12 |   test('should allow user registration', async ({ page }) => {
   13 |     // Navigate to registration page
   14 |     await page.goto('/register');
   15 |     
   16 |     // Check registration form
   17 |     const registrationForm = await page.locator('form');
   18 |     await expect(registrationForm).toBeVisible();
   19 |     
   20 |     // Check form fields
   21 |     await expect(registrationForm.locator('input[name="name"]')).toBeVisible();
   22 |     await expect(registrationForm.locator('input[name="email"]')).toBeVisible();
   23 |     await expect(registrationForm.locator('input[name="password"]')).toBeVisible();
   24 |     await expect(registrationForm.locator('input[name="confirmPassword"]')).toBeVisible();
   25 |     
   26 |     // Fill registration form
   27 |     await registrationForm.locator('input[name="name"]').fill('Test User');
   28 |     await registrationForm.locator('input[name="email"]').fill('<EMAIL>');
   29 |     await registrationForm.locator('input[name="password"]').fill('Password123!');
   30 |     await registrationForm.locator('input[name="confirmPassword"]').fill('Password123!');
   31 |     
   32 |     // Accept terms and conditions
   33 |     const termsCheckbox = await registrationForm.locator('input[type="checkbox"]');
   34 |     await termsCheckbox.check();
   35 |     
   36 |     // Submit registration form
   37 |     const registerButton = await registrationForm.getByRole('button', { name: 'Register' });
   38 |     await registerButton.click();
   39 |     
   40 |     // Check for success message or redirect to dashboard
   41 |     await expect(page).toHaveURL(/\/dashboard|\/verification/);
   42 |   });
   43 |
   44 |   test('should validate registration form', async ({ page }) => {
   45 |     // Navigate to registration page
   46 |     await page.goto('/register');
   47 |     
   48 |     // Check registration form
   49 |     const registrationForm = await page.locator('form');
   50 |     await expect(registrationForm).toBeVisible();
   51 |     
   52 |     // Submit empty form
   53 |     const registerButton = await registrationForm.getByRole('button', { name: 'Register' });
   54 |     await registerButton.click();
   55 |     
   56 |     // Check validation errors
   57 |     const nameError = await page.getByText('Name is required');
   58 |     const emailError = await page.getByText('Email is required');
   59 |     const passwordError = await page.getByText('Password is required');
   60 |     
   61 |     await expect(nameError).toBeVisible();
   62 |     await expect(emailError).toBeVisible();
   63 |     await expect(passwordError).toBeVisible();
   64 |     
   65 |     // Test invalid email
   66 |     await registrationForm.locator('input[name="name"]').fill('Test User');
   67 |     await registrationForm.locator('input[name="email"]').fill('invalid-email');
   68 |     await registrationForm.locator('input[name="password"]').fill('Password123!');
   69 |     await registrationForm.locator('input[name="confirmPassword"]').fill('Password123!');
   70 |     
   71 |     await registerButton.click();
   72 |     
   73 |     const invalidEmailError = await page.getByText('Invalid email address');
   74 |     await expect(invalidEmailError).toBeVisible();
   75 |     
   76 |     // Test password mismatch
   77 |     await registrationForm.locator('input[name="email"]').fill('<EMAIL>');
   78 |     await registrationForm.locator('input[name="password"]').fill('Password123!');
   79 |     await registrationForm.locator('input[name="confirmPassword"]').fill('DifferentPassword123!');
   80 |     
   81 |     await registerButton.click();
   82 |     
   83 |     const passwordMismatchError = await page.getByText('Passwords do not match');
   84 |     await expect(passwordMismatchError).toBeVisible();
   85 |     
   86 |     // Test weak password
   87 |     await registrationForm.locator('input[name="password"]').fill('weak');
   88 |     await registrationForm.locator('input[name="confirmPassword"]').fill('weak');
   89 |     
   90 |     await registerButton.click();
   91 |     
   92 |     const weakPasswordError = await page.getByText('Password must be at least 8 characters');
   93 |     await expect(weakPasswordError).toBeVisible();
   94 |   });
   95 |
   96 |   test('should allow user login', async ({ page }) => {
   97 |     // Navigate to login page
   98 |     await page.goto('/login');
   99 |     
  100 |     // Check login form
  101 |     const loginForm = await page.locator('form');
> 102 |     await expect(loginForm).toBeVisible();
      |                             ^ Error: expect.toBeVisible: Error: strict mode violation: locator('form') resolved to 4 elements:
  103 |     
  104 |     // Check form fields
  105 |     await expect(loginForm.locator('input[name="email"]')).toBeVisible();
  106 |     await expect(loginForm.locator('input[name="password"]')).toBeVisible();
  107 |     
  108 |     // Fill login form
  109 |     await loginForm.locator('input[name="email"]').fill('<EMAIL>');
  110 |     await loginForm.locator('input[name="password"]').fill('Password123!');
  111 |     
  112 |     // Submit login form
  113 |     const loginButton = await loginForm.getByRole('button', { name: 'Login' });
  114 |     await loginButton.click();
  115 |     
  116 |     // Check redirect to dashboard
  117 |     await expect(page).toHaveURL('/dashboard');
  118 |   });
  119 |
  120 |   test('should validate login form', async ({ page }) => {
  121 |     // Navigate to login page
  122 |     await page.goto('/login');
  123 |     
  124 |     // Check login form
  125 |     const loginForm = await page.locator('form');
  126 |     await expect(loginForm).toBeVisible();
  127 |     
  128 |     // Submit empty form
  129 |     const loginButton = await loginForm.getByRole('button', { name: 'Login' });
  130 |     await loginButton.click();
  131 |     
  132 |     // Check validation errors
  133 |     const emailError = await page.getByText('Email is required');
  134 |     const passwordError = await page.getByText('Password is required');
  135 |     
  136 |     await expect(emailError).toBeVisible();
  137 |     await expect(passwordError).toBeVisible();
  138 |     
  139 |     // Test invalid credentials
  140 |     await loginForm.locator('input[name="email"]').fill('<EMAIL>');
  141 |     await loginForm.locator('input[name="password"]').fill('WrongPassword123!');
  142 |     
  143 |     await loginButton.click();
  144 |     
  145 |     const invalidCredentialsError = await page.getByText('Invalid email or password');
  146 |     await expect(invalidCredentialsError).toBeVisible();
  147 |   });
  148 |
  149 |   test('should allow password reset', async ({ page }) => {
  150 |     // Navigate to login page
  151 |     await page.goto('/login');
  152 |     
  153 |     // Click forgot password link
  154 |     const forgotPasswordLink = await page.getByRole('link', { name: 'Forgot Password?' });
  155 |     await forgotPasswordLink.click();
  156 |     
  157 |     // Check reset password page
  158 |     await expect(page).toHaveURL('/forgot-password');
  159 |     
  160 |     // Check reset password form
  161 |     const resetForm = await page.locator('form');
  162 |     await expect(resetForm).toBeVisible();
  163 |     
  164 |     // Check form fields
  165 |     await expect(resetForm.locator('input[name="email"]')).toBeVisible();
  166 |     
  167 |     // Fill reset form
  168 |     await resetForm.locator('input[name="email"]').fill('<EMAIL>');
  169 |     
  170 |     // Submit reset form
  171 |     const resetButton = await resetForm.getByRole('button', { name: 'Reset Password' });
  172 |     await resetButton.click();
  173 |     
  174 |     // Check success message
  175 |     const successMessage = await page.getByText('Password reset instructions sent');
  176 |     await expect(successMessage).toBeVisible();
  177 |   });
  178 |
  179 |   test('should support social login options', async ({ page }) => {
  180 |     // Navigate to login page
  181 |     await page.goto('/login');
  182 |     
  183 |     // Check social login options
  184 |     const socialLoginSection = await page.locator('.social-login');
  185 |     await expect(socialLoginSection).toBeVisible();
  186 |     
  187 |     // Check Google login button
  188 |     const googleLoginButton = await socialLoginSection.getByRole('button', { name: 'Continue with Google' });
  189 |     await expect(googleLoginButton).toBeVisible();
  190 |     
  191 |     // Check Facebook login button
  192 |     const facebookLoginButton = await socialLoginSection.getByRole('button', { name: 'Continue with Facebook' });
  193 |     await expect(facebookLoginButton).toBeVisible();
  194 |     
  195 |     // Note: We can't fully test social login due to external redirects
  196 |     // but we can verify the buttons exist and have the correct attributes
  197 |     
  198 |     await expect(googleLoginButton).toHaveAttribute('type', 'button');
  199 |     await expect(facebookLoginButton).toHaveAttribute('type', 'button');
  200 |   });
  201 |
  202 |   test('should allow user logout', async ({ page }) => {
```