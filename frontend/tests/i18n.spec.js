// @ts-check
const { test, expect } = require('@playwright/test');

/**
 * Test for internationalization features
 * 
 * This test verifies that:
 * 1. The language selector is visible and functional
 * 2. Changing the language updates the UI text
 * 3. RTL support works correctly
 */
test.describe('Internationalization', () => {
  test.beforeEach(async ({ page }) => {
    // Go to the home page
    await page.goto('/');
  });

  test('Language selector is visible', async ({ page }) => {
    // Check that the language selector is visible
    const languageSelector = page.locator('button[aria-label="Language"]');
    await expect(languageSelector).toBeVisible();
  });

  test('Can change language to Spanish', async ({ page }) => {
    // Open the language selector dropdown
    const languageSelector = page.locator('button[aria-label="Language"]');
    await languageSelector.click();
    
    // Wait for the dropdown to appear
    await page.waitForSelector('[role="menu"]');
    
    // Click on the Spanish option
    await page.locator('text=Español').click();
    
    // Check that the page content is now in Spanish
    await expect(page.locator('text=El Mercado Comunitario para Posibilidades Compartidas Infinitas')).toBeVisible();
    
    // Check that navigation items are in Spanish
    await expect(page.locator('text=Explorar todo')).toBeVisible();
    await expect(page.locator('text=Subastas')).toBeVisible();
    
    // Check that the HTML lang attribute is set correctly
    const htmlLang = await page.evaluate(() => document.documentElement.lang);
    expect(htmlLang).toBe('es');
  });

  test('Can change language to French', async ({ page }) => {
    // Open the language selector dropdown
    const languageSelector = page.locator('button[aria-label="Language"]');
    await languageSelector.click();
    
    // Wait for the dropdown to appear
    await page.waitForSelector('[role="menu"]');
    
    // Click on the French option
    await page.locator('text=Français').click();
    
    // Check that the HTML lang attribute is set correctly
    const htmlLang = await page.evaluate(() => document.documentElement.lang);
    expect(htmlLang).toBe('fr');
  });

  test('Can change language to German', async ({ page }) => {
    // Open the language selector dropdown
    const languageSelector = page.locator('button[aria-label="Language"]');
    await languageSelector.click();
    
    // Wait for the dropdown to appear
    await page.waitForSelector('[role="menu"]');
    
    // Click on the German option
    await page.locator('text=Deutsch').click();
    
    // Check that the HTML lang attribute is set correctly
    const htmlLang = await page.evaluate(() => document.documentElement.lang);
    expect(htmlLang).toBe('de');
  });

  test('Can change language to Chinese', async ({ page }) => {
    // Open the language selector dropdown
    const languageSelector = page.locator('button[aria-label="Language"]');
    await languageSelector.click();
    
    // Wait for the dropdown to appear
    await page.waitForSelector('[role="menu"]');
    
    // Click on the Chinese option
    await page.locator('text=中文').click();
    
    // Check that the HTML lang attribute is set correctly
    const htmlLang = await page.evaluate(() => document.documentElement.lang);
    expect(htmlLang).toBe('zh');
  });

  test('Language preference is persisted', async ({ page }) => {
    // Open the language selector dropdown
    const languageSelector = page.locator('button[aria-label="Language"]');
    await languageSelector.click();
    
    // Wait for the dropdown to appear
    await page.waitForSelector('[role="menu"]');
    
    // Click on the Spanish option
    await page.locator('text=Español').click();
    
    // Reload the page
    await page.reload();
    
    // Check that the language is still Spanish
    await expect(page.locator('text=El Mercado Comunitario para Posibilidades Compartidas Infinitas')).toBeVisible();
    
    // Check that the HTML lang attribute is still set to Spanish
    const htmlLang = await page.evaluate(() => document.documentElement.lang);
    expect(htmlLang).toBe('es');
  });

  test('RTL support works correctly', async ({ page }) => {
    // Open the language selector dropdown
    const languageSelector = page.locator('button[aria-label="Language"]');
    await languageSelector.click();
    
    // Wait for the dropdown to appear
    await page.waitForSelector('[role="menu"]');
    
    // Click on the Arabic option (if available)
    const arabicOption = page.locator('text=العربية');
    if (await arabicOption.count() > 0) {
      await arabicOption.click();
      
      // Check that the HTML dir attribute is set to RTL
      const htmlDir = await page.evaluate(() => document.documentElement.dir);
      expect(htmlDir).toBe('rtl');
      
      // Check that the RTL class is added to the body
      const hasRtlClass = await page.evaluate(() => document.body.classList.contains('rtl'));
      expect(hasRtlClass).toBe(true);
    } else {
      console.log('Arabic language option not available, skipping RTL test');
    }
  });
});
