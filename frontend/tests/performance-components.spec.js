// @ts-check
import { test, expect } from '@playwright/test';
import { AxeBuilder } from '@axe-core/playwright';

// Test the performance-optimized components
test.describe('Performance-optimized components', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
  });

  test('should load the home page with optimized images', async ({ page }) => {
    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle');
    
    // Check if the page has loaded
    await expect(page).toHaveTitle(/RentUP/);
    
    // Check if images are loaded with proper attributes
    const images = await page.locator('img').all();
    expect(images.length).toBeGreaterThan(0);
    
    // Check at least one image for responsive attributes
    const firstImage = images[0];
    const srcset = await firstImage.getAttribute('srcset');
    const sizes = await firstImage.getAttribute('sizes');
    const loading = await firstImage.getAttribute('loading');
    
    // Images should have responsive attributes
    expect(srcset || sizes).toBeTruthy();
    expect(loading).toBe('lazy');
  });
  
  test('should have proper performance metrics', async ({ page }) => {
    // Navigate to a page with performance monitoring
    await page.goto('/visualization-demo');
    
    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle');
    
    // Check if performance metrics are being collected
    const hasPerformanceMetrics = await page.evaluate(() => {
      return typeof window.performance !== 'undefined' && 
        typeof window.performance.getEntriesByType === 'function' &&
        window.performance.getEntriesByType('measure').length > 0;
    });
    
    expect(hasPerformanceMetrics).toBe(true);
  });
  
  test('should have service worker registered', async ({ page }) => {
    // Wait for the service worker to be registered
    await page.waitForLoadState('networkidle');
    
    // Check if service worker is registered
    const hasServiceWorker = await page.evaluate(() => {
      return 'serviceWorker' in navigator && 
        navigator.serviceWorker.controller !== null;
    });
    
    // Service worker might not be registered in test environment
    // So we'll just log this rather than asserting
    console.log('Service worker registered:', hasServiceWorker);
  });
  
  test('should render virtualized lists efficiently', async ({ page }) => {
    // Navigate to a page with virtualized lists
    await page.goto('/enhanced-visualization-demo');
    
    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle');
    
    // Check if virtualized list is rendered
    const virtualizedList = await page.locator('[data-testid="virtualized-list"]').count();
    
    // If the page exists and has a virtualized list
    if (virtualizedList > 0) {
      // Check that only a subset of items are in the DOM
      const listItems = await page.locator('[data-testid^="virtualized-list-item-"]').count();
      
      // The number of DOM items should be less than the total items
      // This is a basic check that virtualization is working
      expect(listItems).toBeLessThan(100);
    } else {
      // Skip this test if the page doesn't exist yet
      test.skip();
    }
  });
  
  test('should be accessible', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
    
    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle');
    
    // Run accessibility tests
    const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
    
    // Check for accessibility violations
    expect(accessibilityScanResults.violations).toEqual([]);
  });
  
  test('should have proper meta tags for SEO and social sharing', async ({ page }) => {
    // Navigate to the home page
    await page.goto('/');
    
    // Wait for the page to be fully loaded
    await page.waitForLoadState('networkidle');
    
    // Check for essential meta tags
    const description = await page.locator('meta[name="description"]').getAttribute('content');
    const ogTitle = await page.locator('meta[property="og:title"]').getAttribute('content');
    const ogDescription = await page.locator('meta[property="og:description"]').getAttribute('content');
    
    // Meta tags should exist and have content
    expect(description).toBeTruthy();
    expect(ogTitle).toBeTruthy();
    expect(ogDescription).toBeTruthy();
  });
  
  test('should have proper security headers', async ({ page, request }) => {
    // Make a request to the home page
    const response = await request.get('/');
    
    // Check for security headers
    const headers = response.headers();
    
    // Security headers should exist
    expect(headers['x-content-type-options']).toBe('nosniff');
    expect(headers['x-frame-options']).toBe('DENY');
    expect(headers['content-security-policy']).toBeTruthy();
  });
});
