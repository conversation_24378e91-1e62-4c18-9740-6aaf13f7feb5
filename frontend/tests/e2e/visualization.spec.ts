import { test, expect } from '@playwright/test';

// Test data
const mockUser = {
  email: '<EMAIL>',
  password: 'Password123!',
};

// Using individual tests instead of describe blocks to match the project configuration

test('should show sign-in message when not authenticated', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Check that the page title is displayed
  await expect(page.locator('h1')).toContainText('AI Recommendation Insights');

  // Check that the sign-in message is displayed
  await expect(page.getByText('Sign In to See Your Personalized Insights')).toBeVisible();
  await expect(page.getByText('To view your personalized recommendation insights')).toBeVisible();

  // Check that the sign-in button is displayed
  const signInButton = page.getByRole('link', { name: 'Sign In' });
  await expect(signInButton).toBeVisible();

  // Check that the information sections are displayed
  await expect(page.getByText('How Our Recommendations Work')).toBeVisible();
  await expect(page.getByText('Privacy and Your Data')).toBeVisible();
  await expect(page.getByText('Frequently Asked Questions')).toBeVisible();
});

test('should navigate to login page when clicking sign-in button', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Click the sign-in button
  await page.getByRole('link', { name: 'Sign In' }).click();

  // Check that we're on the login page
  await expect(page).toHaveURL(/.*login/);
});

test('should display information about recommendations', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Check that the information about recommendations is displayed
  await expect(page.getByText('Preference Tracking:')).toBeVisible();
  await expect(page.getByText('Item Embeddings:')).toBeVisible();
  await expect(page.getByText('Contextual Factors:')).toBeVisible();
  await expect(page.getByText('Collaborative Signals:')).toBeVisible();
});

test('should display privacy information', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Check that the privacy information is displayed
  await expect(page.getByText('Data Control:')).toBeVisible();
  await expect(page.getByText('Anonymized Processing:')).toBeVisible();
  await expect(page.getByText('No Third-Party Sharing:')).toBeVisible();
  await expect(page.getByText('Transparency:')).toBeVisible();
});

test('should display FAQ section', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Check that the FAQ section is displayed
  await expect(page.getByText('How can I improve my recommendations?')).toBeVisible();
  await expect(page.getByText('Can I reset my recommendation preferences?')).toBeVisible();
  await expect(page.getByText('Why am I seeing certain recommendations?')).toBeVisible();
  await expect(page.getByText('How often are recommendations updated?')).toBeVisible();
});

test('should navigate to privacy policy when clicking the link', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Click the privacy policy link
  await page.getByRole('link', { name: 'Learn more about our privacy policy' }).click();

  // Check that we're on the privacy policy page
  await expect(page).toHaveURL(/.*privacy-policy/);
});

// Tests that require authentication
// Helper function to set up authentication
async function setupAuthentication(page) {
  // Mock authentication
  // This is a simplified example - in a real test, you would use the actual login flow
  // or set up authentication tokens directly

  // For this example, we'll just set a localStorage item to mock authentication
  await page.goto('/');
  await page.evaluate(() => {
    localStorage.setItem('auth', JSON.stringify({
      user: { id: 'user-123', name: 'Test User' },
      token: 'fake-token',
      isAuthenticated: true
    }));
  });

  // Reload the page to apply the authentication
  await page.reload();
}

test('should display visualization components when authenticated', async ({ page }) => {
  // Set up authentication
  await setupAuthentication(page);

  // Navigate to the visualization page
  await page.goto('/visualization');

  // Check that the page title is displayed
  await expect(page.locator('h1')).toContainText('AI Recommendation Insights');

  // Check that the sign-in message is not displayed
  await expect(page.getByText('Sign In to See Your Personalized Insights')).not.toBeVisible();

  // Check that the visualization demo is displayed
  // Note: The exact selectors will depend on how the components are implemented
  // These are examples based on the component structure we've seen
  await expect(page.getByText('AI Recommendation Visualizations')).toBeVisible();

  // Check that the preference visualization tab is selected by default
  const preferencesTab = page.getByRole('tab', { name: 'Preference Visualization' });
  await expect(preferencesTab).toBeVisible();
  await expect(preferencesTab).toHaveAttribute('aria-selected', 'true');

  // Check that the preference visualization description is displayed
  await expect(page.getByText(/This visualization shows how your preferences are tracked/)).toBeVisible();
});

test('should switch between visualization tabs', async ({ page }) => {
  // Set up authentication
  await setupAuthentication(page);

  // Navigate to the visualization page
  await page.goto('/visualization');

  // Check that the preference visualization tab is selected by default
  const preferencesTab = page.getByRole('tab', { name: 'Preference Visualization' });
  await expect(preferencesTab).toHaveAttribute('aria-selected', 'true');

  // Click on the embedding visualization tab
  const embeddingsTab = page.getByRole('tab', { name: 'Embedding Visualization' });
  await embeddingsTab.click();

  // Check that the embedding visualization tab is now selected
  await expect(embeddingsTab).toHaveAttribute('aria-selected', 'true');
  await expect(preferencesTab).toHaveAttribute('aria-selected', 'false');

  // Check that the embedding visualization description is displayed
  await expect(page.getByText(/This visualization shows how items are related to each other/)).toBeVisible();

  // Click back on the preference visualization tab
  await preferencesTab.click();

  // Check that the preference visualization tab is now selected again
  await expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
  await expect(embeddingsTab).toHaveAttribute('aria-selected', 'false');
});
