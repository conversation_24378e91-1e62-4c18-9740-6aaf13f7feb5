import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

// Using individual tests instead of describe blocks to match the project configuration
test('should not have any automatically detectable accessibility issues', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Run the accessibility scan
  const accessibilityScanResults = await new AxeBuilder({ page })
    .withTags(['wcag2a', 'wcag2aa', 'wcag21a', 'wcag21aa'])
    .analyze();

  // Assert that there are no violations
  expect(accessibilityScanResults.violations).toEqual([]);
});

test('should have proper heading structure', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Check that there is an h1 heading
  const h1Elements = await page.locator('h1').count();
  expect(h1Elements).toBe(1);

  // Check that h2 headings come after h1
  const h2Elements = await page.locator('h2').all();
  for (const h2 of h2Elements) {
    const h2Text = await h2.textContent();
    expect(h2Text).toBeTruthy();
  }
});

test('should have sufficient color contrast', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Run the accessibility scan specifically for color contrast
  const accessibilityScanResults = await new AxeBuilder({ page })
    .withTags(['wcag2aa'])
    .options({
      runOnly: {
        type: 'rule',
        values: ['color-contrast'],
      },
    })
    .analyze();

  // Assert that there are no color contrast violations
  expect(accessibilityScanResults.violations).toEqual([]);
});

test('should have proper focus management', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Mock authentication
  await page.evaluate(() => {
    localStorage.setItem('auth', JSON.stringify({
      user: { id: 'user-123', name: 'Test User' },
      token: 'fake-token',
      isAuthenticated: true
    }));
  });

  // Reload the page to apply the authentication
  await page.reload();

  // Tab through the page and check that focus is visible
  await page.keyboard.press('Tab');

  // Check that the first tab button is focused
  const focusedElement = await page.evaluate(() => {
    const activeElement = document.activeElement;
    return activeElement ? activeElement.tagName : null;
  });

  expect(focusedElement).not.toBeNull();

  // Tab to the first visualization tab
  await page.keyboard.press('Tab');
  await page.keyboard.press('Tab');

  // Check that we can activate the tab with the keyboard
  await page.keyboard.press('Enter');

  // Check that the tab is selected
  const preferencesTab = page.getByRole('tab', { name: 'Preference Visualization' });
  await expect(preferencesTab).toHaveAttribute('aria-selected', 'true');

  // Tab to the second visualization tab
  await page.keyboard.press('Tab');

  // Check that we can activate the tab with the keyboard
  await page.keyboard.press('Enter');

  // Check that the tab is selected
  const embeddingsTab = page.getByRole('tab', { name: 'Embedding Visualization' });
  await expect(embeddingsTab).toHaveAttribute('aria-selected', 'true');
});

test('should have proper ARIA attributes', async ({ page }) => {
  // Navigate to the visualization page
  await page.goto('/visualization');

  // Mock authentication
  await page.evaluate(() => {
    localStorage.setItem('auth', JSON.stringify({
      user: { id: 'user-123', name: 'Test User' },
      token: 'fake-token',
      isAuthenticated: true
    }));
  });

  // Reload the page to apply the authentication
  await page.reload();

  // Check that the tabs have proper ARIA attributes
  const tablist = page.getByRole('tablist');
  await expect(tablist).toBeVisible();

  const preferencesTab = page.getByRole('tab', { name: 'Preference Visualization' });
  await expect(preferencesTab).toHaveAttribute('aria-selected', 'true');

  const embeddingsTab = page.getByRole('tab', { name: 'Embedding Visualization' });
  await expect(embeddingsTab).toHaveAttribute('aria-selected', 'false');

  // Check that the tabpanels have proper ARIA attributes
  const preferencesPanel = page.getByRole('tabpanel');
  await expect(preferencesPanel).toBeVisible();
  await expect(preferencesPanel).toHaveAttribute('aria-labelledby', await preferencesTab.getAttribute('id'));
});
