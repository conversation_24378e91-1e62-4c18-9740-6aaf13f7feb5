import { test, expect } from '@playwright/test';

/**
 * E2E tests for the Business Accounts feature
 */
test.describe('Business Accounts', () => {
  // Setup: Login before each test
  test.beforeEach(async ({ page }) => {
    // Go to the login page
    await page.goto('/login');
    
    // Fill in login credentials
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    
    // Click the login button
    await page.click('button[type="submit"]');
    
    // Wait for navigation to complete
    await page.waitForURL('**/dashboard');
  });
  
  test('should navigate to business select page', async ({ page }) => {
    // Go to the business select page
    await page.goto('/business/select');
    
    // Check that the page title is displayed
    await expect(page.locator('h1')).toContainText('Select Business Account');
    
    // Check that the personal account option is displayed
    await expect(page.getByText('Personal Account')).toBeVisible();
  });
  
  test('should create a new business account', async ({ page }) => {
    // Go to the business create page
    await page.goto('/business/create');
    
    // Check that the page title is displayed
    await expect(page.locator('h1')).toContainText('Create Business Account');
    
    // Fill in the form
    await page.fill('input[name="name"]', 'Test Business');
    await page.fill('textarea[name="description"]', 'This is a test business account');
    await page.selectOption('select[name="industry"]', 'real_estate');
    await page.selectOption('select[name="size"]', 'small');
    await page.fill('input[name="website"]', 'https://example.com');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone_number"]', '+**********');
    
    // Submit the form
    await page.click('button[type="submit"]');
    
    // Wait for navigation to complete
    await page.waitForURL('**/business/dashboard');
    
    // Check that the business dashboard is displayed
    await expect(page.locator('h1')).toContainText('Test Business');
  });
  
  test('should navigate to business dashboard', async ({ page }) => {
    // Go to the business dashboard
    await page.goto('/business/dashboard');
    
    // Check that the dashboard is displayed
    await expect(page.getByText('Business Dashboard')).toBeVisible();
    
    // Check that the statistics are displayed
    await expect(page.getByText('Listings')).toBeVisible();
    await expect(page.getByText('Rentals')).toBeVisible();
    await expect(page.getByText('Members')).toBeVisible();
    await expect(page.getByText('Performance')).toBeVisible();
  });
  
  test('should navigate to business members page', async ({ page }) => {
    // Go to the business members page
    await page.goto('/business/members');
    
    // Check that the page title is displayed
    await expect(page.locator('h1')).toContainText('Business Members');
    
    // Check that the invite member button is displayed
    await expect(page.getByText('Invite Member')).toBeVisible();
    
    // Check that the members list is displayed
    await expect(page.getByText('Members List')).toBeVisible();
  });
  
  test('should invite a new member', async ({ page }) => {
    // Go to the business members page
    await page.goto('/business/members');
    
    // Click the invite member button
    await page.click('button:has-text("Invite Member")');
    
    // Check that the invite form is displayed
    await expect(page.getByText('Invite Member')).toBeVisible();
    
    // Fill in the form
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.selectOption('select[name="role"]', 'member');
    
    // Submit the form
    await page.click('button:has-text("Send Invite")');
    
    // Check that the success message is displayed
    await expect(page.getByText('Invitation sent successfully')).toBeVisible();
  });
  
  test('should navigate to business settings page', async ({ page }) => {
    // Go to the business settings page
    await page.goto('/business/settings');
    
    // Check that the page title is displayed
    await expect(page.locator('h1')).toContainText('Business Settings');
    
    // Check that the tabs are displayed
    await expect(page.getByText('Profile')).toBeVisible();
    await expect(page.getByText('Branding')).toBeVisible();
    await expect(page.getByText('Notifications')).toBeVisible();
    await expect(page.getByText('Security')).toBeVisible();
  });
  
  test('should update business settings', async ({ page }) => {
    // Go to the business settings page
    await page.goto('/business/settings');
    
    // Fill in the form
    await page.fill('input[name="name"]', 'Updated Business Name');
    await page.fill('textarea[name="description"]', 'Updated business description');
    
    // Submit the form
    await page.click('button:has-text("Save Changes")');
    
    // Check that the success message is displayed
    await expect(page.getByText('Settings updated successfully')).toBeVisible();
  });
  
  test('should switch between business accounts', async ({ page }) => {
    // Go to the business select page
    await page.goto('/business/select');
    
    // Check that the business accounts are displayed
    await expect(page.getByText('Business Accounts')).toBeVisible();
    
    // Click on a business account
    await page.click('div:has-text("Test Business")');
    
    // Wait for navigation to complete
    await page.waitForURL('**/business/dashboard');
    
    // Check that the business dashboard is displayed
    await expect(page.locator('h1')).toContainText('Test Business');
  });
  
  test('should switch to personal account', async ({ page }) => {
    // Go to the business select page
    await page.goto('/business/select');
    
    // Click on the personal account
    await page.click('div:has-text("Personal Account")');
    
    // Wait for navigation to complete
    await page.waitForURL('**/');
    
    // Check that the personal dashboard is displayed
    await expect(page.getByText('Dashboard')).toBeVisible();
  });
});
