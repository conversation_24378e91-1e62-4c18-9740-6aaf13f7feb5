# Test info

- Name: Home Page >> should display category showcase section
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/home.test.js:88:7

# Error details

```
Error: expect(received).not.toBeNull()

Matcher error: this matcher must not have an expected argument

Expected has type:  string
Expected has value: "Category cards should be present"
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/home.test.js:112:33
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: 96 • Excellent Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: 91 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: Rent-to-Buy 84 • Very Good Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day RTB
  - img
  - text: "Eastside • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: Rent-to-Buy 94 • Excellent Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day RTB
  - img
  - text: "Northside • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   12 | import {
   13 |   findElement,
   14 |   findTextContent,
   15 |   sectionExists,
   16 |   waitForNavigation,
   17 |   fillField,
   18 |   clickButton,
   19 |   isElementVisible
   20 | } from './helpers/test-helpers';
   21 |
   22 | // Home page tests
   23 | test.describe('Home Page', () => {
   24 |   test.beforeEach(async ({ page }) => {
   25 |     // Navigate to the home page before each test
   26 |     await page.goto('/');
   27 |
   28 |     // Wait for the page to be fully loaded
   29 |     await page.waitForLoadState('networkidle');
   30 |   });
   31 |
   32 |   test('should display the hero section with search functionality', async ({ page }) => {
   33 |     // Check hero title using multiple possible text matches
   34 |     const heroTitleTexts = [
   35 |       'The Community Marketplace for Endless Sharing Possibilities',
   36 |       'Community Marketplace',
   37 |       'Sharing Possibilities',
   38 |       'rentUP'
   39 |     ];
   40 |
   41 |     const heroTitle = await findTextContent(page, heroTitleTexts);
   42 |     expect(heroTitle).not.toBeNull('Hero title should be present');
   43 |
   44 |     if (heroTitle) {
   45 |       await expect(heroTitle).toBeVisible('Hero title should be visible');
   46 |     }
   47 |
   48 |     // Check search input exists using multiple selector strategies
   49 |     const searchInputSelectors = [
   50 |       'input[placeholder="What would you like to rent today?"]',
   51 |       'input[type="search"]',
   52 |       'input[type="text"]',
   53 |       '.search-input',
   54 |       'input[placeholder*="rent"]',
   55 |       'input[placeholder*="search"]'
   56 |     ];
   57 |
   58 |     const searchInput = await findElement(page, searchInputSelectors);
   59 |     expect(searchInput).not.toBeNull('Search input should be present');
   60 |
   61 |     // Check search button exists using multiple selector strategies
   62 |     const searchButtonSelectors = [
   63 |       'button:has-text("Search")',
   64 |       'button[type="submit"]',
   65 |       '.search-button',
   66 |       'button:has(svg)'
   67 |     ];
   68 |
   69 |     const searchButton = await findElement(page, searchButtonSelectors);
   70 |     expect(searchButton).not.toBeNull('Search button should be present');
   71 |
   72 |     // Only proceed with search test if both input and button are found
   73 |     if (searchInput && searchButton) {
   74 |       // Test search functionality
   75 |       await searchInput.fill('power drill');
   76 |       await searchButton.click();
   77 |
   78 |       // Should navigate to search page with query parameter
   79 |       const navigationSuccessful = await waitForNavigation(page, /\/search/);
   80 |       expect(navigationSuccessful).toBe(true, 'Navigation to search page should be successful');
   81 |
   82 |       // Check if URL contains search query
   83 |       const url = page.url();
   84 |       expect(url).toContain('power', 'URL should contain search term');
   85 |     }
   86 |   });
   87 |
   88 |   test('should display category showcase section', async ({ page }) => {
   89 |     // Check if category section exists using multiple possible heading texts
   90 |     const categorySectionHeadings = [
   91 |       'Browse by Category',
   92 |       'Categories',
   93 |       'Explore Categories',
   94 |       'Browse Categories'
   95 |     ];
   96 |
   97 |     const categoryTitle = await findTextContent(page, categorySectionHeadings);
   98 |
   99 |     // If category section exists, proceed with testing
  100 |     if (categoryTitle) {
  101 |       await expect(categoryTitle).toBeVisible('Category title should be visible');
  102 |
  103 |       // Check if categories are displayed using multiple selector strategies
  104 |       const categoryCardSelectors = [
  105 |         '.category-card',
  106 |         '[class*="category"]',
  107 |         'a[href*="category"]',
  108 |         'a[href*="search"]'
  109 |       ];
  110 |
  111 |       const categoryCards = await findElement(page, categoryCardSelectors);
> 112 |       expect(categoryCards).not.toBeNull('Category cards should be present');
      |                                 ^ Error: expect(received).not.toBeNull()
  113 |
  114 |       // Check for some specific categories from sitemap.md
  115 |       // We don't check all to make the test more resilient
  116 |       const categoryNames = [
  117 |         'Vehicles',
  118 |         'Medical',
  119 |         'Real Estate',
  120 |         'Electronics',
  121 |         'Home',
  122 |         'Tools',
  123 |         'Sports',
  124 |         'Fashion'
  125 |       ];
  126 |
  127 |       // Count how many categories we can find
  128 |       let foundCategories = 0;
  129 |
  130 |       for (const name of categoryNames) {
  131 |         const categoryElement = await findTextContent(page, name);
  132 |         if (categoryElement && await categoryElement.isVisible()) {
  133 |           foundCategories++;
  134 |         }
  135 |       }
  136 |
  137 |       // We expect to find at least some of the categories
  138 |       expect(foundCategories).toBeGreaterThan(0, 'At least some categories should be visible');
  139 |
  140 |       // Test category navigation if we found category cards
  141 |       if (categoryCards) {
  142 |         // Get all category cards
  143 |         const allCards = await page.locator(categoryCardSelectors[0]).all();
  144 |
  145 |         if (allCards.length > 0) {
  146 |           // Click the first category
  147 |           await allCards[0].click();
  148 |
  149 |           // Should navigate to search page with category filter
  150 |           const navigationSuccessful = await waitForNavigation(page, /\/search/);
  151 |           expect(navigationSuccessful).toBe(true, 'Navigation to search page should be successful');
  152 |         }
  153 |       }
  154 |     } else {
  155 |       console.log('Category section not found, might not be implemented yet');
  156 |       test.skip();
  157 |     }
  158 |   });
  159 |
  160 |   test('should display featured items section', async ({ page }) => {
  161 |     // Check if featured items section exists using multiple possible heading texts
  162 |     const featuredSectionHeadings = [
  163 |       'Featured Items',
  164 |       'Featured',
  165 |       'Popular Items',
  166 |       'Trending Items',
  167 |       'Recommended'
  168 |     ];
  169 |
  170 |     const featuredTitle = await findTextContent(page, featuredSectionHeadings);
  171 |
  172 |     // If featured items section exists, proceed with testing
  173 |     if (featuredTitle) {
  174 |       await expect(featuredTitle).toBeVisible('Featured items title should be visible');
  175 |
  176 |       // Check if items are displayed using multiple selector strategies
  177 |       const itemCardSelectors = [
  178 |         '.item-card',
  179 |         '[class*="item"]',
  180 |         '[class*="product"]',
  181 |         '[class*="card"]'
  182 |       ];
  183 |
  184 |       const itemCards = await findElement(page, itemCardSelectors);
  185 |       expect(itemCards).not.toBeNull('Item cards should be present');
  186 |
  187 |       // Only proceed with item details test if we found item cards
  188 |       if (itemCards) {
  189 |         // Get all item cards
  190 |         const allCards = await page.locator(itemCardSelectors[0]).all();
  191 |
  192 |         if (allCards.length > 0) {
  193 |           // Check item details using multiple selector strategies
  194 |           const firstItem = allCards[0];
  195 |
  196 |           // Check for item name
  197 |           const nameSelectors = [
  198 |             '.item-name',
  199 |             '[class*="name"]',
  200 |             '[class*="title"]',
  201 |             'h3',
  202 |             'h4'
  203 |           ];
  204 |
  205 |           const itemName = await findElement(firstItem, nameSelectors);
  206 |           expect(itemName).not.toBeNull('Item name should be present');
  207 |
  208 |           // Check for item price
  209 |           const priceSelectors = [
  210 |             '.item-price',
  211 |             '[class*="price"]',
  212 |             '[class*="cost"]'
```