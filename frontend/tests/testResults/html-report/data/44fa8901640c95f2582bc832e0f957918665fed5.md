# Test info

- Name: should switch between visualization tabs
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/visualization.spec.ts:135:5

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toHaveAttribute(expected)

Locator: getByRole('tab', { name: 'Preference Visualization' })
Expected string: "true"
Received: <element(s) not found>
Call log:
  - expect.toHaveAttribute with timeout 5000ms
  - waiting for getBy<PERSON>ole('tab', { name: 'Preference Visualization' })

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/visualization.spec.ts:144:32
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - heading "AI Recommendation Insights" [level=1]
  - paragraph: Explore how our AI recommendation system works to provide you with personalized suggestions.
  - heading "Sign In to See Your Personalized Insights" [level=2]
  - paragraph: To view your personalized recommendation insights, please sign in to your account. This will allow us to show you how your preferences influence the recommendations you receive.
  - link "Sign In":
    - /url: /login
  - heading "How Our Recommendations Work" [level=2]
  - paragraph: Our AI recommendation system uses a combination of collaborative filtering and content-based approaches to provide personalized recommendations tailored to your preferences.
  - list:
    - listitem:
      - strong: "Preference Tracking:"
      - text: We analyze your interactions with items, including views, likes, and rentals.
    - listitem:
      - strong: "Item Embeddings:"
      - text: We create mathematical representations of items based on their features and relationships.
    - listitem:
      - strong: "Contextual Factors:"
      - text: We consider factors like location, season, and time of day.
    - listitem:
      - strong: "Collaborative Signals:"
      - text: We identify patterns among users with similar preferences.
  - heading "Privacy and Your Data" [level=2]
  - paragraph: "We take your privacy seriously. Here's how we handle your data in our recommendation system:"
  - list:
    - listitem:
      - strong: "Data Control:"
      - text: You can view and manage your preference data at any time.
    - listitem:
      - strong: "Anonymized Processing:"
      - text: Your data is anonymized during processing.
    - listitem:
      - strong: "No Third-Party Sharing:"
      - text: We never share your preference data with third parties.
    - listitem:
      - strong: "Transparency:"
      - text: The visualizations on this page show exactly how your data influences recommendations.
  - link "Learn more about our privacy policy":
    - /url: /privacy-policy
  - heading "Frequently Asked Questions" [level=2]
  - heading "How can I improve my recommendations?" [level=3]
  - paragraph: The more you interact with items on RentUp, the better our recommendations become. Like items you're interested in, create favorites, and browse categories you enjoy.
  - heading "Can I reset my recommendation preferences?" [level=3]
  - paragraph: Yes, you can reset your recommendation preferences in your account settings. This will clear your preference data and start fresh.
  - heading "Why am I seeing certain recommendations?" [level=3]
  - paragraph: The visualizations on this page help explain why you're seeing certain recommendations. Your preferences, browsing history, and similar users all influence what you see.
  - heading "How often are recommendations updated?" [level=3]
  - paragraph: Your recommendations are updated in real-time as you interact with the platform. The system continuously learns from your behavior to improve suggestions.
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   44 |   // Navigate to the visualization page
   45 |   await page.goto('/visualization');
   46 |
   47 |   // Check that the information about recommendations is displayed
   48 |   await expect(page.getByText('Preference Tracking:')).toBeVisible();
   49 |   await expect(page.getByText('Item Embeddings:')).toBeVisible();
   50 |   await expect(page.getByText('Contextual Factors:')).toBeVisible();
   51 |   await expect(page.getByText('Collaborative Signals:')).toBeVisible();
   52 | });
   53 |
   54 | test('should display privacy information', async ({ page }) => {
   55 |   // Navigate to the visualization page
   56 |   await page.goto('/visualization');
   57 |
   58 |   // Check that the privacy information is displayed
   59 |   await expect(page.getByText('Data Control:')).toBeVisible();
   60 |   await expect(page.getByText('Anonymized Processing:')).toBeVisible();
   61 |   await expect(page.getByText('No Third-Party Sharing:')).toBeVisible();
   62 |   await expect(page.getByText('Transparency:')).toBeVisible();
   63 | });
   64 |
   65 | test('should display FAQ section', async ({ page }) => {
   66 |   // Navigate to the visualization page
   67 |   await page.goto('/visualization');
   68 |
   69 |   // Check that the FAQ section is displayed
   70 |   await expect(page.getByText('How can I improve my recommendations?')).toBeVisible();
   71 |   await expect(page.getByText('Can I reset my recommendation preferences?')).toBeVisible();
   72 |   await expect(page.getByText('Why am I seeing certain recommendations?')).toBeVisible();
   73 |   await expect(page.getByText('How often are recommendations updated?')).toBeVisible();
   74 | });
   75 |
   76 | test('should navigate to privacy policy when clicking the link', async ({ page }) => {
   77 |   // Navigate to the visualization page
   78 |   await page.goto('/visualization');
   79 |
   80 |   // Click the privacy policy link
   81 |   await page.getByRole('link', { name: 'Learn more about our privacy policy' }).click();
   82 |
   83 |   // Check that we're on the privacy policy page
   84 |   await expect(page).toHaveURL(/.*privacy-policy/);
   85 | });
   86 |
   87 | // Tests that require authentication
   88 | // Helper function to set up authentication
   89 | async function setupAuthentication(page) {
   90 |   // Mock authentication
   91 |   // This is a simplified example - in a real test, you would use the actual login flow
   92 |   // or set up authentication tokens directly
   93 |
   94 |   // For this example, we'll just set a localStorage item to mock authentication
   95 |   await page.goto('/');
   96 |   await page.evaluate(() => {
   97 |     localStorage.setItem('auth', JSON.stringify({
   98 |       user: { id: 'user-123', name: 'Test User' },
   99 |       token: 'fake-token',
  100 |       isAuthenticated: true
  101 |     }));
  102 |   });
  103 |
  104 |   // Reload the page to apply the authentication
  105 |   await page.reload();
  106 | }
  107 |
  108 | test('should display visualization components when authenticated', async ({ page }) => {
  109 |   // Set up authentication
  110 |   await setupAuthentication(page);
  111 |
  112 |   // Navigate to the visualization page
  113 |   await page.goto('/visualization');
  114 |
  115 |   // Check that the page title is displayed
  116 |   await expect(page.locator('h1')).toContainText('AI Recommendation Insights');
  117 |
  118 |   // Check that the sign-in message is not displayed
  119 |   await expect(page.getByText('Sign In to See Your Personalized Insights')).not.toBeVisible();
  120 |
  121 |   // Check that the visualization demo is displayed
  122 |   // Note: The exact selectors will depend on how the components are implemented
  123 |   // These are examples based on the component structure we've seen
  124 |   await expect(page.getByText('AI Recommendation Visualizations')).toBeVisible();
  125 |
  126 |   // Check that the preference visualization tab is selected by default
  127 |   const preferencesTab = page.getByRole('tab', { name: 'Preference Visualization' });
  128 |   await expect(preferencesTab).toBeVisible();
  129 |   await expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
  130 |
  131 |   // Check that the preference visualization description is displayed
  132 |   await expect(page.getByText(/This visualization shows how your preferences are tracked/)).toBeVisible();
  133 | });
  134 |
  135 | test('should switch between visualization tabs', async ({ page }) => {
  136 |   // Set up authentication
  137 |   await setupAuthentication(page);
  138 |
  139 |   // Navigate to the visualization page
  140 |   await page.goto('/visualization');
  141 |
  142 |   // Check that the preference visualization tab is selected by default
  143 |   const preferencesTab = page.getByRole('tab', { name: 'Preference Visualization' });
> 144 |   await expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
      |                                ^ Error: Timed out 5000ms waiting for expect(locator).toHaveAttribute(expected)
  145 |
  146 |   // Click on the embedding visualization tab
  147 |   const embeddingsTab = page.getByRole('tab', { name: 'Embedding Visualization' });
  148 |   await embeddingsTab.click();
  149 |
  150 |   // Check that the embedding visualization tab is now selected
  151 |   await expect(embeddingsTab).toHaveAttribute('aria-selected', 'true');
  152 |   await expect(preferencesTab).toHaveAttribute('aria-selected', 'false');
  153 |
  154 |   // Check that the embedding visualization description is displayed
  155 |   await expect(page.getByText(/This visualization shows how items are related to each other/)).toBeVisible();
  156 |
  157 |   // Click back on the preference visualization tab
  158 |   await preferencesTab.click();
  159 |
  160 |   // Check that the preference visualization tab is now selected again
  161 |   await expect(preferencesTab).toHaveAttribute('aria-selected', 'true');
  162 |   await expect(embeddingsTab).toHaveAttribute('aria-selected', 'false');
  163 | });
  164 |
```