# Test info

- Name: Homepage >> should have a search functionality
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/example.spec.ts:62:7

# Error details

```
Error: locator.isVisible: Error: strict mode violation: locator('input[type="search"]') resolved to 2 elements:
    1) <input value="" type="search" id="desktop-search" aria-label="Search for items to rent" placeholder="Search for items to rent..." class="w-full h-full py-2 px-4 pr-10 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent shadow-inner"/> aka getByRole('searchbox', { name: 'Search for items to rent' })
    2) <input value="" type="search" id="mobile-search" aria-label="Search for items to rent" placeholder="Search for items to rent..." class="w-full py-2 px-4 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"/> aka getByLabel('Mobile site search').getByPlaceholder('Search for items to rent...')

Call log:
    - checking visibility of locator('input[type="search"]')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/example.spec.ts:79:36
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: Rent-to-Buy 81 • Very Good Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day RTB
  - img
  - text: "Downtown • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: Rent-to-Buy 94 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day RTB
  - img
  - text: "Westside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: Rent-to-Buy 80 • Very Good Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day RTB
  - img
  - text: "Eastside • 4 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: 89 • Very Good Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day
  - img
  - text: "Northside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | // tests/example.spec.ts
   2 | import { test, expect } from '@playwright/test';
   3 |
   4 | test.describe('Homepage', () => {
   5 |   // Runs before each test in this describe block
   6 |   test.beforeEach(async ({ page }) => {
   7 |     // Go to the homepage (baseURL is prepended automatically)
   8 |     await page.goto('/');
   9 |     
   10 |     // Wait for the page to be fully loaded
   11 |     await page.waitForLoadState('networkidle');
   12 |   });
   13 |
   14 |   test('should have the correct title', async ({ page }) => {
   15 |     // Get the page title
   16 |     const title = await page.title();
   17 |     
   18 |     // Check that the title contains expected text
   19 |     expect(title.length).toBeGreaterThan(0, 'Page should have a title');
   20 |     
   21 |     // Check if title contains any of the expected keywords
   22 |     const expectedKeywords = ['rentUP', 'Rent', 'Marketplace', 'Community'];
   23 |     let titleContainsKeyword = false;
   24 |     
   25 |     for (const keyword of expectedKeywords) {
   26 |       if (title.includes(keyword)) {
   27 |         titleContainsKeyword = true;
   28 |         break;
   29 |       }
   30 |     }
   31 |     
   32 |     expect(titleContainsKeyword).toBe(true, 'Title should contain one of the expected keywords');
   33 |   });
   34 |
   35 |   test('should display the main heading', async ({ page }) => {
   36 |     // Try multiple approaches to find the main heading
   37 |     const headingSelectors = [
   38 |       'h1',
   39 |       '[role="heading"][aria-level="1"]',
   40 |       '.hero h1',
   41 |       'h1:has-text("Community")',
   42 |       'h1:has-text("Marketplace")',
   43 |       'h1:has-text("rentUP")'
   44 |     ];
   45 |     
   46 |     // Try each selector until we find a visible heading
   47 |     let headingFound = false;
   48 |     
   49 |     for (const selector of headingSelectors) {
   50 |       const heading = page.locator(selector);
   51 |       const count = await heading.count();
   52 |       
   53 |       if (count > 0 && await heading.isVisible()) {
   54 |         headingFound = true;
   55 |         break;
   56 |       }
   57 |     }
   58 |     
   59 |     expect(headingFound).toBe(true, 'Main heading should be visible');
   60 |   });
   61 |
   62 |   test('should have a search functionality', async ({ page }) => {
   63 |     // Try multiple approaches to find the search input
   64 |     const searchInputSelectors = [
   65 |       'input[type="search"]',
   66 |       'input[placeholder*="search" i]',
   67 |       'input[placeholder*="find" i]',
   68 |       'input[placeholder*="rent" i]',
   69 |       'input[type="text"]'
   70 |     ];
   71 |     
   72 |     // Try each selector until we find a visible search input
   73 |     let searchInput = null;
   74 |     
   75 |     for (const selector of searchInputSelectors) {
   76 |       const input = page.locator(selector);
   77 |       const count = await input.count();
   78 |       
>  79 |       if (count > 0 && await input.isVisible()) {
      |                                    ^ Error: locator.isVisible: Error: strict mode violation: locator('input[type="search"]') resolved to 2 elements:
   80 |         searchInput = input;
   81 |         break;
   82 |       }
   83 |     }
   84 |     
   85 |     // If search input is not found, skip the test
   86 |     if (!searchInput) {
   87 |       test.skip('Search input not found');
   88 |       return;
   89 |     }
   90 |     
   91 |     // Test that we can type in the search input
   92 |     await searchInput.fill('test search');
   93 |     expect(await searchInput.inputValue()).toBe('test search', 'Should be able to type in search input');
   94 |   });
   95 |
   96 |   test('should have a responsive layout', async ({ page }) => {
   97 |     // Test mobile viewport
   98 |     await page.setViewportSize({ width: 375, height: 667 });
   99 |     await page.waitForTimeout(500); // Wait for responsive changes
  100 |     
  101 |     // Check that the page is still visible on mobile
  102 |     const bodyMobile = await page.locator('body');
  103 |     await expect(bodyMobile).toBeVisible('Body should be visible on mobile');
  104 |     
  105 |     // Test desktop viewport
  106 |     await page.setViewportSize({ width: 1280, height: 800 });
  107 |     await page.waitForTimeout(500); // Wait for responsive changes
  108 |     
  109 |     // Check that the page is still visible on desktop
  110 |     const bodyDesktop = await page.locator('body');
  111 |     await expect(bodyDesktop).toBeVisible('Body should be visible on desktop');
  112 |   });
  113 | });
  114 |
```