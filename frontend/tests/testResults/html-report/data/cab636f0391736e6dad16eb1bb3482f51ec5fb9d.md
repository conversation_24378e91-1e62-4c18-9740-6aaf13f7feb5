# Test info

- Name: Home Page >> should have functioning hero section with search
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/enhanced-home.test.js:95:7

# Error details

```
Error: expect(received).not.toBeNull()

Matcher error: this matcher must not have an expected argument

Expected has type:  string
Expected has value: "Hero section should be present"
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/enhanced-home.test.js:101:29
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: 87 • Very Good Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: Rent-to-Buy New 97 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day RTB
  - img
  - text: "Westside • 4 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: 96 • Excellent Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day
  - img
  - text: "Eastside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: Rent-to-Buy 81 • Very Good Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day RTB
  - img
  - text: "Northside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | /**
   2 |  * Enhanced Home Page Tests
   3 |  * 
   4 |  * Comprehensive tests for the home page of the RentUP application.
   5 |  * This test script follows the testing methodology outlined in testmethod.md.
   6 |  * 
   7 |  * Features tested:
   8 |  * - Page loading and critical elements
   9 |  * - Hero section with search functionality
   10 |  * - Category showcase section
   11 |  * - Featured items section
   12 |  * - Trust section
   13 |  * - Call-to-action section
   14 |  * - Responsive behavior
   15 |  */
   16 |
   17 | import { test, expect } from '@playwright/test';
   18 |
   19 | // Define viewport sizes for responsive testing
   20 | const viewports = {
   21 |   mobile: { width: 375, height: 667 },
   22 |   tablet: { width: 768, height: 1024 },
   23 |   desktop: { width: 1280, height: 800 }
   24 | };
   25 |
   26 | // Helper function to find elements with multiple selector strategies
   27 | async function findElement(page, options) {
   28 |   const selectors = [
   29 |     options.testId ? `[data-testid="${options.testId}"]` : null,
   30 |     options.ariaLabel ? `[aria-label="${options.ariaLabel}"]` : null,
   31 |     options.text ? `:text("${options.text}")` : null,
   32 |     options.className ? `.${options.className}` : null,
   33 |     options.selector
   34 |   ].filter(Boolean);
   35 |   
   36 |   for (const selector of selectors) {
   37 |     const element = page.locator(selector).first();
   38 |     if (await element.count() > 0) {
   39 |       return element;
   40 |     }
   41 |   }
   42 |   
   43 |   return null;
   44 | }
   45 |
   46 | // Helper function to check if element exists
   47 | async function elementExists(page, selector) {
   48 |   const count = await page.locator(selector).count();
   49 |   return count > 0;
   50 | }
   51 |
   52 | // Home page tests
   53 | test.describe('Home Page', () => {
   54 |   test.beforeEach(async ({ page }) => {
   55 |     // Navigate to the home page before each test
   56 |     await page.goto('/');
   57 |     
   58 |     // Wait for the page to be fully loaded
   59 |     await page.waitForLoadState('networkidle');
   60 |   });
   61 |
   62 |   test('should load with all critical elements', async ({ page }) => {
   63 |     // Check if the page loads without errors
   64 |     const body = await page.locator('body');
   65 |     await expect(body).toBeVisible('Body should be visible');
   66 |     
   67 |     // Check for header
   68 |     const header = await findElement(page, { 
   69 |       selector: 'header',
   70 |       className: 'header'
   71 |     });
   72 |     expect(header).not.toBeNull('Header should be present');
   73 |     
   74 |     // Check for footer
   75 |     const footer = await findElement(page, {
   76 |       selector: 'footer',
   77 |       className: 'footer'
   78 |     });
   79 |     expect(footer).not.toBeNull('Footer should be present');
   80 |     
   81 |     // Check for main content
   82 |     const main = await findElement(page, {
   83 |       selector: 'main'
   84 |     });
   85 |     expect(main).not.toBeNull('Main content area should be present');
   86 |     
   87 |     // Check for hero section
   88 |     const heroSection = await findElement(page, {
   89 |       selector: 'section:first-child',
   90 |       className: 'hero-section'
   91 |     });
   92 |     expect(heroSection).not.toBeNull('Hero section should be present');
   93 |   });
   94 |
   95 |   test('should have functioning hero section with search', async ({ page }) => {
   96 |     // Find hero section using multiple strategies
   97 |     const heroSection = await findElement(page, {
   98 |       selector: 'section:first-child, [class*="hero"]',
   99 |       className: 'hero-section'
  100 |     });
> 101 |     expect(heroSection).not.toBeNull('Hero section should be present');
      |                             ^ Error: expect(received).not.toBeNull()
  102 |     
  103 |     if (heroSection) {
  104 |       // Check for hero title
  105 |       const heroTitle = await page.locator('h1').first();
  106 |       await expect(heroTitle).toBeVisible('Hero title should be visible');
  107 |       
  108 |       // Check for search input
  109 |       const searchInput = await findElement(page, {
  110 |         selector: 'input[type="text"], input[type="search"]',
  111 |         ariaLabel: 'Search for items to rent'
  112 |       });
  113 |       expect(searchInput).not.toBeNull('Search input should be present');
  114 |       
  115 |       if (searchInput) {
  116 |         // Test search functionality
  117 |         await searchInput.fill('test search');
  118 |         expect(await searchInput.inputValue()).toBe('test search', 'Search input should accept text');
  119 |         
  120 |         // Find search button
  121 |         const searchButton = await findElement(page, {
  122 |           selector: 'button[type="submit"]',
  123 |           text: 'Search',
  124 |           ariaLabel: 'Search'
  125 |         });
  126 |         expect(searchButton).not.toBeNull('Search button should be present');
  127 |         
  128 |         if (searchButton) {
  129 |           // Click search button (but intercept navigation)
  130 |           await page.route('**/search**', route => route.abort());
  131 |           await searchButton.click();
  132 |           
  133 |           // Check that navigation was attempted
  134 |           const requests = [];
  135 |           page.on('request', request => requests.push(request));
  136 |           
  137 |           // Verify search was attempted
  138 |           expect(requests.some(r => r.url().includes('/search'))).toBe(true, 'Search should navigate to search page');
  139 |         }
  140 |       }
  141 |     }
  142 |   });
  143 |
  144 |   test('should display category showcase section', async ({ page }) => {
  145 |     // Find category section using multiple strategies
  146 |     const categorySection = await findElement(page, {
  147 |       selector: 'section:has(h2:text("Category"), h2:text("Categories"))',
  148 |       ariaLabel: 'Category showcase'
  149 |     });
  150 |     
  151 |     // If we can't find it with specific selectors, try a more general approach
  152 |     if (!categorySection) {
  153 |       // Look for any section that might be the category section
  154 |       const sections = await page.locator('section').all();
  155 |       
  156 |       for (const section of sections) {
  157 |         const headingText = await section.locator('h2').textContent();
  158 |         if (headingText && headingText.toLowerCase().includes('categor')) {
  159 |           // This is likely the category section
  160 |           
  161 |           // Check for category cards or items
  162 |           const categoryItems = await section.locator('a, .card, [class*="category"], [class*="card"]').all();
  163 |           expect(categoryItems.length).toBeGreaterThan(0, 'Category section should contain category items');
  164 |           
  165 |           // Check for "View All" or similar link
  166 |           const viewAllLink = await section.locator('a:text("View All"), a:text("See All"), a:text("Browse All")').first();
  167 |           const hasViewAll = await viewAllLink.count() > 0;
  168 |           
  169 |           if (hasViewAll) {
  170 |             await expect(viewAllLink).toBeVisible('View All link should be visible');
  171 |           }
  172 |           
  173 |           return; // We found and verified the section
  174 |         }
  175 |       }
  176 |     } else {
  177 |       // We found the section with our selectors
  178 |       
  179 |       // Check for category cards or items
  180 |       const categoryItems = await categorySection.locator('a, .card, [class*="category"], [class*="card"]').all();
  181 |       expect(categoryItems.length).toBeGreaterThan(0, 'Category section should contain category items');
  182 |       
  183 |       // Check for "View All" or similar link
  184 |       const viewAllLink = await categorySection.locator('a:text("View All"), a:text("See All"), a:text("Browse All")').first();
  185 |       const hasViewAll = await viewAllLink.count() > 0;
  186 |       
  187 |       if (hasViewAll) {
  188 |         await expect(viewAllLink).toBeVisible('View All link should be visible');
  189 |       }
  190 |     }
  191 |   });
  192 |
  193 |   test('should display featured items section', async ({ page }) => {
  194 |     // Find featured items section using multiple strategies
  195 |     const featuredSection = await findElement(page, {
  196 |       selector: 'section:has(h2:text("Featured"), h2:text("Popular"))',
  197 |       ariaLabel: 'Featured items'
  198 |     });
  199 |     
  200 |     // If we can't find it with specific selectors, try a more general approach
  201 |     if (!featuredSection) {
```