# Test info

- Name: Accessibility >> Automated Accessibility Scans >> home page should be accessible
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/accessibility.test.js:68:9

# Error details

```
Error: 
      Rule: color-contrast (serious impact)
      Description: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
      Help: Elements must meet minimum color contrast ratio thresholds
      Elements: 30 elements affected
      First element: <a class="py-3 text-black hover:text-primary transition-colors font-medium" href="/auctions" data-discover="true">navigation.auctions</a>
    

      Rule: page-has-heading-one (moderate impact)
      Description: Ensure that the page, or at least one of its frames contains a level-one heading
      Help: Page should contain a level-one heading
      Elements: 1 elements affected
      First element: <html lang="en">
    

expect(received).toEqual(expected) // deep equality

- Expected  -    1
+ Received  + 1104

- Array []
+ Array [
+   Object {
+     "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds",
+     "help": "Elements must meet minimum color contrast ratio thresholds",
+     "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/color-contrast?application=playwright",
+     "id": "color-contrast",
+     "impact": "serious",
+     "nodes": Array [
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/auctions\" data-discover=\"true\">navigation.auctions</a>",
+                 "target": Array [
+                   "a[href$=\"auctions\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/auctions\" data-discover=\"true\">navigation.auctions</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"auctions\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/visualization\" data-discover=\"true\">navigation.aiInsights</a>",
+                 "target": Array [
+                   "a[href$=\"visualization\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/visualization\" data-discover=\"true\">navigation.aiInsights</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"visualization\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/design-system\" data-discover=\"true\">navigation.designSystem</a>",
+                 "target": Array [
+                   "a[href$=\"design-system\"]:nth-child(6)",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/design-system\" data-discover=\"true\">navigation.designSystem</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"design-system\"]:nth-child(6)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/responsive-design-system\" data-discover=\"true\">navigation.responsiveDesign</a>",
+                 "target": Array [
+                   "a[href$=\"responsive-design-system\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/responsive-design-system\" data-discover=\"true\">navigation.responsiveDesign</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"responsive-design-system\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#eff6ff",
+               "contrastRatio": 1.08,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "10.5pt (14px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"text-sm font-semibold uppercase tracking-wider bg-accent px-3 py-1.5 rounded-md inline-block shadow-sm border border-accent-dark\">CLAIM THIS OFFER NOW</div>",
+                 "target": Array [
+                   ".tracking-wider.py-1\\.5.px-3",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<div class=\"text-sm font-semibold uppercase tracking-wider bg-accent px-3 py-1.5 rounded-md inline-block shadow-sm border border-accent-dark\">CLAIM THIS OFFER NOW</div>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".tracking-wider.py-1\\.5.px-3",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#60a5fa",
+               "contrastRatio": 2.33,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#eff6fe",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.33 (foreground color: #eff6fe, background color: #60a5fa, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"bg-primary-light w-full h-full rounded-lg overflow-hidden\">",
+                 "target": Array [
+                   ".bg-primary-light",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.33 (foreground color: #eff6fe, background color: #60a5fa, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<p class=\"text-white/90 mt-2\">Activity Tracker, Calorie Tracker</p>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".text-white\\/90",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#eff6ff",
+               "contrastRatio": 1.08,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "10.5pt (14px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"text-sm font-semibold mb-2 bg-accent text-white px-3 py-1.5 rounded-md inline-block shadow-sm border border-accent-dark\">15% CASH BACK</div>",
+                 "target": Array [
+                   ".bg-warning\\/20 > div:nth-child(1) > .py-1\\.5.px-3.border-accent-dark",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<div class=\"text-sm font-semibold mb-2 bg-accent text-white px-3 py-1.5 rounded-md inline-block shadow-sm border border-accent-dark\">15% CASH BACK</div>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".bg-warning\\/20 > div:nth-child(1) > .py-1\\.5.px-3.border-accent-dark",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">99</span><span class=\"mx-1\">•</span><span>Excellent</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">99</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2 > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">99</span><span class=\"mx-1\">•</span><span>Excellent</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>Excellent</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2 > span:nth-child(3)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"flex items-baseline mb-3 bg-light-gray p-2 rounded-md\"><span class=\"text-primary font-bold\">$25.00</span><span class=\"text-xs text-medium-gray ml-1\">/ day</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"text-xs text-medium-gray ml-1\">/ day</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-1.text-medium-gray.text-xs",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">Owner has had for:</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>0 months</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > span:nth-child(2)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">83</span><span class=\"mx-1\">•</span><span>Very Good</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">83</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2 > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">83</span><span class=\"mx-1\">•</span><span>Very Good</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>Very Good</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2 > span:nth-child(3)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"flex items-baseline mb-3 bg-light-gray p-2 rounded-md\"><span class=\"text-primary font-bold\">$15.00</span><span class=\"text-xs text-medium-gray ml-1\">/ day</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"text-xs text-medium-gray ml-1\">/ day</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-1.text-medium-gray.text-xs",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">Owner has had for:</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>0 months</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > span:nth-child(2)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">92</span><span class=\"mx-1\">•</span><span>Excellent</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">92</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2 > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">92</span><span class=\"mx-1\">•</span><span>Excellent</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>Excellent</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2 > span:nth-child(3)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"flex items-baseline mb-3 bg-light-gray p-2 rounded-md\"><span class=\"text-primary font-bold\">$40.00</span><span class=\"text-xs text-medium-gray ml-1\">/ day</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"text-xs text-medium-gray ml-1\">/ day</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-1.text-medium-gray.text-xs",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">Owner has had for:</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>0 months</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > span:nth-child(2)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">88</span><span class=\"mx-1\">•</span><span>Very Good</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">88</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2 > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">88</span><span class=\"mx-1\">•</span><span>Very Good</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>Very Good</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.px-2 > span:nth-child(3)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"flex items-baseline mb-3 bg-light-gray p-2 rounded-md\"><span class=\"text-primary font-bold\">$20.00</span><span class=\"text-xs text-medium-gray ml-1\">/ day</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"text-xs text-medium-gray ml-1\">/ day</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-1.text-medium-gray.text-xs",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">Owner has had for:</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>0 months</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > span:nth-child(2)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+                 "target": Array [
+                   ".p-8.rounded-lg.text-center:nth-child(1) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".p-8.rounded-lg.text-center:nth-child(1) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+                 "target": Array [
+                   ".p-8.rounded-lg.text-center:nth-child(2) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".p-8.rounded-lg.text-center:nth-child(2) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+                 "target": Array [
+                   ".p-8.rounded-lg.text-center:nth-child(3) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".p-8.rounded-lg.text-center:nth-child(3) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+         ],
+       },
+     ],
+     "tags": Array [
+       "cat.color",
+       "wcag2aa",
+       "wcag143",
+       "TTv5",
+       "TT13.c",
+       "EN-301-549",
+       "EN-*******",
+       "ACT",
+     ],
+   },
+   Object {
+     "description": "Ensure that the page, or at least one of its frames contains a level-one heading",
+     "help": "Page should contain a level-one heading",
+     "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/page-has-heading-one?application=playwright",
+     "id": "page-has-heading-one",
+     "impact": "moderate",
+     "nodes": Array [
+       Object {
+         "all": Array [
+           Object {
+             "data": null,
+             "id": "page-has-heading-one",
+             "impact": "moderate",
+             "message": "Page must have a level-one heading",
+             "relatedNodes": Array [],
+           },
+         ],
+         "any": Array [],
+         "failureSummary": "Fix all of the following:
+   Page must have a level-one heading",
+         "html": "<html lang=\"en\">",
+         "impact": "moderate",
+         "none": Array [],
+         "target": Array [
+           "html",
+         ],
+       },
+     ],
+     "tags": Array [
+       "cat.semantics",
+       "best-practice",
+     ],
+   },
+ ]
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/accessibility.test.js:70:72
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: 99 • Excellent Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 4 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: 83 • Very Good Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: 92 • Excellent Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day
  - img
  - text: "Eastside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: 88 • Very Good Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day
  - img
  - text: "Northside • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | /**
   2 |  * Enhanced Accessibility Tests
   3 |  *
   4 |  * Comprehensive tests for the accessibility features of the RentUp application.
   5 |  * Based on WCAG 2.1 AA standards, sitemap.md and README.md requirements.
   6 |  *
   7 |  * These tests check for:
   8 |  * - Automated accessibility violations using axe-core
   9 |  * - Proper ARIA attributes and roles
   10 |  * - Keyboard navigation
   11 |  * - Color contrast
   12 |  * - Focus management
   13 |  * - Screen reader support
   14 |  */
   15 |
   16 | import { test, expect } from '@playwright/test';
   17 | import AxeBuilder from '@axe-core/playwright';
   18 |
   19 | // Helper function to format accessibility violations for better readability
   20 | function formatViolations(violations) {
   21 |   if (violations.length === 0) return 'No violations found';
   22 |
   23 |   return violations.map(violation => {
   24 |     return `
   25 |       Rule: ${violation.id} (${violation.impact} impact)
   26 |       Description: ${violation.description}
   27 |       Help: ${violation.help}
   28 |       Elements: ${violation.nodes.length} elements affected
   29 |       First element: ${violation.nodes[0]?.html || 'N/A'}
   30 |     `;
   31 |   }).join('\n');
   32 | }
   33 |
   34 | // Helper function to run accessibility tests on a page
   35 | async function runAccessibilityTests(page, url, options = {}) {
   36 |   // Navigate to the page
   37 |   await page.goto(url);
   38 |   await page.waitForLoadState('networkidle');
   39 |
   40 |   // Configure axe builder with options
   41 |   let axeBuilder = new AxeBuilder({ page });
   42 |
   43 |   // Add specific rules if provided
   44 |   if (options.rules) {
   45 |     axeBuilder = axeBuilder.include(options.rules);
   46 |   }
   47 |
   48 |   // Add specific context if provided
   49 |   if (options.context) {
   50 |     axeBuilder = axeBuilder.include(options.context);
   51 |   }
   52 |
   53 |   // Run axe accessibility tests
   54 |   const accessibilityScanResults = await axeBuilder.analyze();
   55 |
   56 |   // Log detailed information about violations for debugging
   57 |   if (accessibilityScanResults.violations.length > 0) {
   58 |     console.log(`Accessibility violations on ${url}:\n${formatViolations(accessibilityScanResults.violations)}`);
   59 |   }
   60 |
   61 |   // Return the results
   62 |   return accessibilityScanResults;
   63 | }
   64 |
   65 | // Accessibility tests
   66 | test.describe('Accessibility', () => {
   67 |   test.describe('Automated Accessibility Scans', () => {
   68 |     test('home page should be accessible', async ({ page }) => {
   69 |       const results = await runAccessibilityTests(page, '/');
>  70 |       expect(results.violations, formatViolations(results.violations)).toEqual([]);
      |                                                                        ^ Error: 
   71 |     });
   72 |
   73 |     test('search page should be accessible', async ({ page }) => {
   74 |       const results = await runAccessibilityTests(page, '/search');
   75 |       expect(results.violations, formatViolations(results.violations)).toEqual([]);
   76 |     });
   77 |
   78 |     test('item details page should be accessible', async ({ page }) => {
   79 |       // Try to use a real item ID if possible, fallback to sample
   80 |       const results = await runAccessibilityTests(page, '/items/sample-item-123');
   81 |       expect(results.violations, formatViolations(results.violations)).toEqual([]);
   82 |     });
   83 |
   84 |     test('login page should be accessible', async ({ page }) => {
   85 |       const results = await runAccessibilityTests(page, '/login');
   86 |       expect(results.violations, formatViolations(results.violations)).toEqual([]);
   87 |     });
   88 |   });
   89 |
   90 |   test.describe('ARIA and Semantic HTML', () => {
   91 |     test('should use proper heading structure', async ({ page }) => {
   92 |       await page.goto('/');
   93 |       await page.waitForLoadState('networkidle');
   94 |
   95 |       // Get all headings
   96 |       const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
   97 |
   98 |       // Check that we have at least one heading
   99 |       expect(headings.length).toBeGreaterThan(0, 'Page should have at least one heading');
  100 |
  101 |       // Get heading levels
  102 |       const headingLevels = await Promise.all(
  103 |         headings.map(async (heading) => {
  104 |           const tagName = await heading.evaluate(el => el.tagName);
  105 |           return parseInt(tagName.substring(1));
  106 |         })
  107 |       );
  108 |
  109 |       // Check if heading levels are properly nested (no skipping levels)
  110 |       let previousLevel = 0;
  111 |       let hasProperStructure = true;
  112 |       let firstViolation = '';
  113 |
  114 |       for (let i = 0; i < headingLevels.length; i++) {
  115 |         const level = headingLevels[i];
  116 |         if (level > previousLevel + 1) {
  117 |           hasProperStructure = false;
  118 |           const headingText = await headings[i].textContent();
  119 |           firstViolation = `Heading level jumped from h${previousLevel || '(none)'} to h${level} with text: "${headingText}"`;
  120 |           break;
  121 |         }
  122 |         previousLevel = level === 1 ? 1 : previousLevel;
  123 |       }
  124 |
  125 |       expect(hasProperStructure).toBe(true, firstViolation || 'Heading levels should not skip (e.g., h1 to h3)');
  126 |     });
  127 |
  128 |     test('should have proper landmark regions', async ({ page }) => {
  129 |       await page.goto('/');
  130 |       await page.waitForLoadState('networkidle');
  131 |
  132 |       // Check for header landmark
  133 |       const header = await page.locator('header, [role="banner"]');
  134 |       await expect(header).toBeVisible('Page should have a header landmark');
  135 |
  136 |       // Check for main landmark
  137 |       const main = await page.locator('main, [role="main"]');
  138 |       await expect(main).toBeVisible('Page should have a main landmark');
  139 |
  140 |       // Check for footer landmark
  141 |       const footer = await page.locator('footer, [role="contentinfo"]');
  142 |       await expect(footer).toBeVisible('Page should have a footer landmark');
  143 |
  144 |       // Check for navigation landmark
  145 |       const nav = await page.locator('nav, [role="navigation"]');
  146 |       await expect(nav).toBeVisible('Page should have a navigation landmark');
  147 |     });
  148 |
  149 |     test('should have proper document language', async ({ page }) => {
  150 |       await page.goto('/');
  151 |       await page.waitForLoadState('networkidle');
  152 |
  153 |       // Check for lang attribute on html element
  154 |       const html = await page.locator('html');
  155 |       const lang = await html.getAttribute('lang');
  156 |
  157 |       expect(lang).toBeTruthy('HTML element should have a lang attribute');
  158 |       expect(lang.length).toBeGreaterThanOrEqual(2, 'Lang attribute should be at least 2 characters');
  159 |     });
  160 |
  161 |     test('should have proper ARIA attributes on interactive elements', async ({ page }) => {
  162 |       await page.goto('/');
  163 |       await page.waitForLoadState('networkidle');
  164 |
  165 |       // Check buttons for proper ARIA attributes
  166 |       const buttons = await page.locator('button:visible, [role="button"]:visible').all();
  167 |
  168 |       for (const button of buttons) {
  169 |         // Check if button has accessible name
  170 |         const hasText = (await button.textContent())?.trim().length > 0;
```