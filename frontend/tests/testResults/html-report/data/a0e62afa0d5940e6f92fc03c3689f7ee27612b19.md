# Test info

- Name: Business Accounts >> should navigate to business select page
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:23:7

# Error details

```
Error: page.waitForURL: Test timeout of 30000ms exceeded.
=========================== logs ===========================
waiting for navigation to "**/dashboard" until "load"
============================================================
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/e2e/business-accounts.spec.ts:20:16
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - img "rentUP Logo"
  - text: The Community Marketplace for Endless Shared Possibilities Rent anything from tools to spaces, or list your items to earn extra income. Join our community of sharers today!
  - heading "Log In" [level=1]
  - paragraph: Welcome to rentUP
  - text: Password QR Code Email
  - textbox "Email": <EMAIL>
  - text: Password
  - textbox "Password": password123
  - button "Show password":
    - img
  - checkbox "Remember me"
  - text: Remember me
  - link "Forgot password?":
    - /url: /forgot-password
  - button "LOG IN"
  - text: OR
  - button "Sign in with Google":
    - img
    - text: Sign in with Google
  - iframe
  - button "Facebook":
    - img
    - text: Facebook
  - button "Sign in with Apple":
    - img
    - text: Sign in with Apple
  - paragraph:
    - text: New to rentUP?
    - link "Sign up":
      - /url: /register
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | /**
   4 |  * E2E tests for the Business Accounts feature
   5 |  */
   6 | test.describe('Business Accounts', () => {
   7 |   // Setup: Login before each test
   8 |   test.beforeEach(async ({ page }) => {
   9 |     // Go to the login page
   10 |     await page.goto('/login');
   11 |     
   12 |     // Fill in login credentials
   13 |     await page.fill('input[name="email"]', '<EMAIL>');
   14 |     await page.fill('input[name="password"]', 'password123');
   15 |     
   16 |     // Click the login button
   17 |     await page.click('button[type="submit"]');
   18 |     
   19 |     // Wait for navigation to complete
>  20 |     await page.waitForURL('**/dashboard');
      |                ^ Error: page.waitForURL: Test timeout of 30000ms exceeded.
   21 |   });
   22 |   
   23 |   test('should navigate to business select page', async ({ page }) => {
   24 |     // Go to the business select page
   25 |     await page.goto('/business/select');
   26 |     
   27 |     // Check that the page title is displayed
   28 |     await expect(page.locator('h1')).toContainText('Select Business Account');
   29 |     
   30 |     // Check that the personal account option is displayed
   31 |     await expect(page.getByText('Personal Account')).toBeVisible();
   32 |   });
   33 |   
   34 |   test('should create a new business account', async ({ page }) => {
   35 |     // Go to the business create page
   36 |     await page.goto('/business/create');
   37 |     
   38 |     // Check that the page title is displayed
   39 |     await expect(page.locator('h1')).toContainText('Create Business Account');
   40 |     
   41 |     // Fill in the form
   42 |     await page.fill('input[name="name"]', 'Test Business');
   43 |     await page.fill('textarea[name="description"]', 'This is a test business account');
   44 |     await page.selectOption('select[name="industry"]', 'real_estate');
   45 |     await page.selectOption('select[name="size"]', 'small');
   46 |     await page.fill('input[name="website"]', 'https://example.com');
   47 |     await page.fill('input[name="email"]', '<EMAIL>');
   48 |     await page.fill('input[name="phone_number"]', '+**********');
   49 |     
   50 |     // Submit the form
   51 |     await page.click('button[type="submit"]');
   52 |     
   53 |     // Wait for navigation to complete
   54 |     await page.waitForURL('**/business/dashboard');
   55 |     
   56 |     // Check that the business dashboard is displayed
   57 |     await expect(page.locator('h1')).toContainText('Test Business');
   58 |   });
   59 |   
   60 |   test('should navigate to business dashboard', async ({ page }) => {
   61 |     // Go to the business dashboard
   62 |     await page.goto('/business/dashboard');
   63 |     
   64 |     // Check that the dashboard is displayed
   65 |     await expect(page.getByText('Business Dashboard')).toBeVisible();
   66 |     
   67 |     // Check that the statistics are displayed
   68 |     await expect(page.getByText('Listings')).toBeVisible();
   69 |     await expect(page.getByText('Rentals')).toBeVisible();
   70 |     await expect(page.getByText('Members')).toBeVisible();
   71 |     await expect(page.getByText('Performance')).toBeVisible();
   72 |   });
   73 |   
   74 |   test('should navigate to business members page', async ({ page }) => {
   75 |     // Go to the business members page
   76 |     await page.goto('/business/members');
   77 |     
   78 |     // Check that the page title is displayed
   79 |     await expect(page.locator('h1')).toContainText('Business Members');
   80 |     
   81 |     // Check that the invite member button is displayed
   82 |     await expect(page.getByText('Invite Member')).toBeVisible();
   83 |     
   84 |     // Check that the members list is displayed
   85 |     await expect(page.getByText('Members List')).toBeVisible();
   86 |   });
   87 |   
   88 |   test('should invite a new member', async ({ page }) => {
   89 |     // Go to the business members page
   90 |     await page.goto('/business/members');
   91 |     
   92 |     // Click the invite member button
   93 |     await page.click('button:has-text("Invite Member")');
   94 |     
   95 |     // Check that the invite form is displayed
   96 |     await expect(page.getByText('Invite Member')).toBeVisible();
   97 |     
   98 |     // Fill in the form
   99 |     await page.fill('input[name="email"]', '<EMAIL>');
  100 |     await page.selectOption('select[name="role"]', 'member');
  101 |     
  102 |     // Submit the form
  103 |     await page.click('button:has-text("Send Invite")');
  104 |     
  105 |     // Check that the success message is displayed
  106 |     await expect(page.getByText('Invitation sent successfully')).toBeVisible();
  107 |   });
  108 |   
  109 |   test('should navigate to business settings page', async ({ page }) => {
  110 |     // Go to the business settings page
  111 |     await page.goto('/business/settings');
  112 |     
  113 |     // Check that the page title is displayed
  114 |     await expect(page.locator('h1')).toContainText('Business Settings');
  115 |     
  116 |     // Check that the tabs are displayed
  117 |     await expect(page.getByText('Profile')).toBeVisible();
  118 |     await expect(page.getByText('Branding')).toBeVisible();
  119 |     await expect(page.getByText('Notifications')).toBeVisible();
  120 |     await expect(page.getByText('Security')).toBeVisible();
```