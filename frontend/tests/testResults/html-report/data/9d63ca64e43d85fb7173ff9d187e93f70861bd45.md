# Test info

- Name: Search Page >> should display map view
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/search.test.js:185:7

# Error details

```
Error: locator.click: Test ended.
Call log:
  - waiting for getBy<PERSON>ole('button', { name: 'Map View' })

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/search.test.js:188:25
```

# Test source

```ts
   88 |     await expect(searchResults).toBeVisible();
   89 |     
   90 |     // Check result items are from the selected category
   91 |     const resultItems = await page.locator('.item-card').all();
   92 |     expect(resultItems.length).toBeGreaterThan(0);
   93 |     
   94 |     // Check category badge on items
   95 |     const categoryBadges = await page.locator('.category-badge').all();
   96 |     for (const badge of categoryBadges) {
   97 |       await expect(badge).toContainText('Tools', { ignoreCase: true });
   98 |     }
   99 |   });
  100 |
  101 |   test('should filter results by price range', async ({ page }) => {
  102 |     // Set price range filter
  103 |     const minPriceInput = await page.locator('input[name="minPrice"]');
  104 |     const maxPriceInput = await page.locator('input[name="maxPrice"]');
  105 |     
  106 |     await minPriceInput.fill('10');
  107 |     await maxPriceInput.fill('50');
  108 |     
  109 |     const applyFilterButton = await page.getByRole('button', { name: 'Apply' });
  110 |     await applyFilterButton.click();
  111 |     
  112 |     // Check URL has price range filter
  113 |     await expect(page).toHaveURL(/\/search\?.*minPrice=10.*maxPrice=50/);
  114 |     
  115 |     // Check filtered results
  116 |     const searchResults = await page.locator('.search-results');
  117 |     await expect(searchResults).toBeVisible();
  118 |     
  119 |     // Check result items are within price range
  120 |     const resultItems = await page.locator('.item-card').all();
  121 |     expect(resultItems.length).toBeGreaterThan(0);
  122 |     
  123 |     // Check prices on items
  124 |     const itemPrices = await page.locator('.item-price').all();
  125 |     for (const price of itemPrices) {
  126 |       const priceText = await price.textContent();
  127 |       const priceValue = parseFloat(priceText.replace(/[^0-9.]/g, ''));
  128 |       expect(priceValue).toBeGreaterThanOrEqual(10);
  129 |       expect(priceValue).toBeLessThanOrEqual(50);
  130 |     }
  131 |   });
  132 |
  133 |   test('should filter results by listing type', async ({ page }) => {
  134 |     // Select rent-to-buy listing type
  135 |     const rentToBuyFilter = await page.getByText('Rent-to-Buy', { exact: false });
  136 |     await rentToBuyFilter.click();
  137 |     
  138 |     // Check URL has listing type filter
  139 |     await expect(page).toHaveURL(/\/search\?.*listingType=rent-to-buy/);
  140 |     
  141 |     // Check filtered results
  142 |     const searchResults = await page.locator('.search-results');
  143 |     await expect(searchResults).toBeVisible();
  144 |     
  145 |     // Check result items are rent-to-buy listings
  146 |     const resultItems = await page.locator('.item-card').all();
  147 |     expect(resultItems.length).toBeGreaterThan(0);
  148 |     
  149 |     // Check rent-to-buy badge on items
  150 |     const rtbBadges = await page.locator('.rtb-badge').all();
  151 |     expect(rtbBadges.length).toBe(resultItems.length);
  152 |   });
  153 |
  154 |   test('should sort search results', async ({ page }) => {
  155 |     // Open sort dropdown
  156 |     const sortDropdown = await page.locator('.sort-dropdown');
  157 |     await sortDropdown.click();
  158 |     
  159 |     // Select price: low to high
  160 |     const priceLowToHigh = await page.getByText('Price: Low to High');
  161 |     await priceLowToHigh.click();
  162 |     
  163 |     // Check URL has sort parameter
  164 |     await expect(page).toHaveURL(/\/search\?.*sort=price_asc/);
  165 |     
  166 |     // Check sorted results
  167 |     const searchResults = await page.locator('.search-results');
  168 |     await expect(searchResults).toBeVisible();
  169 |     
  170 |     // Check prices are in ascending order
  171 |     const itemPrices = await page.locator('.item-price').all();
  172 |     const prices = [];
  173 |     
  174 |     for (const price of itemPrices) {
  175 |       const priceText = await price.textContent();
  176 |       const priceValue = parseFloat(priceText.replace(/[^0-9.]/g, ''));
  177 |       prices.push(priceValue);
  178 |     }
  179 |     
  180 |     // Check if prices are sorted
  181 |     const sortedPrices = [...prices].sort((a, b) => a - b);
  182 |     expect(prices).toEqual(sortedPrices);
  183 |   });
  184 |
  185 |   test('should display map view', async ({ page }) => {
  186 |     // Click map view button
  187 |     const mapViewButton = await page.getByRole('button', { name: 'Map View' });
> 188 |     await mapViewButton.click();
      |                         ^ Error: locator.click: Test ended.
  189 |     
  190 |     // Check map is displayed
  191 |     const mapContainer = await page.locator('.map-container');
  192 |     await expect(mapContainer).toBeVisible();
  193 |     
  194 |     // Check map markers
  195 |     const mapMarkers = await page.locator('.map-marker').all();
  196 |     expect(mapMarkers.length).toBeGreaterThan(0);
  197 |     
  198 |     // Click a map marker
  199 |     await mapMarkers[0].click();
  200 |     
  201 |     // Check item popup is displayed
  202 |     const itemPopup = await page.locator('.map-item-popup');
  203 |     await expect(itemPopup).toBeVisible();
  204 |     
  205 |     // Check item details in popup
  206 |     await expect(itemPopup.locator('.item-name')).toBeVisible();
  207 |     await expect(itemPopup.locator('.item-price')).toBeVisible();
  208 |     
  209 |     // Click view item button in popup
  210 |     const viewItemButton = await itemPopup.getByRole('link', { name: 'View Item' });
  211 |     await viewItemButton.click();
  212 |     
  213 |     // Should navigate to item details page
  214 |     await expect(page).toHaveURL(/\/items\/[a-zA-Z0-9-]+/);
  215 |   });
  216 | });
  217 |
```