# Test info

- Name: Authentication >> should allow password reset
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/authentication.test.js:149:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('form') resolved to 3 elements:
    1) <form role="search" class="relative flex" aria-label="Site search">…</form> aka getByRole('search', { name: 'Site search' })
    2) <form role="search" class="relative" aria-label="Mobile site search">…</form> aka getByLabel('Mobile site search')
    3) <form class="flex flex-col sm:flex-row gap-2 max-w-md mx-auto">…</form> aka locator('form').filter({ hasText: 'Subscribe' })

Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('form')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/authentication.test.js:162:29
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - heading "404" [level=1]
  - heading "Page Not Found" [level=2]
  - paragraph: The page you are looking for doesn't exist or has been moved.
  - link "Go to Home":
    - /url: /
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   62 |     await expect(emailError).toBeVisible();
   63 |     await expect(passwordError).toBeVisible();
   64 |     
   65 |     // Test invalid email
   66 |     await registrationForm.locator('input[name="name"]').fill('Test User');
   67 |     await registrationForm.locator('input[name="email"]').fill('invalid-email');
   68 |     await registrationForm.locator('input[name="password"]').fill('Password123!');
   69 |     await registrationForm.locator('input[name="confirmPassword"]').fill('Password123!');
   70 |     
   71 |     await registerButton.click();
   72 |     
   73 |     const invalidEmailError = await page.getByText('Invalid email address');
   74 |     await expect(invalidEmailError).toBeVisible();
   75 |     
   76 |     // Test password mismatch
   77 |     await registrationForm.locator('input[name="email"]').fill('<EMAIL>');
   78 |     await registrationForm.locator('input[name="password"]').fill('Password123!');
   79 |     await registrationForm.locator('input[name="confirmPassword"]').fill('DifferentPassword123!');
   80 |     
   81 |     await registerButton.click();
   82 |     
   83 |     const passwordMismatchError = await page.getByText('Passwords do not match');
   84 |     await expect(passwordMismatchError).toBeVisible();
   85 |     
   86 |     // Test weak password
   87 |     await registrationForm.locator('input[name="password"]').fill('weak');
   88 |     await registrationForm.locator('input[name="confirmPassword"]').fill('weak');
   89 |     
   90 |     await registerButton.click();
   91 |     
   92 |     const weakPasswordError = await page.getByText('Password must be at least 8 characters');
   93 |     await expect(weakPasswordError).toBeVisible();
   94 |   });
   95 |
   96 |   test('should allow user login', async ({ page }) => {
   97 |     // Navigate to login page
   98 |     await page.goto('/login');
   99 |     
  100 |     // Check login form
  101 |     const loginForm = await page.locator('form');
  102 |     await expect(loginForm).toBeVisible();
  103 |     
  104 |     // Check form fields
  105 |     await expect(loginForm.locator('input[name="email"]')).toBeVisible();
  106 |     await expect(loginForm.locator('input[name="password"]')).toBeVisible();
  107 |     
  108 |     // Fill login form
  109 |     await loginForm.locator('input[name="email"]').fill('<EMAIL>');
  110 |     await loginForm.locator('input[name="password"]').fill('Password123!');
  111 |     
  112 |     // Submit login form
  113 |     const loginButton = await loginForm.getByRole('button', { name: 'Login' });
  114 |     await loginButton.click();
  115 |     
  116 |     // Check redirect to dashboard
  117 |     await expect(page).toHaveURL('/dashboard');
  118 |   });
  119 |
  120 |   test('should validate login form', async ({ page }) => {
  121 |     // Navigate to login page
  122 |     await page.goto('/login');
  123 |     
  124 |     // Check login form
  125 |     const loginForm = await page.locator('form');
  126 |     await expect(loginForm).toBeVisible();
  127 |     
  128 |     // Submit empty form
  129 |     const loginButton = await loginForm.getByRole('button', { name: 'Login' });
  130 |     await loginButton.click();
  131 |     
  132 |     // Check validation errors
  133 |     const emailError = await page.getByText('Email is required');
  134 |     const passwordError = await page.getByText('Password is required');
  135 |     
  136 |     await expect(emailError).toBeVisible();
  137 |     await expect(passwordError).toBeVisible();
  138 |     
  139 |     // Test invalid credentials
  140 |     await loginForm.locator('input[name="email"]').fill('<EMAIL>');
  141 |     await loginForm.locator('input[name="password"]').fill('WrongPassword123!');
  142 |     
  143 |     await loginButton.click();
  144 |     
  145 |     const invalidCredentialsError = await page.getByText('Invalid email or password');
  146 |     await expect(invalidCredentialsError).toBeVisible();
  147 |   });
  148 |
  149 |   test('should allow password reset', async ({ page }) => {
  150 |     // Navigate to login page
  151 |     await page.goto('/login');
  152 |     
  153 |     // Click forgot password link
  154 |     const forgotPasswordLink = await page.getByRole('link', { name: 'Forgot Password?' });
  155 |     await forgotPasswordLink.click();
  156 |     
  157 |     // Check reset password page
  158 |     await expect(page).toHaveURL('/forgot-password');
  159 |     
  160 |     // Check reset password form
  161 |     const resetForm = await page.locator('form');
> 162 |     await expect(resetForm).toBeVisible();
      |                             ^ Error: expect.toBeVisible: Error: strict mode violation: locator('form') resolved to 3 elements:
  163 |     
  164 |     // Check form fields
  165 |     await expect(resetForm.locator('input[name="email"]')).toBeVisible();
  166 |     
  167 |     // Fill reset form
  168 |     await resetForm.locator('input[name="email"]').fill('<EMAIL>');
  169 |     
  170 |     // Submit reset form
  171 |     const resetButton = await resetForm.getByRole('button', { name: 'Reset Password' });
  172 |     await resetButton.click();
  173 |     
  174 |     // Check success message
  175 |     const successMessage = await page.getByText('Password reset instructions sent');
  176 |     await expect(successMessage).toBeVisible();
  177 |   });
  178 |
  179 |   test('should support social login options', async ({ page }) => {
  180 |     // Navigate to login page
  181 |     await page.goto('/login');
  182 |     
  183 |     // Check social login options
  184 |     const socialLoginSection = await page.locator('.social-login');
  185 |     await expect(socialLoginSection).toBeVisible();
  186 |     
  187 |     // Check Google login button
  188 |     const googleLoginButton = await socialLoginSection.getByRole('button', { name: 'Continue with Google' });
  189 |     await expect(googleLoginButton).toBeVisible();
  190 |     
  191 |     // Check Facebook login button
  192 |     const facebookLoginButton = await socialLoginSection.getByRole('button', { name: 'Continue with Facebook' });
  193 |     await expect(facebookLoginButton).toBeVisible();
  194 |     
  195 |     // Note: We can't fully test social login due to external redirects
  196 |     // but we can verify the buttons exist and have the correct attributes
  197 |     
  198 |     await expect(googleLoginButton).toHaveAttribute('type', 'button');
  199 |     await expect(facebookLoginButton).toHaveAttribute('type', 'button');
  200 |   });
  201 |
  202 |   test('should allow user logout', async ({ page }) => {
  203 |     // Login first
  204 |     await page.goto('/login');
  205 |     
  206 |     await page.fill('input[name="email"]', '<EMAIL>');
  207 |     await page.fill('input[name="password"]', 'Password123!');
  208 |     
  209 |     await page.click('button[type="submit"]');
  210 |     
  211 |     // Wait for dashboard to load
  212 |     await page.waitForURL('/dashboard');
  213 |     
  214 |     // Click user menu
  215 |     const userMenu = await page.locator('.user-menu');
  216 |     await userMenu.click();
  217 |     
  218 |     // Click logout button
  219 |     const logoutButton = await page.getByRole('button', { name: 'Logout' });
  220 |     await logoutButton.click();
  221 |     
  222 |     // Check redirect to home page
  223 |     await expect(page).toHaveURL('/');
  224 |     
  225 |     // Verify logged out state by checking login button is visible
  226 |     const loginButton = await page.getByRole('link', { name: 'Login' });
  227 |     await expect(loginButton).toBeVisible();
  228 |   });
  229 |
  230 |   test('should enforce authentication for protected routes', async ({ page }) => {
  231 |     // Try to access dashboard without login
  232 |     await page.goto('/dashboard');
  233 |     
  234 |     // Should redirect to login page
  235 |     await expect(page).toHaveURL('/login');
  236 |     
  237 |     // Check for unauthorized message
  238 |     const unauthorizedMessage = await page.getByText('Please login to access this page');
  239 |     await expect(unauthorizedMessage).toBeVisible();
  240 |     
  241 |     // Try to access my rentals without login
  242 |     await page.goto('/dashboard/my-rentals');
  243 |     
  244 |     // Should redirect to login page
  245 |     await expect(page).toHaveURL('/login');
  246 |   });
  247 | });
  248 |
```