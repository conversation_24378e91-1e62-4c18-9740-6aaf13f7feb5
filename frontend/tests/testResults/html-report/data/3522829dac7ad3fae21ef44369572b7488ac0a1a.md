# Test info

- Name: Performance-optimized components >> should be accessible
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/performance-components.spec.js:90:7

# Error details

```
Error: expect(received).toEqual(expected) // deep equality

- Expected  -    1
+ Received  + 1244

- Array []
+ Array [
+   Object {
+     "description": "Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds",
+     "help": "Elements must meet minimum color contrast ratio thresholds",
+     "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/color-contrast?application=playwright",
+     "id": "color-contrast",
+     "impact": "serious",
+     "nodes": Array [
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/auctions\" data-discover=\"true\">navigation.auctions</a>",
+                 "target": Array [
+                   "a[href$=\"auctions\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/auctions\" data-discover=\"true\">navigation.auctions</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"auctions\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/visualization\" data-discover=\"true\">navigation.aiInsights</a>",
+                 "target": Array [
+                   "a[href$=\"visualization\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/visualization\" data-discover=\"true\">navigation.aiInsights</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"visualization\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/design-system\" data-discover=\"true\">navigation.designSystem</a>",
+                 "target": Array [
+                   "a[href$=\"design-system\"]:nth-child(6)",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/design-system\" data-discover=\"true\">navigation.designSystem</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"design-system\"]:nth-child(6)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/responsive-design-system\" data-discover=\"true\">navigation.responsiveDesign</a>",
+                 "target": Array [
+                   "a[href$=\"responsive-design-system\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"py-3 text-black hover:text-primary transition-colors font-medium\" href=\"/responsive-design-system\" data-discover=\"true\">navigation.responsiveDesign</a>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           "a[href$=\"responsive-design-system\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#eff6ff",
+               "contrastRatio": 1.08,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "10.5pt (14px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"text-sm font-semibold uppercase tracking-wider bg-accent px-3 py-1.5 rounded-md inline-block shadow-sm border border-accent-dark\">CLAIM THIS OFFER NOW</div>",
+                 "target": Array [
+                   ".tracking-wider.py-1\\.5.px-3",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<div class=\"text-sm font-semibold uppercase tracking-wider bg-accent px-3 py-1.5 rounded-md inline-block shadow-sm border border-accent-dark\">CLAIM THIS OFFER NOW</div>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".tracking-wider.py-1\\.5.px-3",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#60a5fa",
+               "contrastRatio": 2.33,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#eff6fe",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.33 (foreground color: #eff6fe, background color: #60a5fa, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"bg-primary-light w-full h-full rounded-lg overflow-hidden\">",
+                 "target": Array [
+                   ".bg-primary-light",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.33 (foreground color: #eff6fe, background color: #60a5fa, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<p class=\"text-white/90 mt-2\">Activity Tracker, Calorie Tracker</p>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".text-white\\/90",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#eff6ff",
+               "contrastRatio": 1.08,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "10.5pt (14px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"text-sm font-semibold mb-2 bg-accent text-white px-3 py-1.5 rounded-md inline-block shadow-sm border border-accent-dark\">15% CASH BACK</div>",
+                 "target": Array [
+                   ".bg-warning\\/20 > div:nth-child(1) > .py-1\\.5.px-3.border-accent-dark",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 10.5pt (14px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<div class=\"text-sm font-semibold mb-2 bg-accent text-white px-3 py-1.5 rounded-md inline-block shadow-sm border border-accent-dark\">15% CASH BACK</div>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".bg-warning\\/20 > div:nth-child(1) > .py-1\\.5.px-3.border-accent-dark",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">83</span><span class=\"mx-1\">•</span><span>Very Good</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">83</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5 > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">83</span><span class=\"mx-1\">•</span><span>Very Good</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>Very Good</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5 > span:nth-child(3)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"flex items-baseline mb-3 bg-light-gray p-2 rounded-md\"><span class=\"text-primary font-bold\">$25.00</span><span class=\"text-xs text-medium-gray ml-1\">/ day</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"text-xs text-medium-gray ml-1\">/ day</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-1.text-medium-gray.text-xs",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">Owner has had for:</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>0 months</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(1) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > span:nth-child(2)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">83</span><span class=\"mx-1\">•</span><span>Very Good</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">83</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5 > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">83</span><span class=\"mx-1\">•</span><span>Very Good</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>Very Good</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5 > span:nth-child(3)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"flex items-baseline mb-3 bg-light-gray p-2 rounded-md\"><span class=\"text-primary font-bold\">$15.00</span><span class=\"text-xs text-medium-gray ml-1\">/ day</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"text-xs text-medium-gray ml-1\">/ day</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-1.text-medium-gray.text-xs",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">Owner has had for:</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>0 months</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(2) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > span:nth-child(2)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#eff6ff",
+               "contrastRatio": 1.08,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<span class=\"bg-accent text-white text-xs px-2 py-1 rounded-md shadow-sm transform transition-transform group-hover:scale-105\">Rent-to-Buy</span>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .aspect-w-1.aspect-h-1.bg-light-gray > .left-2.space-y-1.top-2 > .py-1.group-hover\\:scale-105.px-2",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"bg-accent text-white text-xs px-2 py-1 rounded-md shadow-sm transform transition-transform group-hover:scale-105\">Rent-to-Buy</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .aspect-w-1.aspect-h-1.bg-light-gray > .left-2.space-y-1.top-2 > .py-1.group-hover\\:scale-105.px-2",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">99</span><span class=\"mx-1\">•</span><span>Excellent</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">99</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5 > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">99</span><span class=\"mx-1\">•</span><span>Excellent</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>Excellent</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5 > span:nth-child(3)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"flex items-baseline mb-3 bg-light-gray p-2 rounded-md\"><span class=\"text-primary font-bold\">$40.00</span><span class=\"text-xs text-medium-gray ml-1\">/ day</span><span class=\"ml-auto text-xs bg-accent text-white px-1.5 py-0.5 rounded-full\">RTB</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"text-xs text-medium-gray ml-1\">/ day</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-1.text-medium-gray.text-xs",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#eff6ff",
+               "contrastRatio": 1.08,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<span class=\"ml-auto text-xs bg-accent text-white px-1.5 py-0.5 rounded-full\">RTB</span>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-auto.px-1\\.5.py-0\\.5",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"ml-auto text-xs bg-accent text-white px-1.5 py-0.5 rounded-full\">RTB</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-auto.px-1\\.5.py-0\\.5",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">Owner has had for:</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>0 months</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(3) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > span:nth-child(2)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#eff6ff",
+               "contrastRatio": 1.08,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<span class=\"bg-accent text-white text-xs px-2 py-1 rounded-md shadow-sm transform transition-transform group-hover:scale-105\">Rent-to-Buy</span>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .aspect-w-1.aspect-h-1.bg-light-gray > .left-2.space-y-1.top-2 > .py-1.group-hover\\:scale-105.px-2:nth-child(1)",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"bg-accent text-white text-xs px-2 py-1 rounded-md shadow-sm transform transition-transform group-hover:scale-105\">Rent-to-Buy</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .aspect-w-1.aspect-h-1.bg-light-gray > .left-2.space-y-1.top-2 > .py-1.group-hover\\:scale-105.px-2:nth-child(1)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">83</span><span class=\"mx-1\">•</span><span>Very Good</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">83</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5 > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#22c55e",
+               "contrastRatio": 2.27,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"inline-flex items-center bg-success text-white rounded-full text-xs px-2 py-0.5\"><span class=\"font-semibold\">83</span><span class=\"mx-1\">•</span><span>Very Good</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 2.27 (foreground color: #ffffff, background color: #22c55e, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>Very Good</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .aspect-w-1.aspect-h-1.bg-light-gray > .right-2.top-2.group-hover\\:scale-110 > .item-reliability > .bg-success.inline-flex.py-0\\.5 > span:nth-child(3)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"flex items-baseline mb-3 bg-light-gray p-2 rounded-md\"><span class=\"text-primary font-bold\">$20.00</span><span class=\"text-xs text-medium-gray ml-1\">/ day</span><span class=\"ml-auto text-xs bg-accent text-white px-1.5 py-0.5 rounded-full\">RTB</span></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"text-xs text-medium-gray ml-1\">/ day</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-1.text-medium-gray.text-xs",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#eff6ff",
+               "contrastRatio": 1.08,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#ffffff",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<span class=\"ml-auto text-xs bg-accent text-white px-1.5 py-0.5 rounded-full\">RTB</span>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-auto.px-1\\.5.py-0\\.5",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 1.08 (foreground color: #ffffff, background color: #eff6ff, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"ml-auto text-xs bg-accent text-white px-1.5 py-0.5 rounded-full\">RTB</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .items-baseline.p-2.rounded-md > .ml-auto.px-1\\.5.py-0\\.5",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span class=\"font-semibold\">Owner has had for:</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > .font-semibold",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#f3f4f6",
+               "contrastRatio": 4.39,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#6b7280",
+               "fontSize": "9.0pt (12px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<div class=\"mb-3 text-xs text-medium-gray bg-light-gray bg-opacity-50 p-2 rounded-md\"><div class=\"flex items-center justify-between mb-1\"><span class=\"font-semibold\">Owner has had for:</span><span>0 months</span></div></div>",
+                 "target": Array [
+                   ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 4.39 (foreground color: #6b7280, background color: #f3f4f6, font size: 9.0pt (12px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<span>0 months</span>",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".shadow-sm.bg-white.rounded-lg:nth-child(4) > .p-4.h-full.flex-col > .bg-opacity-50.p-2.rounded-md > .mb-1.justify-between.items-center > span:nth-child(2)",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+                 "target": Array [
+                   ".p-8.rounded-lg.text-center:nth-child(1) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".p-8.rounded-lg.text-center:nth-child(1) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+                 "target": Array [
+                   ".p-8.rounded-lg.text-center:nth-child(2) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".p-8.rounded-lg.text-center:nth-child(2) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+         ],
+       },
+       Object {
+         "all": Array [],
+         "any": Array [
+           Object {
+             "data": Object {
+               "bgColor": "#ffffff",
+               "contrastRatio": 3.67,
+               "expectedContrastRatio": "4.5:1",
+               "fgColor": "#3b82f6",
+               "fontSize": "12.0pt (16px)",
+               "fontWeight": "normal",
+               "messageKey": null,
+             },
+             "id": "color-contrast",
+             "impact": "serious",
+             "message": "Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+             "relatedNodes": Array [
+               Object {
+                 "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+                 "target": Array [
+                   ".p-8.rounded-lg.text-center:nth-child(3) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+                 ],
+               },
+             ],
+           },
+         ],
+         "failureSummary": "Fix any of the following:
+   Element has insufficient color contrast of 3.67 (foreground color: #3b82f6, background color: #ffffff, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1",
+         "html": "<a class=\"text-primary hover:text-primary-dark font-medium inline-flex items-center transition-colors\" href=\"/safety\" data-discover=\"true\">",
+         "impact": "serious",
+         "none": Array [],
+         "target": Array [
+           ".p-8.rounded-lg.text-center:nth-child(3) > .hover\\:text-primary-dark.inline-flex[href$=\"safety\"]",
+         ],
+       },
+     ],
+     "tags": Array [
+       "cat.color",
+       "wcag2aa",
+       "wcag143",
+       "TTv5",
+       "TT13.c",
+       "EN-301-549",
+       "EN-*******",
+       "ACT",
+     ],
+   },
+   Object {
+     "description": "Ensure that the page, or at least one of its frames contains a level-one heading",
+     "help": "Page should contain a level-one heading",
+     "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/page-has-heading-one?application=playwright",
+     "id": "page-has-heading-one",
+     "impact": "moderate",
+     "nodes": Array [
+       Object {
+         "all": Array [
+           Object {
+             "data": null,
+             "id": "page-has-heading-one",
+             "impact": "moderate",
+             "message": "Page must have a level-one heading",
+             "relatedNodes": Array [],
+           },
+         ],
+         "any": Array [],
+         "failureSummary": "Fix all of the following:
+   Page must have a level-one heading",
+         "html": "<html lang=\"en\">",
+         "impact": "moderate",
+         "none": Array [],
+         "target": Array [
+           "html",
+         ],
+       },
+     ],
+     "tags": Array [
+       "cat.semantics",
+       "best-practice",
+     ],
+   },
+ ]
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/performance-components.spec.js:101:49
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: 83 • Very Good Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: 83 • Very Good Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: Rent-to-Buy 99 • Excellent Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day RTB
  - img
  - text: "Eastside • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: Rent-to-Buy New 83 • Very Good Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day RTB
  - img
  - text: "Northside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   1 | // @ts-check
   2 | import { test, expect } from '@playwright/test';
   3 | import { AxeBuilder } from '@axe-core/playwright';
   4 |
   5 | // Test the performance-optimized components
   6 | test.describe('Performance-optimized components', () => {
   7 |   test.beforeEach(async ({ page }) => {
   8 |     // Navigate to the home page
   9 |     await page.goto('/');
   10 |   });
   11 |
   12 |   test('should load the home page with optimized images', async ({ page }) => {
   13 |     // Wait for the page to be fully loaded
   14 |     await page.waitForLoadState('networkidle');
   15 |     
   16 |     // Check if the page has loaded
   17 |     await expect(page).toHaveTitle(/RentUP/);
   18 |     
   19 |     // Check if images are loaded with proper attributes
   20 |     const images = await page.locator('img').all();
   21 |     expect(images.length).toBeGreaterThan(0);
   22 |     
   23 |     // Check at least one image for responsive attributes
   24 |     const firstImage = images[0];
   25 |     const srcset = await firstImage.getAttribute('srcset');
   26 |     const sizes = await firstImage.getAttribute('sizes');
   27 |     const loading = await firstImage.getAttribute('loading');
   28 |     
   29 |     // Images should have responsive attributes
   30 |     expect(srcset || sizes).toBeTruthy();
   31 |     expect(loading).toBe('lazy');
   32 |   });
   33 |   
   34 |   test('should have proper performance metrics', async ({ page }) => {
   35 |     // Navigate to a page with performance monitoring
   36 |     await page.goto('/visualization-demo');
   37 |     
   38 |     // Wait for the page to be fully loaded
   39 |     await page.waitForLoadState('networkidle');
   40 |     
   41 |     // Check if performance metrics are being collected
   42 |     const hasPerformanceMetrics = await page.evaluate(() => {
   43 |       return typeof window.performance !== 'undefined' && 
   44 |         typeof window.performance.getEntriesByType === 'function' &&
   45 |         window.performance.getEntriesByType('measure').length > 0;
   46 |     });
   47 |     
   48 |     expect(hasPerformanceMetrics).toBe(true);
   49 |   });
   50 |   
   51 |   test('should have service worker registered', async ({ page }) => {
   52 |     // Wait for the service worker to be registered
   53 |     await page.waitForLoadState('networkidle');
   54 |     
   55 |     // Check if service worker is registered
   56 |     const hasServiceWorker = await page.evaluate(() => {
   57 |       return 'serviceWorker' in navigator && 
   58 |         navigator.serviceWorker.controller !== null;
   59 |     });
   60 |     
   61 |     // Service worker might not be registered in test environment
   62 |     // So we'll just log this rather than asserting
   63 |     console.log('Service worker registered:', hasServiceWorker);
   64 |   });
   65 |   
   66 |   test('should render virtualized lists efficiently', async ({ page }) => {
   67 |     // Navigate to a page with virtualized lists
   68 |     await page.goto('/enhanced-visualization-demo');
   69 |     
   70 |     // Wait for the page to be fully loaded
   71 |     await page.waitForLoadState('networkidle');
   72 |     
   73 |     // Check if virtualized list is rendered
   74 |     const virtualizedList = await page.locator('[data-testid="virtualized-list"]').count();
   75 |     
   76 |     // If the page exists and has a virtualized list
   77 |     if (virtualizedList > 0) {
   78 |       // Check that only a subset of items are in the DOM
   79 |       const listItems = await page.locator('[data-testid^="virtualized-list-item-"]').count();
   80 |       
   81 |       // The number of DOM items should be less than the total items
   82 |       // This is a basic check that virtualization is working
   83 |       expect(listItems).toBeLessThan(100);
   84 |     } else {
   85 |       // Skip this test if the page doesn't exist yet
   86 |       test.skip();
   87 |     }
   88 |   });
   89 |   
   90 |   test('should be accessible', async ({ page }) => {
   91 |     // Navigate to the home page
   92 |     await page.goto('/');
   93 |     
   94 |     // Wait for the page to be fully loaded
   95 |     await page.waitForLoadState('networkidle');
   96 |     
   97 |     // Run accessibility tests
   98 |     const accessibilityScanResults = await new AxeBuilder({ page }).analyze();
   99 |     
  100 |     // Check for accessibility violations
> 101 |     expect(accessibilityScanResults.violations).toEqual([]);
      |                                                 ^ Error: expect(received).toEqual(expected) // deep equality
  102 |   });
  103 |   
  104 |   test('should have proper meta tags for SEO and social sharing', async ({ page }) => {
  105 |     // Navigate to the home page
  106 |     await page.goto('/');
  107 |     
  108 |     // Wait for the page to be fully loaded
  109 |     await page.waitForLoadState('networkidle');
  110 |     
  111 |     // Check for essential meta tags
  112 |     const description = await page.locator('meta[name="description"]').getAttribute('content');
  113 |     const ogTitle = await page.locator('meta[property="og:title"]').getAttribute('content');
  114 |     const ogDescription = await page.locator('meta[property="og:description"]').getAttribute('content');
  115 |     
  116 |     // Meta tags should exist and have content
  117 |     expect(description).toBeTruthy();
  118 |     expect(ogTitle).toBeTruthy();
  119 |     expect(ogDescription).toBeTruthy();
  120 |   });
  121 |   
  122 |   test('should have proper security headers', async ({ page, request }) => {
  123 |     // Make a request to the home page
  124 |     const response = await request.get('/');
  125 |     
  126 |     // Check for security headers
  127 |     const headers = response.headers();
  128 |     
  129 |     // Security headers should exist
  130 |     expect(headers['x-content-type-options']).toBe('nosniff');
  131 |     expect(headers['x-frame-options']).toBe('DENY');
  132 |     expect(headers['content-security-policy']).toBeTruthy();
  133 |   });
  134 | });
  135 |
```