# Test info

- Name: Home Page >> should display trust section
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/home.test.js:240:7

# Error details

```
Error: locator.isVisible: Error: strict mode violation: getByText('Reliability') resolved to 4 elements:
    1) <p class="text-dark-gray max-w-2xl mx-auto">Our Item Reliability & History System ensures you…</p> aka getByText('Our Item Reliability &')
    2) <h3 class="text-xl font-semibold text-near-black mb-3">Reliability Scoring</h3> aka getByRole('heading', { name: 'Reliability Scoring' })
    3) <p class="text-dark-gray mb-4">Every item receives a reliability score based on …</p> aka getByText('Every item receives a')
    4) <p class="text-dark-gray">"The reliability score gave me confidence to rent…</p> aka getByText('"The reliability score gave')

Call log:
    - checking visibility of getByText('Reliability')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/home.test.js:270:54
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: Rent-to-Buy 84 • Very Good Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day RTB
  - img
  - text: "Downtown • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: 82 • Very Good Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: Rent-to-Buy 93 • Excellent Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day RTB
  - img
  - text: "Eastside • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: 93 • Excellent Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day
  - img
  - text: "Northside • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
  170 |     const featuredTitle = await findTextContent(page, featuredSectionHeadings);
  171 |
  172 |     // If featured items section exists, proceed with testing
  173 |     if (featuredTitle) {
  174 |       await expect(featuredTitle).toBeVisible('Featured items title should be visible');
  175 |
  176 |       // Check if items are displayed using multiple selector strategies
  177 |       const itemCardSelectors = [
  178 |         '.item-card',
  179 |         '[class*="item"]',
  180 |         '[class*="product"]',
  181 |         '[class*="card"]'
  182 |       ];
  183 |
  184 |       const itemCards = await findElement(page, itemCardSelectors);
  185 |       expect(itemCards).not.toBeNull('Item cards should be present');
  186 |
  187 |       // Only proceed with item details test if we found item cards
  188 |       if (itemCards) {
  189 |         // Get all item cards
  190 |         const allCards = await page.locator(itemCardSelectors[0]).all();
  191 |
  192 |         if (allCards.length > 0) {
  193 |           // Check item details using multiple selector strategies
  194 |           const firstItem = allCards[0];
  195 |
  196 |           // Check for item name
  197 |           const nameSelectors = [
  198 |             '.item-name',
  199 |             '[class*="name"]',
  200 |             '[class*="title"]',
  201 |             'h3',
  202 |             'h4'
  203 |           ];
  204 |
  205 |           const itemName = await findElement(firstItem, nameSelectors);
  206 |           expect(itemName).not.toBeNull('Item name should be present');
  207 |
  208 |           // Check for item price
  209 |           const priceSelectors = [
  210 |             '.item-price',
  211 |             '[class*="price"]',
  212 |             '[class*="cost"]'
  213 |           ];
  214 |
  215 |           const itemPrice = await findElement(firstItem, priceSelectors);
  216 |
  217 |           // Check for item location
  218 |           const locationSelectors = [
  219 |             '.item-location',
  220 |             '[class*="location"]',
  221 |             '[class*="address"]'
  222 |           ];
  223 |
  224 |           const itemLocation = await findElement(firstItem, locationSelectors);
  225 |
  226 |           // Test item navigation
  227 |           await firstItem.click();
  228 |
  229 |           // Should navigate to item details page
  230 |           const navigationSuccessful = await waitForNavigation(page, /\/items\//);
  231 |           expect(navigationSuccessful).toBe(true, 'Navigation to item details page should be successful');
  232 |         }
  233 |       }
  234 |     } else {
  235 |       console.log('Featured items section not found, might not be implemented yet');
  236 |       test.skip();
  237 |     }
  238 |   });
  239 |
  240 |   test('should display trust section', async ({ page }) => {
  241 |     // Check if trust section exists using multiple possible heading texts
  242 |     const trustSectionHeadings = [
  243 |       'Rent with Confidence',
  244 |       'Trust & Safety',
  245 |       'Why Choose Us',
  246 |       'Our Guarantees',
  247 |       'Reliability'
  248 |     ];
  249 |
  250 |     const trustTitle = await findTextContent(page, trustSectionHeadings);
  251 |
  252 |     // If trust section exists, proceed with testing
  253 |     if (trustTitle) {
  254 |       await expect(trustTitle).toBeVisible('Trust section title should be visible');
  255 |
  256 |       // Check for trust features using more flexible matching
  257 |       const trustFeatures = [
  258 |         ['Reliability', 'Score', 'Rating'],
  259 |         ['Maintenance', 'Track', 'History'],
  260 |         ['Verified', 'Reviews', 'Feedback']
  261 |       ];
  262 |
  263 |       // Count how many features we can find
  264 |       let foundFeatures = 0;
  265 |
  266 |       for (const featureOptions of trustFeatures) {
  267 |         // Try each option in the feature array
  268 |         for (const option of featureOptions) {
  269 |           const featureElement = await findTextContent(page, option);
> 270 |           if (featureElement && await featureElement.isVisible()) {
      |                                                      ^ Error: locator.isVisible: Error: strict mode violation: getByText('Reliability') resolved to 4 elements:
  271 |             foundFeatures++;
  272 |             break; // Found one option for this feature, move to next feature
  273 |           }
  274 |         }
  275 |       }
  276 |
  277 |       // We expect to find at least some of the features
  278 |       expect(foundFeatures).toBeGreaterThan(0, 'At least some trust features should be visible');
  279 |     } else {
  280 |       console.log('Trust section not found, might not be implemented yet');
  281 |       test.skip();
  282 |     }
  283 |   });
  284 |
  285 |   test('should display call-to-action section', async ({ page }) => {
  286 |     // Check if CTA section exists using multiple possible heading texts
  287 |     const ctaSectionHeadings = [
  288 |       'Start Saving Today',
  289 |       'Get Started',
  290 |       'Join Now',
  291 |       'Start Renting',
  292 |       'Sign Up Today'
  293 |     ];
  294 |
  295 |     const ctaTitle = await findTextContent(page, ctaSectionHeadings);
  296 |
  297 |     // If CTA section exists, proceed with testing
  298 |     if (ctaTitle) {
  299 |       await expect(ctaTitle).toBeVisible('CTA title should be visible');
  300 |
  301 |       // Check for CTA buttons using multiple selector strategies
  302 |       const primaryButtonSelectors = [
  303 |         'a:has-text("Get Started")',
  304 |         'a:has-text("Join Now")',
  305 |         'a:has-text("Sign Up")',
  306 |         'a[href*="register"]',
  307 |         'button:has-text("Get Started")',
  308 |         '.cta-button',
  309 |         '.primary-button'
  310 |       ];
  311 |
  312 |       const secondaryButtonSelectors = [
  313 |         'a:has-text("Learn More")',
  314 |         'a:has-text("About Us")',
  315 |         'a:has-text("How It Works")',
  316 |         'button:has-text("Learn More")',
  317 |         '.secondary-button'
  318 |       ];
  319 |
  320 |       // Find primary button
  321 |       const primaryButton = await findElement(page, primaryButtonSelectors);
  322 |       expect(primaryButton).not.toBeNull('Primary CTA button should be present');
  323 |
  324 |       // Only proceed with button test if we found the primary button
  325 |       if (primaryButton) {
  326 |         // Test CTA button navigation
  327 |         await primaryButton.click();
  328 |
  329 |         // Should navigate to registration or login page
  330 |         const navigationSuccessful = await waitForNavigation(page, /\/(register|login|signup)/);
  331 |         expect(navigationSuccessful).toBe(true, 'Navigation from CTA button should be successful');
  332 |       }
  333 |     } else {
  334 |       console.log('Call-to-action section not found, might not be implemented yet');
  335 |       test.skip();
  336 |     }
  337 |   });
  338 |
  339 |   test('should have proper header and footer', async ({ page }) => {
  340 |     // Check for header using multiple selector strategies
  341 |     const headerSelectors = [
  342 |       'header',
  343 |       '[role="banner"]',
  344 |       '.header',
  345 |       'nav',
  346 |       '.navbar'
  347 |     ];
  348 |
  349 |     const header = await findElement(page, headerSelectors);
  350 |     expect(header).not.toBeNull('Header should be present');
  351 |
  352 |     // Check for logo using multiple selector strategies
  353 |     const logoSelectors = [
  354 |       '.logo',
  355 |       'a:has-text("rentUP")',
  356 |       'a:has-text("rent")',
  357 |       'img[alt*="logo" i]',
  358 |       'a:first-child img'
  359 |     ];
  360 |
  361 |     const logo = await findElement(page, logoSelectors);
  362 |     expect(logo).not.toBeNull('Logo should be present');
  363 |
  364 |     // Check for navigation links - we don't check specific links to make the test more resilient
  365 |     const navLinkSelectors = [
  366 |       'nav a',
  367 |       'header a',
  368 |       '.nav-links a',
  369 |       '.navbar a'
  370 |     ];
```