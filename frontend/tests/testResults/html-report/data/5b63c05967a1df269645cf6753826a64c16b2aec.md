# Test info

- Name: Home Page >> should display category showcase if implemented
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/resilient-home.test.js:138:7

# Error details

```
Error: expect(received).not.toBeNull()

Matcher error: this matcher must not have an expected argument

Expected has type:  string
Expected has value: "Category section should have category items"
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/resilient-home.test.js:155:33
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: New 92 • Excellent Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: Rent-to-Buy 92 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day RTB
  - img
  - text: "Westside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: 96 • Excellent Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day
  - img
  - text: "Eastside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: Rent-to-Buy 96 • Excellent Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day RTB
  - img
  - text: "Northside • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   55 |     // Wait for the page to be fully loaded
   56 |     await page.waitForLoadState('networkidle');
   57 |     
   58 |     // Log the current URL to help with debugging
   59 |     console.log(`Current URL: ${page.url()}`);
   60 |   });
   61 |   
   62 |   test('should load the home page successfully', async ({ page }) => {
   63 |     // Check if the page has loaded
   64 |     const body = await page.locator('body');
   65 |     await expect(body).toBeVisible();
   66 |     
   67 |     // Check if the page has a title
   68 |     const title = await page.title();
   69 |     expect(title.length).toBeGreaterThan(0, 'Page should have a title');
   70 |     
   71 |     // Check for basic structure
   72 |     const header = await findElement(page, ['header', '[role="banner"]']);
   73 |     expect(header).not.toBeNull('Page should have a header');
   74 |     
   75 |     const main = await findElement(page, ['main', '[role="main"]', '#root > div']);
   76 |     expect(main).not.toBeNull('Page should have a main content area');
   77 |     
   78 |     const footer = await findElement(page, ['footer', '[role="contentinfo"]']);
   79 |     expect(footer).not.toBeNull('Page should have a footer');
   80 |   });
   81 |   
   82 |   test('should display hero section with appropriate content', async ({ page }) => {
   83 |     // Look for hero section using multiple approaches
   84 |     const heroSection = await findElement(page, [
   85 |       '.hero-section', 
   86 |       '[class*="hero"]', 
   87 |       'div[class*="bg-gradient"]',
   88 |       'div:has(h1)'
   89 |     ]);
   90 |     
   91 |     expect(heroSection).not.toBeNull('Hero section should be present');
   92 |     
   93 |     if (heroSection) {
   94 |       // Check for hero title with flexible matching
   95 |       const heroTitle = await findTextContent(page, [
   96 |         'The Community Marketplace for Endless Sharing Possibilities',
   97 |         'Community Marketplace',
   98 |         'Sharing Possibilities',
   99 |         'rentUP'
  100 |       ]);
  101 |       
  102 |       expect(heroTitle).not.toBeNull('Hero section should have a title');
  103 |       
  104 |       // Check for search functionality
  105 |       const searchInput = await findElement(page, [
  106 |         'input[type="text"]', 
  107 |         'input[type="search"]',
  108 |         'input[placeholder*="search" i]',
  109 |         'input[placeholder*="rent" i]'
  110 |       ]);
  111 |       
  112 |       if (searchInput) {
  113 |         // Test search input functionality
  114 |         await searchInput.fill('test search');
  115 |         expect(await searchInput.inputValue()).toBe('test search', 'Search input should accept text');
  116 |         
  117 |         // Look for search button
  118 |         const searchButton = await findElement(page, [
  119 |           'button[type="submit"]',
  120 |           'button:has(svg)',
  121 |           'button:right-of(input)',
  122 |           'button:has-text("Search")'
  123 |         ]);
  124 |         
  125 |         if (searchButton) {
  126 |           // We found a search button, but we won't click it to avoid navigation
  127 |           await expect(searchButton).toBeVisible('Search button should be visible');
  128 |         } else {
  129 |           // If no search button, the form might submit on Enter
  130 |           console.log('No search button found, search might work through form submission');
  131 |         }
  132 |       } else {
  133 |         console.log('Search input not found, search functionality might not be implemented yet');
  134 |       }
  135 |     }
  136 |   });
  137 |   
  138 |   test('should display category showcase if implemented', async ({ page }) => {
  139 |     // Check if category section exists
  140 |     const hasCategorySection = await sectionExists(page, [
  141 |       'Browse by Category',
  142 |       'Categories',
  143 |       'Shop by Category'
  144 |     ]);
  145 |     
  146 |     if (hasCategorySection) {
  147 |       // Look for category cards or items
  148 |       const categoryItems = await findElement(page, [
  149 |         '.category-card',
  150 |         '[class*="category"]',
  151 |         'a[href*="category"]',
  152 |         'a[href*="search"]'
  153 |       ]);
  154 |       
> 155 |       expect(categoryItems).not.toBeNull('Category section should have category items');
      |                                 ^ Error: expect(received).not.toBeNull()
  156 |       
  157 |       // Check for "View All" or similar button
  158 |       const viewAllButton = await findElement(page, [
  159 |         'a:has-text("View All")',
  160 |         'a:has-text("See All")',
  161 |         'a:has-text("Browse All")',
  162 |         'a:has-text("All Categories")'
  163 |       ]);
  164 |       
  165 |       if (viewAllButton) {
  166 |         await expect(viewAllButton).toBeVisible('View All button should be visible');
  167 |       }
  168 |     } else {
  169 |       console.log('Category showcase section not found, might not be implemented yet');
  170 |     }
  171 |   });
  172 |   
  173 |   test('should display featured items if implemented', async ({ page }) => {
  174 |     // Check if featured items section exists
  175 |     const hasFeaturedSection = await sectionExists(page, [
  176 |       'Featured Items',
  177 |       'Popular Items',
  178 |       'Trending Items'
  179 |     ]);
  180 |     
  181 |     if (hasFeaturedSection) {
  182 |       // Look for item cards or listings
  183 |       const itemCards = await findElement(page, [
  184 |         '.item-card',
  185 |         '[class*="item-"]',
  186 |         '[class*="card"]',
  187 |         'div:has(img):has(h3)'
  188 |       ]);
  189 |       
  190 |       expect(itemCards).not.toBeNull('Featured items section should have item cards');
  191 |       
  192 |       // Check for item details like price, name, etc.
  193 |       const itemDetails = await findElement(page, [
  194 |         '.item-price',
  195 |         '.item-name',
  196 |         '[class*="price"]',
  197 |         '[class*="name"]'
  198 |       ]);
  199 |       
  200 |       if (itemDetails) {
  201 |         await expect(itemDetails).toBeVisible('Item details should be visible');
  202 |       }
  203 |     } else {
  204 |       console.log('Featured items section not found, might not be implemented yet');
  205 |     }
  206 |   });
  207 |   
  208 |   test('should display call-to-action section if implemented', async ({ page }) => {
  209 |     // Check if CTA section exists
  210 |     const hasCtaSection = await sectionExists(page, [
  211 |       'Start Saving Today',
  212 |       'Join Now',
  213 |       'Get Started',
  214 |       'Start Renting'
  215 |     ]);
  216 |     
  217 |     if (hasCtaSection) {
  218 |       // Look for CTA buttons
  219 |       const ctaButton = await findElement(page, [
  220 |         'a:has-text("Get Started")',
  221 |         'a:has-text("Join Now")',
  222 |         'a:has-text("Sign Up")',
  223 |         'a[href*="register"]'
  224 |       ]);
  225 |       
  226 |       expect(ctaButton).not.toBeNull('CTA section should have a call-to-action button');
  227 |       
  228 |       if (ctaButton) {
  229 |         // We won't click it to avoid navigation, but we'll check it's visible
  230 |         await expect(ctaButton).toBeVisible('CTA button should be visible');
  231 |       }
  232 |     } else {
  233 |       console.log('Call-to-action section not found, might not be implemented yet');
  234 |     }
  235 |   });
  236 |   
  237 |   test('should have responsive design', async ({ page }) => {
  238 |     // Test mobile viewport
  239 |     await page.setViewportSize({ width: 375, height: 667 });
  240 |     await page.waitForTimeout(300); // Wait for responsive changes
  241 |     
  242 |     // Check if page is still visible on mobile
  243 |     const bodyMobile = await page.locator('body');
  244 |     await expect(bodyMobile).toBeVisible('Page should be visible on mobile');
  245 |     
  246 |     // Look for mobile menu button or hamburger icon
  247 |     const mobileMenuButton = await findElement(page, [
  248 |       'button[aria-label*="menu" i]',
  249 |       'button:has(svg)',
  250 |       '[class*="mobile-menu"]',
  251 |       'header button'
  252 |     ]);
  253 |     
  254 |     if (mobileMenuButton) {
  255 |       await expect(mobileMenuButton).toBeVisible('Mobile menu button should be visible on mobile');
```