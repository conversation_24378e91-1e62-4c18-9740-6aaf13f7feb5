# Test info

- Name: Item Details Page >> should display similar items section
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/item-details.test.js:198:7

# Error details

```
Error: Timed out 5000ms waiting for expect(locator).toBeVisible()

Locator: locator('.similar-items')
Expected: visible
Received: <element(s) not found>
Call log:
  - expect.toBeVisible with timeout 5000ms
  - waiting for locator('.similar-items')

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/item-details.test.js:201:39
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
- alert:
  - img
  - paragraph
  - paragraph
  - button "Close":
    - text: Close
    - img
```

# Test source

```ts
  101 |       // Check rent-to-buy details
  102 |       const rtbDetails = await pricingSection.locator('.rent-to-buy-details');
  103 |       await expect(rtbDetails).toBeVisible();
  104 |       
  105 |       // Check conversion terms
  106 |       const conversionTerms = await rtbDetails.getByText('Conversion Terms', { exact: false });
  107 |       await expect(conversionTerms).toBeVisible();
  108 |     }
  109 |     
  110 |     // Check if auction option is available
  111 |     const auctionOption = await pricingSection.locator('.auction-option').count();
  112 |     
  113 |     if (auctionOption > 0) {
  114 |       // Check auction details
  115 |       const auctionDetails = await pricingSection.locator('.auction-details');
  116 |       await expect(auctionDetails).toBeVisible();
  117 |       
  118 |       // Check auction status
  119 |       const auctionStatus = await auctionDetails.getByText('Auction Status', { exact: false });
  120 |       await expect(auctionStatus).toBeVisible();
  121 |     }
  122 |   });
  123 |
  124 |   test('should display owner profile', async ({ page }) => {
  125 |     // Check owner profile section
  126 |     const ownerProfile = await page.locator('.owner-profile');
  127 |     await expect(ownerProfile).toBeVisible();
  128 |     
  129 |     // Check owner name
  130 |     const ownerName = await ownerProfile.locator('.owner-name');
  131 |     await expect(ownerName).toBeVisible();
  132 |     
  133 |     // Check owner rating
  134 |     const ownerRating = await ownerProfile.locator('.owner-rating');
  135 |     await expect(ownerRating).toBeVisible();
  136 |     
  137 |     // Check contact owner button
  138 |     const contactButton = await ownerProfile.getByRole('button', { name: 'Contact Owner' });
  139 |     await expect(contactButton).toBeVisible();
  140 |     
  141 |     // Test contact owner functionality
  142 |     await contactButton.click();
  143 |     
  144 |     // Check if message modal appears
  145 |     const messageModal = await page.locator('.message-modal');
  146 |     await expect(messageModal).toBeVisible();
  147 |     
  148 |     // Check message form
  149 |     const messageForm = await messageModal.locator('form');
  150 |     await expect(messageForm).toBeVisible();
  151 |     
  152 |     // Close modal
  153 |     const closeButton = await messageModal.getByRole('button', { name: 'Close' });
  154 |     await closeButton.click();
  155 |     
  156 |     // Check modal is closed
  157 |     await expect(messageModal).not.toBeVisible();
  158 |   });
  159 |
  160 |   test('should display reviews section', async ({ page }) => {
  161 |     // Check reviews section
  162 |     const reviewsSection = await page.locator('.reviews-section');
  163 |     await expect(reviewsSection).toBeVisible();
  164 |     
  165 |     // Check reviews title
  166 |     const reviewsTitle = await reviewsSection.getByRole('heading', { name: 'Reviews' });
  167 |     await expect(reviewsTitle).toBeVisible();
  168 |     
  169 |     // Check if reviews exist
  170 |     const reviews = await reviewsSection.locator('.review-item').all();
  171 |     
  172 |     if (reviews.length > 0) {
  173 |       // Check first review
  174 |       const firstReview = reviews[0];
  175 |       
  176 |       // Check reviewer name
  177 |       const reviewerName = await firstReview.locator('.reviewer-name');
  178 |       await expect(reviewerName).toBeVisible();
  179 |       
  180 |       // Check review rating
  181 |       const reviewRating = await firstReview.locator('.review-rating');
  182 |       await expect(reviewRating).toBeVisible();
  183 |       
  184 |       // Check review content
  185 |       const reviewContent = await firstReview.locator('.review-content');
  186 |       await expect(reviewContent).toBeVisible();
  187 |       
  188 |       // Check review date
  189 |       const reviewDate = await firstReview.locator('.review-date');
  190 |       await expect(reviewDate).toBeVisible();
  191 |     } else {
  192 |       // Check no reviews message
  193 |       const noReviews = await reviewsSection.getByText('No reviews yet');
  194 |       await expect(noReviews).toBeVisible();
  195 |     }
  196 |   });
  197 |
  198 |   test('should display similar items section', async ({ page }) => {
  199 |     // Check similar items section
  200 |     const similarItemsSection = await page.locator('.similar-items');
> 201 |     await expect(similarItemsSection).toBeVisible();
      |                                       ^ Error: Timed out 5000ms waiting for expect(locator).toBeVisible()
  202 |     
  203 |     // Check similar items title
  204 |     const similarItemsTitle = await similarItemsSection.getByRole('heading', { name: 'Similar Items' });
  205 |     await expect(similarItemsTitle).toBeVisible();
  206 |     
  207 |     // Check similar item cards
  208 |     const similarItems = await similarItemsSection.locator('.item-card').all();
  209 |     expect(similarItems.length).toBeGreaterThan(0);
  210 |     
  211 |     // Check first similar item
  212 |     const firstSimilarItem = similarItems[0];
  213 |     
  214 |     // Check item name
  215 |     const itemName = await firstSimilarItem.locator('.item-name');
  216 |     await expect(itemName).toBeVisible();
  217 |     
  218 |     // Check item price
  219 |     const itemPrice = await firstSimilarItem.locator('.item-price');
  220 |     await expect(itemPrice).toBeVisible();
  221 |     
  222 |     // Test navigation to similar item
  223 |     await firstSimilarItem.click();
  224 |     
  225 |     // Should navigate to similar item details page
  226 |     await expect(page).toHaveURL(/\/items\/[a-zA-Z0-9-]+/);
  227 |     await expect(page).not.toHaveURL('/items/sample-item-123');
  228 |   });
  229 |
  230 |   test('should allow booking the item', async ({ page }) => {
  231 |     // Check book now button
  232 |     const bookNowButton = await page.getByRole('button', { name: 'Book Now' });
  233 |     await expect(bookNowButton).toBeVisible();
  234 |     
  235 |     // Test booking functionality
  236 |     await bookNowButton.click();
  237 |     
  238 |     // Check if booking form appears
  239 |     const bookingForm = await page.locator('.booking-form');
  240 |     await expect(bookingForm).toBeVisible();
  241 |     
  242 |     // Check date selection
  243 |     const startDateInput = await bookingForm.locator('input[name="startDate"]');
  244 |     const endDateInput = await bookingForm.locator('input[name="endDate"]');
  245 |     
  246 |     await expect(startDateInput).toBeVisible();
  247 |     await expect(endDateInput).toBeVisible();
  248 |     
  249 |     // Select dates
  250 |     await startDateInput.fill('2025-05-01');
  251 |     await endDateInput.fill('2025-05-03');
  252 |     
  253 |     // Check booking summary
  254 |     const bookingSummary = await bookingForm.locator('.booking-summary');
  255 |     await expect(bookingSummary).toBeVisible();
  256 |     
  257 |     // Check total price
  258 |     const totalPrice = await bookingSummary.locator('.total-price');
  259 |     await expect(totalPrice).toBeVisible();
  260 |     
  261 |     // Submit booking
  262 |     const confirmBookingButton = await bookingForm.getByRole('button', { name: 'Confirm Booking' });
  263 |     await confirmBookingButton.click();
  264 |     
  265 |     // Should navigate to payment page
  266 |     await expect(page).toHaveURL(/\/booking\/[a-zA-Z0-9-]+/);
  267 |   });
  268 | });
  269 |
```