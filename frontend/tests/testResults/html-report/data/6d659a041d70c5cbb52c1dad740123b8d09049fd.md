# Test info

- Name: User Dashboard >> should display notifications
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/user-dashboard.test.js:102:7

# Error details

```
Error: page.waitForURL: Test ended.
=========================== logs ===========================
waiting for navigation to "/dashboard" until "load"
============================================================
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/user-dashboard.test.js:24:16
```

# Test source

```ts
   1 | /**
   2 |  * User Dashboard Tests
   3 |  * 
   4 |  * Tests for the user dashboard functionality of the RentUp application.
   5 |  * Based on sitemap.md and README.md requirements.
   6 |  */
   7 |
   8 | import { test, expect } from '@playwright/test';
   9 |
   10 | // User dashboard tests
   11 | test.describe('User Dashboard', () => {
   12 |   test.beforeEach(async ({ page }) => {
   13 |     // Login before each test
   14 |     await page.goto('/login');
   15 |     
   16 |     // Fill login form
   17 |     await page.fill('input[name="email"]', '<EMAIL>');
   18 |     await page.fill('input[name="password"]', 'password123');
   19 |     
   20 |     // Submit login form
   21 |     await page.click('button[type="submit"]');
   22 |     
   23 |     // Wait for dashboard to load
>  24 |     await page.waitForURL('/dashboard');
      |                ^ Error: page.waitForURL: Test ended.
   25 |   });
   26 |
   27 |   test('should display dashboard overview', async ({ page }) => {
   28 |     // Check dashboard title
   29 |     const dashboardTitle = await page.getByRole('heading', { name: 'Dashboard' });
   30 |     await expect(dashboardTitle).toBeVisible();
   31 |     
   32 |     // Check activity overview section
   33 |     const activityOverview = await page.locator('.activity-overview');
   34 |     await expect(activityOverview).toBeVisible();
   35 |     
   36 |     // Check quick actions menu
   37 |     const quickActions = await page.locator('.quick-actions');
   38 |     await expect(quickActions).toBeVisible();
   39 |     
   40 |     // Check dashboard cards
   41 |     const dashboardCards = [
   42 |       'My Rentals',
   43 |       'My Listings',
   44 |       'Messages',
   45 |       'Saved Items',
   46 |       'Transaction History'
   47 |     ];
   48 |     
   49 |     for (const card of dashboardCards) {
   50 |       const cardElement = await page.getByText(card, { exact: false });
   51 |       await expect(cardElement).toBeVisible();
   52 |     }
   53 |   });
   54 |
   55 |   test('should display messages section', async ({ page }) => {
   56 |     // Navigate to messages section
   57 |     await page.click('a[href="/dashboard/messages"]');
   58 |     
   59 |     // Check messages title
   60 |     const messagesTitle = await page.getByRole('heading', { name: 'Messages' });
   61 |     await expect(messagesTitle).toBeVisible();
   62 |     
   63 |     // Check message list
   64 |     const messageList = await page.locator('.message-list');
   65 |     await expect(messageList).toBeVisible();
   66 |     
   67 |     // Check message conversation
   68 |     const conversations = await messageList.locator('.conversation-item').all();
   69 |     
   70 |     if (conversations.length > 0) {
   71 |       // Click on first conversation
   72 |       await conversations[0].click();
   73 |       
   74 |       // Check conversation details
   75 |       const conversationDetails = await page.locator('.conversation-details');
   76 |       await expect(conversationDetails).toBeVisible();
   77 |       
   78 |       // Check message thread
   79 |       const messageThread = await conversationDetails.locator('.message-thread');
   80 |       await expect(messageThread).toBeVisible();
   81 |       
   82 |       // Check message input
   83 |       const messageInput = await conversationDetails.locator('textarea[name="message"]');
   84 |       await expect(messageInput).toBeVisible();
   85 |       
   86 |       // Test sending a message
   87 |       await messageInput.fill('Test message');
   88 |       
   89 |       const sendButton = await conversationDetails.getByRole('button', { name: 'Send' });
   90 |       await sendButton.click();
   91 |       
   92 |       // Check message is sent
   93 |       const sentMessage = await messageThread.getByText('Test message');
   94 |       await expect(sentMessage).toBeVisible();
   95 |     } else {
   96 |       // Check no messages state
   97 |       const noMessages = await page.getByText('No messages yet');
   98 |       await expect(noMessages).toBeVisible();
   99 |     }
  100 |   });
  101 |
  102 |   test('should display notifications', async ({ page }) => {
  103 |     // Navigate to notifications section
  104 |     await page.click('a[href="/dashboard/notifications"]');
  105 |     
  106 |     // Check notifications title
  107 |     const notificationsTitle = await page.getByRole('heading', { name: 'Notifications' });
  108 |     await expect(notificationsTitle).toBeVisible();
  109 |     
  110 |     // Check notifications list
  111 |     const notificationsList = await page.locator('.notifications-list');
  112 |     await expect(notificationsList).toBeVisible();
  113 |     
  114 |     // Check notification items
  115 |     const notifications = await notificationsList.locator('.notification-item').all();
  116 |     
  117 |     if (notifications.length > 0) {
  118 |       // Check first notification
  119 |       const firstNotification = notifications[0];
  120 |       
  121 |       // Check notification content
  122 |       const notificationContent = await firstNotification.locator('.notification-content');
  123 |       await expect(notificationContent).toBeVisible();
  124 |       
```