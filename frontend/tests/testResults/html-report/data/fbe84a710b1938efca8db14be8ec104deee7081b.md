# Test info

- Name: Responsive Design >> typography should be responsive
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/responsive.test.js:414:7

# Error details

```
Error: locator.evaluate: Test timeout of 30000ms exceeded.
Call log:
  - waiting for getByRole('heading', { level: 1 })

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/responsive.test.js:420:48
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - button "common.language"
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - button "Toggle mobile menu": Open menu
  - search "Mobile site search":
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Search for items": Search
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
- main:
  - text: WEE<PERSON><PERSON> SPECIAL
  - heading "Professional DSLR Camera Kit" [level=2]
  - paragraph: 24MP, 4K Video, 3 Lenses Included
  - link "RENT NOW":
    - /url: /search?category=cameras
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "Professional DSLR Camera Kit"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: Rent-to-Buy 88 • Very Good Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day RTB
  - img
  - text: "Downtown • 4 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: New 90 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day
  - img
  - text: "Westside • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: New 89 • Very Good Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day
  - img
  - text: "Eastside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: Rent-to-Buy 91 • Excellent Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day RTB
  - img
  - text: "Northside • 1 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
  320 |
  321 |     // Check if gallery and details are side by side
  322 |     const gallery = await page.locator('.photo-gallery');
  323 |     const details = await page.locator('.item-details');
  324 |
  325 |     const galleryBounds = await gallery.boundingBox();
  326 |     const detailsBounds = await details.boundingBox();
  327 |
  328 |     // Check if gallery is on the left and details on the right
  329 |     expect(galleryBounds.x + galleryBounds.width).toBeLessThanOrEqual(detailsBounds.x + 20);
  330 |
  331 |     // Check if they are roughly on the same vertical position
  332 |     expect(Math.abs(galleryBounds.y - detailsBounds.y)).toBeLessThan(50);
  333 |   });
  334 |
  335 |   test('user dashboard should be responsive on mobile', async ({ page }) => {
  336 |     // Login first
  337 |     await page.goto('/login');
  338 |
  339 |     await page.fill('input[name="email"]', '<EMAIL>');
  340 |     await page.fill('input[name="password"]', 'Password123!');
  341 |
  342 |     await page.click('button[type="submit"]');
  343 |
  344 |     // Wait for dashboard to load
  345 |     await page.waitForURL('/dashboard');
  346 |
  347 |     // Set viewport to mobile size
  348 |     await page.setViewportSize(viewports.mobile);
  349 |
  350 |     // Check if sidebar is hidden by default
  351 |     const sidebar = await page.locator('.dashboard-sidebar');
  352 |     await expect(sidebar).not.toBeVisible();
  353 |
  354 |     // Check if sidebar toggle button is visible
  355 |     const sidebarToggle = await page.locator('.sidebar-toggle');
  356 |     await expect(sidebarToggle).toBeVisible();
  357 |
  358 |     // Open sidebar
  359 |     await sidebarToggle.click();
  360 |
  361 |     // Check if sidebar is now visible
  362 |     await expect(sidebar).toBeVisible();
  363 |
  364 |     // Check if dashboard cards are stacked
  365 |     const dashboardCards = await page.locator('.dashboard-card').all();
  366 |     if (dashboardCards.length > 1) {
  367 |       const firstCard = dashboardCards[0];
  368 |       const secondCard = dashboardCards[1];
  369 |
  370 |       const firstCardBounds = await firstCard.boundingBox();
  371 |       const secondCardBounds = await secondCard.boundingBox();
  372 |
  373 |       expect(secondCardBounds.y).toBeGreaterThan(firstCardBounds.y + firstCardBounds.height - 10);
  374 |     }
  375 |   });
  376 |
  377 |   test('user dashboard should be responsive on desktop', async ({ page }) => {
  378 |     // Login first
  379 |     await page.goto('/login');
  380 |
  381 |     await page.fill('input[name="email"]', '<EMAIL>');
  382 |     await page.fill('input[name="password"]', 'Password123!');
  383 |
  384 |     await page.click('button[type="submit"]');
  385 |
  386 |     // Wait for dashboard to load
  387 |     await page.waitForURL('/dashboard');
  388 |
  389 |     // Set viewport to desktop size
  390 |     await page.setViewportSize(viewports.desktop);
  391 |
  392 |     // Check if sidebar is visible by default
  393 |     const sidebar = await page.locator('.dashboard-sidebar');
  394 |     await expect(sidebar).toBeVisible();
  395 |
  396 |     // Check if sidebar toggle button is not visible
  397 |     const sidebarToggle = await page.locator('.sidebar-toggle').count();
  398 |     expect(sidebarToggle).toBe(0);
  399 |
  400 |     // Check if dashboard cards are in a grid
  401 |     const dashboardCards = await page.locator('.dashboard-card').all();
  402 |     if (dashboardCards.length > 2) {
  403 |       const firstCard = dashboardCards[0];
  404 |       const secondCard = dashboardCards[1];
  405 |
  406 |       const firstCardBounds = await firstCard.boundingBox();
  407 |       const secondCardBounds = await secondCard.boundingBox();
  408 |
  409 |       // Check if cards are side by side
  410 |       expect(Math.abs(firstCardBounds.y - secondCardBounds.y)).toBeLessThan(10);
  411 |     }
  412 |   });
  413 |
  414 |   test('typography should be responsive', async ({ page }) => {
  415 |     // Test on mobile
  416 |     await page.setViewportSize(viewports.mobile);
  417 |     await page.goto('/');
  418 |
  419 |     const mobileHeading = await page.getByRole('heading', { level: 1 });
> 420 |     const mobileFontSize = await mobileHeading.evaluate(el => parseInt(window.getComputedStyle(el).fontSize));
      |                                                ^ Error: locator.evaluate: Test timeout of 30000ms exceeded.
  421 |
  422 |     // Test on desktop
  423 |     await page.setViewportSize(viewports.desktop);
  424 |     await page.goto('/');
  425 |
  426 |     const desktopHeading = await page.getByRole('heading', { level: 1 });
  427 |     const desktopFontSize = await desktopHeading.evaluate(el => parseInt(window.getComputedStyle(el).fontSize));
  428 |
  429 |     // Desktop heading should be larger than mobile heading
  430 |     expect(desktopFontSize).toBeGreaterThan(mobileFontSize);
  431 |   });
  432 |
  433 |   test('images should be responsive', async ({ page }) => {
  434 |     // Test on mobile
  435 |     await page.setViewportSize(viewports.mobile);
  436 |     await page.goto('/');
  437 |
  438 |     const mobileImages = await page.locator('img').all();
  439 |
  440 |     for (const img of mobileImages) {
  441 |       const hasSrcset = await img.evaluate(el => el.hasAttribute('srcset') || el.parentElement.style.backgroundImage !== '');
  442 |       const hasResponsiveClass = await img.evaluate(el => {
  443 |         return el.classList.contains('img-fluid') ||
  444 |                el.classList.contains('responsive-img') ||
  445 |                window.getComputedStyle(el).maxWidth === '100%';
  446 |       });
  447 |
  448 |       expect(hasSrcset || hasResponsiveClass).toBe(true);
  449 |     }
  450 |   });
  451 | });
  452 |
```