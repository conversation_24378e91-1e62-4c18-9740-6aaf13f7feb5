# Test info

- Name: Accessibility >> form validation should be accessible
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/accessibility.test.js:495:7

# Error details

```
Error: locator.click: Test timeout of 30000ms exceeded.
Call log:
  - waiting for getByRole('button', { name: 'Login' })

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/accessibility.test.js:501:24
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - img "rentUP Logo"
  - text: The Community Marketplace for Endless Shared Possibilities Rent anything from tools to spaces, or list your items to earn extra income. Join our community of sharers today!
  - heading "Log In" [level=1]
  - paragraph: Welcome to rentUP
  - text: Password QR Code Email
  - textbox "Email"
  - text: Password
  - textbox "Password"
  - button "Show password":
    - img
  - checkbox "Remember me"
  - text: Remember me
  - link "Forgot password?":
    - /url: /forgot-password
  - button "LOG IN"
  - text: OR
  - button "Sign in with Google":
    - img
    - text: Sign in with Google
  - iframe
  - button "Facebook":
    - img
    - text: Facebook
  - button "Sign in with Apple":
    - img
    - text: Sign in with Apple
  - paragraph:
    - text: New to rentUP?
    - link "Sign up":
      - /url: /register
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
  401 |     // Check form inputs have proper labels
  402 |     const inputs = await page.locator('input:not([type="hidden"]), select, textarea').all();
  403 |
  404 |     for (const input of inputs) {
  405 |       // Check for associated label or aria-label
  406 |       const hasLabel = await input.evaluate((el) => {
  407 |         const id = el.getAttribute('id');
  408 |         return id && document.querySelector(`label[for="${id}"]`) !== null ||
  409 |                el.getAttribute('aria-label') !== null ||
  410 |                el.getAttribute('aria-labelledby') !== null;
  411 |       });
  412 |
  413 |       expect(hasLabel).toBe(true);
  414 |     }
  415 |   });
  416 |
  417 |   test('images should have alt text', async ({ page }) => {
  418 |     // Navigate to the home page
  419 |     await page.goto('/');
  420 |
  421 |     // Check all images have alt text
  422 |     const images = await page.locator('img').all();
  423 |
  424 |     for (const image of images) {
  425 |       // Check for alt attribute
  426 |       const hasAlt = await image.evaluate((el) => {
  427 |         return el.hasAttribute('alt');
  428 |       });
  429 |
  430 |       expect(hasAlt).toBe(true);
  431 |     }
  432 |
  433 |     // Check decorative images have empty alt text
  434 |     const decorativeImages = await page.locator('img[alt=""]').all();
  435 |
  436 |     for (const image of decorativeImages) {
  437 |       // Check if image is truly decorative (e.g., has role="presentation")
  438 |       const isDecorative = await image.evaluate((el) => {
  439 |         return el.getAttribute('role') === 'presentation' ||
  440 |                el.getAttribute('aria-hidden') === 'true';
  441 |       });
  442 |
  443 |       expect(isDecorative).toBe(true);
  444 |     }
  445 |   });
  446 |
  447 |   test('color contrast should meet WCAG standards', async ({ page }) => {
  448 |     // Navigate to the home page
  449 |     await page.goto('/');
  450 |
  451 |     // Run axe accessibility tests with specific rules for color contrast
  452 |     const accessibilityScanResults = await new AxeBuilder({ page })
  453 |       .include(['color-contrast'])
  454 |       .analyze();
  455 |
  456 |     // Check for color contrast violations
  457 |     expect(accessibilityScanResults.violations).toEqual([]);
  458 |   });
  459 |
  460 |   test('should support screen readers', async ({ page }) => {
  461 |     // Navigate to the home page
  462 |     await page.goto('/');
  463 |
  464 |     // Check for skip link
  465 |     const skipLink = await page.locator('a[href="#main-content"]');
  466 |     await expect(skipLink).toBeVisible();
  467 |
  468 |     // Check for proper heading structure
  469 |     const headings = await page.locator('h1, h2, h3, h4, h5, h6').all();
  470 |
  471 |     // Get heading levels
  472 |     const headingLevels = await Promise.all(
  473 |       headings.map(async (heading) => {
  474 |         const tagName = await heading.evaluate(el => el.tagName);
  475 |         return parseInt(tagName.substring(1));
  476 |       })
  477 |     );
  478 |
  479 |     // Check if heading levels are properly nested (no skipping levels)
  480 |     let previousLevel = 0;
  481 |     for (const level of headingLevels) {
  482 |       expect(level).toBeLessThanOrEqual(previousLevel + 1);
  483 |       previousLevel = level;
  484 |     }
  485 |
  486 |     // Check for proper landmark regions
  487 |     const landmarks = await page.locator('header, main, footer, nav, [role="banner"], [role="main"], [role="contentinfo"], [role="navigation"]').all();
  488 |     expect(landmarks.length).toBeGreaterThan(0);
  489 |
  490 |     // Check for proper document language
  491 |     const htmlLang = await page.evaluate(() => document.documentElement.lang);
  492 |     expect(htmlLang).not.toBe('');
  493 |   });
  494 |
  495 |   test('form validation should be accessible', async ({ page }) => {
  496 |     // Navigate to the login page
  497 |     await page.goto('/login');
  498 |
  499 |     // Submit empty form to trigger validation
  500 |     const submitButton = await page.getByRole('button', { name: 'Login' });
> 501 |     await submitButton.click();
      |                        ^ Error: locator.click: Test timeout of 30000ms exceeded.
  502 |
  503 |     // Check for accessible error messages
  504 |     const errorMessages = await page.locator('[role="alert"]').all();
  505 |     expect(errorMessages.length).toBeGreaterThan(0);
  506 |
  507 |     // Check if error messages are associated with form fields
  508 |     const emailInput = await page.locator('input[name="email"]');
  509 |     const emailInputId = await emailInput.evaluate(el => el.id);
  510 |
  511 |     const emailError = await page.locator(`[aria-describedby*="${emailInputId}"]`);
  512 |     await expect(emailError).toBeVisible();
  513 |   });
  514 |
  515 |   test('focus indicators should be visible', async ({ page }) => {
  516 |     // Navigate to the home page
  517 |     await page.goto('/');
  518 |
  519 |     // Focus on a button
  520 |     const button = await page.locator('button').first();
  521 |     await button.focus();
  522 |
  523 |     // Check if focus style is applied
  524 |     const hasFocusStyle = await button.evaluate((el) => {
  525 |       const styles = window.getComputedStyle(el);
  526 |       return styles.outlineWidth !== '0px' ||
  527 |              styles.boxShadow !== 'none' ||
  528 |              el.classList.contains('focus') ||
  529 |              el.classList.contains('focus-visible');
  530 |     });
  531 |
  532 |     expect(hasFocusStyle).toBe(true);
  533 |   });
  534 | });
  535 |
```