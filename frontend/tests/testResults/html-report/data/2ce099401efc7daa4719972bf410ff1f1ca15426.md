# Test info

- Name: Basic Smoke Test >> should have critical UI components
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/basic-smoke.test.js:146:7

# Error details

```
Error: expect(received).not.toBeNull()

Matcher error: this matcher must not have an expected argument

Expected has type:  string
Expected has value: "Header or navigation should be present"
    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/testscripts/frontend/basic-smoke.test.js:160:24
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - text: CLAIM THIS OFFER NOW
  - heading "New Bluetooth Calling Smart Watch, 550" [level=2]
  - paragraph: Activity Tracker, Calorie Tracker
  - link "RENT NOW":
    - /url: /search?category=smart-watches
  - button "Go to slide 1"
  - button "Go to slide 2"
  - button "Go to slide 3"
  - img "New Bluetooth Calling Smart Watch, 550"
  - text: 15% CASH BACK
  - heading "Apple iPhone 14 Pro Max" [level=3]
  - paragraph: Retina XDR Display
  - link "RENT NOW":
    - /url: /search?category=smartphones
  - img "iPhone 14 Pro Max"
  - text: BEST DISCOUNTS
  - heading "Meta Oculus Quest 128GB" [level=3]
  - paragraph: High Resolution
  - link "RENT NOW":
    - /url: /search?category=vr-headsets
  - img "Meta Oculus Quest"
  - img
  - paragraph: Android TV
  - img
  - paragraph: Speakers
  - img
  - paragraph: Cameras
  - img
  - paragraph: Mobile
  - img
  - paragraph: New Laptop
  - img
  - paragraph: Smart Watches
  - region "Browse by Category":
    - text: Explore Our Categories
    - heading "Browse by Category" [level=2]
    - paragraph: Discover thousands of items available for rent across our diverse categories
    - text: Failed to load categories. Using fallback data.
    - link "Medical & Mobility Equipment category with 8 subcategories":
      - /url: /search?category=medical-equipment
      - heading "Medical & Mobility Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Medical devices, mobility aids, and healthcare equipment
      - img
    - link "Vehicles & Transportation category with 8 subcategories":
      - /url: /search?category=vehicles
      - heading "Vehicles & Transportation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Cars, motorcycles, boats, bicycles, and other modes of transportation
      - img
    - link "Real Estate & Spaces category with 8 subcategories":
      - /url: /search?category=real-estate
      - heading "Real Estate & Spaces" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Residential properties, commercial spaces, event venues, and more
      - img
    - link "Electronics & Technology category with 8 subcategories":
      - /url: /search?category=electronics
      - heading "Electronics & Technology" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Computers, smartphones, audio equipment, and other electronic devices
      - img
    - link "Home & Furniture category with 8 subcategories":
      - /url: /search?category=home-furniture
      - heading "Home & Furniture" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Furniture, appliances, home decor, and household items
      - img
    - link "Tools & Equipment category with 8 subcategories":
      - /url: /search?category=tools-equipment
      - heading "Tools & Equipment" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Power tools, hand tools, construction equipment, and specialized tools
      - img
    - link "Sports & Recreation category with 8 subcategories":
      - /url: /search?category=sports-recreation
      - heading "Sports & Recreation" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Sports equipment, outdoor gear, fitness equipment, and recreational items
      - img
    - link "Fashion & Accessories category with 8 subcategories":
      - /url: /search?category=fashion
      - heading "Fashion & Accessories" [level=3]
      - paragraph: 8 subcategories
      - paragraph: Clothing, accessories, jewelry, and fashion items
      - img
    - link "View all categories":
      - /url: /search
      - text: View All Categories
      - img
  - text: Featured Rentals
  - heading "Featured Items" [level=2]
  - paragraph: Discover our most popular and highly-rated rental items
  - img "Power Drill"
  - link "Quick view of this item":
    - /url: /items/1
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/1
    - img
  - text: New 83 • Very Good Tools & Equipment
  - heading "Power Drill" [level=3]:
    - link "Power Drill":
      - /url: /items/1
  - img
  - text: By John D. •
  - img
  - text: 4.5 (12) $25.00 / day
  - img
  - text: "Downtown • 3 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/1
    - text: Rent Now
    - img
  - img "Mountain Bike"
  - link "Quick view of this item":
    - /url: /items/2
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/2
    - img
  - text: Rent-to-Buy New 91 • Excellent Tools & Equipment
  - heading "Mountain Bike" [level=3]:
    - link "Mountain Bike":
      - /url: /items/2
  - img
  - text: By John D. •
  - img
  - text: 5.0 (8) $15.00 / day RTB
  - img
  - text: "Westside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/2
    - text: Rent Now
    - img
  - img "Projector"
  - link "Quick view of this item":
    - /url: /items/3
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/3
    - img
  - text: Rent-to-Buy 87 • Very Good Tools & Equipment
  - heading "Projector" [level=3]:
    - link "Projector":
      - /url: /items/3
  - img
  - text: By John D. •
  - img
  - text: 3.5 (3) $40.00 / day RTB
  - img
  - text: "Eastside • 5 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/3
    - text: Rent Now
    - img
  - img "Camping Tent"
  - link "Quick view of this item":
    - /url: /items/4
    - img
  - button "Add this item to your wishlist":
    - img
  - link "Rent this item now":
    - /url: /book/4
    - img
  - text: 99 • Excellent Tools & Equipment
  - heading "Camping Tent" [level=3]:
    - link "Camping Tent":
      - /url: /items/4
  - img
  - text: By John D. •
  - img
  - text: 4.5 (15) $20.00 / day
  - img
  - text: "Northside • 2 miles away Owner has had for: 0 months"
  - link "Rent Now":
    - /url: /items/4
    - text: Rent Now
    - img
  - link "View all items":
    - /url: /search
    - text: View All Items
    - img
  - heading "Rent with Confidence" [level=2]
  - paragraph: Our Item Reliability & History System ensures you always know the condition and history of what you're renting
  - img
  - heading "Reliability Scoring" [level=3]
  - paragraph: Every item receives a reliability score based on its history, maintenance records, and testing frequency.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Maintenance Tracking" [level=3]
  - paragraph: View detailed maintenance records and testing history for every item before you rent.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - img
  - heading "Verified Reviews" [level=3]
  - paragraph: All reviews come from verified renters who have actually used the item, ensuring honest feedback.
  - link "Learn More":
    - /url: /safety
    - text: Learn More
    - img
  - heading "What Our Community Says" [level=3]
  - heading "Sarah K." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"The reliability score gave me confidence to rent an expensive camera for my daughter's wedding. It worked perfectly and saved us thousands of dollars!\""
  - heading "Michael T." [level=4]
  - img
  - img
  - img
  - img
  - img
  - paragraph: "\"As someone who rents medical equipment for my mother, the maintenance records and testing history give me peace of mind that everything will work properly.\""
  - heading "Start Saving Today!" [level=2]
  - paragraph: Join thousands of smart renters and lenders in your community. List your first item in minutes.
  - link "Get Started":
    - /url: /register
  - link "How It Works":
    - /url: /how-it-works
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   60 |     await page.reload();
   61 |     await page.waitForLoadState('networkidle');
   62 |
   63 |     // Log any errors found but don't fail the test for non-critical console errors
   64 |     if (errors.length > 0) {
   65 |       console.log(`Console errors found: ${errors.join(', ')}`);
   66 |     }
   67 |
   68 |     // Check if page is interactive by looking for clickable elements
   69 |     const interactiveElementSelectors = [
   70 |       'a[href]',
   71 |       'button',
   72 |       'input',
   73 |       '[role="button"]',
   74 |       '[class*="button"]'
   75 |     ];
   76 |
   77 |     const interactiveElement = await findElement(page, interactiveElementSelectors);
   78 |     expect(interactiveElement).not.toBeNull('Page should have interactive elements');
   79 |   });
   80 |
   81 |   test('should have proper HTML structure and metadata', async ({ page }) => {
   82 |     // Check for HTML lang attribute
   83 |     const html = await page.locator('html');
   84 |     const langAttribute = await html.getAttribute('lang');
   85 |     expect(langAttribute).toBeTruthy('HTML should have a lang attribute');
   86 |
   87 |     // Check for meta tags using multiple selector strategies
   88 |     const metaTagSelectors = [
   89 |       'meta[name="viewport"]',
   90 |       'meta[charset]',
   91 |       'meta'
   92 |     ];
   93 |
   94 |     const metaTag = await findElement(page, metaTagSelectors);
   95 |     expect(metaTag).not.toBeNull('Page should have meta tags');
   96 |
   97 |     // Check for favicon using multiple selector strategies
   98 |     const faviconSelectors = [
   99 |       'link[rel="icon"]',
  100 |       'link[rel*="icon"]',
  101 |       'link[href*="favicon"]',
  102 |       'link[href*=".ico"]',
  103 |       'link[href*=".svg"]'
  104 |     ];
  105 |
  106 |     const favicon = await findElement(page, faviconSelectors);
  107 |     expect(favicon).not.toBeNull('Page should have a favicon');
  108 |
  109 |     // Check for title
  110 |     const title = await page.title();
  111 |     expect(title.length).toBeGreaterThan(0, 'Page should have a title');
  112 |
  113 |     // Check for Google Fonts
  114 |     const googleFontsSelectors = [
  115 |       'link[href*="fonts.googleapis.com"]',
  116 |       'link[href*="fonts"]'
  117 |     ];
  118 |
  119 |     const googleFonts = await findElement(page, googleFontsSelectors);
  120 |
  121 |     // Don't fail the test if Google Fonts are not found, just log it
  122 |     if (!googleFonts) {
  123 |       console.log('Google Fonts not found, but this is not critical');
  124 |     }
  125 |   });
  126 |
  127 |   test('should have a proper document title', async ({ page }) => {
  128 |     // Check if the page has a title
  129 |     const title = await page.title();
  130 |     expect(title.length).toBeGreaterThan(0, 'Page should have a title');
  131 |
  132 |     // Check if title contains expected text using case-insensitive matching
  133 |     const expectedTitleTexts = ['rentUP', 'rent', 'community', 'marketplace'];
  134 |     let foundExpectedText = false;
  135 |
  136 |     for (const expectedText of expectedTitleTexts) {
  137 |       if (title.toLowerCase().includes(expectedText.toLowerCase())) {
  138 |         foundExpectedText = true;
  139 |         break;
  140 |       }
  141 |     }
  142 |
  143 |     expect(foundExpectedText).toBe(true, 'Title should contain expected text');
  144 |   });
  145 |
  146 |   test('should have critical UI components', async ({ page }) => {
  147 |     // Check for header using multiple selector strategies
  148 |     const headerSelectors = [
  149 |       'header',
  150 |       '[role="banner"]',
  151 |       '.header',
  152 |       'nav',
  153 |       '.navbar',
  154 |       'div:has(a:has-text("rentUP"))',
  155 |       'div:has(a:has-text("rent"))',
  156 |       'div:first-child'
  157 |     ];
  158 |
  159 |     const header = await findElement(page, headerSelectors);
> 160 |     expect(header).not.toBeNull('Header or navigation should be present');
      |                        ^ Error: expect(received).not.toBeNull()
  161 |
  162 |     // Check for main content area using multiple selector strategies
  163 |     const mainContentSelectors = [
  164 |       'main',
  165 |       '[role="main"]',
  166 |       '.main',
  167 |       '.content',
  168 |       '#root > div > div:nth-child(2)',
  169 |       'div.flex-grow',
  170 |       'div:not(:first-child):not(:last-child)'
  171 |     ];
  172 |
  173 |     const mainContent = await findElement(page, mainContentSelectors);
  174 |     expect(mainContent).not.toBeNull('Main content area should be present');
  175 |
  176 |     // Check for footer using multiple selector strategies
  177 |     const footerSelectors = [
  178 |       'footer',
  179 |       '[role="contentinfo"]',
  180 |       '.footer',
  181 |       'div:last-child:has(p)',
  182 |       'div:has-text("rights reserved")',
  183 |       'div:has-text("©")',
  184 |       'div:last-child'
  185 |     ];
  186 |
  187 |     const footer = await findElement(page, footerSelectors);
  188 |
  189 |     // Don't fail the test if footer is not found, just log it
  190 |     if (!footer) {
  191 |       console.log('Footer not found, but this is not critical');
  192 |     }
  193 |
  194 |     // Check for navigation links
  195 |     const navLinkSelectors = [
  196 |       'a[href]',
  197 |       'button',
  198 |       '[role="link"]',
  199 |       '[role="button"]'
  200 |     ];
  201 |
  202 |     const navLinks = await findElement(page, navLinkSelectors);
  203 |     expect(navLinks).not.toBeNull('Navigation links should be present');
  204 |
  205 |     // Check for logo or brand name
  206 |     const logoSelectors = [
  207 |       '.logo',
  208 |       'img[alt*="logo" i]',
  209 |       'a:has-text("rentUP")',
  210 |       'a:has-text("rent")',
  211 |       'a:first-child'
  212 |     ];
  213 |
  214 |     const logo = await findElement(page, logoSelectors);
  215 |     expect(logo).not.toBeNull('Logo or brand name should be present');
  216 |   });
  217 |
  218 |   test('should have working navigation links', async ({ page }) => {
  219 |     // Find navigation links using multiple selector strategies
  220 |     const navLinkSelectors = [
  221 |       'a[href]',
  222 |       'button[onclick]',
  223 |       '[role="link"]',
  224 |       '[role="button"]'
  225 |     ];
  226 |
  227 |     // Get all navigation links
  228 |     let navLinks = [];
  229 |
  230 |     for (const selector of navLinkSelectors) {
  231 |       const links = await page.locator(selector).all();
  232 |       if (links.length > 0) {
  233 |         navLinks = links;
  234 |         break; // Found some links, no need to try other selectors
  235 |       }
  236 |     }
  237 |
  238 |     expect(navLinks.length).toBeGreaterThan(0, 'Page should have navigation links');
  239 |
  240 |     // Check for home link using multiple selector strategies
  241 |     const homeLinkSelectors = [
  242 |       'a[href="/"]',
  243 |       'a:has-text("Home")',
  244 |       'a:has-text("rentUP")',
  245 |       'a:has-text("rent")',
  246 |       'a:first-child'
  247 |     ];
  248 |
  249 |     const homeLink = await findElement(page, homeLinkSelectors);
  250 |
  251 |     // Don't fail the test if home link is not found, just log it
  252 |     if (!homeLink) {
  253 |       console.log('Home link not found, but this is not critical');
  254 |     }
  255 |
  256 |     // Check for search link using multiple selector strategies
  257 |     const searchLinkSelectors = [
  258 |       'a[href*="/search"]',
  259 |       'a:has-text("Search")',
  260 |       'a:has(svg)',
```