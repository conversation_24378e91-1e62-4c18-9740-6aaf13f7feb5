# Test info

- Name: Agreement Components >> should be able to submit the signature
- Location: /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/agreement-components.spec.js:128:7

# Error details

```
Error: page.waitForSelector: Test timeout of 30000ms exceeded.
Call log:
  - waiting for locator('h2:has-text("Sign Agreement")') to be visible

    at /home/<USER>/Documents/agentLabsNetwork/rentup/frontend/tests/agreement-components.spec.js:133:16
```

# Page snapshot

```yaml
- banner:
  - link "Skip to main content":
    - /url: "#main-content"
  - paragraph: common.tagline
  - button "common.language": English
  - navigation "Secondary Navigation":
    - link "navigation.aboutUs":
      - /url: /about
    - link "navigation.contactUs":
      - /url: /contact
    - link "common.help":
      - /url: /help
  - link "rentUP Logo rentUP":
    - /url: /
    - img "rentUP Logo"
    - text: rentUP
  - search "Site search":
    - button "Select category":
      - text: All Categories
      - img
    - text: Search for items to rent
    - searchbox "Search for items to rent"
    - button "Submit search": Search
  - link "Log In Account":
    - /url: /login
    - img
    - text: Log In Account
  - link "0 Item Wishlist":
    - /url: /wishlist
    - img
    - text: 0 Item Wishlist
  - link "0 $0.00 My Cart":
    - /url: /cart
    - img
    - text: 0 $0.00 My Cart
  - navigation "Category Navigation":
    - button "Shop by categories":
      - img
      - text: Shop by Categories
      - img
    - navigation "Main Navigation":
      - link "navigation.browseAll":
        - /url: /search
      - link "navigation.rentToBuy":
        - /url: /rent-to-buy
      - link "navigation.auctions":
        - /url: /auctions
      - link "navigation.aiInsights":
        - /url: /visualization
      - link "navigation.howItWorks":
        - /url: /how-it-works
      - link "navigation.designSystem":
        - /url: /design-system
      - link "navigation.responsiveDesign":
        - /url: /responsive-design-system
- main:
  - img "rentUP Logo"
  - text: The Community Marketplace for Endless Shared Possibilities Rent anything from tools to spaces, or list your items to earn extra income. Join our community of sharers today!
  - heading "Log In" [level=1]
  - paragraph: Welcome to rentUP
  - text: Password QR Code Email
  - textbox "Email"
  - text: Password
  - textbox "Password"
  - button "Show password":
    - img
  - checkbox "Remember me"
  - text: Remember me
  - link "Forgot password?":
    - /url: /forgot-password
  - button "LOG IN"
  - text: OR
  - button "Sign in with Google":
    - img
    - text: Sign in with Google
  - iframe
  - button "Facebook":
    - img
    - text: Facebook
  - button "Sign in with Apple":
    - img
    - text: Sign in with Apple
  - paragraph:
    - text: New to rentUP?
    - link "Sign up":
      - /url: /register
- contentinfo:
  - heading "Join Our Community" [level=3]
  - paragraph: Subscribe to our newsletter for the latest rental opportunities and community updates.
  - textbox "Email address for newsletter"
  - img
  - button "Subscribe to newsletter": Subscribe
  - heading "About rentUP" [level=4]
  - paragraph: The Community Marketplace for Endless Sharing Possibilities. Rent anything from tools to spaces, or list your items to earn extra income.
  - link "Visit our Facebook page":
    - /url: https://facebook.com
    - text: Facebook
  - link "Visit our X (Twitter) page":
    - /url: https://x.com
    - text: X (Twitter)
  - link "Visit our TikTok page":
    - /url: https://tiktok.com
    - text: TikTok
  - link "Visit our Instagram page":
    - /url: https://instagram.com
    - text: Instagram
  - link "Visit our Xiaohongshu page":
    - /url: https://www.xiaohongshu.com
    - text: Xiaohongshu
  - heading "Quick Links" [level=4]
  - list:
    - listitem:
      - link "Browse Items":
        - /url: /search
    - listitem:
      - link "Rent-to-Buy":
        - /url: /rent-to-buy
    - listitem:
      - link "How It Works":
        - /url: /how-it-works
    - listitem:
      - link "About Us":
        - /url: /about
    - listitem:
      - link "Blog":
        - /url: /blog
    - listitem:
      - link "Contact Us":
        - /url: /contact
  - heading "Support" [level=4]
  - list:
    - listitem:
      - link "Help Center":
        - /url: /help
    - listitem:
      - link "Safety Center":
        - /url: /safety
    - listitem:
      - link "Financial FAQ":
        - /url: /financial-faq
    - listitem:
      - link "Community Guidelines":
        - /url: /guidelines
    - listitem:
      - link "Report an Issue":
        - /url: /report
  - heading "Legal" [level=4]
  - list:
    - listitem:
      - link "Terms of Service":
        - /url: /terms
    - listitem:
      - link "Privacy Policy":
        - /url: /privacy
    - listitem:
      - link "Cookie Policy":
        - /url: /cookies
    - listitem:
      - link "Accessibility":
        - /url: /accessibility
    - listitem:
      - link "Sitemap":
        - /url: /sitemap
  - paragraph: © 2025 rentUP. All rights reserved.
  - paragraph: The Community Marketplace for Endless Sharing Possibilities
  - img "Accepted Payment Methods"
- button "Get support":
  - img
- button "Give feedback":
  - img
- button "Report a dispute":
  - img
- button "Chat with us":
  - img
- button "Open Rent Planner":
  - img
```

# Test source

```ts
   33 |     // Wait for the page to load
   34 |     await page.waitForSelector('h2:has-text("Sign Agreement")');
   35 |     
   36 |     // Get the signature canvas
   37 |     const signatureCanvas = await page.locator('canvas').first();
   38 |     
   39 |     // Get the bounding box of the canvas
   40 |     const boundingBox = await signatureCanvas.boundingBox();
   41 |     if (!boundingBox) {
   42 |       throw new Error('Canvas bounding box not found');
   43 |     }
   44 |     
   45 |     // Draw a signature on the canvas
   46 |     await page.mouse.move(
   47 |       boundingBox.x + boundingBox.width / 4,
   48 |       boundingBox.y + boundingBox.height / 2
   49 |     );
   50 |     await page.mouse.down();
   51 |     await page.mouse.move(
   52 |       boundingBox.x + boundingBox.width / 2,
   53 |       boundingBox.y + boundingBox.height / 4,
   54 |       { steps: 5 }
   55 |     );
   56 |     await page.mouse.move(
   57 |       boundingBox.x + boundingBox.width * 3 / 4,
   58 |       boundingBox.y + boundingBox.height / 2,
   59 |       { steps: 5 }
   60 |     );
   61 |     await page.mouse.up();
   62 |     
   63 |     // Check if the confirm button is enabled after drawing
   64 |     const confirmButton = await page.locator('button:has-text("Confirm Signature")');
   65 |     await expect(confirmButton).not.toBeDisabled();
   66 |   });
   67 |
   68 |   test('should be able to change pen color and thickness', async ({ page }) => {
   69 |     // Navigate to the agreement sign page
   70 |     await page.goto('/agreements/agreement-1/sign');
   71 |     
   72 |     // Wait for the page to load
   73 |     await page.waitForSelector('h2:has-text("Sign Agreement")');
   74 |     
   75 |     // Get the blue color button and click it
   76 |     const blueColorButton = await page.locator('button[title="Blue"]');
   77 |     await blueColorButton.click();
   78 |     
   79 |     // Get the thickness button for 5px and click it
   80 |     const thicknessButton = await page.locator('button:has-text("5")');
   81 |     await thicknessButton.click();
   82 |     
   83 |     // Check if the buttons have the selected state
   84 |     await expect(blueColorButton).toHaveClass(/ring-2/);
   85 |     await expect(thicknessButton).toHaveClass(/bg-primary/);
   86 |   });
   87 |
   88 |   test('should be able to clear the signature', async ({ page }) => {
   89 |     // Navigate to the agreement sign page
   90 |     await page.goto('/agreements/agreement-1/sign');
   91 |     
   92 |     // Wait for the page to load
   93 |     await page.waitForSelector('h2:has-text("Sign Agreement")');
   94 |     
   95 |     // Get the signature canvas
   96 |     const signatureCanvas = await page.locator('canvas').first();
   97 |     
   98 |     // Get the bounding box of the canvas
   99 |     const boundingBox = await signatureCanvas.boundingBox();
  100 |     if (!boundingBox) {
  101 |       throw new Error('Canvas bounding box not found');
  102 |     }
  103 |     
  104 |     // Draw a signature on the canvas
  105 |     await page.mouse.move(
  106 |       boundingBox.x + boundingBox.width / 4,
  107 |       boundingBox.y + boundingBox.height / 2
  108 |     );
  109 |     await page.mouse.down();
  110 |     await page.mouse.move(
  111 |       boundingBox.x + boundingBox.width * 3 / 4,
  112 |       boundingBox.y + boundingBox.height / 2,
  113 |       { steps: 5 }
  114 |     );
  115 |     await page.mouse.up();
  116 |     
  117 |     // Check if the confirm button is enabled after drawing
  118 |     const confirmButton = await page.locator('button:has-text("Confirm Signature")');
  119 |     await expect(confirmButton).not.toBeDisabled();
  120 |     
  121 |     // Click the clear button
  122 |     await page.locator('button:has-text("Clear")').click();
  123 |     
  124 |     // Check if the confirm button is disabled after clearing
  125 |     await expect(confirmButton).toBeDisabled();
  126 |   });
  127 |
  128 |   test('should be able to submit the signature', async ({ page }) => {
  129 |     // Navigate to the agreement sign page
  130 |     await page.goto('/agreements/agreement-1/sign');
  131 |     
  132 |     // Wait for the page to load
> 133 |     await page.waitForSelector('h2:has-text("Sign Agreement")');
      |                ^ Error: page.waitForSelector: Test timeout of 30000ms exceeded.
  134 |     
  135 |     // Get the signature canvas
  136 |     const signatureCanvas = await page.locator('canvas').first();
  137 |     
  138 |     // Get the bounding box of the canvas
  139 |     const boundingBox = await signatureCanvas.boundingBox();
  140 |     if (!boundingBox) {
  141 |       throw new Error('Canvas bounding box not found');
  142 |     }
  143 |     
  144 |     // Draw a signature on the canvas
  145 |     await page.mouse.move(
  146 |       boundingBox.x + boundingBox.width / 4,
  147 |       boundingBox.y + boundingBox.height / 2
  148 |     );
  149 |     await page.mouse.down();
  150 |     await page.mouse.move(
  151 |       boundingBox.x + boundingBox.width * 3 / 4,
  152 |       boundingBox.y + boundingBox.height / 2,
  153 |       { steps: 5 }
  154 |     );
  155 |     await page.mouse.up();
  156 |     
  157 |     // Check the agreement checkbox
  158 |     await page.locator('input[type="checkbox"]').check();
  159 |     
  160 |     // Click the confirm signature button
  161 |     await page.locator('button:has-text("Confirm Signature")').click();
  162 |     
  163 |     // Click the submit button
  164 |     await page.locator('button:has-text("Sign Agreement")').click();
  165 |     
  166 |     // Check if the success message is shown
  167 |     await page.waitForSelector('text=Agreement signed successfully');
  168 |   });
  169 | });
  170 |
```