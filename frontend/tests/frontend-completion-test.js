#!/usr/bin/env node

/**
 * Frontend Completion Test
 * 
 * This script verifies that all critical frontend components are implemented
 * and functioning correctly for the RentUp platform.
 */

const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// Test configuration
const BASE_URL = 'http://localhost:5173';
const TIMEOUT = 30000;

// Critical pages to test
const CRITICAL_PAGES = [
  { path: '/', name: 'Home Page' },
  { path: '/login', name: 'Login Page' },
  { path: '/register', name: 'Register Page' },
  { path: '/search', name: 'Search Page' },
  { path: '/dashboard', name: 'Dashboard Page' },
  { path: '/business/dashboard', name: 'Business Dashboard' },
  { path: '/visualization', name: 'Visualization Page' },
  { path: '/auctions', name: 'Auction Page' },
  { path: '/agreements', name: 'Agreement Page' }
];

// Critical components to verify
const CRITICAL_COMPONENTS = [
  'Hero',
  'CategoryShowcase', 
  'FeaturedItems',
  'TrustSection',
  'CallToAction',
  'Login',
  'SearchResults',
  'ItemCard',
  'BookingForm',
  'AvailabilityCalendar'
];

// Test results
let testResults = {
  pages: {},
  components: {},
  accessibility: {},
  responsive: {},
  performance: {},
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    completion: 0
  }
};

/**
 * Test page accessibility
 */
async function testAccessibility(page, pageName) {
  try {
    // Check for basic accessibility requirements
    const hasHeadings = await page.locator('h1, h2, h3, h4, h5, h6').count() > 0;
    const hasAltText = await page.locator('img[alt]').count() >= await page.locator('img').count() * 0.8;
    const hasLabels = await page.locator('input[aria-label], input[id]').count() >= await page.locator('input').count() * 0.8;
    
    return {
      hasHeadings,
      hasAltText,
      hasLabels,
      score: (hasHeadings + hasAltText + hasLabels) / 3
    };
  } catch (error) {
    return { error: error.message, score: 0 };
  }
}

/**
 * Test responsive design
 */
async function testResponsive(page, pageName) {
  try {
    const viewports = [
      { width: 375, height: 667, name: 'Mobile' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 1280, height: 720, name: 'Desktop' }
    ];
    
    let responsiveScore = 0;
    
    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(500);
      
      // Check if content is visible and properly laid out
      const isContentVisible = await page.locator('body').isVisible();
      const hasOverflow = await page.evaluate(() => {
        return document.body.scrollWidth > window.innerWidth;
      });
      
      if (isContentVisible && !hasOverflow) {
        responsiveScore += 1;
      }
    }
    
    return {
      score: responsiveScore / viewports.length,
      tested: viewports.length
    };
  } catch (error) {
    return { error: error.message, score: 0 };
  }
}

/**
 * Test page performance
 */
async function testPerformance(page, pageName) {
  try {
    const startTime = Date.now();
    await page.goto(`${BASE_URL}${CRITICAL_PAGES.find(p => p.name === pageName)?.path || '/'}`);
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    // Check for performance metrics
    const performanceScore = loadTime < 3000 ? 1 : loadTime < 5000 ? 0.7 : 0.3;
    
    return {
      loadTime,
      score: performanceScore
    };
  } catch (error) {
    return { error: error.message, score: 0 };
  }
}

/**
 * Main test function
 */
async function runCompletionTest() {
  console.log('🚀 Starting Frontend Completion Test...\n');
  
  const { chromium } = require('playwright');
  const browser = await chromium.launch();
  const page = await browser.newPage();
  
  try {
    // Test critical pages
    console.log('📄 Testing Critical Pages...');
    for (const pageInfo of CRITICAL_PAGES) {
      console.log(`  Testing ${pageInfo.name}...`);
      
      try {
        await page.goto(`${BASE_URL}${pageInfo.path}`, { timeout: TIMEOUT });
        await page.waitForLoadState('domcontentloaded');
        
        // Basic page tests
        const title = await page.title();
        const hasContent = await page.locator('body').textContent();
        const hasNavigation = await page.locator('nav, header').count() > 0;
        
        // Accessibility test
        const accessibility = await testAccessibility(page, pageInfo.name);
        
        // Responsive test
        const responsive = await testResponsive(page, pageInfo.name);
        
        // Performance test
        const performance = await testPerformance(page, pageInfo.name);
        
        testResults.pages[pageInfo.name] = {
          status: 'PASSED',
          title,
          hasContent: !!hasContent,
          hasNavigation,
          accessibility,
          responsive,
          performance,
          score: (accessibility.score + responsive.score + performance.score) / 3
        };
        
        testResults.summary.passed++;
        console.log(`    ✅ ${pageInfo.name} - PASSED`);
        
      } catch (error) {
        testResults.pages[pageInfo.name] = {
          status: 'FAILED',
          error: error.message,
          score: 0
        };
        testResults.summary.failed++;
        console.log(`    ❌ ${pageInfo.name} - FAILED: ${error.message}`);
      }
      
      testResults.summary.total++;
    }
    
    // Calculate completion percentage
    testResults.summary.completion = Math.round((testResults.summary.passed / testResults.summary.total) * 100);
    
    // Generate report
    await generateReport();
    
  } finally {
    await browser.close();
  }
}

/**
 * Generate completion report
 */
async function generateReport() {
  const reportPath = path.join(__dirname, 'frontend-completion-report.md');
  
  const report = `# Frontend Completion Test Report

**Date:** ${new Date().toISOString()}
**Completion Rate:** ${testResults.summary.completion}%

## Summary
- **Total Tests:** ${testResults.summary.total}
- **Passed:** ${testResults.summary.passed}
- **Failed:** ${testResults.summary.failed}

## Page Test Results

${Object.entries(testResults.pages).map(([pageName, result]) => `
### ${pageName}
- **Status:** ${result.status}
- **Score:** ${Math.round((result.score || 0) * 100)}%
- **Has Navigation:** ${result.hasNavigation ? '✅' : '❌'}
- **Accessibility Score:** ${Math.round((result.accessibility?.score || 0) * 100)}%
- **Responsive Score:** ${Math.round((result.responsive?.score || 0) * 100)}%
- **Performance Score:** ${Math.round((result.performance?.score || 0) * 100)}%
${result.error ? `- **Error:** ${result.error}` : ''}
`).join('\n')}

## Recommendations

${testResults.summary.completion >= 95 ? `
🎉 **EXCELLENT!** Frontend is ${testResults.summary.completion}% complete and ready for production!

### Next Steps:
1. Final testing and bug fixes
2. Performance optimization
3. SEO optimization
4. Production deployment preparation
` : testResults.summary.completion >= 85 ? `
✅ **GOOD!** Frontend is ${testResults.summary.completion}% complete with minor issues to address.

### Priority Fixes:
${Object.entries(testResults.pages).filter(([_, result]) => result.status === 'FAILED').map(([pageName, result]) => `- Fix ${pageName}: ${result.error}`).join('\n')}
` : `
⚠️ **NEEDS WORK!** Frontend is ${testResults.summary.completion}% complete with significant issues.

### Critical Fixes Required:
${Object.entries(testResults.pages).filter(([_, result]) => result.status === 'FAILED').map(([pageName, result]) => `- Fix ${pageName}: ${result.error}`).join('\n')}
`}

---
Generated by Frontend Completion Test
`;

  fs.writeFileSync(reportPath, report);
  console.log(`\n📊 Report generated: ${reportPath}`);
  console.log(`\n🎯 Frontend Completion: ${testResults.summary.completion}%`);
  
  if (testResults.summary.completion >= 95) {
    console.log('🎉 FRONTEND IS PRODUCTION READY!');
  } else if (testResults.summary.completion >= 85) {
    console.log('✅ Frontend is nearly complete with minor issues.');
  } else {
    console.log('⚠️ Frontend needs significant work before completion.');
  }
}

// Run the test if called directly
if (require.main === module) {
  runCompletionTest().catch(console.error);
}

module.exports = { runCompletionTest, testResults };
