{"name": "frontend", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "build:typecheck": "tsc -b && vite build", "build:analyze": "vite build --mode analyze", "lint": "eslint .", "lint:fix": "eslint . --fix", "typecheck": "tsc --noEmit", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "test:smoke": "playwright test tests/smoke.spec.js", "test:home": "playwright test tests/home-simple.spec.js", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed", "test:report": "playwright show-report tests/testResults/html-report", "test:cypress": "cypress run", "test:cypress:open": "cypress open", "test:unit": "jest", "test:unit:watch": "jest --watch", "test:unit:coverage": "jest --coverage", "test:perf": "jest --testMatch='**/*.perf.test.{ts,tsx}'", "test:a11y": "jest --testMatch='**/*.a11y.test.{ts,tsx}'", "test:i18n": "playwright test tests/i18n.spec.js", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss}\"", "prepare": "husky install", "test:completion": "node tests/frontend-completion-test.js", "complete": "node complete-frontend.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@floating-ui/react-dom": "^2.1.2", "@greatsumini/react-facebook-login": "^3.4.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.3.4", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.3.0", "@react-oauth/google": "^0.12.2", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/postcss": "^4.1.6", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.6", "@tanstack/react-query": "^5.75.7", "@types/google-one-tap": "^1.2.6", "autoprefixer": "^10.4.21", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "date-fns": "^4.1.0", "framer-motion": "^12.10.5", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.5.0", "postcss": "^8.5.3", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-apple-signin-auth": "^1.1.1", "react-date-range": "^2.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.2.3", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "react-i18next": "^14.1.3", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.0", "tailwindcss": "^4.1.6", "tailwindcss-fluid-type": "^2.0.7", "web-vitals": "^5.0.1", "zod": "^3.24.4", "zustand": "^5.0.3"}, "devDependencies": {"@axe-core/playwright": "^4.10.1", "@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@eslint/config-array": "^0.20.0", "@eslint/js": "^9.23.0", "@eslint/object-schema": "^2.1.6", "@playwright/test": "^1.52.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-links": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.12", "@types/node": "^22.15.21", "@types/prop-types": "^15.7.14", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vitejs/plugin-react": "^4.3.4", "axe-playwright": "^2.1.0", "babel-jest": "^29.7.0", "chalk": "^5.4.1", "cypress": "^14.3.3", "cypress-file-upload": "^5.0.8", "eslint": "^9.24.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "glob": "^11.0.1", "globals": "^16.1.0", "husky": "^9.0.11", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.2", "lru-cache": "^11.1.0", "mochawesome": "^7.1.3", "mochawesome-merge": "^5.0.0", "mochawesome-report-generator": "^6.2.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "rollup-plugin-visualizer": "^5.12.0", "terser": "^5.29.2", "typescript": "~5.8.2", "typescript-eslint": "^8.32.0", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-pwa": "^0.19.0"}}