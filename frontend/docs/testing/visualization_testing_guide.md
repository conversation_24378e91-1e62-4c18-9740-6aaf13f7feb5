# Visualization Components Testing Guide

This guide provides best practices and patterns for testing visualization components in the RentUp project.

## Table of Contents

1. [Testing Challenges](#testing-challenges)
2. [Recommended Architecture](#recommended-architecture)
3. [Testing Strategies](#testing-strategies)
4. [Mock Implementations](#mock-implementations)
5. [Testing Utilities](#testing-utilities)
6. [Example Tests](#example-tests)
7. [Common Pitfalls](#common-pitfalls)
8. [Best Practices](#best-practices)

## Testing Challenges

Visualization components present several testing challenges:

1. **Asynchronous Operations**: Components fetch data asynchronously, making it difficult to determine when the component has finished rendering.
2. **Complex State Management**: Components have multiple states (loading, error, empty, data) and transitions between them.
3. **Responsive Design**: Components adapt to different screen sizes, requiring testing across multiple viewport dimensions.
4. **External Dependencies**: Components rely on hooks like `useMediaQuery` and libraries like Framer Motion.
5. **Complex Rendering Logic**: Components render different UI elements based on the data and state.

## Recommended Architecture

To address these challenges, we recommend adopting the Container/Presenter pattern:

### Container Component

- Handles data fetching and state management
- Delegates rendering to the presenter component
- Example: `EmbeddingVisualizationContainer.tsx`

### Presenter Component

- Pure rendering component with no side effects
- Receives all data and callbacks as props
- Example: `EmbeddingVisualizationPresenter.tsx`

This separation makes testing easier:
- Container components can be tested for data fetching and state management
- Presenter components can be tested for rendering and user interactions

## Testing Strategies

### Testing Presenter Components

1. **Test Each State Separately**: Test loading, error, empty, and data states independently.
2. **Test User Interactions**: Test that callbacks are called when the user interacts with the component.
3. **Test Conditional Rendering**: Test that different UI elements are rendered based on props.

### Testing Container Components

1. **Mock External Dependencies**: Mock the API, hooks, and child components.
2. **Test State Transitions**: Test that the component transitions between states correctly.
3. **Test Error Handling**: Test that errors are handled correctly.
4. **Test Callback Invocation**: Test that callbacks are invoked with the correct arguments.

## Mock Implementations

### Mock Service Layer

We've created a comprehensive mock service layer for testing:

```typescript
// mockRecommendationService.ts
const mockRecommendationService = {
  getApiUrl: jest.fn(() => 'http://localhost:8000'),
  getEmbeddingVisualization: jest.fn(),
  getPreferenceVisualization: jest.fn(),
  getUserRecommendations: jest.fn(),
  getSimilarItems: jest.fn(),

  // Helper methods for testing
  mockError: (methodName, error) => { /* ... */ },
  mockEmpty: (methodName) => { /* ... */ },
  reset: () => { /* ... */ },
};
```

### Mock Hooks

We've created testable versions of hooks:

```typescript
// useTestMediaQuery.ts
export const useTestMediaQuery = (query: string, initialValue = false): boolean => {
  if (process.env.NODE_ENV === 'test') {
    return initialValue;
  }
  // Real implementation...
};

export const useTestIsMobile = (initialValue = false): boolean => {
  return useTestMediaQuery('(max-width: 640px)', initialValue);
};

export const useTestIsTablet = (initialValue = false): boolean => {
  return useTestMediaQuery('(min-width: 641px) and (max-width: 1024px)', initialValue);
};

export const useTestIsDesktop = (initialValue = true): boolean => {
  return useTestMediaQuery('(min-width: 1025px)', initialValue);
};
```

### Mock Framer Motion

We've created a comprehensive mock for Framer Motion:

```typescript
// framerMotionMock.tsx
const motion = {
  div: createMockComponent('div'),
  button: createMockComponent('button'),
  // ... other components
};

const AnimatePresence = ({ children }) => <>{children}</>;

// Mock hooks
const useAnimation = () => ({ start: jest.fn(), stop: jest.fn() });
const useMotionValue = (initial) => ({ get: () => initial, set: jest.fn() });
// ... other hooks
```

### Mock Child Components

Mock child components to simplify testing:

```typescript
// Mock EmbeddingVisualizationPresenter
jest.mock('../EmbeddingVisualizationPresenter', () => {
  return {
    __esModule: true,
    default: jest.fn(props => {
      // Simplified implementation for testing
      if (props.loading) {
        return <div data-testid={`${props.testId}-loading`}>Loading...</div>;
      }
      // ... other states
    }),
  };
});
```

## Testing Utilities

We've created a comprehensive set of testing utilities to make it easier to test visualization components:

### Setup Utility

```typescript
// setupVisualizationTest.tsx
export const setupVisualizationTest = <P extends object>(
  Component: React.ComponentType<P>,
  config: TestConfig = defaultConfig,
  renderOptions?: Omit<RenderOptions, 'wrapper'>
): RenderResult => {
  // Set up mocks and render the component
  // ...
};

export const resetVisualizationTest = () => {
  // Reset all mocks
  // ...
};
```

### Accessibility Testing

```typescript
// accessibilityTest.tsx
export const testAccessibility = async (options: AccessibilityTestOptions) => {
  // Test a component for accessibility issues
  // ...
};

export const testAccessibilityInStates = async <P extends object>(
  Component: React.ComponentType<P>,
  states: Record<string, P>,
  options?: Omit<AccessibilityTestOptions, 'component' | 'componentName'>
) => {
  // Test a component for accessibility issues in different states
  // ...
};
```

### Responsive Design Testing

```typescript
// responsiveTest.tsx
export const testResponsive = async (options: ResponsiveTestOptions) => {
  // Test a component at different breakpoints
  // ...
};

export const renderAtBreakpoint = (
  breakpoint: BreakpointKey,
  component: React.ReactElement,
  renderOptions?: RenderOptions
): RenderResult => {
  // Render a component at a specific breakpoint
  // ...
};
```

### Performance Testing

```typescript
// performanceTest.tsx
export const testPerformance = (options: PerformanceTestOptions): PerformanceTestResults => {
  // Test the rendering performance of a component
  // ...
};

export const testPerformanceWithProps = <P extends object>(
  Component: React.ComponentType<P>,
  propsVariations: Record<string, P>,
  options?: Omit<PerformanceTestOptions, 'component'>
): Record<string, PerformanceTestResults> => {
  // Test the rendering performance of a component with different props
  // ...
};
```

### User Interaction Testing

```typescript
// interactionTest.tsx
export const testInteractions = async (options: InteractionTestOptions) => {
  // Test a series of user interactions
  // ...
};
```

### Custom Matchers

```typescript
// visualizationMatchers.ts
expect.extend({
  toBeInLoadingState,
  toBeInErrorState,
  toBeInEmptyState,
  toBeInDataState,
  toHavePoint,
  toHaveCategory,
  toHaveZoomControls,
  toHaveLegend,
});
```

## Example Tests

### Using the Test Setup Utility

We've created a utility to set up tests with all the necessary mocks:

```typescript
// setupVisualizationTest.tsx
export const setupVisualizationTest = <P extends object>(
  Component: React.ComponentType<P>,
  config: TestConfig = defaultConfig,
  renderOptions?: Omit<RenderOptions, 'wrapper'>
): RenderResult => {
  // Set up mocks and render the component
  // ...
};
```

Example usage:

```typescript
it('renders loading state initially', () => {
  setupVisualizationTest(
    SimpleVisualization,
    {
      componentProps: {
        testId: 'simple-viz',
        state: 'loading',
      },
    }
  );

  expect('simple-viz').toBeInLoadingState();
  expect(screen.getByText('Loading visualization data...')).toBeInTheDocument();
});
```

### Using Custom Matchers

We've created custom matchers to make tests more readable:

```typescript
// visualizationMatchers.ts
expect.extend({
  toBeInLoadingState,
  toBeInErrorState,
  toBeInEmptyState,
  toBeInDataState,
  toHavePoint,
  toHaveCategory,
  toHaveZoomControls,
  toHaveLegend,
});
```

Example usage:

```typescript
// Check component state
expect('simple-viz').toBeInLoadingState();
expect('simple-viz').toBeInErrorState();
expect('simple-viz').toBeInEmptyState();
expect('simple-viz').toBeInDataState();

// Check specific elements
expect('simple-viz').toHavePoint('item1');
expect('simple-viz').toHaveCategory('electronics');
expect('simple-viz').toHaveZoomControls();
expect('simple-viz').toHaveLegend();
```

### Testing Presenter Components

```typescript
it('renders loading state when loading is true', () => {
  render(
    <EmbeddingVisualizationPresenter
      embeddings={[]}
      loading={true}
      error={null}
      // ...other props
    />
  );
  expect(screen.getByTestId('embed-viz-loading')).toBeInTheDocument();
});
```

### Testing Container Components

```typescript
it('renders error state when API call fails', async () => {
  mockFetch.mockImplementation(() =>
    Promise.resolve({
      ok: false,
      status: 500,
    })
  );

  await act(async () => {
    render(<EmbeddingVisualizationContainer testId="embed-viz" />);
  });

  await waitFor(() => {
    expect(screen.getByTestId('embed-viz-error')).toBeInTheDocument();
  }, { timeout: 10000 });
});
```

### Comprehensive Test Example

Here's an example of a comprehensive test that uses all our testing utilities:

```typescript
describe('Comprehensive Test Example', () => {
  // Reset all mocks after each test
  afterEach(() => {
    resetVisualizationTest();
  });

  it('passes all tests', async () => {
    // 1. Render the component
    setupVisualizationTest(
      SimpleVisualization,
      {
        componentProps: {
          testId: 'simple-viz',
          state: 'data',
          data: mockRecommendationService.mockEmbeddingData,
        },
      }
    );

    // 2. Check that it renders correctly
    expect('simple-viz').toBeInDataState();
    expect('simple-viz').toHavePoint('item1');
    expect('simple-viz').toHaveCategory('electronics');

    // 3. Check accessibility
    await testAccessibility({
      component: (
        <SimpleVisualization
          testId="simple-viz"
          state="data"
          data={mockRecommendationService.mockEmbeddingData}
        />
      ),
    });

    // 4. Check responsive design
    await testResponsive({
      component: (
        <SimpleVisualization
          testId="simple-viz"
          state="data"
          data={mockRecommendationService.mockEmbeddingData}
        />
      ),
      breakpoints: ['xs', 'xl'],
      callback: () => {
        expect(screen.getByTestId('simple-viz')).toBeInTheDocument();
      },
    });

    // 5. Check performance
    const performanceResults = testPerformance({
      component: (
        <SimpleVisualization
          testId="simple-viz"
          state="data"
          data={mockRecommendationService.mockEmbeddingData}
        />
      ),
      iterations: 5,
    });
    expect(performanceResults.passed).toBe(true);

    // 6. Check user interactions
    await testInteractions({
      component: (
        <SimpleVisualization
          testId="simple-viz"
          state="error"
          onRetry={() => {}}
        />
      ),
      interactions: [
        {
          type: 'click',
          testId: 'simple-viz-retry-button',
          description: 'Click the retry button',
        },
      ],
    });
  });
});

## Common Pitfalls

1. **Not Waiting for Asynchronous Operations**: Always use `act()` and `waitFor()` when testing components with asynchronous operations. Our `setupVisualizationTest` utility helps with this.

2. **Not Mocking External Dependencies**: Mock all external dependencies to isolate the component being tested. We've created comprehensive mocks for:
   - Recommendation service
   - Media queries
   - Framer Motion
   - Error handling

3. **Not Testing All States**: Test all possible states of the component. Our custom matchers make it easy to check for different states:
   ```typescript
   expect('simple-viz').toBeInLoadingState();
   expect('simple-viz').toBeInErrorState();
   expect('simple-viz').toBeInEmptyState();
   expect('simple-viz').toBeInDataState();
   ```

4. **Not Testing Error Handling**: Test that errors are handled correctly. Our `mockServiceError` and `mockFetchError` options in the test setup utility make it easy to simulate errors.

5. **Not Testing User Interactions**: Test that callbacks are called when the user interacts with the component. Use `fireEvent` to simulate user interactions.

6. **Not Testing Conditional Rendering**: Test that different UI elements are rendered based on props. Our custom matchers make it easy to check for specific elements:
   ```typescript
   expect('simple-viz').toHavePoint('item1');
   expect('simple-viz').toHaveCategory('electronics');
   ```

7. **Not Testing Responsive Design**: Test that the component adapts to different screen sizes. Our test setup utility makes it easy to simulate different screen sizes:
   ```typescript
   setupVisualizationTest(Component, { isMobile: true });
   setupVisualizationTest(Component, { isTablet: true });
   setupVisualizationTest(Component, { isDesktop: true });
   ```

8. **Not Testing Accessibility**: Test that the component is accessible. Use the `jest-axe` library to test for accessibility issues.

## Best Practices

1. **Use the Container/Presenter Pattern**: Separate data fetching and state management from rendering to make components easier to test.

2. **Use Data-Testid Attributes**: Add data-testid attributes to all important UI elements. Follow a consistent naming convention:
   ```tsx
   <div data-testid={`${testId}-container`}>
     <div data-testid={`${testId}-point-${point.id}`}>
       {point.name}
     </div>
   </div>
   ```

3. **Use findBy Methods**: Use findBy methods instead of waitFor() when possible. They have built-in waiting functionality:
   ```typescript
   const errorElement = await screen.findByTestId('embed-viz-error');
   ```

4. **Increase Test Timeouts**: Increase timeouts for tests that involve complex asynchronous operations:
   ```typescript
   await waitFor(() => {
     expect(screen.getByTestId('embed-viz-error')).toBeInTheDocument();
   }, { timeout: 10000 });
   ```

5. **Use act()**: Wrap component rendering and state updates in act():
   ```typescript
   await act(async () => {
     render(<EmbeddingVisualizationContainer testId="embed-viz" />);
   });
   ```

6. **Test One Thing at a Time**: Each test should test one specific behavior. Use descriptive test names that explain what the test is testing:
   ```typescript
   it('renders error state when API call fails', async () => {
     // ...
   });
   ```

7. **Use the Test Setup Utility**: Use the `setupVisualizationTest` utility to set up tests with all the necessary mocks:
   ```typescript
   setupVisualizationTest(
     SimpleVisualization,
     {
       componentProps: {
         testId: 'simple-viz',
         state: 'loading',
       },
     }
   );
   ```

8. **Use Custom Matchers**: Use the custom matchers to make tests more readable:
   ```typescript
   expect('simple-viz').toBeInLoadingState();
   expect('simple-viz').toHavePoint('item1');
   ```

---

Last Updated: 2024-05-17
