# RentUp Directory Structure

This document provides a comprehensive overview of the RentUp project's directory structure and file organization to help developers navigate the codebase efficiently. It serves as a codebase index for developers to quickly locate files and understand their purpose.

**Last Updated: 2025-05-26**

## Table of Contents

- [Project Root](#project-root)
- [Backend Directory](#backend-directory)
- [Frontend Directory](#frontend-directory)
- [Documentation Structure](#documentation-structure)
- [Data Directory](#data-directory)
- [Monitoring Directory](#monitoring-directory)
- [Wireframes Directory](#wireframes-directory)
- [Test Scripts Directory](#test-scripts-directory)
- [Test Results Directory](#test-results-directory)
- [Key Files](#key-files)
- [File Relationships](#file-relationships)
- [Development Workflow](#development-workflow)

## Project Root

- `frontend/` - Frontend React application
- `backend/` - Backend FastAPI application
- `docs/` - Project documentation
- `knowledge_base/` - Comprehensive guides and best practices
  - `frontend/` - Frontend development guides
  - `backend/` - Backend development guides
- `wireframes/` - UI/UX wireframes and design specifications
- `data/` - Data files and resources
- `scripts/` - Utility scripts for development and deployment
- `testResults/` - Test files and results organized by development phases
- `testscripts/` - Test scripts for the project
- `monitoring/` - Monitoring and alerting configuration
- `.env` - Environment variables (not committed to version control)
- `.env.example` - Example environment variables template
- `README.md` - Project overview and setup instructions (Source of Truth)
- `dirStructure.md` - Directory structure and organization (Source of Truth)
- `fileRelations.md` - Documentation of file relationships and dependencies (Source of Truth)
- `testMethod.md` - Testing methodology and standards (Source of Truth)
- `build.md` - Build process and deployment procedures (Source of Truth)
- `Makefile` - Automation scripts and commands (Source of Truth)
- `sitemap.md` - Sitemap file outlining the structure of the website (Source of Truth)
- `DEVELOPMENT.md` - Development setup instructions
- `requirements.txt` - Python dependencies file for the backend
- `run_tests.py` - Comprehensive test runner script
- `Dockerfile` - Dockerfile for containerizing the application
- `archive/` - Archived files and scripts

## Backend Directory

*   `backend`: Contains the backend code for the project.
    *   `alembic`: Contains the Alembic migrations for the database.
        *   `versions`: Contains the migration scripts for database schema changes.
    *   `app`: Contains the code for the main application.
    *   `scripts`: Contains utility scripts for the backend.
        *   `code_quality`: Contains code quality tools.
            *   `README.md`: Documentation for code quality tools.
            *   `check_redundancies.py`: Script to check for code redundancies.
        *   **Production Optimization Scripts (Phase 7 - May 2025)**:
            *   `production_optimization.py`: Comprehensive production optimization script.
            *   `google_adk_migration.py`: Google ADK migration analysis and planning script.
            *   `verify_production_deployment.py`: Production deployment verification script.
            *   `start-prod.sh`: Production startup script with health checks.
        *   **Production Deployment Files**:
            *   `Dockerfile.prod`: Multi-stage production Docker build.
            *   `docker-compose.prod.yml`: Production Docker Compose with monitoring stack.
            *   `requirements-prod.txt`: Production Python dependencies.
        *   **Google ADK Migration Output**:
            *   `adk_migration_output/`: Google ADK migration analysis results.
                *   `agents_analysis.json`: Analysis of existing AI agents.
                *   `migration_plan.json`: 8-week migration roadmap.
                *   `recommendation_agent_adk.py`: ADK recommendation agent definition.
                *   `fraud_detection_agent_adk.py`: ADK fraud detection agent definition.
                *   `main_router_agent_adk.py`: ADK main router agent definition.
                *   `evaluation_framework.py`: ADK evaluation framework.
        *   `agents`: Contains the code for the AI agents.
            *   `router.py`: Agent router for directing queries to appropriate agents.
        *   `ai`: Contains the code for the AI architecture (Mixture of Experts).
            *   `router.py`: Central router for directing AI requests to specialized agents.
            *   `models.py`: Pydantic models for AI requests and responses.
            *   `recommendation`: Recommendation agent for item matching and personalized recommendations.
                *   `agent.py`: Primary recommendation agent implementation.
                *   `fallback.py`: Fallback recommendation agent for faster response times.
            *   `fraud_detection`: Fraud detection agent for identifying suspicious patterns.
                *   `agent.py`: Primary fraud detection agent implementation.
                *   `fallback.py`: Fallback fraud detection agent for faster response times.
            *   `pricing`: Pricing agent for dynamic pricing optimization.
                *   `agent.py`: Primary pricing agent implementation.
                *   `fallback.py`: Fallback pricing agent for faster response times.
            *   `user_analysis`: User analysis agent for behavior tracking and prediction.
                *   `agent.py`: Primary user analysis agent implementation.
                *   `fallback.py`: Fallback user analysis agent for faster response times.
            *   `content_moderation`: Content moderation agent for filtering inappropriate content.
                *   `agent.py`: Primary content moderation agent implementation.
                *   `fallback.py`: Fallback content moderation agent for faster response times.
            *   `utils`: Utility functions for AI components.
                *   `embedding_service.py`: Text embedding generation service.
                *   `cache_service.py`: Caching service for AI results.
        *   `api`: Contains the API endpoints.
            *   `v1`: Contains the v1 API endpoints.
                *   `agreements.py`: Agreement-related endpoints for creating and managing rental agreements.
                *   `auctions.py`: Auction-related endpoints for creating and managing auctions.
                *   `fraud_prevention.py`: Fraud prevention endpoints for risk scoring and fraud reporting.
                *   `items.py`: Item-related endpoints including search, listing, and multi-modal search.
                *   `router.py`: API router for directing requests to appropriate endpoints.
                *   `verification.py`: Verification-related endpoints for user identity verification.
                *   `api.py`: API configuration and router setup.
                *   `endpoints`: Contains endpoint modules for different features.
                    *   `users.py`: User-related endpoints.
                    *   `items.py`: Item-related endpoints.
                    *   `payments.py`: Payment-related endpoints.
                    *   `categories.py`: Category-related endpoints.
                    *   `uploads.py`: Upload-related endpoints.
                    *   `auth.py`: Authentication-related endpoints.
                    *   `recommendations.py`: Recommendation-related endpoints.
                    *   `pricing.py`: Pricing-related endpoints.
                    *   `analytics.py`: Analytics-related endpoints.
                    *   `curation.py`: Curation-related endpoints.
                    *   `cache.py`: Cache management endpoints.
                    *   `bookings.py`: Booking-related endpoints.
                    *   `reviews.py`: Review-related endpoints.
                    *   `preferences.py`: Preference-related endpoints.
                    *   `embeddings.py`: Embedding-related endpoints.
                    *   `rentals.py`: Rental-related endpoints.
        *   `components`: Contains React components (Note: These appear to be misplaced and should be in the frontend).
            *   `properties`: Contains property related components.
            *   `rentals`: Contains rental related components.
            *   `ui`: Contains UI components.
        *   `core`: Contains the core code for the application.
            *   `auth.py`: Authentication and authorization logic.
            *   `config.py`: Contains the configuration for the application.
            *   `database.py`: Contains the database connection code.
            *   `qdrant.py`: Contains the Qdrant vector database connection code for similarity search and vector operations.
            *   **Production Optimization Modules (Phase 7 - May 2025)**:
                *   `auth_config.py`: Enhanced JWT authentication configuration with MFA support.
                *   `rate_limiting.py`: Redis-based rate limiting system for API endpoints.
                *   `validation.py`: Enhanced input validation with Pydantic v2 and sanitization.
                *   `audit.py`: Comprehensive audit logging system with sensitive data protection.
                *   `query_optimization.py`: Query performance tracking and optimization.
                *   `join_optimization.py`: JOIN strategy optimization with intelligent selection.
                *   `query_cache.py`: Multi-level caching system with TTL management.
                *   `db_config.py`: Centralized database configuration management.
                *   `database_connection.py`: Enhanced database connection management with monitoring.
            *   **Security Middleware**:
                *   `middleware/security.py`: Security headers middleware (HSTS, CSP, XSS protection).
        *   `models`: Contains the database models.
            *   `agreement.py`: Agreement model for rental contracts.
            *   `auction.py`: Auction model for auction-based rentals.
            *   `item.py`: Item model for rental items.
            *   `rental.py`: Rental model for tracking rentals.
            *   `user.py`: User model for user accounts.
        *   `services`: Contains the services for the application.
            *   `agreement_service.py`: Agreement-related services for generating and managing agreements.
            *   `auction_service.py`: Auction-related services for managing auctions.
            *   `embedding_service.py`: Text embedding generation and vector search services.
            *   `image_embedding_service.py`: Image embedding generation and image search services.
            *   `fraud_detection_service.py`: Fraud detection services for identifying suspicious activities.
            *   `notification_service.py`: Notification-related services for sending notifications.
            *   `recommendation_service.py`: Core recommendation logic for personalized suggestions.
            *   `preference_modeling.py`: User preference analysis and tracking.
            *   `item_embedding_service.py`: Enhanced item embeddings for recommendations.
            *   `context_filtering.py`: Contextual recommendation filtering.
            *   `visualization_service.py`: Visualization components for recommendations.
            *   `ai_curation_service.py`: AI-powered content curation and management.
            *   **Production Optimization Services (Phase 7 - May 2025)**:
                *   `db_optimization_service.py`: Unified database optimization API service.
        *   `main.py`: Contains the main application code and FastAPI setup.
    *   `templates`: Contains templates for various generated content.
        *   `agreements`: Contains agreement templates.
            *   `auction_agreement.html`: Auction-based rental agreement template.
            *   `standard_agreement.html`: Standard rental agreement template.
            *   `vehicle_agreement.html`: Vehicle-specific agreement template.

## Data Directory

*   `data`: Contains the data used for the AI models.
    *   `schema.md`: Data schema file describing the structure of the data.
    *   `models`: Contains the trained AI models.
    *   `processed`: Contains the processed data ready for model training.
    *   `raw`: Contains the raw data before processing.

## Monitoring Directory

*   `monitoring`: Contains the monitoring and alerting configuration.
    *   `prometheus`: Contains Prometheus configuration files.
        *   `prometheus.yml`: Main Prometheus configuration file.
        *   `rules`: Contains Prometheus alert rules.
            *   `app_rules.yml`: Application alert rules.
            *   `database_rules.yml`: Database alert rules.
            *   `node_rules.yml`: Node/system alert rules.
    *   `alertmanager`: Contains Alertmanager configuration files.
        *   `alertmanager.yml`: Main Alertmanager configuration file.
        *   `templates`: Contains alert templates.
            *   `email.tmpl`: Email alert templates.
            *   `slack.tmpl`: Slack alert templates.
    *   `grafana`: Contains Grafana configuration files.
        *   `dashboards`: Contains Grafana dashboard definitions.
            *   `overview.json`: Overview dashboard.
            *   `backend.json`: Backend performance dashboard.
            *   `database.json`: Database performance dashboard.
            *   `system.json`: System resources dashboard.
        *   `datasources`: Contains Grafana data source configurations.
            *   `prometheus.yml`: Prometheus data source configuration.
    *   `exporters`: Contains exporter configurations.
        *   `node_exporter`: Contains Node Exporter configuration.
        *   `postgres_exporter`: Contains PostgreSQL Exporter configuration.
    *   `loki`: Contains Loki configuration files.
        *   `loki.yml`: Main Loki configuration file.
    *   `tempo`: Contains Tempo configuration files.
        *   `tempo.yml`: Main Tempo configuration file.
    *   `scripts`: Contains monitoring-related scripts.
        *   `dashboard_generator.py`: Dashboard generation script.
        *   `log_analyzer.py`: Log analysis script.

## Documentation Structure

```
docs/
├── api/                   # API documentation
│   └── financial-services-api.md
├── architecture/          # Architecture documentation
│   └── system-architecture.md

knowledge_base/
├── README.md              # Knowledge base overview
├── frontend/              # Frontend development guides
│   ├── frontend_trends_2025.md        # Latest frontend trends
│   ├── react_nextjs_best_practices.md # React and Next.js best practices
│   └── frontend_testing_guide.md      # Frontend testing guide
└── backend/               # Backend development guides
    ├── backend_trends_2025.md         # Latest backend trends
    ├── fastapi_optimization_guide.md  # FastAPI optimization guide
    ├── database_optimization_guide.md # Database optimization guide
    └── testing_best_practices.md      # Backend testing best practices
├── components/            # Component documentation
│   ├── button.md
│   ├── checkbox.md
│   ├── component-library.md
│   ├── date-picker.md
│   ├── input.md
│   ├── modal.md
│   ├── property-card.md
│   ├── search.md
│   └── select.md
├── design-system/         # Design system documentation
│   ├── color-palette.md
│   ├── design-system.md
│   ├── implementation-plan.md
│   ├── implementation-progress.md
│   ├── implementation-summary.md
│   ├── interactive-elements.md
│   ├── layout-design.md
│   └── phase1-completion.md
├── dev_guide/             # Development phase guides
│   ├── devPhase0/         # Phase 0: Development Environment Setup
│   │   ├── devPhase0.md   # Phase 0 development guide
│   │   └── devPhase0_tasks.md # Phase 0 tasks list
│   ├── devPhase1/         # Phase 1: Frontend Scaffolding
│   │   ├── devPhase1.md   # Phase 1 development guide
│   │   └── devPhase1_tasks.md # Phase 1 tasks list
│   ├── devPhase2/         # Phase 2: Backend Integration
│   │   ├── devPhase2.md   # Phase 2 development guide (completed)
│   │   └── devPhase2_tasks.md # Phase 2 tasks list (completed)
│   ├── devPhase3/         # Phase 3: Advanced Features
│   │   ├── devPhase3.md   # Phase 3 development guide
│   │   └── devPhase3_tasks.md # Phase 3 tasks list
│   ├── devPhase4/         # Phase 4: Optimization & Deployment
│   │   ├── devPhase4.md   # Phase 4 development guide
│   │   └── devPhase4_tasks.md # Phase 4 tasks list
│   ├── devPhase5/         # Phase 5: Security & Compliance
│   │   ├── devPhase5.md   # Phase 5 development guide
│   │   └── devPhase5_tasks.md # Phase 5 tasks list
│   ├── devPhase6/         # Phase 6: Production Readiness
│   │   ├── devPhase6.md   # Phase 6 development guide
│   │   └── devPhase6_tasks.md # Phase 6 tasks list
│   ├── devPhase6_ai/      # Phase 6: AI Architecture Implementation
│   │   ├── devPhase6_ai.md # AI architecture implementation guide
│   │   ├── devPhase6_ai_tasks.md # AI architecture implementation tasks
│   │   └── technical_specs/ # Detailed technical specifications
│   │       ├── README.md  # Overview of technical specifications
│   │       ├── comparative-analysis.md # Comparative analysis of AI models
│   │       ├── implementation-plan.md # Implementation plan for AI agents
│   │       ├── fine-tuning-spec.md # Fine-tuning technical specification
│   │       ├── integration-architecture.md # Integration architecture
│   │       ├── resource-requirements.md # Resource requirements
│   │       └── cost-analysis.md # Cost analysis
│   ├── devPhase7/         # Phase 7: Backend Optimization and Production Readiness
│   │   ├── devPhase7.md   # Phase 7 development guide
│   │   └── devPhase7_tasks.md # Phase 7 tasks list
│   ├── devPhase8/         # Phase 8: Advanced Backend Optimization and Scaling
│   │   ├── devPhase8.md   # Phase 8 development guide
│   │   └── devPhase8_tasks.md # Phase 8 tasks list
│   ├── devPhase9/         # Phase 9: Monitoring, Alerting, and Observability
│   │   ├── devPhase9.md   # Phase 9 development guide
│   │   └── devPhase9_tasks.md # Phase 9 tasks list
│   ├── devPhase10/        # Phase 10: Advanced Backend Optimizations (2025)
│   │   ├── devPhase10.md  # Phase 10 development guide
│   │   └── devPhase10_tasks.md # Phase 10 tasks list
│   ├── devPhase11/        # Phase 11: Google ADK AI Framework Migration (2025)
│   │   ├── devPhase11.md  # Phase 11 development guide
│   │   └── devPhase11_tasks.md # Phase 11 tasks list
│   └── documentation_update_summary.md
├── integration/           # Integration documentation
│   └── service-integration-map.md
├── tech-stack/            # Technology stack documentation
│   ├── index.md           # Overview of technology stack
│   ├── frontend.md        # Frontend technology stack
│   ├── backend.md         # Backend technology stack
│   ├── ai-ml.md           # AI/ML technology stack
│   ├── ai-architecture.md # AI architecture documentation
│   ├── infrastructure.md  # Infrastructure and DevOps
│   └── security.md        # Security best practices
├── monitoring/            # Monitoring documentation
│   └── monitoring_and_alerting_guide.md # Comprehensive monitoring guide
├── ai/                    # Deprecated - Content moved to dev_guide/devPhase6_ai/technical_specs
├── testing/               # Testing documentation
│   ├── README.md          # Testing overview
│   ├── test_methodology.md # Comprehensive testing methodology
│   ├── cypress_best_practices.md # Best practices for Cypress testing
│   ├── playwright_best_practices.md # Best practices for Playwright testing
│   └── pytest_best_practices.md # Best practices for pytest testing
├── agreement-system.md    # Agreement system documentation
├── ai-model-strategy.md   # AI model strategy
├── api-specification.md   # API specification
├── auction-system.md      # Auction system documentation
├── CI-CD-deployment.md    # CI/CD deployment documentation
├── core-components.md     # Core components documentation
├── development-plan.md    # Comprehensive development plan
├── fraud-prevention.md    # Fraud prevention documentation
├── github-update-plan.md  # GitHub update plan
├── internationalization.md # Internationalization documentation
├── performance-optimization.md # Performance optimization documentation
├── technologies-architecture.md # Technology architecture
├── ui-ux-audit.md         # UI/UX audit documentation
├── user-stories.md        # User stories
└── wireframe-task-mapping.md # Mapping of wireframes to tasks
```

## Frontend Directory

*   `frontend`: Contains the frontend code for the project.
    *   `.gitignore`: Git ignore file for frontend-specific files.
    *   `package.json`: Frontend dependencies file.
    *   `README.md`: Frontend README file.
    *   `babel.config.js`: Babel configuration for Jest tests.
    *   `jest.config.js`: Jest configuration for unit testing.
    *   `cypress`: Contains Cypress test files.

### Frontend Code Organization Guidelines (May 2025)

According to the May 2025 code optimization guidelines, all JavaScript/TypeScript files should follow these size constraints:
- **Target Size**: 300-500 lines per file
- **Maximum Threshold**: 600 lines (files exceeding this must be refactored)
- **Absolute Limit**: 800 lines (files exceeding this must be refactored immediately)

#### Component Organization Pattern

For large components exceeding 600 lines, use the following directory structure pattern:

```
frontend/src/components/FeatureName/
├── index.tsx                      # Main export file (150-200 lines)
├── components/                    # Extracted subcomponents
│   ├── SubComponentA.tsx          # Extracted component (100-200 lines)
│   ├── SubComponentB.tsx          # Extracted component (100-200 lines)
│   └── SubComponentC.tsx          # Extracted component (100-200 lines)
├── hooks/                         # Feature-specific hooks
│   ├── useFeatureSpecificHookA.ts # Custom hook (50-100 lines)
│   └── useFeatureSpecificHookB.ts # Custom hook (50-100 lines)
└── utils/                         # Feature-specific utilities
    ├── helperFunctions.ts         # Helper functions (50-100 lines)
    └── constants.ts               # Constants and types (50-100 lines)
```

#### Page Organization Pattern

For large page components exceeding 600 lines, use the following directory structure pattern:

```
frontend/src/pages/PageName/
├── index.tsx                      # Main export file (150-200 lines)
├── components/                    # Page-specific components
│   ├── SectionA.tsx               # Section component (100-200 lines)
│   ├── SectionB.tsx               # Section component (100-200 lines)
│   └── SectionC.tsx               # Section component (100-200 lines)
├── hooks/                         # Page-specific hooks
│   ├── usePageSpecificHookA.ts    # Custom hook (50-100 lines)
│   └── usePageSpecificHookB.ts    # Custom hook (50-100 lines)
└── utils/                         # Page-specific utilities
    ├── helperFunctions.ts         # Helper functions (50-100 lines)
    └── constants.ts               # Constants and types (50-100 lines)
```
        *   `e2e`: Contains end-to-end test files.
            *   `basic.cy.js`: Basic Cypress test file.
            *   `fixed.cy.js`: Fixed Cypress test file that passes all tests.
            *   `simple.cy.js`: Simple Cypress test file.
            *   `phase1`: Phase 1 specific test files.
                *   `authentication.cy.js`: Tests for authentication components.
                *   `core-ui.cy.js`: Tests for core UI components.
                *   `item-listing.cy.js`: Tests for item and listing UI.
                *   `user-booking.cy.js`: Tests for user and booking UI.
        *   `support`: Contains Cypress support files.
            *   `commands.js`: Custom Cypress commands.
            *   `e2e.js`: Cypress e2e support file.
        *   `screenshots`: Contains screenshots taken during Cypress test runs.
    *   `cypress.config.js`: Cypress configuration file.
    *   `playwright.config.js`: Playwright configuration file.
    *   `public`: Contains static assets served directly by the web server.
    *   `src`: Contains the source code for the frontend.
        *   `assets`: Contains static assets like images and icons.
            *   `footer`: Footer-specific assets.
        *   `components`: Contains reusable React components organized by feature.
            *   `admin`: Admin-related components.
                *   `__tests__`: Tests for admin components.
            *   `Agreement`: Contains agreement-related components.
                *   `AgreementSearch.tsx`: Component for searching agreements.
                *   `AgreementViewer.tsx`: Component for viewing rental agreements.
                *   `ClauseExplainer.tsx`: Component for explaining legal clauses.
                *   `SignatureCanvas.tsx`: Component for capturing signatures.
                *   `TemplateSelection.tsx`: Component for selecting agreement templates.
            *   `AI`: AI-related components.
                *   `ResponsiveAICurationInterface.tsx`: Responsive AI curation interface.
                *   `ResponsiveAIFeatures.tsx`: Responsive AI features component.
            *   `Analytics`: Analytics-related components.
                *   `ResponsiveAnalyticsDashboard.tsx`: Responsive analytics dashboard.
            *   `Auction`: Contains auction-related components.
                *   `AuctionCard.tsx`: Displays auction information.
                *   `AuctionTimer.tsx`: Countdown timer for auctions.
            *   `Business`: Business account-related components.
                *   `BusinessAccountSwitcher.tsx`: Component for switching between personal and business accounts.
                *   `__tests__`: Tests for business account components.
                    *   `BusinessAccountSwitcher.test.tsx`: Tests for the business account switcher component.
                *   `BidForm.tsx`: Form for placing bids.
                *   `BidHistory.tsx`: Displays bid history.
            *   `Auth`: Authentication-related components.
                *   `ProtectedRoute.tsx`: Route that requires authentication.
                *   `RoleBasedRoute.tsx`: Route that requires specific user roles.
            *   `Category`: Category-related components.
                *   `CategoryCard.tsx`: Category card component.
                *   `CategoryDropdown.tsx`: Category dropdown component.
                *   `CategoryIcons.tsx`: Category icon components.
                *   `CategoryNav.tsx`: Category navigation component.
                *   `CategorySection.tsx`: Category section component.
                *   `CategorySelector.tsx`: Category selector component.
                *   `CategoryShowcase.tsx`: Category showcase component.
                *   `FloatingDropdown.tsx`: Floating dropdown component.
                *   `ShopByCategoriesDropdown.tsx`: Shop by categories dropdown.
            *   `common`: Common utility components.
                *   `Toast.tsx`: Toast notification component.
            *   `Debug`: Debugging components.
                *   `InteractiveDemo.tsx`: Interactive demo component.
                *   `StyleDebugger.tsx`: Style debugger component.
            *   `Error`: Error handling components.
                *   `ErrorBoundary.tsx`: Error boundary component.
                *   `ErrorDemo.tsx`: Error demonstration component.
                *   `ErrorFallback.tsx`: Error fallback component.
            *   `Feedback`: Feedback-related components.
                *   `RatingDisplay.tsx`: Rating display component.
                *   `ReviewForm.tsx`: Review form component.
                *   `Toast.tsx`: Toast notification component.
            *   `FileUpload`: File upload components.
                *   `FileUpload.tsx`: File upload component.
            *   `Home`: Home page components.
                *   `CallToAction.tsx`: Call to action component.
                *   `Hero.tsx`: Hero section component.
                *   `HowItWorks.tsx`: How it works component.
                *   `Testimonials.tsx`: Testimonials component.
                *   `TrustSection.tsx`: Trust section component.
            *   `Item`: Item-related components.
                *   `AvailabilityCalendar.tsx`: Calendar showing item availability.
                *   `BookingForm.tsx`: Form for booking an item.
                *   `CustomItemCard.tsx`: Custom item card component.
                *   `FeaturedItems.tsx`: Featured items component.
                *   `ImageUploader.tsx`: Image uploader component.
                *   `ItemCard.tsx`: Displays individual item information.
                *   `ItemReliabilityBadge.tsx`: Item reliability badge component.
                *   `ItemReliabilityDetails.tsx`: Item reliability details component.
                *   `UnifiedItemCard.tsx`: Enhanced item card with additional features.
            *   `Language`: Language-related components.
                *   `LanguageSelector.tsx`: Language selection dropdown component.
                *   `__tests__`: Tests for language components.
                    *   `LanguageSelector.test.tsx`: Tests for language selector component.
            *   `Layout`: Layout-related components.
                *   `CategoryDropdown.tsx`: Category dropdown component.
                *   `CustomFooter.tsx`: Custom footer component.
                *   `CustomHeader.tsx`: Custom header component.
                *   `DynamicGrid.tsx`: Grid layout component.
                *   `Layout.tsx`: Main layout component.
                *   `LoginTopBar.tsx`: Login top bar component.
                *   `ResponsiveContainer.tsx`: Responsive container component.
                *   `ShopByCategoriesDropdown.tsx`: Shop by categories dropdown.
                *   `TopBar.tsx`: Top bar component.
                *   `TopBarLink.tsx`: Top bar link component.
            *   `Messaging`: Messaging-related components.
                *   `MessageList.tsx`: Message list component.
                *   `NotificationList.tsx`: Notification list component.
            *   `Navigation`: Navigation-related components.
                *   `FloatingDropdown.tsx`: Floating dropdown component.
                *   `NestedDropdown.tsx`: Nested dropdown component.
                *   `ResponsiveNav.tsx`: Responsive navigation component.
            *   `Properties`: Property-related components.
                *   `PropertyCard.tsx`: Property card component.
            *   `Recommendations`: Recommendation-related components.
                *   `RecommendationExplanation.tsx`: Component that explains why items are recommended.
                *   `RecommendationFeedback.tsx`: Recommendation feedback component.
                *   `RecommendationSection.tsx`: Recommendation section component.
                *   `ResponsiveRecommendationSection.tsx`: Responsive recommendation section.
                *   `YouMightAlsoLike.tsx`: "You might also like" component.
            *   `Rentals`: Rental-related components.
                *   `RentalCard.tsx`: Rental card component.
            *   `Search`: Search-related components.
                *   `FilterSidebar.tsx`: Sidebar with search filters.
                *   `ListViewItem.tsx`: Item display for list view.
                *   `MapView.tsx`: Map view for search results.
                *   `MatchTypeFilter.tsx`: Filter for search match types.
                *   `MultiModalSearch.tsx`: Search form for text and image search.
                *   `SearchBar.tsx`: Basic search input component.
                *   `SearchInsights.tsx`: Displays analytics about search results.
                *   `SearchMetadataDisplay.tsx`: Shows search metadata.
                *   `SearchQueryDisplay.tsx`: Displays search query information.
                *   `SearchResults.tsx`: Displays search results.
                *   `SearchResultsHeader.tsx`: Header for search results page.
                *   `SimilarItemsSection.tsx`: Shows similar items based on search.
            *   `ui`: UI components.
                *   `Alert.tsx`: Alert component.
                *   `Badge.tsx`: Badge component.
                *   `Button.tsx`: Button component.
                *   `Card.tsx`: Card component.
                *   `Input.tsx`: Input component.
                *   `ResponsiveImage.tsx`: Responsive image component with modern format support.
                *   `Select.tsx`: Select component.
                *   `Tabs.tsx`: Tabbed interface component.
                *   `Toast.tsx`: Toast notification component.
                *   `VirtualizedList.tsx`: Virtualized list component for efficient rendering of long lists.
            *   `User`: User-related components.
                *   `UserProfile.tsx`: User profile component.
                *   `UserStats.tsx`: User statistics component.
            *   `Verification`: Verification-related components.
                *   `DocumentUpload.tsx`: Component for uploading verification documents.
                *   `FraudAlert.tsx`: Displays fraud warnings.
                *   `VerificationStatus.tsx`: Displays verification status.
                *   `VerificationStep.tsx`: User verification component.
            *   `Visualization`: Visualization-related components.
                *   `EmbeddingVisualization.tsx`: Embedding visualization component.
                *   `PreferenceVisualization.tsx`: Preference visualization component.
                *   `VisualizationDemo.tsx`: Visualization demo component.
            *   `CategorySelector.tsx`: Category selector component.
            *   `Icons.tsx`: Icon components.
            *   `ItemCard.tsx`: Item card component.
            *   `MessageList.tsx`: Message list component.
            *   `NotificationList.tsx`: Notification list component.
            *   `ReviewForm.tsx`: Review form component.
            *   `SearchBar.tsx`: Search bar component.
        *   `config`: Contains configuration files.
            *   `auth.config.ts`: Authentication configuration.
            *   `categories.ts`: Category configuration.
        *   `context`: Contains React context providers.
            *   `AuthContext.tsx`: Authentication context.
            *   `ToastContext.tsx`: Toast notification context.
        *   `contexts`: Contains additional React context providers.
            *   `AuthContext.tsx`: Authentication context.
            *   `ToastContext.tsx`: Toast notification context.
        *   `hooks`: Contains custom React hooks.
            *   `useApi.ts`: API hook for making API calls.
            *   `useAuth.ts`: Authentication hook.
            *   `useErrorHandler.ts`: Error handling hook.
            *   `useInteractive.ts`: Interactive element hook.
            *   `useMediaQuery.ts`: Media query hook.
            *   `usePerformance.ts`: Performance monitoring hook for measuring component and function performance.
            *   `useScrollDirection.ts`: Scroll direction hook.
            *   `useToast.ts`: Toast notification hook.
            *   `useVirtualization.ts`: Hook for virtualizing lists and grids.
        *   `i18n`: Contains internationalization files.
            *   `i18n.ts`: Internationalization configuration.
            *   `locales`: Contains translation files for different languages.
                *   `en`: English translations.
                    *   `translation.json`: English translation file.
                *   `es`: Spanish translations.
                    *   `translation.json`: Spanish translation file.
                *   `fr`: French translations.
                    *   `translation.json`: French translation file.
                *   `de`: German translations.
                    *   `translation.json`: German translation file.
                *   `zh`: Chinese translations.
                    *   `translation.json`: Chinese translation file.
            *   `__tests__`: Tests for internationalization.
                *   `i18n.test.ts`: Tests for i18n configuration.
        *   `lib`: Contains library code.
        *   `pages`: Contains page-level components (routes).
            *   `admin`: Admin-related pages.
            *   `AgreementManagement.tsx`: Agreement management page.
            *   `AgreementSign.tsx`: Sign agreement page.
            *   `AgreementView.tsx`: View agreement page.
            *   `AnalyticsDashboard.tsx`: Analytics dashboard page.
            *   `AuctionCreate.tsx`: Create auction page.
            *   `AuctionDashboard.tsx`: Auction dashboard page.
            *   `AuctionDetail.tsx`: Auction details page.
            *   `AuctionHelp.tsx`: Auction help page.
            *   `AuctionList.tsx`: List of auctions page.
            *   `Business`: Business account-related pages.
                *   `BusinessDashboard.tsx`: Business account dashboard page.
                *   `BusinessCreate.tsx`: Create business account page.
                *   `BusinessSelect.tsx`: Select business account page.
                *   `index.ts`: Business pages index file.
                *   `__tests__`: Tests for business account pages.
                    *   `BusinessDashboard.test.tsx`: Tests for the business dashboard page.
            *   `BookingForm.tsx`: Form for booking an item.
            *   `Contact.tsx`: Contact page.
            *   `DesignSystem.tsx`: Design system page.
            *   `FileUploadDemo.tsx`: File upload demo page.
            *   `FinancialFAQ.tsx`: Financial FAQ page.
            *   `Guidelines.tsx`: Guidelines page.
            *   `HelpCenter.tsx`: Help center page.
            *   `Home.tsx`: The home page component.
            *   `HowItWorks.tsx`: How it works page.
            *   `ItemDetails.tsx`: Item details page.
            *   `ItemForm.tsx`: Form for adding/editing items.
            *   `ItemList.tsx`: Displays a list of items.
            *   `Login.tsx`: Login page component.
            *   `Messages.tsx`: Messages page.
            *   `Notifications.tsx`: Notifications page.
            *   `Payment.tsx`: Payment processing page.
            *   `Privacy.tsx`: Privacy policy page.
            *   `ProfilePage.tsx`: User profile page.
            *   `Register.tsx`: Registration page component.
            *   `RentToBuy.tsx`: Rent-to-buy page.
            *   `ReportForm.tsx`: Form for reporting issues.
            *   `ResponsiveDesignSystem.tsx`: Responsive design system page.
            *   `SafetyCenter.tsx`: Safety center page.
            *   `SearchPage.tsx`: Search page.
            *   `SearchResults.tsx`: Displays search results.
            *   `Sitemap.tsx`: Sitemap page.
            *   `Terms.tsx`: Terms and conditions page.
            *   `TestServices.tsx`: Test services page.
            *   `Unauthorized.tsx`: Unauthorized access page.
            *   `UserProfile.tsx`: User profile page component.
            *   `VerificationCenter.tsx`: User verification center.
            *   `VisualizationDemo.tsx`: Visualization demo page.
            *   `EnhancedVisualizationDemo.tsx`: Enhanced visualization demo with interactive features.
            *   `PerformanceDashboard.tsx`: Performance monitoring dashboard.
        *   `services`: Contains API service functions.
            *   `agreementService.ts`: Agreement-related service functions.
            *   `auctionService.ts`: Auction-related service functions.
            *   `authService.ts`: Authentication-related service functions.
            *   `businessAccountService.ts`: Business account-related service functions.
            *   `categoryService.ts`: Category-related service functions.
            *   `index.ts`: Service index file.
            *   `itemService.ts`: Item and search related service functions.
            *   `paymentService.ts`: Payment processing related service functions.
            *   `recommendationService.ts`: Recommendation-related service functions.
            *   `uploadService.ts`: File upload service functions.
            *   `userService.ts`: User-related service functions.
            *   `verificationService.ts`: Verification-related service functions.
        *   `shared`: Contains shared components and utilities.
            *   `ui`: Contains shared UI components.
                *   `Badge`: Badge components.
                    *   `Badge.tsx`: Badge component.
                *   `Card`: Card components.
                    *   `Card.tsx`: Card component.
                    *   `CardContent.tsx`: Card content component.
                    *   `CardFooter.tsx`: Card footer component.
                    *   `CardHeader.tsx`: Card header component.
                    *   `CardMedia.tsx`: Card media component.
                    *   `__tests__`: Card component tests.
                        *   `Card.test.tsx`: Card component test.
                *   `__tests__`: UI component tests.
                    *   `Button.test.tsx`: Button component test.
                    *   `ItemCard.test.tsx`: Item card component test.
                *   `Button.tsx`: Button component.
                *   `Checkbox.tsx`: Checkbox component.
                *   `CheckboxGroup.tsx`: Checkbox group component.
                *   `DatePicker.tsx`: Date picker component.
                *   `Icons.tsx`: Icon components.
                *   `Input.tsx`: Input component.
                *   `InteractiveButton.tsx`: Interactive button component.
                *   `InteractiveLink.tsx`: Interactive link component.
                *   `ItemCard.tsx`: Item card component.
                *   `Modal.tsx`: Modal component.
                *   `Radio.tsx`: Radio button component.
                *   `Search.tsx`: Search component.
                *   `Select.tsx`: Select component.
        *   `styles`: Contains CSS styles.
        *   `tests`: Contains test files.
        *   `types`: Contains TypeScript type definitions.
        *   `utils`: Contains utility functions.
            *   `imageOptimization.ts`: Utilities for optimizing images with modern formats and responsive loading.
            *   `performanceMonitoring.ts`: Utilities for monitoring and reporting performance metrics.
            *   `serviceWorker.ts`: Service worker registration and management for offline support.
        *   `App.css`: CSS styles for the App component.
        *   `App.tsx`: Main application component.
        *   `index.css`: Global CSS styles.
        *   `main.tsx`: Entry point for the React application.
        *   `setupTests.js`: Setup file for Jest tests.
        *   `vite-env.d.ts`: TypeScript environment declarations for Vite.
    *   `tests`: Contains Playwright test files.
        *   `accessibility.spec.js`: Accessibility test file.
        *   `agreement-components.spec.js`: Agreement components test file.
        *   `apple-signin.spec.js`: Apple sign-in test file.
        *   `basic-navigation.test.js`: Basic navigation test file.
        *   `basic.spec.js`: Basic test file.
        *   `codebase-analyzer.js`: Codebase analyzer script.
        *   `component-test.spec.js`: Component test file.
        *   `example.spec.js`: Example test file.
        *   `example.spec.ts`: TypeScript example test file.
        *   `fixed-basic.spec.js`: Fixed basic test file.
        *   `home.test.js`: Home page test file.
        *   `page-objects`: Contains page object models for Playwright tests.
            *   `BasePage.js`: Base page object class.
            *   `HomePage.js`: Home page object class.
            *   `HomePage.ts`: TypeScript home page object class.
        *   `README.md`: README file for tests.
        *   `recommendations.test.js`: Recommendations test file.
        *   `reporters`: Contains custom reporters for Playwright.
            *   `DetailedReporter.js`: Custom reporter for detailed test output.
        *   `simple.spec.js`: Simple test file.
        *   `simple-test.spec.js`: Simple test file.
        *   `smoke.spec.js`: Smoke test file.
        *   `specs`: Contains more specific test files.
            *   `home.spec.js`: Tests for the home page.
        *   `testResults`: Contains test results from Playwright tests.
            *   `html-report`: Contains HTML reports generated by Playwright.
        *   `testscripts`: Contains test scripts.
            *   `frontend`: Frontend test scripts.
                *   `accessibility.test.js`: Accessibility test script.
                *   `authentication.test.js`: Authentication test script.
                *   `basic-smoke.test.js`: Basic smoke test script.
                *   `enhanced-home.test.js`: Enhanced home test script.
                *   `expected-results.md`: Expected test results.
                *   `helpers`: Test helper functions.
                    *   `test-helpers.js`: Test helper functions.
                    *   `test-utils.js`: Test utility functions.
                *   `home.test.js`: Home page test script.
                *   `item-details.test.js`: Item details test script.
                *   `resilient-home.test.js`: Resilient home test script.
                *   `responsive.test.js`: Responsive design test script.
                *   `run-tests-with-results.js`: Script to run tests with results.
                *   `search.test.js`: Search test script.
                *   `simple-test.js`: Simple test script.
                *   `simple-test.test.js`: Simple test script.
                *   `test-results-template.md`: Test results template.
                *   `user-dashboard.test.js`: User dashboard test script.
            *   `playwright.config.ts`: Playwright configuration file.
            *   `run-test-loop.js`: Script to run tests in a loop.
            *   `run-tests.js`: Script to run tests.
            *   `validate-code-structure.js`: Script to validate code structure.
        *   `very-simple.spec.js`: Very simple test file.
        *   `visualization-fixed.test.js`: Fixed visualization test file.
        *   `visualization.test.js`: Visualization test file.
    *   `__mocks__`: Contains mock files for Jest tests.
        *   `fileMock.js`: Mock file for static file imports in Jest tests.

## Wireframes Directory

*   `wireframes`: Contains the wireframes and design mockups for the project.

## Test Scripts Directory

*   `testscripts`: Contains the test scripts for the project.
    *   `frontend`: Contains the frontend test scripts.
        *   `README.md`: Documentation for the frontend test scripts.
        *   `home.test.js`: Tests for the home page functionality.
        *   `search.test.js`: Tests for the search functionality and results page.
        *   `item-details.test.js`: Tests for the item details page.
        *   `user-dashboard.test.js`: Tests for the user dashboard functionality.
        *   `authentication.test.js`: Tests for the authentication functionality.
        *   `accessibility.test.js`: Tests for accessibility compliance.
        *   `responsive.test.js`: Tests for responsive design across different screen sizes.
        *   `basic-smoke.test.js`: Basic smoke tests for the application.
    *   `backend`: Contains the backend test scripts (to be implemented).

## Test Results Directory

*   `testResults`: Contains the test files and results organized by development phases.
    *   `README.md`: Overview of the test results directory structure.
    *   `phase0`: Contains test files and results for Phase 0 (Project Setup).
        *   `phase0-test-report.md`: Comprehensive test report for Phase 0.
    *   `phase1`: Contains test files and results for Phase 1 (Frontend Scaffolding).
        *   `phase1-test-report.md`: Comprehensive test report for Phase 1.
        *   `cypress`: Contains Cypress test results for Phase 1.
            *   `e2e`: End-to-end test results.
                *   `phase1`: Phase 1 specific test files.
    *   `phase2`: Contains test files and results for Phase 2 (Backend Integration) - COMPLETED.
        *   `phase2_full_testResult.md`: Comprehensive test report for Phase 2.
        *   `phase2_status_update.md`: Final status update for Phase 2.
        *   `api-tests`: API test results for Phase 2.
    *   `phase3`: Contains test files and results for Phase 3 (Advanced Features).
        *   `README.md`: Overview of Phase 3 test results.
        *   `test_report.md`: Comprehensive test report for Phase 3.
        *   `implementation`: Implementation reports for Phase 3.
            *   `final_implementation_report.md`: Detailed implementation report.
    *   `phase4`: Contains test files and results for Phase 4 (Optimization & Deployment) (to be implemented).
    *   `frontend`: Frontend-specific test results.
        *   `README.md`: Overview of frontend test results.
        *   `tests`: Frontend test results.
            *   `screenshots`: Screenshots from frontend tests.
    *   `backend`: Backend-specific test results.
        *   `README.md`: Overview of backend test results.
        *   `tests`: Backend test results.
    *   `archive`: Archived test results and files.
        *   `README.md`: Overview of archived test results.
        *   `playwright_artifacts`: Playwright test artifacts.
        *   `hash_files`: Files with hash names from previous test runs.
        *   `test_results`: Archived test results.

## Key Files

### Frontend

- `frontend/src/main.tsx` - Application entry point that renders the App component
- `frontend/src/App.tsx` - Main application component that sets up routing and global providers
- `frontend/src/pages/Login.tsx` - Login page with authentication functionality
- `frontend/src/pages/Home.tsx` - Home page with featured items and categories
- `frontend/src/context/ToastContext.tsx` - Toast notification context for displaying alerts
- `frontend/src/context/AuthContext.tsx` - Authentication context for managing user sessions
- `frontend/src/services/authService.ts` - Authentication service for API interactions
- `frontend/tailwind.config.ts` - Tailwind CSS configuration for styling

### Backend

- `backend/app/main.py` - Backend entry point that initializes the FastAPI application
- `backend/app/core/config.py` - Configuration settings for the backend application
- `backend/app/core/database.py` - Database connection and session management
- `backend/app/api/v1/router.py` - Main API router that includes all endpoint routers
- `backend/app/models/user.py` - User model for database interactions
- `backend/app/models/item.py` - Item model for database interactions
- `backend/app/services/auction_service.py` - Business logic for auctions
- `backend/requirements.txt` - Python dependencies for the backend

### Monitoring

- `monitoring/prometheus/prometheus.yml` - Prometheus configuration for metrics collection
- `monitoring/prometheus/rules/app_rules.yml` - Application alert rules for Prometheus
- `monitoring/alertmanager/alertmanager.yml` - Alertmanager configuration for alert routing
- `monitoring/grafana/dashboards/overview.json` - Overview dashboard for Grafana
- `docs/monitoring/monitoring_and_alerting_guide.md` - Comprehensive monitoring guide

## File Relationships

### Frontend Relationships

- **Pages and Components**: Pages (`frontend/src/pages/`) use components from `frontend/src/components/` to build the UI. For example, `ItemDetails.tsx` uses `ItemCard.tsx`, `ItemReliabilityBadge.tsx`, and other components.

- **Components and UI**: Components use UI elements from `frontend/src/shared/ui/` for consistent styling. The `components/ui/` directory imports and re-exports these shared UI components.

- **Hooks and Context**: Components use hooks from `frontend/src/hooks/` to access functionality and context from `frontend/src/context/`. For example, `useAuth()` provides access to the `AuthContext`.

- **Services and API**: Components and hooks use services from `frontend/src/services/` to interact with the backend API. For example, `authService.ts` handles authentication API calls.

- **Styles and Components**: Components use styles from `frontend/src/styles/` for consistent styling. Global styles are defined in `index.css` and `App.css`.

### Backend Relationships

- **API and Services**: API endpoints in `backend/app/api/v1/` use services from `backend/app/services/` to implement business logic. For example, `auctions.py` uses `auction_service.py`.

- **Services and Models**: Services interact with database models in `backend/app/models/` to perform CRUD operations. For example, `auction_service.py` uses `auction.py` model.

- **Models and Database**: Models use the database connection from `backend/app/core/database.py` to interact with the database.

- **API and Schemas**: API endpoints use schemas from `backend/app/schemas/` to validate request and response data.

- **Templates and Services**: Services use templates from `backend/templates/` to generate content. For example, `agreement_service.py` uses templates from `templates/agreements/`.

### Cross-Application Relationships

- **Frontend and Backend**: Frontend services make HTTP requests to backend API endpoints. For example, `authService.ts` calls endpoints defined in `auth.py`.

- **Shared Types**: Both frontend and backend use similar data structures, with TypeScript types in `frontend/src/types/` corresponding to Pydantic schemas in `backend/app/schemas/`.

## Development Workflow

1. **Frontend Development**:
   - Start with page components in `frontend/src/pages/`
   - Create or modify UI components in `frontend/src/components/` or `frontend/src/shared/ui/`
   - Implement API interactions in `frontend/src/services/`
   - Add styles in `frontend/src/styles/`
   - Write tests in `frontend/tests/`

2. **Backend Development**:
   - Define API endpoints in `backend/app/api/v1/`
   - Implement business logic in `backend/app/services/`
   - Create or modify database models in `backend/app/models/`
   - Define schemas in `backend/app/schemas/`
   - Write tests in `backend/app/tests/`

3. **Database Changes**:
   - Modify models in `backend/app/models/`
   - Create migration scripts using Alembic: `alembic revision --autogenerate -m "description"`
   - Apply migrations: `alembic upgrade head`

4. **Documentation**:
   - Update component documentation in `docs/components/`
   - Update API documentation in `docs/api/`
   - Update architecture documentation in `docs/architecture/`
   - Update this directory structure document when making significant changes

## Using This Document for Codebase Indexing

This document can be used as a reference for quickly locating files in the codebase:

1. Use the table of contents to navigate to the relevant section
2. File paths are provided in a consistent format: `directory/subdirectory/file.extension`
3. Each file includes a brief description of its purpose
4. For searching specific functionality, use the descriptions to identify relevant files

When adding new files to the project, please update this document to maintain its usefulness as a codebase index.

---

Last Updated: 2025-05-12
