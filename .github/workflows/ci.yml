name: RentUp Backend CI

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - '.github/workflows/ci.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/**'
      - '.github/workflows/ci.yml'

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy
        pip install -r backend/requirements.txt
    
    - name: Lint with flake8
      run: |
        flake8 backend --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 backend --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
    
    - name: Check formatting with black
      run: |
        black --check backend
    
    - name: Check imports with isort
      run: |
        isort --check-only --profile black backend
    
    - name: Type check with mypy
      run: |
        mypy backend

  test:
    name: Test
    runs-on: ubuntu-latest
    needs: lint
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: rentup
          POSTGRES_PASSWORD: rentup_secure_password
          POSTGRES_DB: rentup_test
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov
        pip install -r backend/requirements.txt
    
    - name: Run tests
      env:
        POSTGRES_SERVER: localhost
        POSTGRES_USER: rentup
        POSTGRES_PASSWORD: rentup_secure_password
        POSTGRES_DB: rentup_test
        POSTGRES_PORT: 5432
        SECRET_KEY: test_secret_key
        ENVIRONMENT: test
      run: |
        cd backend
        pytest --cov=app --cov-report=xml
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        fail_ci_if_error: false

  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        cache: 'pip'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
        pip install -r backend/requirements.txt
    
    - name: Run security audit with bandit
      run: |
        bandit -r backend/app -f json -o bandit-results.json
      continue-on-error: true
    
    - name: Check dependencies with safety
      run: |
        safety check -r backend/requirements.txt --json > safety-results.json
      continue-on-error: true
    
    - name: Run custom security audit
      run: |
        cd backend
        python scripts/security_audit.py --all --output security_audit_report.json
      continue-on-error: true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-results.json
          safety-results.json
          backend/security_audit_report.json

  build:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to GitHub Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata for Docker
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ghcr.io/${{ github.repository }}/backend
        tags: |
          type=ref,event=branch
          type=sha,format=short
    
    - name: Build and push
      uses: docker/build-push-action@v4
      with:
        context: ./backend
        file: ./backend/Dockerfile.prod
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'
    
    - name: Configure kubectl
      run: |
        mkdir -p $HOME/.kube
        echo "${{ secrets.KUBE_CONFIG }}" > $HOME/.kube/config
        chmod 600 $HOME/.kube/config
    
    - name: Deploy to Kubernetes
      run: |
        # Update image tag in deployment
        IMAGE_TAG=$(echo ${{ github.sha }} | cut -c1-7)
        sed -i "s|image: ghcr.io/${{ github.repository }}/backend:.*|image: ghcr.io/${{ github.repository }}/backend:sha-$IMAGE_TAG|g" kubernetes/rentup-deployment.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f kubernetes/rentup-deployment.yaml
        
        # Wait for deployment to complete
        kubectl rollout status deployment/rentup-backend -n rentup --timeout=300s
    
    - name: Run database migrations
      run: |
        # Run migrations using Kubernetes job
        cat <<EOF | kubectl apply -f -
        apiVersion: batch/v1
        kind: Job
        metadata:
          name: rentup-migrations-$IMAGE_TAG
          namespace: rentup
        spec:
          ttlSecondsAfterFinished: 100
          template:
            spec:
              containers:
              - name: migrations
                image: ghcr.io/${{ github.repository }}/backend:sha-$IMAGE_TAG
                command: ["alembic", "upgrade", "head"]
                env:
                - name: POSTGRES_SERVER
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_host
                - name: POSTGRES_USER
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_user
                - name: POSTGRES_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_password
                - name: POSTGRES_DB
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_name
              restartPolicy: Never
          backoffLimit: 4
        EOF
        
        # Wait for migration job to complete
        kubectl wait --for=condition=complete job/rentup-migrations-$IMAGE_TAG -n rentup --timeout=300s
    
    - name: Run security tests
      run: |
        # Run security tests using Kubernetes job
        cat <<EOF | kubectl apply -f -
        apiVersion: batch/v1
        kind: Job
        metadata:
          name: rentup-security-tests-$IMAGE_TAG
          namespace: rentup
        spec:
          ttlSecondsAfterFinished: 100
          template:
            spec:
              containers:
              - name: security-tests
                image: ghcr.io/${{ github.repository }}/backend:sha-$IMAGE_TAG
                command: ["python", "scripts/test_security.py", "--all", "--api-url", "http://rentup-backend.rentup.svc.cluster.local"]
                env:
                - name: POSTGRES_SERVER
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_host
                - name: POSTGRES_USER
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_user
                - name: POSTGRES_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_password
                - name: POSTGRES_DB
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_name
              restartPolicy: Never
          backoffLimit: 4
        EOF
        
        # Wait for security tests job to complete
        kubectl wait --for=condition=complete job/rentup-security-tests-$IMAGE_TAG -n rentup --timeout=300s
    
    - name: Notify deployment
      run: |
        curl -X POST -H "Content-Type: application/json" -d '{"text":"RentUp backend deployed to staging: sha-${{ github.sha }}"}' ${{ secrets.SLACK_WEBHOOK_URL }}

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'latest'
    
    - name: Configure kubectl
      run: |
        mkdir -p $HOME/.kube
        echo "${{ secrets.KUBE_CONFIG }}" > $HOME/.kube/config
        chmod 600 $HOME/.kube/config
    
    - name: Deploy to Kubernetes
      run: |
        # Update image tag in deployment
        IMAGE_TAG=$(echo ${{ github.sha }} | cut -c1-7)
        sed -i "s|image: ghcr.io/${{ github.repository }}/backend:.*|image: ghcr.io/${{ github.repository }}/backend:sha-$IMAGE_TAG|g" kubernetes/rentup-deployment.yaml
        
        # Apply Kubernetes manifests
        kubectl apply -f kubernetes/rentup-deployment.yaml
        
        # Wait for deployment to complete
        kubectl rollout status deployment/rentup-backend -n rentup --timeout=300s
    
    - name: Run database migrations
      run: |
        # Run migrations using Kubernetes job
        cat <<EOF | kubectl apply -f -
        apiVersion: batch/v1
        kind: Job
        metadata:
          name: rentup-migrations-$IMAGE_TAG
          namespace: rentup
        spec:
          ttlSecondsAfterFinished: 100
          template:
            spec:
              containers:
              - name: migrations
                image: ghcr.io/${{ github.repository }}/backend:sha-$IMAGE_TAG
                command: ["alembic", "upgrade", "head"]
                env:
                - name: POSTGRES_SERVER
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_host
                - name: POSTGRES_USER
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_user
                - name: POSTGRES_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_password
                - name: POSTGRES_DB
                  valueFrom:
                    secretKeyRef:
                      name: rentup-db-secrets
                      key: db_name
              restartPolicy: Never
          backoffLimit: 4
        EOF
        
        # Wait for migration job to complete
        kubectl wait --for=condition=complete job/rentup-migrations-$IMAGE_TAG -n rentup --timeout=300s
    
    - name: Notify deployment
      run: |
        curl -X POST -H "Content-Type: application/json" -d '{"text":"RentUp backend deployed to production: sha-${{ github.sha }}"}' ${{ secrets.SLACK_WEBHOOK_URL }}
