apiVersion: v1
kind: Namespace
metadata:
  name: rentup
---
apiVersion: v1
kind: Secret
metadata:
  name: rentup-db-secrets
  namespace: rentup
type: Opaque
data:
  # These are placeholders - replace with actual base64-encoded values
  db_host: cG9zdGdyZXMtcHJpbWFyeS5kZWZhdWx0LnN2Yy5jbHVzdGVyLmxvY2Fs
  db_user: cmVudHVw
  db_password: cmVudHVwX3NlY3VyZV9wYXNzd29yZA==
  db_name: cmVudHVw
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rentup-backend
  namespace: rentup
  labels:
    app: rentup-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rentup-backend
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: rentup-backend
    spec:
      containers:
      - name: rentup-backend
        image: rentup/backend:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: POSTGRES_SERVER
          valueFrom:
            secretKeyRef:
              name: rentup-db-secrets
              key: db_host
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: rentup-db-secrets
              key: db_user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rentup-db-secrets
              key: db_password
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: rentup-db-secrets
              key: db_name
        - name: READ_REPLICAS_ENABLED
          value: "true"
        - name: READ_REPLICA_COUNT
          value: "2"
        - name: SHARDING_ENABLED
          value: "true"
        - name: SHARD_COUNT
          value: "4"
        - name: DB_POOL_SIZE
          value: "20"
        - name: DB_MAX_OVERFLOW
          value: "10"
        - name: DB_POOL_TIMEOUT
          value: "30"
        - name: DB_POOL_RECYCLE
          value: "1800"
        - name: QUERY_CACHE_ENABLED
          value: "true"
        - name: QUERY_CACHE_TTL
          value: "3600"
        - name: PLAN_CACHE_ENABLED
          value: "true"
        - name: PLAN_CACHE_TTL
          value: "3600"
        resources:
          limits:
            cpu: "1"
            memory: "1Gi"
          requests:
            cpu: "500m"
            memory: "512Mi"
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
      volumes:
      - name: config-volume
        configMap:
          name: rentup-config
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: rentup-config
  namespace: rentup
data:
  app-config.json: |
    {
      "log_level": "INFO",
      "log_format": "json",
      "monitoring_enabled": true,
      "monitoring_interval": 60,
      "slow_query_threshold": 0.5
    }
---
apiVersion: v1
kind: Service
metadata:
  name: rentup-backend
  namespace: rentup
  labels:
    app: rentup-backend
spec:
  selector:
    app: rentup-backend
  ports:
  - port: 80
    targetPort: 8000
    name: http
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rentup-ingress
  namespace: rentup
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - api.rentup.example.com
    secretName: rentup-tls
  rules:
  - host: api.rentup.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rentup-backend
            port:
              name: http
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: rentup-backend-hpa
  namespace: rentup
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: rentup-backend
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 20
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: rentup-backend-monitor
  namespace: rentup
  labels:
    release: prometheus
spec:
  selector:
    matchLabels:
      app: rentup-backend
  endpoints:
  - port: http
    path: /metrics
    interval: 15s
  namespaceSelector:
    matchNames:
    - rentup
