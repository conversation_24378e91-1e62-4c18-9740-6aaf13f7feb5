"""
Unified database optimization service for RentUp backend.

This module provides a unified API for all database optimization features including:
- Query optimization
- JOIN optimization
- Query caching
- Performance monitoring
- Batch operations
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, TypeVar, Generic, Union

from sqlalchemy.orm import Query, Session

from app.core.query_optimization import (
    OptimizedQuery, 
    query_tracker, 
    analyze_query, 
    optimize_query,
    paginate_query
)
from app.core.join_optimization import (
    optimize_join_query,
    analyze_join_performance,
    JoinStrategy,
    JoinAnalysis
)
from app.core.query_cache import (
    query_cache,
    cached_query,
    CacheStrategy,
    get_cache_stats
)
from app.core.db_config import get_database_config

logger = logging.getLogger(__name__)

# Type variable for model classes
M = TypeVar('M')

# Check if optimization modules are available
OPTIMIZATION_MODULES_AVAILABLE = True


class DatabaseOptimizationService:
    """
    Unified service for database optimization operations.
    """
    
    def __init__(self):
        """Initialize the optimization service."""
        self.config = get_database_config()
        self._enabled = self.config.query_optimization.enabled
    
    def optimized_query(
        self, 
        query: Query[M], 
        session: Session, 
        model_class: Optional[type] = None
    ) -> OptimizedQuery[M]:
        """
        Create an optimized query wrapper.
        
        Args:
            query: SQLAlchemy query to optimize
            session: Database session
            model_class: Optional model class for type hints
            
        Returns:
            OptimizedQuery wrapper
        """
        return OptimizedQuery(query, session, model_class)
    
    def analyze_query_performance(
        self, 
        query: Query[Any], 
        session: Session
    ) -> Dict[str, Any]:
        """
        Analyze query performance and provide recommendations.
        
        Args:
            query: SQLAlchemy query to analyze
            session: Database session
            
        Returns:
            Analysis results with recommendations
        """
        if not self._enabled:
            return {"enabled": False, "message": "Query optimization is disabled"}
        
        try:
            # Analyze query
            analysis = analyze_query(query, session)
            
            # Analyze JOIN performance if applicable
            join_analysis = None
            try:
                join_analysis = analyze_join_performance(session, query)
            except Exception as e:
                logger.debug(f"JOIN analysis failed: {e}")
            
            return {
                "query_hash": analysis.query_hash,
                "execution_plan": analysis.execution_plan,
                "estimated_cost": analysis.estimated_cost,
                "execution_time": analysis.execution_time,
                "rows_returned": analysis.rows_returned,
                "indexes_used": analysis.indexes_used,
                "recommendations": analysis.recommendations,
                "join_analysis": join_analysis.join_patterns if join_analysis else [],
                "cache_stats": get_cache_stats().to_dict() if hasattr(get_cache_stats(), 'to_dict') else {}
            }
        except Exception as e:
            logger.error(f"Query analysis failed: {e}")
            return {"error": str(e)}
    
    def optimize_joins(
        self, 
        query: Query[Any], 
        session: Session,
        strategy: Optional[JoinStrategy] = None
    ) -> Query[Any]:
        """
        Optimize JOIN operations in a query.
        
        Args:
            query: SQLAlchemy query to optimize
            session: Database session
            strategy: Optional JOIN strategy to use
            
        Returns:
            Optimized query
        """
        if not self._enabled:
            return query
        
        try:
            return optimize_join_query(query, session)
        except Exception as e:
            logger.error(f"JOIN optimization failed: {e}")
            return query
    
    def cache_query(
        self, 
        query: Query[Any], 
        ttl: int = 300,
        strategy: CacheStrategy = CacheStrategy.SMART
    ) -> Any:
        """
        Execute a query with caching.
        
        Args:
            query: SQLAlchemy query to execute
            ttl: Cache TTL in seconds
            strategy: Caching strategy
            
        Returns:
            Query results (from cache or database)
        """
        if not self.config.caching.enabled:
            return query.all()
        
        # Generate cache key
        cache_key = query_cache._generate_key(query)
        
        # Try to get from cache
        cached_result = query_cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        # Execute query and cache result
        result = query.all()
        query_cache.set(cache_key, result, ttl, strategy)
        
        return result
    
    def batch_get_by_ids(
        self, 
        session: Session, 
        model_class: type, 
        ids: List[Any]
    ) -> Dict[Any, Any]:
        """
        Efficiently get multiple records by their IDs.
        
        Args:
            session: Database session
            model_class: Model class to query
            ids: List of IDs to fetch
            
        Returns:
            Dictionary mapping IDs to model instances
        """
        if not ids:
            return {}
        
        # Use a single query to fetch all records
        query = session.query(model_class).filter(model_class.id.in_(ids))
        
        # Apply optimization if enabled
        if self._enabled:
            query = optimize_query(query, session)
        
        # Execute query and build result map
        records = query.all()
        return {record.id: record for record in records}
    
    def batch_get_related(
        self, 
        session: Session, 
        model_class: type, 
        parent_ids: List[Any],
        foreign_key_field: str
    ) -> Dict[Any, List[Any]]:
        """
        Efficiently get related records for multiple parent IDs.
        
        Args:
            session: Database session
            model_class: Model class to query
            parent_ids: List of parent IDs
            foreign_key_field: Name of the foreign key field
            
        Returns:
            Dictionary mapping parent IDs to lists of related records
        """
        if not parent_ids:
            return {}
        
        # Use a single query to fetch all related records
        foreign_key = getattr(model_class, foreign_key_field)
        query = session.query(model_class).filter(foreign_key.in_(parent_ids))
        
        # Apply optimization if enabled
        if self._enabled:
            query = optimize_query(query, session)
        
        # Execute query and group by parent ID
        records = query.all()
        result = {parent_id: [] for parent_id in parent_ids}
        
        for record in records:
            parent_id = getattr(record, foreign_key_field)
            if parent_id in result:
                result[parent_id].append(record)
        
        return result
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive performance statistics.
        
        Returns:
            Dictionary with performance metrics
        """
        stats = {
            "optimization_enabled": self._enabled,
            "caching_enabled": self.config.caching.enabled,
            "monitoring_enabled": self.config.monitoring.enabled,
        }
        
        # Add query tracker statistics
        try:
            query_stats = query_tracker.get_statistics()
            stats["query_performance"] = query_stats
        except Exception as e:
            logger.debug(f"Failed to get query stats: {e}")
        
        # Add cache statistics
        try:
            cache_stats = get_cache_stats()
            if hasattr(cache_stats, 'to_dict'):
                stats["cache_performance"] = cache_stats.to_dict()
            else:
                stats["cache_performance"] = {
                    "hits": getattr(cache_stats, 'hits', 0),
                    "misses": getattr(cache_stats, 'misses', 0),
                    "hit_rate": getattr(cache_stats, 'hit_rate', 0.0)
                }
        except Exception as e:
            logger.debug(f"Failed to get cache stats: {e}")
        
        return stats
    
    def clear_caches(self) -> Dict[str, int]:
        """
        Clear all caches.
        
        Returns:
            Dictionary with number of cleared entries
        """
        cleared = {}
        
        try:
            query_cleared = query_cache.invalidate()
            cleared["query_cache"] = query_cleared
        except Exception as e:
            logger.error(f"Failed to clear query cache: {e}")
            cleared["query_cache"] = 0
        
        try:
            query_tracker.reset_statistics()
            cleared["query_stats"] = 1
        except Exception as e:
            logger.error(f"Failed to reset query stats: {e}")
            cleared["query_stats"] = 0
        
        return cleared


# Global optimization service instance
_optimization_service = DatabaseOptimizationService()


def optimized_query(
    query: Query[M], 
    session: Session, 
    model_class: Optional[type] = None
) -> OptimizedQuery[M]:
    """
    Create an optimized query wrapper.
    
    Args:
        query: SQLAlchemy query to optimize
        session: Database session
        model_class: Optional model class for type hints
        
    Returns:
        OptimizedQuery wrapper
    """
    return _optimization_service.optimized_query(query, session, model_class)


def batch_get(
    session: Session, 
    model_class: type, 
    ids: List[Any]
) -> Dict[Any, Any]:
    """
    Efficiently get multiple records by their IDs.
    
    Args:
        session: Database session
        model_class: Model class to query
        ids: List of IDs to fetch
        
    Returns:
        Dictionary mapping IDs to model instances
    """
    return _optimization_service.batch_get_by_ids(session, model_class, ids)


def batch_get_related(
    session: Session, 
    model_class: type, 
    parent_ids: List[Any],
    foreign_key_field: str
) -> Dict[Any, List[Any]]:
    """
    Efficiently get related records for multiple parent IDs.
    
    Args:
        session: Database session
        model_class: Model class to query
        parent_ids: List of parent IDs
        foreign_key_field: Name of the foreign key field
        
    Returns:
        Dictionary mapping parent IDs to lists of related records
    """
    return _optimization_service.batch_get_related(session, model_class, parent_ids, foreign_key_field)


def get_optimization_service() -> DatabaseOptimizationService:
    """Get the global optimization service instance."""
    return _optimization_service
