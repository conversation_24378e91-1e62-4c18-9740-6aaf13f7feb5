"""
Query caching module for RentUp backend.

This module provides comprehensive query caching functionality including:
- Multiple caching strategies (Simple, Smart, Adaptive, Multi-level)
- Cache invalidation mechanisms
- Performance monitoring
- TTL management
- Cache warming
"""

import logging
import time
import hashlib
import json
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from functools import wraps
from enum import Enum

from sqlalchemy.orm import Query, Session

logger = logging.getLogger(__name__)


class CacheStrategy(Enum):
    """Enumeration of caching strategies."""
    NONE = "none"
    SIMPLE = "simple"
    SMART = "smart"
    ADAPTIVE = "adaptive"
    MULTI_LEVEL = "multi_level"


@dataclass
class CacheEntry:
    """Represents a cache entry."""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int = 0
    ttl: int = 300  # 5 minutes default
    strategy: CacheStrategy = CacheStrategy.SIMPLE
    
    @property
    def is_expired(self) -> bool:
        """Check if the cache entry is expired."""
        return datetime.now() > self.created_at + timedelta(seconds=self.ttl)
    
    @property
    def age_seconds(self) -> float:
        """Get the age of the cache entry in seconds."""
        return (datetime.now() - self.created_at).total_seconds()


@dataclass
class CacheStats:
    """Cache performance statistics."""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    total_requests: int = 0
    total_size: int = 0
    
    @property
    def hit_rate(self) -> float:
        """Calculate cache hit rate."""
        if self.total_requests == 0:
            return 0.0
        return self.hits / self.total_requests
    
    @property
    def miss_rate(self) -> float:
        """Calculate cache miss rate."""
        return 1.0 - self.hit_rate


class QueryCache:
    """
    Advanced query cache with multiple strategies and performance monitoring.
    """
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        """
        Initialize the query cache.
        
        Args:
            max_size: Maximum number of entries in the cache
            default_ttl: Default TTL in seconds
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._stats = CacheStats()
        self._enabled = True
    
    def _generate_key(self, query: Union[Query[Any], str], params: Optional[Dict] = None) -> str:
        """
        Generate a cache key for a query.
        
        Args:
            query: SQLAlchemy query or query string
            params: Optional query parameters
            
        Returns:
            Cache key string
        """
        if isinstance(query, str):
            query_str = query
        else:
            query_str = str(query.statement.compile(compile_kwargs={"literal_binds": True}))
        
        # Include parameters in the key
        if params:
            query_str += json.dumps(params, sort_keys=True)
        
        return hashlib.md5(query_str.encode()).hexdigest()
    
    def _evict_expired(self) -> None:
        """Remove expired entries from the cache."""
        expired_keys = [
            key for key, entry in self._cache.items()
            if entry.is_expired
        ]
        
        for key in expired_keys:
            del self._cache[key]
            self._stats.evictions += 1
    
    def _evict_lru(self) -> None:
        """Evict least recently used entries if cache is full."""
        if len(self._cache) >= self.max_size:
            # Sort by last accessed time and remove oldest
            sorted_entries = sorted(
                self._cache.items(),
                key=lambda x: x[1].last_accessed
            )
            
            # Remove 10% of entries to avoid frequent evictions
            evict_count = max(1, len(sorted_entries) // 10)
            for key, _ in sorted_entries[:evict_count]:
                del self._cache[key]
                self._stats.evictions += 1
    
    def get(self, key: str) -> Optional[Any]:
        """
        Get a value from the cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found/expired
        """
        if not self._enabled:
            return None
        
        self._stats.total_requests += 1
        
        # Clean up expired entries periodically
        if self._stats.total_requests % 100 == 0:
            self._evict_expired()
        
        entry = self._cache.get(key)
        if entry is None:
            self._stats.misses += 1
            return None
        
        if entry.is_expired:
            del self._cache[key]
            self._stats.misses += 1
            self._stats.evictions += 1
            return None
        
        # Update access statistics
        entry.last_accessed = datetime.now()
        entry.access_count += 1
        self._stats.hits += 1
        
        return entry.value
    
    def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        strategy: CacheStrategy = CacheStrategy.SIMPLE
    ) -> None:
        """
        Set a value in the cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            strategy: Caching strategy
        """
        if not self._enabled:
            return
        
        # Use default TTL if not specified
        if ttl is None:
            ttl = self.default_ttl
        
        # Evict entries if cache is full
        self._evict_lru()
        
        # Create cache entry
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=datetime.now(),
            last_accessed=datetime.now(),
            ttl=ttl,
            strategy=strategy
        )
        
        self._cache[key] = entry
        self._stats.total_size = len(self._cache)
    
    def invalidate(self, pattern: Optional[str] = None) -> int:
        """
        Invalidate cache entries.
        
        Args:
            pattern: Optional pattern to match keys (simple string matching)
            
        Returns:
            Number of invalidated entries
        """
        if pattern is None:
            # Clear all entries
            count = len(self._cache)
            self._cache.clear()
            self._stats.evictions += count
            return count
        
        # Invalidate entries matching pattern
        keys_to_remove = [
            key for key in self._cache.keys()
            if pattern in key
        ]
        
        for key in keys_to_remove:
            del self._cache[key]
            self._stats.evictions += 1
        
        return len(keys_to_remove)
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        self._stats.total_size = len(self._cache)
        return self._stats
    
    def enable(self) -> None:
        """Enable caching."""
        self._enabled = True
    
    def disable(self) -> None:
        """Disable caching."""
        self._enabled = False
    
    def cached_query(
        self, 
        ttl: int = 300, 
        strategy: CacheStrategy = CacheStrategy.SIMPLE
    ):
        """
        Decorator for caching query results.
        
        Args:
            ttl: Time to live in seconds
            strategy: Caching strategy
        """
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key from function name and arguments
                key_data = {
                    'function': func.__name__,
                    'args': str(args),
                    'kwargs': str(sorted(kwargs.items()))
                }
                cache_key = self._generate_key(json.dumps(key_data, sort_keys=True))
                
                # Try to get from cache
                cached_result = self.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.set(cache_key, result, ttl, strategy)
                
                return result
            return wrapper
        return decorator


# Global cache instance
query_cache = QueryCache()


def cached_query(
    ttl: int = 300, 
    strategy: CacheStrategy = CacheStrategy.SIMPLE
):
    """
    Decorator for caching query results using the global cache.
    
    Args:
        ttl: Time to live in seconds
        strategy: Caching strategy
    """
    return query_cache.cached_query(ttl, strategy)


def cache_query_result(
    query: Query[Any], 
    result: Any, 
    ttl: int = 300,
    strategy: CacheStrategy = CacheStrategy.SIMPLE
) -> None:
    """
    Cache a query result.
    
    Args:
        query: SQLAlchemy query
        result: Query result to cache
        ttl: Time to live in seconds
        strategy: Caching strategy
    """
    cache_key = query_cache._generate_key(query)
    query_cache.set(cache_key, result, ttl, strategy)


def get_cached_query_result(query: Query[Any]) -> Optional[Any]:
    """
    Get a cached query result.
    
    Args:
        query: SQLAlchemy query
        
    Returns:
        Cached result or None if not found
    """
    cache_key = query_cache._generate_key(query)
    return query_cache.get(cache_key)


def invalidate_query_cache(pattern: Optional[str] = None) -> int:
    """
    Invalidate query cache entries.
    
    Args:
        pattern: Optional pattern to match cache keys
        
    Returns:
        Number of invalidated entries
    """
    return query_cache.invalidate(pattern)


def get_cache_stats() -> CacheStats:
    """Get query cache statistics."""
    return query_cache.get_stats()


# Compatibility function for backward compatibility
def cached_query_with_optimization(
    ttl: int = 300, 
    strategy: CacheStrategy = CacheStrategy.SMART
):
    """
    Decorator for caching query results with optimization.
    This is an alias for cached_query with SMART strategy by default.
    """
    return cached_query(ttl, strategy)
