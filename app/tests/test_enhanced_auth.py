"""
Tests for the enhanced authentication security module.
"""
import pytest
from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.testclient import TestClient
from unittest.mock import MagicMock, patch

# Mock the enhanced_auth module

# Create mock classes and functions
class PasswordValidationError:
    def __init__(self, code, message):
        self.code = code
        self.message = message

class LoginAttemptTracker:
    def __init__(self, redis_client, max_attempts=5, lockout_time=900, attempt_expiry=3600):
        self.redis = redis_client
        self.max_attempts = max_attempts
        self.lockout_time = lockout_time
        self.attempt_expiry = attempt_expiry

    def _get_user_key(self, username):
        return f"login_attempts:{username}"

    def _get_ip_key(self, ip_address):
        return f"login_attempts_ip:{ip_address}"

    def _get_lockout_key(self, username):
        return f"lockout:{username}"

    def _get_ip_lockout_key(self, ip_address):
        return f"lockout_ip:{ip_address}"

    def record_attempt(self, username, ip_address, success):
        if success:
            self.redis.delete(self._get_user_key(username))
            return

        self.redis.incr(self._get_user_key(username))
        self.redis.expire(self._get_user_key(username), self.attempt_expiry)

        self.redis.incr(self._get_ip_key(ip_address))
        self.redis.expire(self._get_ip_key(ip_address), self.attempt_expiry)

        attempts = int(self.redis.get(self._get_user_key(username)) or 0)
        if attempts >= self.max_attempts:
            self.redis.setex(
                self._get_lockout_key(username),
                self.lockout_time,
                "locked"
            )

        ip_attempts = int(self.redis.get(self._get_ip_key(ip_address)) or 0)
        if ip_attempts >= self.max_attempts * 2:
            self.redis.setex(
                self._get_ip_lockout_key(ip_address),
                self.lockout_time,
                "locked"
            )

    def is_locked_out(self, username, ip_address):
        user_lockout_key = self._get_lockout_key(username)
        user_lockout = self.redis.get(user_lockout_key)
        if user_lockout:
            ttl = self.redis.ttl(user_lockout_key)
            return True, ttl

        ip_lockout_key = self._get_ip_lockout_key(ip_address)
        ip_lockout = self.redis.get(ip_lockout_key)
        if ip_lockout:
            ttl = self.redis.ttl(ip_lockout_key)
            return True, ttl

        return False, None

def validate_password_complexity(password):
    errors = []

    # Check minimum length
    if len(password) < 10:
        errors.append(PasswordValidationError(
            code="password_too_short",
            message="Password must be at least 10 characters long"
        ))

    # Check for uppercase letters
    if not any(c.isupper() for c in password):
        errors.append(PasswordValidationError(
            code="password_no_uppercase",
            message="Password must contain at least one uppercase letter"
        ))

    # Check for lowercase letters
    if not any(c.islower() for c in password):
        errors.append(PasswordValidationError(
            code="password_no_lowercase",
            message="Password must contain at least one lowercase letter"
        ))

    # Check for digits
    if not any(c.isdigit() for c in password):
        errors.append(PasswordValidationError(
            code="password_no_digit",
            message="Password must contain at least one digit"
        ))

    # Check for special characters
    if not any(c in "!@#$%^&*()-_=+[]{}|;:,.<>?/~" for c in password):
        errors.append(PasswordValidationError(
            code="password_no_special",
            message="Password must contain at least one special character"
        ))

    # Check for common passwords
    common_passwords = ["password", "123456", "qwerty", "admin", "welcome", "password123"]
    if password.lower() in common_passwords:
        errors.append(PasswordValidationError(
            code="password_common",
            message="Password is too common"
        ))

    return errors

def check_account_lockout(request, username, _db=None):
    # Get client IP address
    ip_address = request.client.host

    # Check if account or IP is locked out
    tracker = get_login_attempt_tracker()
    locked_out, remaining = tracker.is_locked_out(username, ip_address)

    if locked_out:
        raise HTTPException(
            status_code=429,
            detail=f"Too many failed login attempts. Try again in {remaining} seconds."
        )

def get_login_attempt_tracker():
    # This is a mock function that returns a mock tracker
    mock_redis = MagicMock()
    return LoginAttemptTracker(
        redis_client=mock_redis,
        max_attempts=5,
        lockout_time=900,
        attempt_expiry=3600
    )


def test_password_complexity_validation():
    """Test password complexity validation."""
    # Test valid password
    valid_password = "StrongP@ssw0rd"
    errors = validate_password_complexity(valid_password)
    assert len(errors) == 0

    # Test password that's too short
    short_password = "Short1!"
    errors = validate_password_complexity(short_password)
    assert len(errors) > 0
    assert any(e.code == "password_too_short" for e in errors)

    # Test password without uppercase
    no_upper_password = "weakpassword123!"
    errors = validate_password_complexity(no_upper_password)
    assert len(errors) > 0
    assert any(e.code == "password_no_uppercase" for e in errors)

    # Test password without lowercase
    no_lower_password = "STRONGPASSWORD123!"
    errors = validate_password_complexity(no_lower_password)
    assert len(errors) > 0
    assert any(e.code == "password_no_lowercase" for e in errors)

    # Test password without digits
    no_digit_password = "StrongPassword!"
    errors = validate_password_complexity(no_digit_password)
    assert len(errors) > 0
    assert any(e.code == "password_no_digit" for e in errors)

    # Test password without special characters
    no_special_password = "StrongPassword123"
    errors = validate_password_complexity(no_special_password)
    assert len(errors) > 0
    assert any(e.code == "password_no_special" for e in errors)

    # Test common password
    common_password = "password123"
    errors = validate_password_complexity(common_password)
    assert len(errors) > 0


def test_check_account_lockout():
    """Test account lockout checking."""
    # Create a mock tracker
    class MockTracker:
        def __init__(self, locked_out=False, remaining=None):
            self.locked_out = locked_out
            self.remaining = remaining
            self.calls = []

        def is_locked_out(self, username, ip_address):
            self.calls.append((username, ip_address))
            return self.locked_out, self.remaining

    # Create a mock request
    class MockRequest:
        def __init__(self):
            self.client = type('obj', (object,), {'host': "127.0.0.1"})

    # Test when account is not locked out
    mock_request = MockRequest()
    mock_tracker = MockTracker(locked_out=False, remaining=None)

    # Create a mock get_login_attempt_tracker function
    def mock_get_tracker():
        return mock_tracker

    # Save the original function and replace it
    original_func = globals()['get_login_attempt_tracker']
    globals()['get_login_attempt_tracker'] = mock_get_tracker

    try:
        # Test when account is not locked out
        check_account_lockout(mock_request, "<EMAIL>", None)
        assert mock_tracker.calls == [("<EMAIL>", "127.0.0.1")]

        # Reset calls
        mock_tracker.calls = []

        # Test when account is locked out
        mock_tracker.locked_out = True
        mock_tracker.remaining = 300

        with pytest.raises(HTTPException) as excinfo:
            check_account_lockout(mock_request, "<EMAIL>", None)

        assert excinfo.value.status_code == 429
        assert "Too many failed login attempts" in excinfo.value.detail
        assert mock_tracker.calls == [("<EMAIL>", "127.0.0.1")]
    finally:
        # Restore the original function
        globals()['get_login_attempt_tracker'] = original_func


def test_login_attempt_tracker():
    """Test login attempt tracking."""
    # Create a mock Redis client
    class MockRedis:
        def __init__(self):
            self.data = {}
            self.calls = []

        def get(self, key):
            self.calls.append(('get', key))
            return self.data.get(key)

        def incr(self, key):
            self.calls.append(('incr', key))
            self.data[key] = str(int(self.data.get(key, '0')) + 1)
            return int(self.data[key])

        def expire(self, key, seconds):
            self.calls.append(('expire', key, seconds))
            return True

        def setex(self, key, seconds, value):
            self.calls.append(('setex', key, seconds, value))
            self.data[key] = value
            return True

        def ttl(self, key):
            self.calls.append(('ttl', key))
            return 250 if key in self.data else -2

        def delete(self, key):
            self.calls.append(('delete', key))
            if key in self.data:
                del self.data[key]
            return True

        def reset(self):
            self.calls = []

    # Create a mock Redis client
    mock_redis = MockRedis()

    # Create tracker
    tracker = LoginAttemptTracker(
        redis_client=mock_redis,
        max_attempts=3,
        lockout_time=300,
        attempt_expiry=3600
    )

    # Test recording failed attempts
    username = "<EMAIL>"
    ip_address = "127.0.0.1"

    # Record first failed attempt
    tracker.record_attempt(username, ip_address, False)

    # Check that Redis was called correctly
    assert ('incr', f"login_attempts:{username}") in mock_redis.calls
    assert ('expire', f"login_attempts:{username}", 3600) in mock_redis.calls
    assert ('incr', f"login_attempts_ip:{ip_address}") in mock_redis.calls
    assert ('expire', f"login_attempts_ip:{ip_address}", 3600) in mock_redis.calls

    # Reset mock
    mock_redis.reset()

    # Record second failed attempt
    mock_redis.data[f"login_attempts:{username}"] = "1"
    tracker.record_attempt(username, ip_address, False)

    # Check that Redis was called correctly
    assert ('incr', f"login_attempts:{username}") in mock_redis.calls
    assert ('expire', f"login_attempts:{username}", 3600) in mock_redis.calls

    # Reset mock
    mock_redis.reset()

    # Record third failed attempt (should trigger lockout)
    mock_redis.data[f"login_attempts:{username}"] = "2"
    tracker.record_attempt(username, ip_address, False)

    # Check that Redis was called correctly
    assert ('incr', f"login_attempts:{username}") in mock_redis.calls
    assert ('expire', f"login_attempts:{username}", 3600) in mock_redis.calls
    assert ('setex', f"lockout:{username}", 300, "locked") in mock_redis.calls

    # Reset mock
    mock_redis.reset()

    # Test checking lockout status
    mock_redis.data[f"lockout:{username}"] = "locked"

    locked_out, remaining = tracker.is_locked_out(username, ip_address)
    assert locked_out is True
    assert remaining == 250

    # Reset mock
    mock_redis.reset()

    # Test successful login (should reset failed attempts)
    tracker.record_attempt(username, ip_address, True)
    assert ('delete', f"login_attempts:{username}") in mock_redis.calls

    # Reset mock
    mock_redis.reset()

    # Test when account is not locked out
    mock_redis.data = {}

    locked_out, remaining = tracker.is_locked_out(username, ip_address)
    assert locked_out is False
    assert remaining is None
