"""
Test module for query caching.

This module tests the query caching functionality in the RentUp backend.
"""

import pytest
import time
from unittest.mock import patch, MagicMock
from sqlalchemy import create_engine, Column, String, Integer, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Query

from app.core.query_cache import (
    QueryCacheManager,
    CacheStrategy,
    InMemoryCache,
    MultiLevelCache,
    cached_query
)

# Create test database
Base = declarative_base()

class TestUser(Base):
    __tablename__ = "test_users"
    
    id = Column(String, primary_key=True)
    name = Column(String)
    
    items = relationship("TestItem", back_populates="owner")

class TestItem(Base):
    __tablename__ = "test_items"
    
    id = Column(String, primary_key=True)
    name = Column(String)
    owner_id = Column(String, ForeignKey("test_users.id"))
    
    owner = relationship("TestUser", back_populates="items")

@pytest.fixture
def db_session():
    """Create a test database session."""
    # Create in-memory SQLite database
    engine = create_engine("sqlite:///:memory:")
    
    # Create tables
    Base.metadata.create_all(engine)
    
    # Create session
    Session = sessionmaker(bind=engine)
    session = Session()
    
    # Add test data
    users = []
    for i in range(10):
        user = TestUser(id=f"user{i}", name=f"User {i}")
        users.append(user)
        session.add(user)
    
    for i in range(20):
        item = TestItem(id=f"item{i}", name=f"Item {i}", owner_id=users[i % 10].id)
        session.add(item)
    
    session.commit()
    
    yield session
    
    # Clean up
    session.close()

def test_in_memory_cache():
    """Test the in-memory cache."""
    # Create cache
    cache = InMemoryCache()
    
    # Test set and get
    cache.set("test_key", "test_value")
    assert cache.get("test_key") == "test_value"
    
    # Test TTL
    cache.set("ttl_key", "ttl_value", ttl=1)
    assert cache.get("ttl_key") == "ttl_value"
    time.sleep(1.1)
    assert cache.get("ttl_key") is None
    
    # Test delete
    cache.set("delete_key", "delete_value")
    assert cache.delete("delete_key")
    assert cache.get("delete_key") is None
    
    # Test delete pattern
    cache.set("pattern_key1", "pattern_value1")
    cache.set("pattern_key2", "pattern_value2")
    cache.set("other_key", "other_value")
    assert cache.delete_pattern("pattern_*") == 2
    assert cache.get("pattern_key1") is None
    assert cache.get("pattern_key2") is None
    assert cache.get("other_key") == "other_value"

def test_multi_level_cache():
    """Test the multi-level cache."""
    # Create cache
    cache = MultiLevelCache(l1_ttl=10, l2_ttl=20)
    
    # Test set and get (L1 hit)
    cache.set("test_key", "test_value")
    assert cache.get("test_key") == "test_value"
    assert cache.stats["l1_hits"] == 1
    assert cache.stats["l1_misses"] == 0
    
    # Test L1 miss, L2 hit (simulate by clearing L1)
    cache.l1_cache.cache.clear()
    
    # Mock L2 cache if available
    if cache.l2_available:
        # Real test with Redis
        assert cache.get("test_key") == "test_value"
        assert cache.stats["l1_hits"] == 1
        assert cache.stats["l1_misses"] == 1
        assert cache.stats["l2_hits"] == 1
    else:
        # Mock test without Redis
        with patch.object(cache, 'l2_available', True):
            with patch.object(cache, 'l2_cache') as mock_l2:
                mock_l2.get.return_value = "test_value"
                assert cache.get("test_key") == "test_value"
                assert cache.stats["l1_hits"] == 1
                assert cache.stats["l1_misses"] == 1
                mock_l2.get.assert_called_once_with("test_key")

def test_query_cache_manager():
    """Test the query cache manager."""
    # Create cache manager
    manager = QueryCacheManager(strategy=CacheStrategy.SIMPLE, ttl=3600)
    
    # Test cache key generation
    key1 = manager.generate_cache_key("SELECT * FROM test")
    key2 = manager.generate_cache_key("SELECT * FROM test", {"param": "value"})
    assert key1 != key2
    
    # Test cache operations
    manager.set_in_cache("test_key", "test_value")
    assert manager.get_from_cache("test_key") == "test_value"
    assert manager.stats["hits"] == 1
    
    # Test invalidation
    assert manager.invalidate("test_key")
    assert manager.get_from_cache("test_key") is None
    assert manager.stats["misses"] == 1
    
    # Test table dependencies
    manager.register_dependency("dep_key", {"test_table"})
    manager.set_in_cache("dep_key", "dep_value")
    assert manager.invalidate_by_table("test_table") == 1
    assert manager.get_from_cache("dep_key") is None

def test_cached_query_decorator(db_session):
    """Test the cached_query decorator."""
    # Create a function to decorate
    @cached_query(ttl=10, strategy=CacheStrategy.SIMPLE)
    def get_users(query):
        time.sleep(0.1)  # Simulate database query
        return query.all()
    
    # Create query
    query = db_session.query(TestUser)
    
    # First call (cache miss)
    start_time = time.time()
    result1 = get_users(query)
    first_call_time = time.time() - start_time
    
    # Second call (cache hit)
    start_time = time.time()
    result2 = get_users(query)
    second_call_time = time.time() - start_time
    
    # Check results
    assert len(result1) == len(result2)
    assert second_call_time < first_call_time  # Cache hit should be faster

def test_adaptive_caching(db_session):
    """Test adaptive caching strategy."""
    # Create cache manager with adaptive strategy
    manager = QueryCacheManager(strategy=CacheStrategy.ADAPTIVE, ttl=10)
    
    # Create a simple query and a complex query
    simple_query = db_session.query(TestUser)
    complex_query = db_session.query(TestUser).join(TestItem).filter(TestItem.name.like("%1%"))
    
    # Generate cache keys
    simple_key = manager.generate_cache_key(simple_query)
    complex_key = manager.generate_cache_key(complex_query)
    
    # Execute queries with adaptive caching
    @cached_query(ttl=10, strategy=CacheStrategy.ADAPTIVE)
    def execute_query(query):
        return query.all()
    
    # Execute both queries
    simple_result = execute_query(simple_query)
    complex_result = execute_query(complex_query)
    
    # Check that both queries returned results
    assert len(simple_result) > 0
    assert len(complex_result) > 0
