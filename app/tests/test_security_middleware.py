"""
Tests for the security middleware.
"""
import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import Test<PERSON>lient
from app.core.security_middleware import add_security_middleware


def test_default_security_headers():
    """Test that default security headers are added to responses."""
    # Create a test FastAPI app
    app = FastAPI()
    
    # Add a test endpoint
    @app.get("/test")
    def test_endpoint():
        return {"message": "test"}
    
    # Add security middleware with default settings
    add_security_middleware(app)
    
    # Create a test client
    client = TestClient(app)
    
    # Make a request to the test endpoint
    response = client.get("/test")
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that default security headers are present
    assert response.headers["X-Content-Type-Options"] == "nosniff"
    assert response.headers["X-Frame-Options"] == "DENY"
    assert response.headers["X-XSS-Protection"] == "1; mode=block"
    assert response.headers["Strict-Transport-Security"] == "max-age=31536000; includeSubDomains"
    assert response.headers["Referrer-Policy"] == "strict-origin-when-cross-origin"
    assert "Permissions-Policy" in response.headers
    assert "Content-Security-Policy" in response.headers


def test_custom_security_headers():
    """Test that custom security headers are added to responses."""
    # Create a test FastAPI app
    app = FastAPI()
    
    # Add a test endpoint
    @app.get("/test")
    def test_endpoint():
        return {"message": "test"}
    
    # Add security middleware with custom settings
    add_security_middleware(
        app,
        content_security_policy="default-src 'self'; script-src 'self' https://example.com;",
        custom_headers={
            "X-Custom-Header": "custom-value",
            "Feature-Policy": "camera 'none'"
        }
    )
    
    # Create a test client
    client = TestClient(app)
    
    # Make a request to the test endpoint
    response = client.get("/test")
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that custom security headers are present
    assert response.headers["Content-Security-Policy"] == "default-src 'self'; script-src 'self' https://example.com;"
    assert response.headers["X-Custom-Header"] == "custom-value"
    assert response.headers["Feature-Policy"] == "camera 'none'"


def test_disable_default_headers():
    """Test that default security headers can be disabled."""
    # Create a test FastAPI app
    app = FastAPI()
    
    # Add a test endpoint
    @app.get("/test")
    def test_endpoint():
        return {"message": "test"}
    
    # Add security middleware with default headers disabled
    add_security_middleware(
        app,
        include_default_headers=False,
        custom_headers={
            "X-Custom-Header": "custom-value"
        }
    )
    
    # Create a test client
    client = TestClient(app)
    
    # Make a request to the test endpoint
    response = client.get("/test")
    
    # Check that the response is successful
    assert response.status_code == 200
    
    # Check that default security headers are not present
    assert "X-Content-Type-Options" not in response.headers
    assert "X-Frame-Options" not in response.headers
    assert "X-XSS-Protection" not in response.headers
    assert "Strict-Transport-Security" not in response.headers
    assert "Referrer-Policy" not in response.headers
    assert "Permissions-Policy" not in response.headers
    assert "Content-Security-Policy" not in response.headers
    
    # Check that custom headers are present
    assert response.headers["X-Custom-Header"] == "custom-value"
