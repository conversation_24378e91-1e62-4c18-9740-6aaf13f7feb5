"""
Tests for the CSRF middleware.
"""
import time
import pytest
from fastapi import FastAPI, Request
from fastapi.testclient import Test<PERSON>lient
from fastapi.responses import JSONResponse
from backend.app.core.csrf_middleware import add_csrf_middleware


def test_csrf_token_generation():
    """Test that CSRF tokens are generated and set in cookies."""
    # Create a test FastAPI app
    app = FastAPI()

    # Add a test endpoint
    @app.get("/test")
    def test_endpoint():
        return {"message": "test"}

    # Add CSRF middleware
    add_csrf_middleware(
        app,
        secret_key="test_secret_key",
        cookie_secure=False  # For testing
    )

    # Create a test client
    client = TestClient(app)

    # Make a request to the test endpoint
    response = client.get("/test")

    # Check that the response is successful
    assert response.status_code == 200

    # Check that a CSRF token cookie is set
    assert "csrf_token" in response.cookies

    # Check that the token has the correct format
    csrf_token = response.cookies["csrf_token"]
    assert ":" in csrf_token
    token_value, expiration = csrf_token.split(":", 1)
    assert len(token_value) == 64  # 32 bytes hex = 64 chars
    assert expiration.isdigit()


def test_csrf_protection_for_unsafe_methods():
    """Test that CSRF protection is applied for unsafe methods."""
    # Create a test FastAPI app
    app = FastAPI()

    # Add test endpoints
    @app.get("/test")
    def get_endpoint():
        return {"message": "get"}

    @app.post("/test")
    def post_endpoint():
        return {"message": "post"}

    @app.put("/test")
    def put_endpoint():
        return {"message": "put"}

    @app.delete("/test")
    def delete_endpoint():
        return {"message": "delete"}

    # Add CSRF middleware
    add_csrf_middleware(
        app,
        secret_key="test_secret_key",
        cookie_secure=False  # For testing
    )

    # Create a test client
    client = TestClient(app)

    # Make a GET request to get a CSRF token
    response = client.get("/test")
    assert response.status_code == 200
    csrf_token = response.cookies["csrf_token"]

    # Test POST request without CSRF token
    response = client.post("/test")
    assert response.status_code == 403
    assert response.json()["detail"] == "CSRF token missing or invalid"

    # Test POST request with CSRF token in header
    response = client.post(
        "/test",
        headers={"X-CSRF-Token": csrf_token},
        cookies={"csrf_token": csrf_token}
    )
    assert response.status_code == 200
    assert response.json()["message"] == "post"

    # Test PUT request with CSRF token in header
    response = client.put(
        "/test",
        headers={"X-CSRF-Token": csrf_token},
        cookies={"csrf_token": csrf_token}
    )
    assert response.status_code == 200
    assert response.json()["message"] == "put"

    # Test DELETE request with CSRF token in header
    response = client.delete(
        "/test",
        headers={"X-CSRF-Token": csrf_token},
        cookies={"csrf_token": csrf_token}
    )
    assert response.status_code == 200
    assert response.json()["message"] == "delete"


def test_csrf_excluded_paths():
    """Test that excluded paths are not protected by CSRF."""
    # Create a test FastAPI app
    app = FastAPI()

    # Add test endpoints
    @app.post("/test")
    def test_endpoint():
        return {"message": "test"}

    @app.post("/api/v1/auth/login")
    def login_endpoint():
        return {"message": "login"}

    # Add CSRF middleware with excluded paths
    add_csrf_middleware(
        app,
        secret_key="test_secret_key",
        cookie_secure=False,  # For testing
        exclude_paths=["/api/v1/auth/"]
    )

    # Create a test client
    client = TestClient(app)

    # Test POST request to protected endpoint without CSRF token
    response = client.post("/test")
    assert response.status_code == 403
    assert response.json()["detail"] == "CSRF token missing or invalid"

    # Test POST request to excluded endpoint without CSRF token
    response = client.post("/api/v1/auth/login")
    assert response.status_code == 200
    assert response.json()["message"] == "login"


def test_get_csrf_token_endpoint():
    """Test the get_csrf_token endpoint."""
    # Create a test FastAPI app
    app = FastAPI()

    # Create a mock CSRF middleware
    class MockCSRFMiddleware:
        def __init__(self):
            self.cookie_name = "csrf_token"
            self.cookie_max_age = 86400
            self.cookie_httponly = True
            self.cookie_secure = False
            self.cookie_samesite = "Lax"

        def _generate_csrf_token(self):
            return "test-csrf-token:1234567890"

    # Create a mock endpoint that uses the mock middleware
    @app.get("/api/v1/csrf-token")
    async def mock_get_csrf_token(request: Request):
        # Create a response with the token
        response = JSONResponse(content={"csrf_token": "test-csrf-token:1234567890"})

        # Set the token in a cookie
        response.set_cookie(
            key="csrf_token",
            value="test-csrf-token:1234567890",
            max_age=86400,
            httponly=True,
            secure=False,
            samesite="Lax"
        )

        return response

    # Add CSRF middleware
    app.middleware = [MockCSRFMiddleware()]

    # Create a test client
    client = TestClient(app)

    # Make a request to get a CSRF token
    response = client.get("/api/v1/csrf-token")

    # Check that the response is successful
    assert response.status_code == 200

    # Check that a CSRF token is returned in the response body
    assert "csrf_token" in response.json()

    # Check that a CSRF token cookie is set
    assert "csrf_token" in response.cookies

    # Check that the token in the response body matches the token in the cookie
    assert response.json()["csrf_token"] == response.cookies["csrf_token"]
