#!/usr/bin/env python3
"""
Simple test to isolate import issues.
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Testing step by step...")

try:
    print("1. Basic imports...")
    import json
    import re
    print("✓ Basic imports OK")
    
    print("2. Pydantic...")
    from pydantic import BaseModel
    print("✓ Pydantic OK")
    
    print("3. AI models...")
    from app.ai.models import FlaggedContent
    print("✓ AI models OK")
    
    print("4. Content checkers only...")
    from app.ai.content_moderation.modules.content_checkers import ContentCheckersService
    print("✓ Content checkers OK")
    
    print("5. Test content checkers...")
    service = ContentCheckersService()
    score, flags = service.check_prohibited_content("test")
    print(f"✓ Content checkers work: score={score}, flags={len(flags)}")
    
    print("6. Test quality assessment...")
    caps_score = service.assess_text_quality("THIS IS ALL CAPS")
    rep_score = service.assess_text_quality("word word word word word")
    print(f"✓ Quality assessment: caps={caps_score}, rep={rep_score}")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
