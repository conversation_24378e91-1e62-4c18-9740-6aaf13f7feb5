{"verification_timestamp": "2025-05-26T21:16:40.416308", "duration_seconds": 0.005227, "summary": {"total_checks": 22, "passed_checks": 19, "failed_checks": 3, "success_rate": 86.36363636363636}, "detailed_results": {"Security module: app/core/auth_config.py": {"success": true, "details": ""}, "Security module: app/core/rate_limiting.py": {"success": true, "details": ""}, "Security module: app/middleware/security.py": {"success": true, "details": ""}, "Security module: app/core/validation.py": {"success": true, "details": ""}, "Security module: app/core/audit.py": {"success": true, "details": ""}, "Optimization module: app/core/query_optimization.py": {"success": true, "details": ""}, "Optimization module: app/core/join_optimization.py": {"success": true, "details": ""}, "Optimization module: app/core/query_cache.py": {"success": true, "details": ""}, "Optimization module: app/core/db_config.py": {"success": true, "details": ""}, "Optimization module: app/core/database_connection.py": {"success": true, "details": ""}, "Optimization module: app/services/db_optimization_service.py": {"success": true, "details": ""}, "Optimization modules import": {"success": false, "details": "No module named 'app.core.query_cache'"}, "Docker file: Dockerfile.prod": {"success": true, "details": ""}, "Docker file: docker-compose.prod.yml": {"success": true, "details": ""}, "Docker file: requirements-prod.txt": {"success": true, "details": ""}, "Production startup script": {"success": true, "details": ""}, "API connectivity": {"success": false, "details": "Cannot connect to API server"}, "Security headers verification": {"success": false, "details": "HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/v1/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7f5f9fbdcaa0>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, "Test file: tests/test_optimization_modules.py": {"success": true, "details": ""}, "Test file: tests/test_database_optimizations.py": {"success": true, "details": ""}, "ADK migration file: adk_migration_output/agents_analysis.json": {"success": true, "details": ""}, "ADK migration file: adk_migration_output/migration_plan.json": {"success": true, "details": ""}}, "production_ready": false}