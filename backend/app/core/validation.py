"""
Enhanced Input Validation for Production
"""

from pydantic import BaseModel, Field, validator, EmailStr
from typing import Optional, List, Dict, Any
import re
import html
from datetime import datetime

class BaseValidationModel(BaseModel):
    """Base model with common validation rules."""
    
    class Config:
        # Pydantic v2 configuration
        str_strip_whitespace = True
        validate_assignment = True
        extra = "forbid"  # Reject extra fields
        max_anystr_length = 10000  # Prevent DoS attacks
    
    @validator('*', pre=True)
    def sanitize_strings(cls, v):
        """Sanitize string inputs to prevent XSS."""
        if isinstance(v, str):
            # HTML escape
            v = html.escape(v)
            # Remove null bytes
            v = v.replace(' ', '')
            # Limit length
            if len(v) > 10000:
                raise ValueError("Input too long")
        return v

class UserRegistrationModel(BaseValidationModel):
    """Enhanced user registration validation."""
    
    email: EmailStr
    password: str = Field(..., min_length=12, max_length=128)
    full_name: str = Field(..., min_length=2, max_length=100)
    phone: Optional[str] = Field(None, regex=r'^\+?1?\d{9,15}$')
    
    @validator('password')
    def validate_password_strength(cls, v):
        """Validate password complexity."""
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain number')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain special character')
        return v
    
    @validator('full_name')
    def validate_name(cls, v):
        """Validate name format."""
        if not re.match(r'^[a-zA-Z\s\-'\.]+$', v):
            raise ValueError('Name contains invalid characters')
        return v

class ItemCreateModel(BaseValidationModel):
    """Enhanced item creation validation."""
    
    title: str = Field(..., min_length=5, max_length=200)
    description: str = Field(..., min_length=20, max_length=5000)
    price_per_day: float = Field(..., gt=0, le=10000)
    category_id: int = Field(..., gt=0)
    location: str = Field(..., min_length=5, max_length=200)
    
    @validator('title', 'description')
    def validate_content(cls, v):
        """Validate content for inappropriate material."""
        # Basic profanity filter (extend as needed)
        prohibited_words = ['spam', 'scam', 'fake']
        v_lower = v.lower()
        for word in prohibited_words:
            if word in v_lower:
                raise ValueError(f'Content contains prohibited word: {word}')
        return v

# Request size validation
class RequestSizeValidator:
    """Validate request size to prevent DoS attacks."""
    
    MAX_REQUEST_SIZE = 10 * 1024 * 1024  # 10MB
    
    @staticmethod
    def validate_size(content_length: Optional[int]) -> bool:
        """Validate request content length."""
        if content_length is None:
            return True
        return content_length <= RequestSizeValidator.MAX_REQUEST_SIZE
