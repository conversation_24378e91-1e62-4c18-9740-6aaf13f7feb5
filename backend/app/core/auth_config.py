"""
Enhanced Authentication Configuration for Production (2025)
"""

from datetime import timed<PERSON><PERSON>
from typing import Optional
import secrets

class AuthConfig:
    """Production-ready authentication configuration."""
    
    # JWT Configuration
    ACCESS_TOKEN_EXPIRE_MINUTES = 15  # Short-lived access tokens
    REFRESH_TOKEN_EXPIRE_DAYS = 7
    JWT_ALGORITHM = "HS256"
    
    # Security Settings
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION_MINUTES = 30
    PASSWORD_MIN_LENGTH = 12
    REQUIRE_SPECIAL_CHARS = True
    REQUIRE_NUMBERS = True
    REQUIRE_UPPERCASE = True
    
    # Session Security
    SESSION_TIMEOUT_MINUTES = 30
    REQUIRE_HTTPS = True
    SECURE_COOKIES = True
    HTTPONLY_COOKIES = True
    SAMESITE_COOKIES = "strict"
    
    # Rate Limiting
    LOGIN_RATE_LIMIT = "5/minute"
    API_RATE_LIMIT = "1000/hour"
    
    # MFA Settings
    MFA_ENABLED = True
    MFA_REQUIRED_FOR_ADMIN = True
    
    @staticmethod
    def generate_secret_key() -> str:
        """Generate a cryptographically secure secret key."""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def get_password_requirements() -> dict:
        """Get password complexity requirements."""
        return {
            "min_length": AuthConfig.PASSWORD_MIN_LENGTH,
            "require_special": AuthConfig.REQUIRE_SPECIAL_CHARS,
            "require_numbers": AuthConfig.REQUIRE_NUMBERS,
            "require_uppercase": AuthConfig.REQUIRE_UPPERCASE
        }

# Global auth configuration instance
auth_config = AuthConfig()
