"""
Rate Limiting Implementation for Production
"""

import time
import redis
from functools import wraps
from fastapi import HTTPException, Request
from typing import Dict, Optional

class RateLimiter:
    """Redis-based rate limiter for API endpoints."""
    
    def __init__(self, redis_client: Optional[redis.Redis] = None):
        self.redis = redis_client or redis.Redis(
            host="localhost", 
            port=6379, 
            decode_responses=True
        )
    
    def limit(self, key: str, limit: int, window: int) -> bool:
        """
        Check if request is within rate limit.
        
        Args:
            key: Unique identifier for the rate limit
            limit: Maximum number of requests
            window: Time window in seconds
            
        Returns:
            True if within limit, False otherwise
        """
        try:
            current = self.redis.get(key)
            if current is None:
                self.redis.setex(key, window, 1)
                return True
            
            if int(current) >= limit:
                return False
            
            self.redis.incr(key)
            return True
            
        except Exception as e:
            # Fail open - allow request if Redis is down
            return True
    
    def get_remaining(self, key: str, limit: int) -> int:
        """Get remaining requests in current window."""
        try:
            current = self.redis.get(key)
            if current is None:
                return limit
            return max(0, limit - int(current))
        except:
            return limit

# Rate limiting decorators
def rate_limit(requests: int, window: int = 60):
    """Decorator for rate limiting endpoints."""
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            limiter = RateLimiter()
            client_ip = request.client.host
            key = f"rate_limit:{client_ip}:{func.__name__}"
            
            if not limiter.limit(key, requests, window):
                raise HTTPException(
                    status_code=429,
                    detail="Rate limit exceeded. Please try again later.",
                    headers={"Retry-After": str(window)}
                )
            
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator

# Global rate limiter instance
rate_limiter = RateLimiter()
