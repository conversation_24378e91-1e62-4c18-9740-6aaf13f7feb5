"""
Audit Logging System for Production
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import Request
from functools import wraps

class AuditLogger:
    """Centralized audit logging system."""
    
    def __init__(self):
        self.logger = logging.getLogger("audit")
        self.logger.setLevel(logging.INFO)
        
        # Create audit log handler
        handler = logging.FileHandler("audit.log")
        formatter = logging.Formatter(
            '%(asctime)s - AUDIT - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def log_event(self, 
                  event_type: str,
                  user_id: Optional[str] = None,
                  resource: Optional[str] = None,
                  action: Optional[str] = None,
                  ip_address: Optional[str] = None,
                  user_agent: Optional[str] = None,
                  additional_data: Optional[Dict[str, Any]] = None):
        """Log an audit event."""
        
        audit_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "event_type": event_type,
            "user_id": user_id,
            "resource": resource,
            "action": action,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "additional_data": additional_data or {}
        }
        
        # Remove sensitive data
        audit_data = self._sanitize_data(audit_data)
        
        self.logger.info(json.dumps(audit_data))
    
    def _sanitize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Remove sensitive information from audit logs."""
        sensitive_keys = ['password', 'token', 'secret', 'key']
        
        def sanitize_dict(d):
            if isinstance(d, dict):
                return {
                    k: "[REDACTED]" if any(sk in k.lower() for sk in sensitive_keys)
                    else sanitize_dict(v)
                    for k, v in d.items()
                }
            elif isinstance(d, list):
                return [sanitize_dict(item) for item in d]
            return d
        
        return sanitize_dict(data)

# Global audit logger instance
audit_logger = AuditLogger()

def audit_log(event_type: str, resource: str = None, action: str = None):
    """Decorator for automatic audit logging."""
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            # Extract request information
            user_id = getattr(request.state, 'user_id', None)
            ip_address = request.client.host
            user_agent = request.headers.get('user-agent')
            
            try:
                result = await func(request, *args, **kwargs)
                
                # Log successful operation
                audit_logger.log_event(
                    event_type=event_type,
                    user_id=user_id,
                    resource=resource,
                    action=action,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    additional_data={"status": "success"}
                )
                
                return result
                
            except Exception as e:
                # Log failed operation
                audit_logger.log_event(
                    event_type=event_type,
                    user_id=user_id,
                    resource=resource,
                    action=action,
                    ip_address=ip_address,
                    user_agent=user_agent,
                    additional_data={
                        "status": "failed",
                        "error": str(e)
                    }
                )
                raise
        
        return wrapper
    return decorator
