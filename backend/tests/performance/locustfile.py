#!/usr/bin/env python3
"""
Locust load testing script for RentUp backend.

This script:
1. Defines user behaviors for different user types
2. Simulates realistic user interactions
3. Measures response times and error rates
4. Provides real-time performance metrics

Usage:
    locust -f locustfile.py --host=http://localhost:8000
"""

import os
import json
import random
import time
import logging
from typing import Dict, List, Any, Optional, Tuple

from locust import HttpUser, task, between, tag, events
from locust.exception import StopUser

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Test data
SAMPLE_USERS = [
    {"email": "<EMAIL>", "password": "password123"},
    {"email": "<EMAIL>", "password": "password123"},
    {"email": "<EMAIL>", "password": "password123"},
    {"email": "<EMAIL>", "password": "password123"},
    {"email": "<EMAIL>", "password": "password123"},
]

SAMPLE_ITEMS = [
    {"name": "Luxury Apartment", "description": "Beautiful luxury apartment in downtown", "price": 2500},
    {"name": "Cozy Studio", "description": "Cozy studio apartment near university", "price": 1200},
    {"name": "Family House", "description": "Spacious family house with garden", "price": 3500},
    {"name": "Beach Condo", "description": "Beachfront condo with amazing views", "price": 4000},
    {"name": "Mountain Cabin", "description": "Rustic cabin in the mountains", "price": 1800},
]

# Performance thresholds
THRESHOLDS = {
    "login": 500,  # ms
    "get_items": 1000,  # ms
    "get_item": 500,  # ms
    "create_item": 1000,  # ms
    "search_items": 1500,  # ms
    "get_user_profile": 500,  # ms
    "update_user_profile": 1000,  # ms
}

# Custom stats
custom_stats = {
    "database_queries": [],
    "cache_hits": [],
    "cache_misses": [],
}

@events.test_start.add_listener
def on_test_start(environment, **kwargs):
    """
    Event handler called when the test is starting.
    """
    logger.info("Starting RentUp load test")

@events.test_stop.add_listener
def on_test_stop(environment, **kwargs):
    """
    Event handler called when the test is stopping.
    """
    logger.info("Stopping RentUp load test")
    
    # Calculate and log custom stats
    if custom_stats["database_queries"]:
        avg_queries = sum(custom_stats["database_queries"]) / len(custom_stats["database_queries"])
        logger.info(f"Average database queries per request: {avg_queries:.2f}")
    
    if custom_stats["cache_hits"] and custom_stats["cache_misses"]:
        total_cache = len(custom_stats["cache_hits"]) + len(custom_stats["cache_misses"])
        hit_ratio = len(custom_stats["cache_hits"]) / total_cache if total_cache > 0 else 0
        logger.info(f"Cache hit ratio: {hit_ratio:.2%}")

class RentUpUser(HttpUser):
    """
    Base user class for RentUp load testing.
    """
    abstract = True
    wait_time = between(1, 5)
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.token = None
        self.user_id = None
        self.items = []
    
    def on_start(self):
        """
        Called when a user starts.
        """
        self.login()
    
    def login(self):
        """
        Log in to the RentUp API.
        """
        user = random.choice(SAMPLE_USERS)
        with self.client.post(
            "/api/v1/login/access-token",
            data={"username": user["email"], "password": user["password"]},
            name="Login",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                self.token = data.get("access_token")
                if not self.token:
                    response.failure("Login failed: No access token in response")
                    raise StopUser()
                
                # Get user profile
                self.get_user_profile()
            else:
                response.failure(f"Login failed: {response.status_code}")
                raise StopUser()
    
    def get_user_profile(self):
        """
        Get the user profile.
        """
        headers = {"Authorization": f"Bearer {self.token}"}
        with self.client.get(
            "/api/v1/users/me",
            headers=headers,
            name="Get User Profile",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                self.user_id = data.get("id")
                if not self.user_id:
                    response.failure("Get user profile failed: No user ID in response")
            else:
                response.failure(f"Get user profile failed: {response.status_code}")
    
    def get_items(self, params=None):
        """
        Get a list of items.
        """
        headers = {"Authorization": f"Bearer {self.token}"}
        with self.client.get(
            "/api/v1/items/",
            headers=headers,
            params=params,
            name="Get Items",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                self.items = data
                
                # Extract custom stats from headers
                if "X-Database-Queries" in response.headers:
                    custom_stats["database_queries"].append(int(response.headers["X-Database-Queries"]))
                
                if "X-Cache-Hit" in response.headers:
                    if response.headers["X-Cache-Hit"] == "1":
                        custom_stats["cache_hits"].append(1)
                    else:
                        custom_stats["cache_misses"].append(1)
                
                # Check performance threshold
                if response.elapsed.total_seconds() * 1000 > THRESHOLDS["get_items"]:
                    response.failure(f"Get items too slow: {response.elapsed.total_seconds() * 1000:.2f}ms")
            else:
                response.failure(f"Get items failed: {response.status_code}")
    
    def get_item(self, item_id):
        """
        Get a specific item.
        """
        headers = {"Authorization": f"Bearer {self.token}"}
        with self.client.get(
            f"/api/v1/items/{item_id}",
            headers=headers,
            name="Get Item",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                # Extract custom stats from headers
                if "X-Database-Queries" in response.headers:
                    custom_stats["database_queries"].append(int(response.headers["X-Database-Queries"]))
                
                if "X-Cache-Hit" in response.headers:
                    if response.headers["X-Cache-Hit"] == "1":
                        custom_stats["cache_hits"].append(1)
                    else:
                        custom_stats["cache_misses"].append(1)
                
                # Check performance threshold
                if response.elapsed.total_seconds() * 1000 > THRESHOLDS["get_item"]:
                    response.failure(f"Get item too slow: {response.elapsed.total_seconds() * 1000:.2f}ms")
            else:
                response.failure(f"Get item failed: {response.status_code}")
    
    def create_item(self, item_data):
        """
        Create a new item.
        """
        headers = {"Authorization": f"Bearer {self.token}"}
        with self.client.post(
            "/api/v1/items/",
            headers=headers,
            json=item_data,
            name="Create Item",
            catch_response=True
        ) as response:
            if response.status_code == 201:
                # Check performance threshold
                if response.elapsed.total_seconds() * 1000 > THRESHOLDS["create_item"]:
                    response.failure(f"Create item too slow: {response.elapsed.total_seconds() * 1000:.2f}ms")
                return response.json()
            else:
                response.failure(f"Create item failed: {response.status_code}")
                return None
    
    def search_items(self, query):
        """
        Search for items.
        """
        headers = {"Authorization": f"Bearer {self.token}"}
        with self.client.get(
            "/api/v1/items/search",
            headers=headers,
            params={"q": query},
            name="Search Items",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                # Extract custom stats from headers
                if "X-Database-Queries" in response.headers:
                    custom_stats["database_queries"].append(int(response.headers["X-Database-Queries"]))
                
                # Check performance threshold
                if response.elapsed.total_seconds() * 1000 > THRESHOLDS["search_items"]:
                    response.failure(f"Search items too slow: {response.elapsed.total_seconds() * 1000:.2f}ms")
            else:
                response.failure(f"Search items failed: {response.status_code}")
    
    def update_user_profile(self, profile_data):
        """
        Update the user profile.
        """
        headers = {"Authorization": f"Bearer {self.token}"}
        with self.client.put(
            f"/api/v1/users/{self.user_id}",
            headers=headers,
            json=profile_data,
            name="Update User Profile",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                # Check performance threshold
                if response.elapsed.total_seconds() * 1000 > THRESHOLDS["update_user_profile"]:
                    response.failure(f"Update user profile too slow: {response.elapsed.total_seconds() * 1000:.2f}ms")
            else:
                response.failure(f"Update user profile failed: {response.status_code}")

class RenterUser(RentUpUser):
    """
    User class simulating a renter.
    """
    wait_time = between(3, 8)
    
    @task(10)
    def browse_items(self):
        """
        Browse items with different filters.
        """
        # Get all items
        self.get_items()
        
        # Get items with price filter
        min_price = random.randint(500, 2000)
        max_price = random.randint(2001, 5000)
        self.get_items(params={"min_price": min_price, "max_price": max_price})
        
        # Get items with sorting
        sort_options = ["price_asc", "price_desc", "newest"]
        self.get_items(params={"sort": random.choice(sort_options)})
    
    @task(5)
    def view_item_details(self):
        """
        View details of a specific item.
        """
        # Get all items first if we don't have any
        if not self.items:
            self.get_items()
        
        # View a random item
        if self.items:
            item = random.choice(self.items)
            self.get_item(item["id"])
    
    @task(3)
    def search_for_items(self):
        """
        Search for items using different keywords.
        """
        keywords = ["luxury", "cozy", "spacious", "modern", "downtown", "beach", "mountain", "garden", "view"]
        query = random.choice(keywords)
        self.search_items(query)
    
    @task(1)
    def update_profile(self):
        """
        Update user profile.
        """
        profile_data = {
            "name": f"Test User {random.randint(1, 1000)}",
            "phone": f"+1{random.randint(1000000000, 9999999999)}",
            "bio": f"I am a test user {random.randint(1, 1000)}"
        }
        self.update_user_profile(profile_data)

class OwnerUser(RentUpUser):
    """
    User class simulating a property owner.
    """
    wait_time = between(5, 15)
    
    @task(5)
    def manage_listings(self):
        """
        Manage property listings.
        """
        # Get own listings
        self.get_items(params={"owner_id": self.user_id})
    
    @task(2)
    def create_listing(self):
        """
        Create a new property listing.
        """
        item_template = random.choice(SAMPLE_ITEMS)
        item_data = {
            "name": f"{item_template['name']} {random.randint(1, 1000)}",
            "description": f"{item_template['description']} {random.randint(1, 1000)}",
            "price": item_template["price"] + random.randint(-500, 500),
            "owner_id": self.user_id
        }
        self.create_item(item_data)
    
    @task(1)
    def update_listing(self):
        """
        Update an existing property listing.
        """
        # Get own listings first
        self.get_items(params={"owner_id": self.user_id})
        
        # Update a random listing
        if self.items:
            item = random.choice(self.items)
            item_data = {
                "name": f"Updated {item['name']}",
                "description": f"Updated {item['description']}",
                "price": item["price"] + random.randint(-200, 200)
            }
            headers = {"Authorization": f"Bearer {self.token}"}
            with self.client.put(
                f"/api/v1/items/{item['id']}",
                headers=headers,
                json=item_data,
                name="Update Item",
                catch_response=True
            ) as response:
                if response.status_code != 200:
                    response.failure(f"Update item failed: {response.status_code}")
    
    @task(1)
    def update_profile(self):
        """
        Update user profile.
        """
        profile_data = {
            "name": f"Owner {random.randint(1, 1000)}",
            "phone": f"+1{random.randint(1000000000, 9999999999)}",
            "bio": f"I am a property owner {random.randint(1, 1000)}"
        }
        self.update_user_profile(profile_data)

class AdminUser(RentUpUser):
    """
    User class simulating an admin user.
    """
    wait_time = between(10, 20)
    
    @task(10)
    def view_all_items(self):
        """
        View all items in the system.
        """
        self.get_items(params={"limit": 100})
    
    @task(5)
    def view_all_users(self):
        """
        View all users in the system.
        """
        headers = {"Authorization": f"Bearer {self.token}"}
        with self.client.get(
            "/api/v1/users/",
            headers=headers,
            params={"limit": 100},
            name="Get All Users",
            catch_response=True
        ) as response:
            if response.status_code != 200:
                response.failure(f"Get all users failed: {response.status_code}")
    
    @task(2)
    def view_system_stats(self):
        """
        View system statistics.
        """
        headers = {"Authorization": f"Bearer {self.token}"}
        with self.client.get(
            "/api/v1/admin/stats",
            headers=headers,
            name="Get System Stats",
            catch_response=True
        ) as response:
            if response.status_code != 200:
                response.failure(f"Get system stats failed: {response.status_code}")
