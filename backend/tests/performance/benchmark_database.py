#!/usr/bin/env python3
"""
Database benchmarking script for RentUp backend.

This script:
1. Measures database query performance
2. Tests different query optimization techniques
3. Compares performance before and after optimizations
4. Generates performance reports
"""

import os
import sys
import argparse
import logging
import time
import json
import statistics
import datetime
import matplotlib.pyplot as plt
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False

from sqlalchemy import text, create_engine
from sqlalchemy.orm import Session

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Benchmark RentUp database performance')
    parser.add_argument('--iterations', type=int, default=10, help='Number of iterations for each test')
    parser.add_argument('--output', default='database_benchmark_report.json', help='Output file for benchmark report')
    parser.add_argument('--plot', action='store_true', help='Generate performance plots')
    parser.add_argument('--plot-dir', default='benchmark_plots', help='Directory for performance plots')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def get_connection_string():
    """Get database connection string."""
    if NEW_MODULES:
        # Use db_config
        return f"postgresql://{db_config.POSTGRES_USER}:{db_config.POSTGRES_PASSWORD}@{db_config.POSTGRES_SERVER}:{db_config.POSTGRES_PORT}/{db_config.POSTGRES_DB}"
    else:
        # Use settings
        return settings.DATABASE_URL

def create_test_data(session: Session, num_users: int = 100, num_items: int = 1000, num_rentals: int = 500):
    """Create test data for benchmarking."""
    logger.info(f"Creating test data: {num_users} users, {num_items} items, {num_rentals} rentals")
    
    try:
        # Check if test data already exists
        result = session.execute(text("SELECT COUNT(*) FROM users WHERE email LIKE 'benchmark%'"))
        count = result.scalar()
        
        if count > 0:
            logger.info(f"Test data already exists: {count} benchmark users found")
            return
        
        # Create users
        for i in range(num_users):
            session.execute(
                text("""
                INSERT INTO users (email, hashed_password, full_name, is_active, is_superuser, created_at)
                VALUES (:email, :password, :name, :is_active, :is_superuser, :created_at)
                """),
                {
                    "email": f"benchmark_user_{i}@example.com",
                    "password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
                    "name": f"Benchmark User {i}",
                    "is_active": True,
                    "is_superuser": False,
                    "created_at": datetime.datetime.now() - datetime.timedelta(days=i % 30)
                }
            )
        
        # Get user IDs
        result = session.execute(text("SELECT id FROM users WHERE email LIKE 'benchmark%'"))
        user_ids = [row[0] for row in result]
        
        # Create items
        for i in range(num_items):
            owner_id = user_ids[i % len(user_ids)]
            price = 1000 + (i % 10) * 500
            session.execute(
                text("""
                INSERT INTO items (name, description, price, owner_id, created_at)
                VALUES (:name, :description, :price, :owner_id, :created_at)
                """),
                {
                    "name": f"Benchmark Item {i}",
                    "description": f"This is a benchmark item {i} for performance testing",
                    "price": price,
                    "owner_id": owner_id,
                    "created_at": datetime.datetime.now() - datetime.timedelta(days=i % 60)
                }
            )
        
        # Get item IDs
        result = session.execute(text("SELECT id FROM items WHERE name LIKE 'Benchmark Item%'"))
        item_ids = [row[0] for row in result]
        
        # Create rentals
        for i in range(num_rentals):
            renter_id = user_ids[i % len(user_ids)]
            item_id = item_ids[i % len(item_ids)]
            start_date = datetime.datetime.now() + datetime.timedelta(days=i)
            end_date = start_date + datetime.timedelta(days=7)
            session.execute(
                text("""
                INSERT INTO rentals (item_id, renter_id, start_date, end_date, status, created_at)
                VALUES (:item_id, :renter_id, :start_date, :end_date, :status, :created_at)
                """),
                {
                    "item_id": item_id,
                    "renter_id": renter_id,
                    "start_date": start_date,
                    "end_date": end_date,
                    "status": "pending",
                    "created_at": datetime.datetime.now() - datetime.timedelta(hours=i)
                }
            )
        
        session.commit()
        logger.info("Test data created successfully")
    except Exception as e:
        session.rollback()
        logger.error(f"Error creating test data: {str(e)}")
        raise

def run_query(session: Session, query: str, params: Dict = None) -> Tuple[float, int]:
    """Run a query and measure execution time."""
    start_time = time.time()
    result = session.execute(text(query), params or {})
    execution_time = time.time() - start_time
    
    # Count rows
    rows = result.fetchall()
    row_count = len(rows)
    
    return execution_time, row_count

def benchmark_query(session: Session, name: str, query: str, params: Dict = None, iterations: int = 10) -> Dict[str, Any]:
    """Benchmark a query by running it multiple times."""
    logger.info(f"Benchmarking query: {name}")
    
    execution_times = []
    row_count = 0
    
    for i in range(iterations):
        # Clear cache before each run (if applicable)
        session.execute(text("DISCARD ALL"))
        
        # Run query
        execution_time, rows = run_query(session, query, params)
        execution_times.append(execution_time)
        row_count = rows
        
        logger.debug(f"Iteration {i+1}/{iterations}: {execution_time:.6f}s, {row_count} rows")
    
    # Calculate statistics
    avg_time = statistics.mean(execution_times)
    min_time = min(execution_times)
    max_time = max(execution_times)
    median_time = statistics.median(execution_times)
    stddev_time = statistics.stdev(execution_times) if len(execution_times) > 1 else 0
    
    logger.info(f"Query: {name}, Avg: {avg_time:.6f}s, Min: {min_time:.6f}s, Max: {max_time:.6f}s, Rows: {row_count}")
    
    return {
        "name": name,
        "iterations": iterations,
        "row_count": row_count,
        "avg_time": avg_time,
        "min_time": min_time,
        "max_time": max_time,
        "median_time": median_time,
        "stddev_time": stddev_time,
        "execution_times": execution_times
    }

def run_benchmarks(session: Session, iterations: int) -> Dict[str, Any]:
    """Run all benchmark queries."""
    logger.info(f"Running database benchmarks with {iterations} iterations")
    
    benchmarks = {}
    
    # Simple queries
    benchmarks["simple_select"] = benchmark_query(
        session,
        "Simple SELECT",
        "SELECT * FROM users LIMIT 100",
        iterations=iterations
    )
    
    benchmarks["simple_count"] = benchmark_query(
        session,
        "Simple COUNT",
        "SELECT COUNT(*) FROM items",
        iterations=iterations
    )
    
    benchmarks["simple_filter"] = benchmark_query(
        session,
        "Simple Filter",
        "SELECT * FROM items WHERE price > :price",
        params={"price": 2000},
        iterations=iterations
    )
    
    # Join queries
    benchmarks["simple_join"] = benchmark_query(
        session,
        "Simple JOIN",
        """
        SELECT i.*, u.full_name as owner_name
        FROM items i
        JOIN users u ON i.owner_id = u.id
        LIMIT 100
        """,
        iterations=iterations
    )
    
    benchmarks["complex_join"] = benchmark_query(
        session,
        "Complex JOIN",
        """
        SELECT i.*, u.full_name as owner_name, r.start_date, r.end_date, ru.full_name as renter_name
        FROM items i
        JOIN users u ON i.owner_id = u.id
        LEFT JOIN rentals r ON i.id = r.item_id
        LEFT JOIN users ru ON r.renter_id = ru.id
        WHERE i.price > :price
        ORDER BY i.created_at DESC
        LIMIT 100
        """,
        params={"price": 1500},
        iterations=iterations
    )
    
    # Aggregation queries
    benchmarks["simple_aggregation"] = benchmark_query(
        session,
        "Simple Aggregation",
        """
        SELECT AVG(price) as avg_price, MIN(price) as min_price, MAX(price) as max_price
        FROM items
        """,
        iterations=iterations
    )
    
    benchmarks["group_by_aggregation"] = benchmark_query(
        session,
        "GROUP BY Aggregation",
        """
        SELECT DATE_TRUNC('day', created_at) as day, COUNT(*) as count
        FROM items
        GROUP BY day
        ORDER BY day DESC
        """,
        iterations=iterations
    )
    
    # Complex queries
    benchmarks["complex_query"] = benchmark_query(
        session,
        "Complex Query",
        """
        SELECT u.id, u.full_name, u.email,
               COUNT(DISTINCT i.id) as item_count,
               COUNT(DISTINCT r.id) as rental_count,
               AVG(i.price) as avg_item_price
        FROM users u
        LEFT JOIN items i ON u.id = i.owner_id
        LEFT JOIN rentals r ON u.id = r.renter_id
        GROUP BY u.id, u.full_name, u.email
        HAVING COUNT(DISTINCT i.id) > 0 OR COUNT(DISTINCT r.id) > 0
        ORDER BY item_count DESC, rental_count DESC
        LIMIT 100
        """,
        iterations=iterations
    )
    
    benchmarks["search_query"] = benchmark_query(
        session,
        "Search Query",
        """
        SELECT i.*, u.full_name as owner_name
        FROM items i
        JOIN users u ON i.owner_id = u.id
        WHERE i.name ILIKE :search OR i.description ILIKE :search
        ORDER BY i.created_at DESC
        LIMIT 100
        """,
        params={"search": "%benchmark%"},
        iterations=iterations
    )
    
    # Optimized queries (with indexes)
    benchmarks["optimized_join"] = benchmark_query(
        session,
        "Optimized JOIN",
        """
        SELECT i.*, u.full_name as owner_name
        FROM items i
        JOIN users u ON i.owner_id = u.id
        WHERE i.price BETWEEN :min_price AND :max_price
        ORDER BY i.price ASC
        LIMIT 100
        """,
        params={"min_price": 1000, "max_price": 3000},
        iterations=iterations
    )
    
    return benchmarks

def generate_report(benchmarks: Dict[str, Any], output_file: str) -> None:
    """Generate a benchmark report."""
    logger.info(f"Generating benchmark report: {output_file}")
    
    # Create report data
    report = {
        "timestamp": datetime.datetime.now().isoformat(),
        "benchmarks": benchmarks,
        "summary": {
            "total_queries": len(benchmarks),
            "fastest_query": min(benchmarks.items(), key=lambda x: x[1]["avg_time"])[0],
            "slowest_query": max(benchmarks.items(), key=lambda x: x[1]["avg_time"])[0],
            "total_execution_time": sum(b["avg_time"] for b in benchmarks.values())
        }
    }
    
    # Write report to file
    with open(output_file, "w") as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Benchmark report generated: {output_file}")

def generate_plots(benchmarks: Dict[str, Any], plot_dir: str) -> None:
    """Generate performance plots."""
    logger.info(f"Generating performance plots in {plot_dir}")
    
    # Create plot directory if it doesn't exist
    os.makedirs(plot_dir, exist_ok=True)
    
    # Average execution time plot
    plt.figure(figsize=(12, 8))
    query_names = list(benchmarks.keys())
    avg_times = [b["avg_time"] for b in benchmarks.values()]
    
    plt.barh(query_names, avg_times)
    plt.xlabel("Average Execution Time (seconds)")
    plt.ylabel("Query")
    plt.title("Average Query Execution Time")
    plt.tight_layout()
    plt.savefig(os.path.join(plot_dir, "avg_execution_time.png"))
    
    # Box plot of execution times
    plt.figure(figsize=(12, 8))
    execution_times = [b["execution_times"] for b in benchmarks.values()]
    
    plt.boxplot(execution_times, labels=query_names, vert=False)
    plt.xlabel("Execution Time (seconds)")
    plt.ylabel("Query")
    plt.title("Query Execution Time Distribution")
    plt.tight_layout()
    plt.savefig(os.path.join(plot_dir, "execution_time_distribution.png"))
    
    logger.info(f"Performance plots generated in {plot_dir}")

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Get database connection string
    connection_string = get_connection_string()
    
    # Create engine
    db_engine = create_engine(connection_string)
    
    try:
        # Create session
        with Session(db_engine) as session:
            # Create test data
            create_test_data(session)
            
            # Run benchmarks
            benchmarks = run_benchmarks(session, args.iterations)
            
            # Generate report
            generate_report(benchmarks, args.output)
            
            # Generate plots
            if args.plot:
                generate_plots(benchmarks, args.plot_dir)
        
        logger.info("Database benchmarking completed successfully")
        return 0
    except Exception as e:
        logger.error(f"Database benchmarking failed: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
