#!/usr/bin/env python3
"""
API benchmarking script for RentUp backend.

This script:
1. Measures API endpoint performance
2. Tests different API optimization techniques
3. Compares performance before and after optimizations
4. Generates performance reports
"""

import os
import sys
import argparse
import logging
import time
import json
import statistics
import datetime
import matplotlib.pyplot as plt
import requests
import concurrent.futures
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Benchmark RentUp API performance')
    parser.add_argument('--base-url', default='http://localhost:8000', help='Base URL of the API')
    parser.add_argument('--iterations', type=int, default=10, help='Number of iterations for each test')
    parser.add_argument('--concurrency', type=int, default=5, help='Number of concurrent requests')
    parser.add_argument('--output', default='api_benchmark_report.json', help='Output file for benchmark report')
    parser.add_argument('--plot', action='store_true', help='Generate performance plots')
    parser.add_argument('--plot-dir', default='benchmark_plots', help='Directory for performance plots')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def login(base_url: str, username: str = "<EMAIL>", password: str = "password123") -> Optional[str]:
    """Log in to the API and get an access token."""
    try:
        response = requests.post(
            f"{base_url}/api/v1/login/access-token",
            data={"username": username, "password": password}
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get("access_token")
        else:
            logger.error(f"Login failed: {response.status_code} {response.text}")
            return None
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        return None

def make_request(url: str, method: str = "GET", headers: Dict = None, json_data: Dict = None, params: Dict = None) -> Tuple[float, int, Dict]:
    """Make an HTTP request and measure response time."""
    start_time = time.time()
    
    try:
        if method == "GET":
            response = requests.get(url, headers=headers, params=params)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=json_data)
        elif method == "PUT":
            response = requests.put(url, headers=headers, json=json_data)
        elif method == "DELETE":
            response = requests.delete(url, headers=headers)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        execution_time = time.time() - start_time
        
        # Extract custom headers
        custom_headers = {}
        for header in ["X-Process-Time", "X-Database-Queries", "X-Cache-Hit"]:
            if header in response.headers:
                custom_headers[header] = response.headers[header]
        
        return execution_time, response.status_code, custom_headers
    except Exception as e:
        execution_time = time.time() - start_time
        logger.error(f"Request error: {str(e)}")
        return execution_time, 0, {}

def benchmark_endpoint(base_url: str, endpoint: str, method: str = "GET", headers: Dict = None, 
                      json_data: Dict = None, params: Dict = None, iterations: int = 10, 
                      concurrency: int = 1) -> Dict[str, Any]:
    """Benchmark an API endpoint by making multiple requests."""
    url = f"{base_url}{endpoint}"
    logger.info(f"Benchmarking endpoint: {method} {url}")
    
    execution_times = []
    status_codes = []
    custom_headers_list = []
    
    # Sequential requests
    if concurrency == 1:
        for i in range(iterations):
            execution_time, status_code, custom_headers = make_request(url, method, headers, json_data, params)
            execution_times.append(execution_time)
            status_codes.append(status_code)
            custom_headers_list.append(custom_headers)
            
            logger.debug(f"Iteration {i+1}/{iterations}: {execution_time:.6f}s, Status: {status_code}")
    # Concurrent requests
    else:
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = []
            for _ in range(iterations):
                future = executor.submit(make_request, url, method, headers, json_data, params)
                futures.append(future)
            
            for i, future in enumerate(concurrent.futures.as_completed(futures)):
                execution_time, status_code, custom_headers = future.result()
                execution_times.append(execution_time)
                status_codes.append(status_code)
                custom_headers_list.append(custom_headers)
                
                logger.debug(f"Iteration {i+1}/{iterations}: {execution_time:.6f}s, Status: {status_code}")
    
    # Calculate statistics
    success_times = [t for t, s in zip(execution_times, status_codes) if 200 <= s < 300]
    
    if success_times:
        avg_time = statistics.mean(success_times)
        min_time = min(success_times)
        max_time = max(success_times)
        median_time = statistics.median(success_times)
        stddev_time = statistics.stdev(success_times) if len(success_times) > 1 else 0
    else:
        avg_time = min_time = max_time = median_time = stddev_time = 0
    
    success_rate = len(success_times) / iterations if iterations > 0 else 0
    
    # Process custom headers
    avg_process_time = 0
    avg_db_queries = 0
    cache_hit_rate = 0
    
    process_times = []
    db_queries = []
    cache_hits = 0
    
    for headers in custom_headers_list:
        if "X-Process-Time" in headers:
            try:
                process_times.append(float(headers["X-Process-Time"]))
            except (ValueError, TypeError):
                pass
        
        if "X-Database-Queries" in headers:
            try:
                db_queries.append(int(headers["X-Database-Queries"]))
            except (ValueError, TypeError):
                pass
        
        if "X-Cache-Hit" in headers and headers["X-Cache-Hit"] == "1":
            cache_hits += 1
    
    if process_times:
        avg_process_time = statistics.mean(process_times)
    
    if db_queries:
        avg_db_queries = statistics.mean(db_queries)
    
    if custom_headers_list:
        cache_hit_rate = cache_hits / len(custom_headers_list)
    
    logger.info(f"Endpoint: {method} {endpoint}, Avg: {avg_time:.6f}s, Success Rate: {success_rate:.2%}")
    
    return {
        "endpoint": endpoint,
        "method": method,
        "iterations": iterations,
        "concurrency": concurrency,
        "avg_time": avg_time,
        "min_time": min_time,
        "max_time": max_time,
        "median_time": median_time,
        "stddev_time": stddev_time,
        "success_rate": success_rate,
        "execution_times": execution_times,
        "status_codes": status_codes,
        "avg_process_time": avg_process_time,
        "avg_db_queries": avg_db_queries,
        "cache_hit_rate": cache_hit_rate
    }

def run_benchmarks(base_url: str, token: str, iterations: int, concurrency: int) -> Dict[str, Any]:
    """Run all benchmark tests."""
    logger.info(f"Running API benchmarks with {iterations} iterations and concurrency {concurrency}")
    
    benchmarks = {}
    
    # Set up headers
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    # Public endpoints
    benchmarks["health_check"] = benchmark_endpoint(
        base_url,
        "/health",
        method="GET",
        iterations=iterations,
        concurrency=concurrency
    )
    
    benchmarks["docs"] = benchmark_endpoint(
        base_url,
        "/docs",
        method="GET",
        iterations=iterations,
        concurrency=concurrency
    )
    
    # Authentication endpoints
    benchmarks["login"] = benchmark_endpoint(
        base_url,
        "/api/v1/login/access-token",
        method="POST",
        json_data={"username": "<EMAIL>", "password": "password123"},
        iterations=iterations,
        concurrency=concurrency
    )
    
    # User endpoints
    benchmarks["get_users"] = benchmark_endpoint(
        base_url,
        "/api/v1/users/",
        method="GET",
        headers=headers,
        iterations=iterations,
        concurrency=concurrency
    )
    
    benchmarks["get_user_me"] = benchmark_endpoint(
        base_url,
        "/api/v1/users/me",
        method="GET",
        headers=headers,
        iterations=iterations,
        concurrency=concurrency
    )
    
    # Item endpoints
    benchmarks["get_items"] = benchmark_endpoint(
        base_url,
        "/api/v1/items/",
        method="GET",
        headers=headers,
        iterations=iterations,
        concurrency=concurrency
    )
    
    benchmarks["get_items_with_filter"] = benchmark_endpoint(
        base_url,
        "/api/v1/items/",
        method="GET",
        headers=headers,
        params={"min_price": 1000, "max_price": 3000},
        iterations=iterations,
        concurrency=concurrency
    )
    
    benchmarks["search_items"] = benchmark_endpoint(
        base_url,
        "/api/v1/items/search",
        method="GET",
        headers=headers,
        params={"q": "apartment"},
        iterations=iterations,
        concurrency=concurrency
    )
    
    # Create a test item
    create_item_response = benchmark_endpoint(
        base_url,
        "/api/v1/items/",
        method="POST",
        headers=headers,
        json_data={
            "name": f"Test Item {time.time()}",
            "description": "This is a test item for benchmarking",
            "price": 1500
        },
        iterations=1,
        concurrency=1
    )
    
    # Get a specific item
    if create_item_response["success_rate"] > 0:
        # Get the item ID from the response
        item_id = None
        try:
            response = requests.post(
                f"{base_url}/api/v1/items/",
                headers=headers,
                json={
                    "name": f"Test Item {time.time()}",
                    "description": "This is a test item for benchmarking",
                    "price": 1500
                }
            )
            if response.status_code == 201:
                item_data = response.json()
                item_id = item_data.get("id")
        except Exception:
            pass
        
        if item_id:
            benchmarks["get_item"] = benchmark_endpoint(
                base_url,
                f"/api/v1/items/{item_id}",
                method="GET",
                headers=headers,
                iterations=iterations,
                concurrency=concurrency
            )
    
    return benchmarks

def generate_report(benchmarks: Dict[str, Any], output_file: str) -> None:
    """Generate a benchmark report."""
    logger.info(f"Generating benchmark report: {output_file}")
    
    # Create report data
    report = {
        "timestamp": datetime.datetime.now().isoformat(),
        "benchmarks": benchmarks,
        "summary": {
            "total_endpoints": len(benchmarks),
            "fastest_endpoint": min(benchmarks.items(), key=lambda x: x[1]["avg_time"])[0],
            "slowest_endpoint": max(benchmarks.items(), key=lambda x: x[1]["avg_time"])[0],
            "total_execution_time": sum(b["avg_time"] for b in benchmarks.values()),
            "avg_success_rate": statistics.mean(b["success_rate"] for b in benchmarks.values())
        }
    }
    
    # Write report to file
    with open(output_file, "w") as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Benchmark report generated: {output_file}")

def generate_plots(benchmarks: Dict[str, Any], plot_dir: str) -> None:
    """Generate performance plots."""
    logger.info(f"Generating performance plots in {plot_dir}")
    
    # Create plot directory if it doesn't exist
    os.makedirs(plot_dir, exist_ok=True)
    
    # Average execution time plot
    plt.figure(figsize=(12, 8))
    endpoint_names = [f"{b['method']} {b['endpoint']}" for b in benchmarks.values()]
    avg_times = [b["avg_time"] for b in benchmarks.values()]
    
    plt.barh(endpoint_names, avg_times)
    plt.xlabel("Average Response Time (seconds)")
    plt.ylabel("Endpoint")
    plt.title("Average API Response Time")
    plt.tight_layout()
    plt.savefig(os.path.join(plot_dir, "avg_response_time.png"))
    
    # Success rate plot
    plt.figure(figsize=(12, 8))
    success_rates = [b["success_rate"] * 100 for b in benchmarks.values()]
    
    plt.barh(endpoint_names, success_rates)
    plt.xlabel("Success Rate (%)")
    plt.ylabel("Endpoint")
    plt.title("API Endpoint Success Rate")
    plt.tight_layout()
    plt.savefig(os.path.join(plot_dir, "success_rate.png"))
    
    # Database queries plot
    plt.figure(figsize=(12, 8))
    db_queries = [b["avg_db_queries"] for b in benchmarks.values()]
    
    plt.barh(endpoint_names, db_queries)
    plt.xlabel("Average Database Queries")
    plt.ylabel("Endpoint")
    plt.title("Average Database Queries per Endpoint")
    plt.tight_layout()
    plt.savefig(os.path.join(plot_dir, "avg_db_queries.png"))
    
    # Cache hit rate plot
    plt.figure(figsize=(12, 8))
    cache_hit_rates = [b["cache_hit_rate"] * 100 for b in benchmarks.values()]
    
    plt.barh(endpoint_names, cache_hit_rates)
    plt.xlabel("Cache Hit Rate (%)")
    plt.ylabel("Endpoint")
    plt.title("API Endpoint Cache Hit Rate")
    plt.tight_layout()
    plt.savefig(os.path.join(plot_dir, "cache_hit_rate.png"))
    
    logger.info(f"Performance plots generated in {plot_dir}")

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Login to get token
        token = login(args.base_url)
        
        if not token:
            logger.warning("Could not log in, some tests may fail")
        
        # Run benchmarks
        benchmarks = run_benchmarks(args.base_url, token, args.iterations, args.concurrency)
        
        # Generate report
        generate_report(benchmarks, args.output)
        
        # Generate plots
        if args.plot:
            generate_plots(benchmarks, args.plot_dir)
        
        logger.info("API benchmarking completed successfully")
        return 0
    except Exception as e:
        logger.error(f"API benchmarking failed: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
