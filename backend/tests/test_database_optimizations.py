"""
Tests for database optimizations in RentUp.

This module contains tests for:
- Database connection pooling
- JOIN operation optimization
- Query caching
- Batch operations
"""

import sys
import time
from pathlib import Path
import pytest

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text, create_engine, Column, String, Float, ForeignKey
from sqlalchemy.orm import relationship, sessionmaker, declarative_base

# Import modules to test
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope, get_db
    from app.core.join_optimization import optimize_join_query, JoinPattern, JoinType
    from app.core.query_cache import query_cache, CacheStrategy, cached_query
    from app.core.query_optimization import optimize_query, paginate_query
    from app.services.db_optimization_service import optimized_query, get_optimization_service
    NEW_MODULES = True
    print("✅ Using new optimization modules")
except ImportError as e:
    # Fall back to old modules
    print(f"⚠️ Falling back to old modules: {e}")
    from app.core.config import settings
    from app.core.database import engine, session_scope, get_db
    NEW_MODULES = False

# Base class for models
Base = declarative_base()

# Test models
class TestUser(Base):
    __tablename__ = "test_users"

    id = Column(String, primary_key=True)
    email = Column(String, nullable=False, unique=True)
    full_name = Column(String, nullable=False)

    # Relationships
    items = relationship("TestItem", back_populates="owner")

class TestItem(Base):
    __tablename__ = "test_items"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(String)
    owner_id = Column(String, ForeignKey("test_users.id"), nullable=False)
    value = Column(Float, nullable=False)

    # Relationships
    owner = relationship("TestUser", back_populates="items")

class TestRental(Base):
    __tablename__ = "test_rentals"

    id = Column(String, primary_key=True)
    item_id = Column(String, ForeignKey("test_items.id"), nullable=False)
    renter_id = Column(String, ForeignKey("test_users.id"), nullable=False)
    start_date = Column(String, nullable=False)
    end_date = Column(String, nullable=False)
    total_price = Column(Float, nullable=False)

@pytest.fixture(scope="module")
def test_db():
    """Create a test database."""
    # Create an in-memory SQLite database for testing
    test_engine = create_engine("sqlite:///:memory:")

    # Create tables
    Base.metadata.create_all(test_engine)

    # Create session
    TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)

    # Create test data
    with TestSessionLocal() as session:
        # Create users
        users = []
        for i in range(5):
            user = TestUser(
                id=f"user{i}",
                email=f"user{i}@example.com",
                full_name=f"Test User {i}"
            )
            users.append(user)
        session.add_all(users)

        # Create items
        items = []
        for i in range(10):
            item = TestItem(
                id=f"item{i}",
                name=f"Test Item {i}",
                description=f"Description for item {i}",
                owner_id=f"user{i % 5}",
                value=100.0 + i * 10.0
            )
            items.append(item)
        session.add_all(items)

        # Create rentals
        rentals = []
        for i in range(15):
            rental = TestRental(
                id=f"rental{i}",
                item_id=f"item{i % 10}",
                renter_id=f"user{(i + 2) % 5}",
                start_date=f"2023-01-{i+1:02d}",
                end_date=f"2023-01-{i+8:02d}",
                total_price=50.0 + i * 5.0
            )
            rentals.append(rental)
        session.add_all(rentals)

        session.commit()

    return test_engine

def test_database_connection(test_db):
    """Test database connection."""
    # Create session
    TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_db)

    with TestSessionLocal() as session:
        # Test simple query
        result = session.execute(text("SELECT 1")).scalar()
        assert result == 1

        # Test query on test_users
        count = session.query(TestUser).count()
        assert count == 5

@pytest.mark.skipif(not NEW_MODULES, reason="New modules not available")
def test_join_optimization(test_db):
    """Test JOIN operation optimization."""
    # Create session
    TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_db)

    with TestSessionLocal() as session:
        # Create a query with JOIN
        query = session.query(TestItem).join(TestUser)

        # Test without optimization
        start_time = time.time()
        unoptimized_result = query.all()
        unoptimized_time = time.time() - start_time

        # Test with optimization
        start_time = time.time()
        optimized_query = optimize_join_query(query, session)
        optimized_result = optimized_query.all() if hasattr(optimized_query, "all") else list(optimized_query)
        optimized_time = time.time() - start_time

        # Check results
        assert len(optimized_result) == len(unoptimized_result)

        # Log performance
        print(f"Unoptimized JOIN time: {unoptimized_time:.6f}s")
        print(f"Optimized JOIN time: {optimized_time:.6f}s")
        print(f"Improvement: {(unoptimized_time - optimized_time) / unoptimized_time * 100:.2f}%")

@pytest.mark.skipif(not NEW_MODULES, reason="New modules not available")
def test_query_caching(test_db):
    """Test query caching."""
    # Create session
    TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_db)

    with TestSessionLocal() as session:
        # Clear cache
        query_cache.invalidate("test_users")

        # Define a cached function
        @cached_query(ttl=60, strategy=CacheStrategy.SMART)
        def get_users(session):
            return session.query(TestUser).all()

        # Test without cache (first call)
        start_time = time.time()
        users_1 = get_users(session)
        uncached_time = time.time() - start_time

        # Test with cache (second call)
        start_time = time.time()
        users_2 = get_users(session)
        cached_time = time.time() - start_time

        # Check results
        assert len(users_1) == len(users_2)

        # Log performance
        print(f"Uncached query time: {uncached_time:.6f}s")
        print(f"Cached query time: {cached_time:.6f}s")
        print(f"Improvement: {(uncached_time - cached_time) / uncached_time * 100:.2f}%")

        # Check cache stats
        stats = query_cache.get_stats()
        assert stats.hits >= 0
        assert stats.misses >= 0

def test_query_optimization(test_db):
    """Test query optimization."""
    # Create session
    TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_db)

    with TestSessionLocal() as session:
        # Create a complex query
        query = session.query(TestItem).filter(TestItem.value > 120.0)

        # Test without optimization
        start_time = time.time()
        unoptimized_result = query.all()
        unoptimized_time = time.time() - start_time

        # Test with optimization
        start_time = time.time()
        if NEW_MODULES:
            optimized_query = optimize_query(query, session)
            optimized_result = optimized_query.all()
        else:
            optimized_result = query.all()
        optimized_time = time.time() - start_time

        # Check results
        assert len(optimized_result) == len(unoptimized_result)

        # Log performance
        print(f"Unoptimized query time: {unoptimized_time:.6f}s")
        print(f"Optimized query time: {optimized_time:.6f}s")
        print(f"Improvement: {(unoptimized_time - optimized_time) / unoptimized_time * 100:.2f}%")

def test_pagination(test_db):
    """Test query pagination."""
    # Create session
    TestSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_db)

    with TestSessionLocal() as session:
        # Test pagination
        query = session.query(TestItem)

        if NEW_MODULES:
            paginated_query, total_count = paginate_query(query, page=1, page_size=5)
            paginated_result = paginated_query.all()

            # Check results
            assert len(paginated_result) == 5
            assert total_count == 10

            # Test second page
            paginated_query, total_count = paginate_query(query, page=2, page_size=5)
            paginated_result = paginated_query.all()

            # Check results
            assert len(paginated_result) == 5
            assert total_count == 10
        else:
            # Manual pagination
            total_count = query.count()
            paginated_result = query.offset(0).limit(5).all()

            # Check results
            assert len(paginated_result) == 5
            assert total_count == 10

if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
