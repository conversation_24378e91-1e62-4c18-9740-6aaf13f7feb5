"""
Tests for content moderation core module.
"""

import pytest
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from sqlalchemy.orm import Session

from app.ai.content_moderation.modules.core import ContentModerationAgent
from app.ai.models import ContentModerationRequest


class TestContentModerationAgent:
    """Test cases for ContentModerationAgent."""

    @pytest.fixture
    def agent(self):
        """Create a ContentModerationAgent instance for testing."""
        return ContentModerationAgent()

    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def text_request(self):
        """Create a text moderation request for testing."""
        return {
            "content_type": "text",
            "text_content": "This is a test message",
            "user_id": "user123",
            "context": "item_listing"
        }

    @pytest.fixture
    def image_request(self):
        """Create an image moderation request for testing."""
        return {
            "content_type": "image",
            "image_urls": ["https://example.com/image.jpg"],
            "user_id": "user123",
            "context": "item_listing"
        }

    @pytest.fixture
    def combined_request(self):
        """Create a combined moderation request for testing."""
        return {
            "content_type": "combined",
            "text_content": "This is a test message",
            "image_urls": ["https://example.com/image.jpg"],
            "user_id": "user123",
            "context": "item_listing"
        }

    @pytest.mark.asyncio
    async def test_process_text_content(self, agent, mock_db, text_request):
        """Test processing text content."""
        with patch.object(agent.text_service, 'moderate_text', new_callable=AsyncMock) as mock_moderate:
            mock_moderate.return_value = {
                "result": {
                    "overall_score": 0.2,
                    "category_scores": {"toxicity": 0.1, "quality": 0.8},
                    "flagged": False,
                    "severity": "low",
                    "recommended_action": "allow"
                },
                "flagged_content": []
            }

            result = await agent.process(text_request, mock_db)

            assert result is not None
            assert "moderation_result" in result
            assert "metadata" in result
            assert result["moderation_result"]["flagged"] is False
            mock_moderate.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_image_content(self, agent, mock_db, image_request):
        """Test processing image content."""
        with patch.object(agent.image_service, 'moderate_images', new_callable=AsyncMock) as mock_moderate:
            mock_moderate.return_value = {
                "result": {
                    "overall_score": 0.1,
                    "category_scores": {"explicit_content": 0.05, "image_quality": 0.9},
                    "flagged": False,
                    "severity": "low",
                    "recommended_action": "allow"
                },
                "flagged_content": []
            }

            result = await agent.process(image_request, mock_db)

            assert result is not None
            assert "moderation_result" in result
            assert result["moderation_result"]["flagged"] is False
            mock_moderate.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_combined_content(self, agent, mock_db, combined_request):
        """Test processing combined text and image content."""
        with patch.object(agent.text_service, 'moderate_text', new_callable=AsyncMock) as mock_text, \
             patch.object(agent.image_service, 'moderate_images', new_callable=AsyncMock) as mock_image:
            
            mock_text.return_value = {
                "result": {
                    "overall_score": 0.3,
                    "category_scores": {"toxicity": 0.2, "quality": 0.8},
                    "flagged": False,
                    "severity": "low",
                    "recommended_action": "allow"
                },
                "flagged_content": []
            }
            
            mock_image.return_value = {
                "result": {
                    "overall_score": 0.1,
                    "category_scores": {"explicit_content": 0.05, "image_quality": 0.9},
                    "flagged": False,
                    "severity": "low",
                    "recommended_action": "allow"
                },
                "flagged_content": []
            }

            result = await agent.process(combined_request, mock_db)

            assert result is not None
            assert "moderation_result" in result
            mock_text.assert_called_once()
            mock_image.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_invalid_content_type(self, agent, mock_db):
        """Test processing with invalid content type."""
        invalid_request = {
            "content_type": "invalid",
            "user_id": "user123",
            "context": "item_listing"
        }

        result = await agent.process(invalid_request, mock_db)

        assert result is not None
        assert "error" in result
        assert result["success"] is False

    @pytest.mark.asyncio
    async def test_process_missing_content(self, agent, mock_db):
        """Test processing with missing content."""
        missing_content_request = {
            "content_type": "text",
            "user_id": "user123",
            "context": "item_listing"
        }

        result = await agent.process(missing_content_request, mock_db)

        assert result is not None
        assert "error" in result
        assert result["success"] is False

    @pytest.mark.asyncio
    async def test_process_with_cache_hit(self, agent, mock_db, text_request):
        """Test processing with cache hit."""
        cached_result = {
            "moderation_result": {"flagged": False},
            "metadata": {"cached": True}
        }

        with patch.object(agent.utils, 'generate_cache_key') as mock_cache_key, \
             patch('app.core.cache.RedisCache') as mock_redis:
            
            mock_cache_key.return_value = "test_cache_key"
            mock_redis.return_value.get.return_value = cached_result

            result = await agent.process(text_request, mock_db)

            assert result == cached_result

    def test_moderate_combined_weights(self, agent):
        """Test combined moderation weight calculation."""
        weights = agent.content_type_weights["combined"]
        
        assert "text" in weights
        assert "image" in weights
        assert weights["text"] + weights["image"] == 1.0

    def test_thresholds_configuration(self, agent):
        """Test threshold configuration."""
        assert agent.high_severity_threshold > agent.medium_severity_threshold
        assert agent.medium_severity_threshold > agent.low_severity_threshold
        assert 0 <= agent.low_severity_threshold <= 1
        assert 0 <= agent.medium_severity_threshold <= 1
        assert 0 <= agent.high_severity_threshold <= 1
