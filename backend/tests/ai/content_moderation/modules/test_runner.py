"""
Test runner for content moderation modules.

This script runs all tests for the content moderation modules and generates a report.
"""

import sys
import pytest
from pathlib import Path

def run_content_moderation_tests():
    """Run all content moderation module tests."""
    
    # Get the test directory
    test_dir = Path(__file__).parent
    
    # Configure pytest arguments
    pytest_args = [
        str(test_dir),
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--strict-markers",  # Strict marker checking
        "--disable-warnings",  # Disable warnings for cleaner output
        "-x",  # Stop on first failure
        "--durations=10",  # Show 10 slowest tests
    ]
    
    print("=" * 80)
    print("Running Content Moderation Module Tests")
    print("=" * 80)
    
    # Run the tests
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("\n" + "=" * 80)
        print("✅ All Content Moderation Tests PASSED!")
        print("=" * 80)
    else:
        print("\n" + "=" * 80)
        print("❌ Some Content Moderation Tests FAILED!")
        print("=" * 80)
    
    return exit_code

if __name__ == "__main__":
    exit_code = run_content_moderation_tests()
    sys.exit(exit_code)
