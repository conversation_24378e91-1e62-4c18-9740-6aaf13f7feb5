"""
Tests for text moderation module.
"""

import pytest
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from sqlalchemy.orm import Session

from app.ai.content_moderation.modules.text_moderation import TextModerationService
from app.ai.models import ContentModerationRequest, FlaggedContent


class TestTextModerationService:
    """Test cases for TextModerationService."""

    @pytest.fixture
    def service(self):
        """Create a TextModerationService instance for testing."""
        weights = {
            "prohibited_content": 0.4,
            "toxicity": 0.3,
            "personal_information": 0.2,
            "external_links": 0.1
        }
        return TextModerationService(weights, 0.5, 0.8)

    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def clean_text_request(self):
        """Create a clean text moderation request."""
        return ContentModerationRequest(
            content_type="text",
            text_content="This is a clean and appropriate message.",
            user_id="user123",
            context="item_listing"
        )

    @pytest.fixture
    def problematic_text_request(self):
        """Create a problematic text moderation request."""
        return ContentModerationRequest(
            content_type="text",
            text_content="This message contains badword1 and some toxic content.",
            user_id="user123",
            context="item_listing"
        )

    @pytest.fixture
    def empty_text_request(self):
        """Create an empty text moderation request."""
        return ContentModerationRequest(
            content_type="text",
            text_content="",
            user_id="user123",
            context="item_listing"
        )

    @pytest.mark.asyncio
    async def test_moderate_clean_text(self, service, mock_db, clean_text_request):
        """Test moderating clean text content."""
        with patch.object(service.content_checkers, 'check_prohibited_content') as mock_prohibited, \
             patch.object(service.content_checkers, 'check_personal_information') as mock_personal, \
             patch.object(service.content_checkers, 'check_external_links') as mock_links, \
             patch.object(service.content_checkers, 'assess_text_quality') as mock_quality, \
             patch.object(service.content_checkers, 'check_basic_toxicity') as mock_toxicity:

            # Mock all checks to return clean results
            mock_prohibited.return_value = (0.0, [])
            mock_personal.return_value = (0.0, [])
            mock_links.return_value = (0.0, [])
            mock_quality.return_value = 0.8
            mock_toxicity.return_value = (0.0, [])

            result = await service.moderate_text(clean_text_request, mock_db)

            assert result is not None
            assert "result" in result
            assert "flagged_content" in result
            assert result["result"]["flagged"] is False
            assert result["result"]["severity"] == "low"
            assert result["result"]["recommended_action"] == "allow"

    @pytest.mark.asyncio
    async def test_moderate_problematic_text(self, service, mock_db, problematic_text_request):
        """Test moderating problematic text content."""
        with patch.object(service.content_checkers, 'check_prohibited_content') as mock_prohibited, \
             patch.object(service.content_checkers, 'check_personal_information') as mock_personal, \
             patch.object(service.content_checkers, 'check_external_links') as mock_links, \
             patch.object(service.content_checkers, 'assess_text_quality') as mock_quality, \
             patch.object(service.content_checkers, 'check_basic_toxicity') as mock_toxicity:

            # Mock checks to return problematic results
            flagged_item = FlaggedContent(
                content_segment="badword1",
                category="profanity",
                confidence=0.9,
                recommendation="Remove profanity"
            )
            mock_prohibited.return_value = (0.8, [flagged_item])
            mock_personal.return_value = (0.0, [])
            mock_links.return_value = (0.0, [])
            mock_quality.return_value = 0.6
            mock_toxicity.return_value = (0.7, [])

            result = await service.moderate_text(problematic_text_request, mock_db)

            assert result is not None
            assert result["result"]["flagged"] is True
            assert result["result"]["severity"] in ["medium", "high"]
            assert len(result["flagged_content"]) > 0

    @pytest.mark.asyncio
    async def test_moderate_empty_text(self, service, mock_db, empty_text_request):
        """Test moderating empty text content."""
        result = await service.moderate_text(empty_text_request, mock_db)

        assert result is not None
        assert result["result"]["flagged"] is False
        assert result["result"]["overall_score"] == 0.0
        assert result["result"]["recommended_action"] == "allow"

    @pytest.mark.asyncio
    async def test_moderate_text_with_ai_enabled(self, service, mock_db, clean_text_request):
        """Test moderating text with AI integration enabled."""
        with patch('app.core.config.settings') as mock_settings, \
             patch.object(service.ai_integration, 'get_ai_text_moderation', new_callable=AsyncMock) as mock_ai, \
             patch.object(service.content_checkers, 'check_prohibited_content') as mock_prohibited, \
             patch.object(service.content_checkers, 'check_personal_information') as mock_personal, \
             patch.object(service.content_checkers, 'check_external_links') as mock_links, \
             patch.object(service.content_checkers, 'assess_text_quality') as mock_quality:

            # Enable AI moderation
            mock_settings.ENABLE_ADVANCED_CONTENT_MODERATION = True

            # Mock AI response
            mock_ai.return_value = {
                "toxicity_score": 0.1,
                "flagged_segments": []
            }

            # Mock other checks
            mock_prohibited.return_value = (0.0, [])
            mock_personal.return_value = (0.0, [])
            mock_links.return_value = (0.0, [])
            mock_quality.return_value = 0.8

            result = await service.moderate_text(clean_text_request, mock_db)

            assert result is not None
            mock_ai.assert_called_once()
            assert result["result"]["category_scores"]["toxicity"] == 0.1

    @pytest.mark.asyncio
    async def test_moderate_text_with_ai_disabled(self, service, mock_db, clean_text_request):
        """Test moderating text with AI integration disabled."""
        with patch('app.core.config.settings') as mock_settings, \
             patch.object(service.content_checkers, 'check_prohibited_content') as mock_prohibited, \
             patch.object(service.content_checkers, 'check_personal_information') as mock_personal, \
             patch.object(service.content_checkers, 'check_external_links') as mock_links, \
             patch.object(service.content_checkers, 'assess_text_quality') as mock_quality, \
             patch.object(service.content_checkers, 'check_basic_toxicity') as mock_toxicity:

            # Disable AI moderation
            mock_settings.ENABLE_ADVANCED_CONTENT_MODERATION = False

            # Mock all checks
            mock_prohibited.return_value = (0.0, [])
            mock_personal.return_value = (0.0, [])
            mock_links.return_value = (0.0, [])
            mock_quality.return_value = 0.8
            mock_toxicity.return_value = (0.1, [])

            result = await service.moderate_text(clean_text_request, mock_db)

            assert result is not None
            assert result["result"]["category_scores"]["toxicity"] == 0.1

    def test_service_initialization(self, service):
        """Test service initialization."""
        assert service.weights is not None
        assert service.medium_threshold == 0.5
        assert service.high_threshold == 0.8
        assert service.content_checkers is not None
        assert service.ai_integration is not None

    @pytest.mark.asyncio
    async def test_moderate_text_with_ai_failure(self, service, mock_db, clean_text_request):
        """Test moderating text when AI integration fails."""
        with patch('app.core.config.settings') as mock_settings, \
             patch.object(service.ai_integration, 'get_ai_text_moderation', new_callable=AsyncMock) as mock_ai, \
             patch.object(service.content_checkers, 'check_prohibited_content') as mock_prohibited, \
             patch.object(service.content_checkers, 'check_personal_information') as mock_personal, \
             patch.object(service.content_checkers, 'check_external_links') as mock_links, \
             patch.object(service.content_checkers, 'assess_text_quality') as mock_quality, \
             patch.object(service.content_checkers, 'check_basic_toxicity') as mock_toxicity:

            # Enable AI moderation but make it fail
            mock_settings.ENABLE_ADVANCED_CONTENT_MODERATION = True
            mock_ai.return_value = None  # AI failure

            # Mock other checks
            mock_prohibited.return_value = (0.0, [])
            mock_personal.return_value = (0.0, [])
            mock_links.return_value = (0.0, [])
            mock_quality.return_value = 0.8
            mock_toxicity.return_value = (0.1, [])

            result = await service.moderate_text(clean_text_request, mock_db)

            assert result is not None
            # Should fall back to basic toxicity check
            assert result["result"]["category_scores"]["toxicity"] == 0.1
