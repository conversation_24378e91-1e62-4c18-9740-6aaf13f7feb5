"""
Tests for content checkers module.
"""

import pytest
from app.ai.content_moderation.modules.content_checkers import ContentCheckersService
from app.ai.models import FlaggedContent


class TestContentCheckersService:
    """Test cases for ContentCheckersService."""

    @pytest.fixture
    def service(self):
        """Create a ContentCheckersService instance for testing."""
        return ContentCheckersService()

    def test_check_prohibited_content_clean(self, service):
        """Test checking clean content for prohibited words."""
        clean_text = "This is a perfectly normal and appropriate message."
        
        score, flagged_content = service.check_prohibited_content(clean_text)
        
        assert score == 0.0
        assert len(flagged_content) == 0

    def test_check_prohibited_content_profanity(self, service):
        """Test checking content with profanity."""
        # Note: Using placeholder words from the service
        profane_text = "This message contains badword1 and badword2."
        
        score, flagged_content = service.check_prohibited_content(profane_text)
        
        assert score > 0.0
        assert len(flagged_content) > 0
        assert any(flag.category == "profanity" for flag in flagged_content)

    def test_check_personal_information_clean(self, service):
        """Test checking clean content for personal information."""
        clean_text = "This message has no personal information."
        
        score, flagged_content = service.check_personal_information(clean_text)
        
        assert score == 0.0
        assert len(flagged_content) == 0

    def test_check_personal_information_phone(self, service):
        """Test checking content with phone numbers."""
        phone_text = "Call me at ************ for more information."
        
        score, flagged_content = service.check_personal_information(phone_text)
        
        assert score > 0.0
        assert len(flagged_content) > 0
        assert any(flag.category == "personal_information" for flag in flagged_content)
        assert "[REDACTED]" in flagged_content[0].content_segment

    def test_check_personal_information_email(self, service):
        """Test checking content with email addresses."""
        email_text = "Contact <NAME_EMAIL> for details."
        
        score, flagged_content = service.check_personal_information(email_text)
        
        assert score > 0.0
        assert len(flagged_content) > 0
        assert "[REDACTED]" in flagged_content[0].content_segment

    def test_check_external_links_clean(self, service):
        """Test checking content without external links."""
        clean_text = "This is just a regular message without any links."
        
        score, flagged_content = service.check_external_links(clean_text, "item_listing")
        
        assert score == 0.0
        assert len(flagged_content) == 0

    def test_check_external_links_http(self, service):
        """Test checking content with HTTP links."""
        link_text = "Check out this website: https://example.com"
        
        score, flagged_content = service.check_external_links(link_text, "item_listing")
        
        assert score > 0.0
        assert len(flagged_content) > 0
        assert any(flag.category == "external_links" for flag in flagged_content)

    def test_check_external_links_context_sensitivity(self, service):
        """Test that external link scoring varies by context."""
        link_text = "Visit https://example.com"
        
        # Test different contexts
        score_listing, _ = service.check_external_links(link_text, "item_listing")
        score_message, _ = service.check_external_links(link_text, "user_message")
        score_review, _ = service.check_external_links(link_text, "review")
        
        # Message context should have higher risk than review
        assert score_message > score_review

    def test_check_basic_toxicity_clean(self, service):
        """Test basic toxicity check on clean content."""
        clean_text = "This is a wonderful and positive message."
        
        score, flagged_content = service.check_basic_toxicity(clean_text)
        
        assert score == 0.0
        assert len(flagged_content) == 0

    def test_check_basic_toxicity_toxic(self, service):
        """Test basic toxicity check on potentially toxic content."""
        toxic_text = "This is stupid and I hate it, worst thing ever."
        
        score, flagged_content = service.check_basic_toxicity(toxic_text)
        
        assert score > 0.0
        assert len(flagged_content) > 0
        assert any(flag.category == "potential_toxicity" for flag in flagged_content)

    def test_assess_text_quality_short(self, service):
        """Test text quality assessment for very short text."""
        short_text = "Hi"
        
        quality_score = service.assess_text_quality(short_text)
        
        assert quality_score == 0.2  # Very short text

    def test_assess_text_quality_medium(self, service):
        """Test text quality assessment for medium length text."""
        medium_text = "This is a medium length message."
        
        quality_score = service.assess_text_quality(medium_text)
        
        assert quality_score == 0.5  # Short text

    def test_assess_text_quality_all_caps(self, service):
        """Test text quality assessment for all caps text."""
        caps_text = "THIS IS ALL CAPS AND POOR QUALITY"
        
        quality_score = service.assess_text_quality(caps_text)
        
        assert quality_score == 0.3  # All caps is poor quality

    def test_assess_text_quality_repetitive(self, service):
        """Test text quality assessment for repetitive text."""
        repetitive_text = "word word word word word word word word"
        
        quality_score = service.assess_text_quality(repetitive_text)
        
        assert quality_score == 0.4  # High repetition

    def test_assess_text_quality_good(self, service):
        """Test text quality assessment for good quality text."""
        good_text = "This is a well-written message with proper capitalization and good content."
        
        quality_score = service.assess_text_quality(good_text)
        
        assert quality_score == 0.8  # Good quality

    def test_check_image_format_valid(self, service):
        """Test checking valid image format."""
        valid_url = "https://example.com/image.jpg"
        
        is_valid, flagged_content = service.check_image_format(valid_url)
        
        assert is_valid is True
        assert flagged_content is None

    def test_check_image_format_invalid(self, service):
        """Test checking invalid image format."""
        invalid_url = "https://example.com/document.pdf"
        
        is_valid, flagged_content = service.check_image_format(invalid_url)
        
        assert is_valid is False
        assert flagged_content is not None
        assert flagged_content.category == "invalid_format"

    def test_assess_image_quality(self, service):
        """Test image quality assessment."""
        image_url = "https://example.com/image.jpg"
        
        quality_score = service.assess_image_quality(image_url)
        
        assert 0.0 <= quality_score <= 1.0
        assert quality_score == 0.7  # Default placeholder value

    def test_check_basic_explicit_content(self, service):
        """Test basic explicit content check."""
        image_url = "https://example.com/image.jpg"
        
        explicit_score = service.check_basic_explicit_content(image_url)
        
        assert 0.0 <= explicit_score <= 1.0
        assert explicit_score == 0.1  # Default low risk value

    def test_service_initialization(self, service):
        """Test service initialization."""
        assert service.prohibited_words is not None
        assert "profanity" in service.prohibited_words
        assert "hate_speech" in service.prohibited_words
        assert "violence" in service.prohibited_words
        
        assert service.personal_info_patterns is not None
        assert len(service.personal_info_patterns) > 0
        
        assert service.external_link_patterns is not None
        assert len(service.external_link_patterns) > 0

    def test_multiple_violations(self, service):
        """Test content with multiple types of violations."""
        problematic_text = "This badword1 message has <NAME_EMAIL> and link https://bad.com"
        
        # Check each type of violation
        prohibited_score, prohibited_flags = service.check_prohibited_content(problematic_text)
        personal_score, personal_flags = service.check_personal_information(problematic_text)
        links_score, links_flags = service.check_external_links(problematic_text, "item_listing")
        
        # Should detect multiple types of issues
        assert prohibited_score > 0.0
        assert personal_score > 0.0
        assert links_score > 0.0
        
        total_flags = len(prohibited_flags) + len(personal_flags) + len(links_flags)
        assert total_flags >= 3  # At least one flag for each type
