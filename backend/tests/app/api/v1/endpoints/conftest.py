"""
Pytest fixtures for the API endpoint tests.
"""

import pytest
from unittest.mock import MagicMock, AsyncMock
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.testclient import TestClient
from datetime import datetime, timezone

from app.api.v1.endpoints.chatbot import router as chatbot_router
from app.ai.chatbot import (
    ChatbotAgent,
    FallbackChatbotAgent,
    ChatIntent
)

# Sample test data
SAMPLE_USER_ID = "user123"
SAMPLE_SESSION_ID = "session456"
SAMPLE_TIMESTAMP = datetime.now(timezone.utc).isoformat()

@pytest.fixture
def app():
    """Create a FastAPI app with the chatbot router."""
    app = FastAPI()
    app.include_router(chatbot_router, prefix="/chatbot")
    return app

@pytest.fixture
def client(app):
    """Create a test client for the app."""
    return TestClient(app)

@pytest.fixture
def mock_db():
    """Mock database session."""
    return MagicMock()

@pytest.fixture
def mock_notification_service():
    """Mock notification service."""
    return MagicMock()

@pytest.fixture
def mock_chatbot_agent(mock_db, mock_notification_service):
    """Mock ChatbotAgent."""
    agent = MagicMock(spec=ChatbotAgent)
    agent.process_request = AsyncMock()
    agent.process_request.return_value = {
        "message": "I'm here to help. What can I do for you?",
        "intent": ChatIntent.GENERAL,
        "suggestions": [
            {"id": "suggestion_1", "text": "How do I list an item?"},
            {"id": "suggestion_2", "text": "How does booking work?"},
            {"id": "suggestion_3", "text": "What are the fees?"}
        ],
        "timestamp": SAMPLE_TIMESTAMP
    }
    return agent

@pytest.fixture
def mock_fallback_agent(mock_db, mock_notification_service):
    """Mock FallbackChatbotAgent."""
    agent = MagicMock(spec=FallbackChatbotAgent)
    agent.process_request = AsyncMock()
    agent.process_request.return_value = {
        "message": "I'm here to help. What can I do for you?",
        "intent": ChatIntent.GENERAL,
        "suggestions": [
            {"id": "general_1", "text": "How do I list an item for rent?"},
            {"id": "general_2", "text": "How does the booking process work?"},
            {"id": "general_3", "text": "What safety measures do you have in place?"}
        ],
        "timestamp": SAMPLE_TIMESTAMP
    }
    return agent

@pytest.fixture
def mock_current_user():
    """Mock current user."""
    user = MagicMock()
    user.id = SAMPLE_USER_ID
    user.email = "<EMAIL>"
    user.is_active = True
    return user

@pytest.fixture
def mock_dependencies(app, mock_db, mock_notification_service, mock_chatbot_agent, mock_fallback_agent, mock_current_user):
    """Mock all dependencies for the chatbot endpoints."""
    app.dependency_overrides = {
        "app.api.v1.endpoints.chatbot.get_db": lambda: mock_db,
        "app.api.v1.endpoints.chatbot.get_notification_service": lambda: mock_notification_service,
        "app.api.v1.endpoints.chatbot.get_chatbot_agent": lambda: mock_chatbot_agent,
        "app.api.v1.endpoints.chatbot.get_fallback_chatbot_agent": lambda: mock_fallback_agent,
        "app.api.v1.endpoints.chatbot.get_optional_current_user": lambda: mock_current_user
    }
    return app
