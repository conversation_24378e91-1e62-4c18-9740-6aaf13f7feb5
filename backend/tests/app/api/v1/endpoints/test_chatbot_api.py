"""
Tests for the Chatbot API endpoints.

This module contains tests for the chatbot API endpoints, including
chat and feedback endpoints.
"""

import pytest
from datetime import datetime, timezone

# Sample test data
SAMPLE_MESSAGE = "I need help with my booking"
SAMPLE_USER_ID = "user123"
SAMPLE_SESSION_ID = "session456"
SAMPLE_TIMESTAMP = datetime.now(timezone.utc).isoformat()

@pytest.mark.asyncio
async def test_chat_endpoint_success(client, mock_chatbot_agent, mock_fallback_agent):
    """Test successful chat endpoint with primary agent."""
    # Test data
    request_data = {
        "user_id": SAMPLE_USER_ID,
        "session_id": SAMPLE_SESSION_ID,
        "messages": [
            {"role": "user", "content": SAMPLE_MESSAGE, "timestamp": SAMPLE_TIMESTAMP}
        ]
    }

    # Make the request
    response = client.post("/chatbot/chat", json=request_data)

    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "intent" in data
    assert "suggestions" in data
    assert len(data["suggestions"]) == 3
    assert "timestamp" in data

    # Verify the primary agent was called
    mock_chatbot_agent.process_request.assert_called_once()
    mock_fallback_agent.process_request.assert_not_called()

@pytest.mark.asyncio
async def test_chat_endpoint_fallback(client, mock_chatbot_agent, mock_fallback_agent):
    """Test chat endpoint with fallback to secondary agent."""
    # Make the primary agent fail
    mock_chatbot_agent.process_request.side_effect = Exception("API error")

    # Test data
    request_data = {
        "user_id": SAMPLE_USER_ID,
        "session_id": SAMPLE_SESSION_ID,
        "messages": [
            {"role": "user", "content": SAMPLE_MESSAGE, "timestamp": SAMPLE_TIMESTAMP}
        ]
    }

    # Make the request
    response = client.post("/chatbot/chat", json=request_data)

    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "intent" in data
    assert "suggestions" in data
    assert len(data["suggestions"]) == 3
    assert "timestamp" in data

    # Verify both agents were called
    mock_chatbot_agent.process_request.assert_called_once()
    mock_fallback_agent.process_request.assert_called_once()

@pytest.mark.asyncio
async def test_feedback_endpoint(client):
    """Test the feedback endpoint."""
    # Test data
    feedback_data = {
        "session_id": SAMPLE_SESSION_ID,
        "message_id": "message123",
        "rating": 5,
        "feedback": "Great response!",
        "timestamp": SAMPLE_TIMESTAMP
    }

    # Make the request
    response = client.post("/chatbot/feedback", json=feedback_data)

    # Check the response
    assert response.status_code == 200
    data = response.json()
    assert "status" in data
    assert data["status"] == "success"
    assert "message" in data
    assert data["message"] == "Thank you for your feedback!"
