"""
Pytest fixtures for the AI Chatbot tests.
"""

import pytest
from unittest.mock import MagicMock, AsyncMock
from datetime import datetime, timezone

from app.ai.chatbot import (
    ChatbotAgent,
    FallbackChatbotAgent,
    ChatbotRequest,
    ChatMessage,
    ChatIntent
)

# Sample test data
SAMPLE_USER_ID = "user123"
SAMPLE_SESSION_ID = "session456"
SAMPLE_TIMESTAMP = datetime.now(timezone.utc).isoformat()

@pytest.fixture
def mock_db():
    """Mock database session."""
    return MagicMock()

@pytest.fixture
def mock_notification_service():
    """Mock notification service."""
    return MagicMock()

@pytest.fixture
def mock_chatbot_agent(mock_db, mock_notification_service):
    """Mock ChatbotAgent."""
    agent = MagicMock(spec=ChatbotAgent)
    agent.process_request = AsyncMock()
    agent.process_request.return_value = {
        "message": "I'm here to help. What can I do for you?",
        "intent": ChatIntent.GENERAL,
        "suggestions": [
            {"id": "suggestion_1", "text": "How do I list an item?"},
            {"id": "suggestion_2", "text": "How does booking work?"},
            {"id": "suggestion_3", "text": "What are the fees?"}
        ],
        "timestamp": SAMPLE_TIMESTAMP
    }
    return agent

@pytest.fixture
def mock_fallback_agent(mock_db, mock_notification_service):
    """Mock FallbackChatbotAgent."""
    agent = MagicMock(spec=FallbackChatbotAgent)
    agent.process_request = AsyncMock()
    agent.process_request.return_value = {
        "message": "I'm here to help. What can I do for you?",
        "intent": ChatIntent.GENERAL,
        "suggestions": [
            {"id": "general_1", "text": "How do I list an item for rent?"},
            {"id": "general_2", "text": "How does the booking process work?"},
            {"id": "general_3", "text": "What safety measures do you have in place?"}
        ],
        "timestamp": SAMPLE_TIMESTAMP
    }
    return agent

@pytest.fixture
def sample_request_general():
    """Sample chatbot request with general intent."""
    return ChatbotRequest(
        user_id=SAMPLE_USER_ID,
        session_id=SAMPLE_SESSION_ID,
        messages=[
            ChatMessage(role="user", content="How does RentUp work?", timestamp=SAMPLE_TIMESTAMP)
        ]
    )

@pytest.fixture
def sample_request_support():
    """Sample chatbot request with support intent."""
    return ChatbotRequest(
        user_id=SAMPLE_USER_ID,
        session_id=SAMPLE_SESSION_ID,
        messages=[
            ChatMessage(role="user", content="I need help with my booking", timestamp=SAMPLE_TIMESTAMP)
        ]
    )

@pytest.fixture
def sample_request_feedback():
    """Sample chatbot request with feedback intent."""
    return ChatbotRequest(
        user_id=SAMPLE_USER_ID,
        session_id=SAMPLE_SESSION_ID,
        messages=[
            ChatMessage(role="user", content="I want to provide some feedback", timestamp=SAMPLE_TIMESTAMP)
        ]
    )

@pytest.fixture
def sample_request_dispute():
    """Sample chatbot request with dispute intent."""
    return ChatbotRequest(
        user_id=SAMPLE_USER_ID,
        session_id=SAMPLE_SESSION_ID,
        messages=[
            ChatMessage(role="user", content="I have a problem with my rental", timestamp=SAMPLE_TIMESTAMP)
        ]
    )
