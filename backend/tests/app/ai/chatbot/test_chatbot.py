"""
Tests for the AI Chatbot module.

This module contains tests for the AI chatbot functionality, including
intent detection, response generation, and fallback mechanisms.
"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from app.ai.chatbot import (
    ChatbotAgent,
    FallbackChatbotAgent,
    ChatbotRequest,
    ChatMessage,
    ChatIntent
)

# Test messages for different intents
SUPPORT_MESSAGE = "I need help with my booking"
FEEDBACK_MESSAGE = "I want to provide some feedback about your service"
DISPUTE_MESSAGE = "I have a problem with my rental, it was damaged"
GENERAL_MESSAGE = "How does RentUp work?"

class TestFallbackChatbotAgent:
    """Tests for the FallbackChatbotAgent class."""

    def test_detect_intent_support(self):
        """Test intent detection for support messages."""
        agent = FallbackChatbotAgent()
        intent = agent._detect_intent(SUPPORT_MESSAGE)
        assert intent == ChatIntent.SUPPORT

    def test_detect_intent_feedback(self):
        """Test intent detection for feedback messages."""
        agent = FallbackChatbotAgent()
        intent = agent._detect_intent(FEEDBACK_MESSAGE)
        assert intent == ChatIntent.FEEDBACK

    def test_detect_intent_dispute(self):
        """Test intent detection for dispute messages."""
        agent = FallbackChatbotAgent()
        intent = agent._detect_intent(DISPUTE_MESSAGE)
        assert intent == ChatIntent.DISPUTE

    def test_detect_intent_general(self):
        """Test intent detection for general messages."""
        agent = FallbackChatbotAgent()
        intent = agent._detect_intent(GENERAL_MESSAGE)
        assert intent == ChatIntent.GENERAL

    def test_get_response_by_intent(self):
        """Test response generation based on intent."""
        agent = FallbackChatbotAgent()

        # Test support response
        support_response = agent._get_response_by_intent(ChatIntent.SUPPORT)
        assert support_response in agent.support_responses

        # Test feedback response
        feedback_response = agent._get_response_by_intent(ChatIntent.FEEDBACK)
        assert feedback_response in agent.feedback_responses

        # Test dispute response
        dispute_response = agent._get_response_by_intent(ChatIntent.DISPUTE)
        assert dispute_response in agent.dispute_responses

        # Test general response
        general_response = agent._get_response_by_intent(ChatIntent.GENERAL)
        assert general_response in agent.general_responses

    def test_get_default_suggestions(self):
        """Test default suggestions based on intent."""
        agent = FallbackChatbotAgent()

        # Test support suggestions
        support_suggestions = agent._get_default_suggestions(ChatIntent.SUPPORT)
        assert len(support_suggestions) == 3
        assert support_suggestions[0].id.startswith("support_")

        # Test feedback suggestions
        feedback_suggestions = agent._get_default_suggestions(ChatIntent.FEEDBACK)
        assert len(feedback_suggestions) == 3
        assert feedback_suggestions[0].id.startswith("feedback_")

        # Test dispute suggestions
        dispute_suggestions = agent._get_default_suggestions(ChatIntent.DISPUTE)
        assert len(dispute_suggestions) == 3
        assert dispute_suggestions[0].id.startswith("dispute_")

        # Test general suggestions
        general_suggestions = agent._get_default_suggestions(ChatIntent.GENERAL)
        assert len(general_suggestions) == 3
        assert general_suggestions[0].id.startswith("general_")

    @pytest.mark.asyncio
    async def test_process_request(self, sample_request_support, mock_db, mock_notification_service):
        """Test processing a chatbot request."""
        agent = FallbackChatbotAgent(db=mock_db, notification_service=mock_notification_service)
        response = await agent.process_request(sample_request_support, mock_db, mock_notification_service)

        assert "intent" in response
        assert response["intent"] == ChatIntent.SUPPORT
        assert "message" in response
        assert "suggestions" in response
        assert len(response["suggestions"]) == 3
        assert "timestamp" in response
