# RentUp Production Environment Configuration
# Copy this file to .env.prod and update the values

# Database Configuration
POSTGRES_SERVER=rentup-primary.example.com
POSTGRES_USER=rentup
POSTGRES_PASSWORD=secure_password_here
POSTGRES_DB=rentup
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=10
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=1800

# Read Replica Configuration
READ_REPLICAS_ENABLED=true
READ_REPLICA_COUNT=2
READ_REPLICA_LOAD_BALANCING=round_robin
READ_PREFERENCE=replica
FALLBACK_TO_PRIMARY=true
MAX_REPLICA_LAG=30.0
HEALTH_CHECK_INTERVAL=60.0
CIRCUIT_BREAKER_THRESHOLD=3
CIRCUIT_BREAKER_TIMEOUT=60.0

# Sharding Configuration
SHARDING_ENABLED=true
SHARD_COUNT=4
SHARD_BASE_PORT=5440

# Caching Configuration
QUERY_CACHE_ENABLED=true
QUERY_CACHE_TTL=3600
QUERY_CACHE_MAX_SIZE=1000
PLAN_CACHE_ENABLED=true
PLAN_CACHE_TTL=3600
PLAN_CACHE_MAX_SIZE=1000
PLAN_CACHE_MIN_EXECUTION_TIME=0.01

# Application Configuration
SECRET_KEY=secure_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
BACKEND_CORS_ORIGINS=["https://rentup.example.com"]
PROJECT_NAME=RentUp
API_V1_STR=/api/v1

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security Configuration
SECURITY_PASSWORD_SALT=secure_salt_here
SECURITY_PASSWORD_HASH=bcrypt
SECURITY_PASSWORD_SCHEMES=["bcrypt"]
SECURITY_DEPRECATED_PASSWORD_SCHEMES=[]

# Email Configuration
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=smtp.example.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=secure_smtp_password_here
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=RentUp

# AWS Configuration (if using AWS)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET=rentup-uploads

# Monitoring Configuration
MONITORING_ENABLED=true
MONITORING_INTERVAL=60
MONITORING_OUTPUT_DIR=monitoring
SLOW_QUERY_THRESHOLD=0.5
