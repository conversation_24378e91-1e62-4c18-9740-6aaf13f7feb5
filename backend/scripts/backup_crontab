# RentUp Backup Crontab
# Install with: crontab backend/scripts/backup_crontab
# 
# Format:
# minute hour day month day-of-week command
#
# Environment variables
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
MAILTO=<EMAIL>
RENTUP_HOME=/opt/rentup

# Daily full backup at 2:00 AM UTC
0 2 * * * cd $RENTUP_HOME && /opt/rentup/backend/scripts/automated_backup.sh --full >> /var/log/rentup/backup.log 2>&1

# Transaction log backups every 15 minutes
*/15 * * * * cd $RENTUP_HOME && /opt/rentup/backend/scripts/automated_backup.sh --transaction-log >> /var/log/rentup/backup.log 2>&1

# Verify the latest backup once a day at 4:00 AM UTC
0 4 * * * cd $RENTUP_HOME && /opt/rentup/backend/scripts/verify_backup.py --cloud-backup --cloud-provider aws --bucket-name rentup-backups --backup-key $(aws s3 ls s3://rentup-backups/database_backups/ --recursive | sort | tail -n 1 | awk '{print $4}') --compressed --report-file /var/log/rentup/backup_verification_$(date +\%Y\%m\%d).json >> /var/log/rentup/backup_verification.log 2>&1

# Cleanup old logs weekly on Sunday at 1:00 AM UTC
0 1 * * 0 find /var/log/rentup -name "*.log" -type f -mtime +30 -delete

# Backup verification report summary weekly on Monday at 8:00 AM UTC
0 8 * * 1 cd $RENTUP_HOME && /opt/rentup/backend/scripts/backup_report_summary.py --email <EMAIL> >> /var/log/rentup/backup_report.log 2>&1
