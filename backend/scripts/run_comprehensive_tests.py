#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run comprehensive tests for all database optimizations in RentUp.

This script:
1. Tests database connection pooling
2. Tests JOIN operation optimization
3. Tests query caching
4. Tests read replicas
5. Tests full-text search optimization
6. Tests database sharding
7. Tests query plan caching
8. Measures performance improvements
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import json
import random
import string
import concurrent.futures
from typing import Dict, List, Any, Optional, Tuple, Callable

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text, func, desc, Column, String, Integer, Float, Boolean, ForeignKey
from sqlalchemy.orm import Session, relationship, declarative_base
from sqlalchemy.ext.declarative import DeclarativeMeta

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    from app.core.join_optimization import optimize_join_query, JoinPattern, JoinType
    from app.core.query_cache import query_cache, CacheStrategy, cached_query
    from app.core.query_optimization import optimize_query, paginate_query
    from app.core.read_replica import get_read_session, get_write_session, read_session_context, write_session_context
    from app.core.query_plan_cache import get_query_plan, cache_query_plan, get_cached_query_plan, QueryPlanKey
    from app.services.db_optimization_service import optimized_query, batch_get, batch_get_related
    from app.models.user import User
    from app.models.item import Item
    from app.models.rental import Rental
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run comprehensive tests for database optimizations')
    parser.add_argument('--connection-pooling', action='store_true', help='Test database connection pooling')
    parser.add_argument('--join-optimization', action='store_true', help='Test JOIN operation optimization')
    parser.add_argument('--query-caching', action='store_true', help='Test query caching')
    parser.add_argument('--read-replicas', action='store_true', help='Test read replicas')
    parser.add_argument('--full-text-search', action='store_true', help='Test full-text search optimization')
    parser.add_argument('--database-sharding', action='store_true', help='Test database sharding')
    parser.add_argument('--query-plan-caching', action='store_true', help='Test query plan caching')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--iterations', type=int, default=10, help='Number of test iterations')
    parser.add_argument('--concurrency', type=int, default=10, help='Number of concurrent operations')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def generate_test_data(db: Session, count: int = 100):
    """Generate test data for benchmarking."""
    logger.info(f"Generating {count} test users and items...")
    
    # Generate users
    users = []
    for i in range(count):
        user_id = f"test_user_{i}"
        user = User(
            id=user_id,
            email=f"user{i}@example.com",
            full_name=f"Test User {i}",
            hashed_password=f"hashed_password_{i}"
        )
        users.append(user)
    
    # Add users to database
    db.add_all(users)
    db.commit()
    
    # Generate items
    items = []
    categories = ["furniture", "electronics", "clothing", "tools", "sports"]
    
    for i in range(count * 2):  # 2 items per user on average
        owner_id = f"test_user_{random.randint(0, count - 1)}"
        category = random.choice(categories)
        
        item = Item(
            id=f"test_item_{i}",
            name=f"Test Item {i}",
            description=f"Description for test item {i}. This is a {category} item.",
            category=category,
            subcategory=f"{category}_sub_{random.randint(1, 5)}",
            daily_price=random.uniform(10.0, 100.0),
            owner_id=owner_id,
            is_available=random.choice([True, False]),
            location=f"Location {random.randint(1, 10)}"
        )
        items.append(item)
    
    # Add items to database
    db.add_all(items)
    db.commit()
    
    # Generate rentals
    rentals = []
    for i in range(count):
        item_id = f"test_item_{random.randint(0, count * 2 - 1)}"
        owner_id = db.query(Item.owner_id).filter(Item.id == item_id).scalar()
        
        # Make sure renter is not the owner
        renter_id = f"test_user_{random.randint(0, count - 1)}"
        while renter_id == owner_id:
            renter_id = f"test_user_{random.randint(0, count - 1)}"
        
        rental = Rental(
            id=f"test_rental_{i}",
            item_id=item_id,
            owner_id=owner_id,
            renter_id=renter_id,
            start_date="2023-01-01",
            end_date="2023-01-10",
            total_price=random.uniform(100.0, 1000.0),
            status=random.choice(["active", "completed", "cancelled"])
        )
        rentals.append(rental)
    
    # Add rentals to database
    db.add_all(rentals)
    db.commit()
    
    logger.info(f"Generated {len(users)} users, {len(items)} items, and {len(rentals)} rentals")

def test_connection_pooling(db: Session, iterations: int = 10, concurrency: int = 10):
    """Test database connection pooling."""
    if not NEW_MODULES:
        logger.error("Connection pooling module not available")
        return {"status": "error", "message": "Connection pooling module not available"}
    
    logger.info("Testing database connection pooling...")
    
    # Define a simple query function
    def execute_query(i):
        with session_scope() as session:
            result = session.execute(text("SELECT 1"))
            return result.scalar()
    
    # Test sequential execution
    start_time = time.time()
    for i in range(iterations):
        execute_query(i)
    sequential_time = time.time() - start_time
    
    # Test concurrent execution
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = [executor.submit(execute_query, i) for i in range(iterations)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    concurrent_time = time.time() - start_time
    
    # Calculate speedup
    speedup = sequential_time / concurrent_time if concurrent_time > 0 else float('inf')
    
    logger.info(f"Sequential execution time: {sequential_time:.6f}s")
    logger.info(f"Concurrent execution time: {concurrent_time:.6f}s")
    logger.info(f"Speedup: {speedup:.2f}x")
    
    return {
        "status": "success",
        "sequential_time": sequential_time,
        "concurrent_time": concurrent_time,
        "speedup": speedup,
        "iterations": iterations,
        "concurrency": concurrency
    }

def test_join_optimization(db: Session, iterations: int = 10):
    """Test JOIN operation optimization."""
    if not NEW_MODULES:
        logger.error("JOIN optimization module not available")
        return {"status": "error", "message": "JOIN optimization module not available"}
    
    logger.info("Testing JOIN operation optimization...")
    
    # Define a complex JOIN query
    query = db.query(Rental).join(Item).join(User, User.id == Rental.renter_id)
    
    # Test without optimization
    start_time = time.time()
    for i in range(iterations):
        results = query.all()
    unoptimized_time = time.time() - start_time
    
    # Test with optimization
    start_time = time.time()
    for i in range(iterations):
        optimized_query_obj = optimize_join_query(query, db)
        results = optimized_query_obj.all() if hasattr(optimized_query_obj, "all") else list(optimized_query_obj)
    optimized_time = time.time() - start_time
    
    # Calculate improvement
    improvement = (unoptimized_time - optimized_time) / unoptimized_time * 100 if unoptimized_time > 0 else 0
    
    logger.info(f"Unoptimized execution time: {unoptimized_time:.6f}s")
    logger.info(f"Optimized execution time: {optimized_time:.6f}s")
    logger.info(f"Improvement: {improvement:.2f}%")
    
    return {
        "status": "success",
        "unoptimized_time": unoptimized_time,
        "optimized_time": optimized_time,
        "improvement": improvement,
        "iterations": iterations
    }

def test_query_caching(db: Session, iterations: int = 10):
    """Test query caching."""
    if not NEW_MODULES:
        logger.error("Query caching module not available")
        return {"status": "error", "message": "Query caching module not available"}
    
    logger.info("Testing query caching...")
    
    # Clear cache
    query_cache.clear_stats()
    
    # Define a cached function
    @cached_query(ttl=60, strategy=CacheStrategy.SMART)
    def get_items_by_category(session, category):
        return session.query(Item).filter(Item.category == category).all()
    
    # Test without cache (first call)
    start_time = time.time()
    items_1 = get_items_by_category(db, "furniture")
    uncached_time = time.time() - start_time
    
    # Test with cache (subsequent calls)
    cached_times = []
    for i in range(iterations):
        start_time = time.time()
        items_2 = get_items_by_category(db, "furniture")
        cached_times.append(time.time() - start_time)
    
    avg_cached_time = sum(cached_times) / len(cached_times) if cached_times else 0
    
    # Calculate improvement
    improvement = (uncached_time - avg_cached_time) / uncached_time * 100 if uncached_time > 0 else 0
    
    # Get cache stats
    stats = query_cache.get_stats()
    
    logger.info(f"Uncached execution time: {uncached_time:.6f}s")
    logger.info(f"Average cached execution time: {avg_cached_time:.6f}s")
    logger.info(f"Improvement: {improvement:.2f}%")
    logger.info(f"Cache hits: {stats.get('hits', 0)}")
    logger.info(f"Cache misses: {stats.get('misses', 0)}")
    logger.info(f"Cache hit ratio: {stats.get('hit_ratio', 0):.2f}")
    
    return {
        "status": "success",
        "uncached_time": uncached_time,
        "avg_cached_time": avg_cached_time,
        "improvement": improvement,
        "cache_stats": stats,
        "iterations": iterations
    }

def test_read_replicas(db: Session, iterations: int = 10, concurrency: int = 10):
    """Test read replicas."""
    if not NEW_MODULES:
        logger.error("Read replica module not available")
        return {"status": "error", "message": "Read replica module not available"}
    
    logger.info("Testing read replicas...")
    
    # Define query functions
    def execute_primary_query(i):
        with session_scope() as session:
            return session.query(Item).all()
    
    def execute_replica_query(i):
        with read_session_context() as session:
            return session.query(Item).all()
    
    # Test primary database
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = [executor.submit(execute_primary_query, i) for i in range(iterations)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    primary_time = time.time() - start_time
    
    # Test read replicas
    start_time = time.time()
    with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
        futures = [executor.submit(execute_replica_query, i) for i in range(iterations)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    replica_time = time.time() - start_time
    
    # Calculate improvement
    improvement = (primary_time - replica_time) / primary_time * 100 if primary_time > 0 else 0
    
    logger.info(f"Primary database execution time: {primary_time:.6f}s")
    logger.info(f"Read replica execution time: {replica_time:.6f}s")
    logger.info(f"Improvement: {improvement:.2f}%")
    
    return {
        "status": "success",
        "primary_time": primary_time,
        "replica_time": replica_time,
        "improvement": improvement,
        "iterations": iterations,
        "concurrency": concurrency
    }

def test_full_text_search(db: Session, iterations: int = 10):
    """Test full-text search optimization."""
    if not NEW_MODULES:
        logger.error("Full-text search module not available")
        return {"status": "error", "message": "Full-text search module not available"}
    
    logger.info("Testing full-text search optimization...")
    
    # Test queries
    search_terms = ["furniture", "electronics", "vintage", "modern", "leather"]
    
    # Test LIKE search
    like_times = []
    for term in search_terms:
        start_time = time.time()
        for i in range(iterations):
            results = db.query(Item).filter(
                (Item.name.ilike(f"%{term}%")) |
                (Item.description.ilike(f"%{term}%")) |
                (Item.category.ilike(f"%{term}%"))
            ).all()
        like_times.append(time.time() - start_time)
    
    avg_like_time = sum(like_times) / len(like_times) if like_times else 0
    
    # Test full-text search
    fts_times = []
    for term in search_terms:
        start_time = time.time()
        for i in range(iterations):
            # Check if search_vector column exists
            try:
                results = db.execute(text("""
                    SELECT * FROM items
                    WHERE search_vector @@ plainto_tsquery('english', :query)
                    ORDER BY ts_rank(search_vector, plainto_tsquery('english', :query)) DESC
                """), {"query": term}).all()
                fts_times.append(time.time() - start_time)
            except Exception as e:
                logger.warning(f"Full-text search failed: {e}")
                # Fall back to LIKE search
                results = db.query(Item).filter(
                    (Item.name.ilike(f"%{term}%")) |
                    (Item.description.ilike(f"%{term}%")) |
                    (Item.category.ilike(f"%{term}%"))
                ).all()
                fts_times.append(time.time() - start_time)
    
    avg_fts_time = sum(fts_times) / len(fts_times) if fts_times else 0
    
    # Calculate improvement
    improvement = (avg_like_time - avg_fts_time) / avg_like_time * 100 if avg_like_time > 0 else 0
    
    logger.info(f"Average LIKE search time: {avg_like_time:.6f}s")
    logger.info(f"Average full-text search time: {avg_fts_time:.6f}s")
    logger.info(f"Improvement: {improvement:.2f}%")
    
    return {
        "status": "success",
        "avg_like_time": avg_like_time,
        "avg_fts_time": avg_fts_time,
        "improvement": improvement,
        "iterations": iterations
    }

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Determine which tests to run
    run_connection_pooling = args.connection_pooling or args.all
    run_join_optimization = args.join_optimization or args.all
    run_query_caching = args.query_caching or args.all
    run_read_replicas = args.read_replicas or args.all
    run_full_text_search = args.full_text_search or args.all
    run_database_sharding = args.database_sharding or args.all
    run_query_plan_caching = args.query_plan_caching or args.all
    
    # If no tests specified, show help
    if not (run_connection_pooling or run_join_optimization or run_query_caching or
            run_read_replicas or run_full_text_search or run_database_sharding or
            run_query_plan_caching):
        logger.info("No tests specified. Use --all or specify individual tests.")
        return 1
    
    # Initialize results
    results = {}
    
    # Generate test data if needed
    with session_scope() as db:
        # Check if test data exists
        user_count = db.query(func.count(User.id)).filter(User.id.like("test_user_%")).scalar()
        
        if user_count < 10:
            generate_test_data(db, count=100)
    
    # Run tests
    if run_connection_pooling:
        with session_scope() as db:
            results["connection_pooling"] = test_connection_pooling(
                db, iterations=args.iterations, concurrency=args.concurrency
            )
    
    if run_join_optimization:
        with session_scope() as db:
            results["join_optimization"] = test_join_optimization(
                db, iterations=args.iterations
            )
    
    if run_query_caching:
        with session_scope() as db:
            results["query_caching"] = test_query_caching(
                db, iterations=args.iterations
            )
    
    if run_read_replicas:
        with session_scope() as db:
            results["read_replicas"] = test_read_replicas(
                db, iterations=args.iterations, concurrency=args.concurrency
            )
    
    if run_full_text_search:
        with session_scope() as db:
            results["full_text_search"] = test_full_text_search(
                db, iterations=args.iterations
            )
    
    # TODO: Implement tests for database sharding and query plan caching
    
    # Save results to file
    output_file = "comprehensive_test_results.json"
    with open(output_file, "w") as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Test results saved to {output_file}")
    
    # Print summary
    logger.info("Test Summary:")
    for test_name, test_results in results.items():
        if test_results.get("status") == "success":
            improvement = test_results.get("improvement", 0)
            logger.info(f"  {test_name}: {improvement:.2f}% improvement")
        else:
            logger.info(f"  {test_name}: {test_results.get('message', 'Failed')}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
