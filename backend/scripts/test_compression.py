#!/usr/bin/env python3
"""
Simple test script for the compression middleware.

This script creates a simple FastAPI app with compression middleware
and tests that responses are properly compressed.
"""

import sys
import os
import gzip
import json
from fastapi import FastAPI, Response
from fastapi.testclient import TestClient

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the compression middleware
from app.core.compression_middleware import add_compression_middleware

# Check if <PERSON><PERSON><PERSON> is available
try:
    import brotli
    BROTLI_AVAILABLE = True
    print("<PERSON><PERSON><PERSON> is available")
except ImportError:
    BROTLI_AVAILABLE = False
    print("<PERSON><PERSON><PERSON> is not available")


def create_test_app():
    """Create a test FastAPI app with compression middleware."""
    app = FastAPI()

    @app.get("/test-text")
    def get_text():
        # Return a large text response that should be compressed
        return {"text": "x" * 10000}

    @app.get("/test-small")
    def get_small():
        # Return a small response that should not be compressed
        return {"text": "small"}

    @app.get("/test-binary")
    def get_binary():
        # Return a binary response with a media type that should be excluded
        return Response(content=b"\x00" * 10000, media_type="image/jpeg")

    @app.get("/excluded-path")
    def get_excluded():
        # Return a response from an excluded path
        return {"text": "x" * 10000}

    # Add compression middleware
    add_compression_middleware(
        app,
        minimum_size=500,
        gzip_level=6,
        brotli_quality=4,
        exclude_paths=["/excluded-path"],
        exclude_media_types=["image/jpeg", "image/png"],
        debug=True
    )

    return app


def test_compression_large_response(client):
    """Test that large responses are compressed."""
    print("\nTesting compression of large response...")

    # Make request with gzip encoding
    response = client.get("/test-text", headers={"Accept-Encoding": "gzip"})

    # Check that response is compressed
    if "Content-Encoding" in response.headers:
        print(f"Response is compressed with {response.headers['Content-Encoding']}")

        # The TestClient automatically decompresses the response
        # So we can just check the content directly
        try:
            data = response.json()
            if "text" in data and data["text"] == "x" * 10000:
                print("✅ Content is correctly compressed and decompressed")
            else:
                print("❌ Content is not correctly decompressed")
                print(f"Content: {data}")
        except Exception as e:
            print(f"❌ Error parsing content: {e}")
            print(f"Content: {response.content[:100]}...")
    else:
        print("❌ Response is not compressed")
        print(f"Headers: {response.headers}")
        print(f"Status code: {response.status_code}")

        # Try to parse the content
        try:
            data = response.json()
            print(f"Content: {data}")
        except Exception as e:
            print(f"Error parsing content: {e}")
            print(f"Content: {response.content[:100]}...")


def test_compression_small_response(client):
    """Test that small responses are not compressed."""
    print("\nTesting compression of small response...")

    # Make request with gzip encoding
    response = client.get("/test-small", headers={"Accept-Encoding": "gzip"})

    # Check that response is not compressed
    if "Content-Encoding" not in response.headers:
        print("✅ Small response is not compressed (as expected)")

        # Verify content
        data = response.json()
        if "text" in data and data["text"] == "small":
            print("✅ Content is correct")
        else:
            print("❌ Content is not correct")
    else:
        print(f"❌ Small response is compressed with {response.headers['Content-Encoding']}")


def test_compression_excluded_media_type(client):
    """Test that responses with excluded media types are not compressed."""
    print("\nTesting compression of excluded media type...")

    # Make request with gzip encoding
    response = client.get("/test-binary", headers={"Accept-Encoding": "gzip"})

    # Check that response is not compressed
    if "Content-Encoding" not in response.headers:
        print("✅ Excluded media type is not compressed (as expected)")

        # Verify content type
        if response.headers["Content-Type"] == "image/jpeg":
            print("✅ Content type is correct")
        else:
            print(f"❌ Content type is not correct: {response.headers['Content-Type']}")

        # Verify content length
        if len(response.content) == 10000:
            print("✅ Content length is correct")
        else:
            print(f"❌ Content length is not correct: {len(response.content)}")
    else:
        print(f"❌ Excluded media type is compressed with {response.headers['Content-Encoding']}")


def test_compression_excluded_path(client):
    """Test that responses from excluded paths are not compressed."""
    print("\nTesting compression of excluded path...")

    # Make request with gzip encoding
    response = client.get("/excluded-path", headers={"Accept-Encoding": "gzip"})

    # Check that response is not compressed
    if "Content-Encoding" not in response.headers:
        print("✅ Excluded path is not compressed (as expected)")

        # Verify content
        data = response.json()
        if "text" in data and data["text"] == "x" * 10000:
            print("✅ Content is correct")
        else:
            print("❌ Content is not correct")
    else:
        print(f"❌ Excluded path is compressed with {response.headers['Content-Encoding']}")


def test_compression_brotli(client):
    """Test that Brotli compression is used when available and supported by client."""
    if not BROTLI_AVAILABLE:
        print("\nSkipping Brotli test (Brotli not available)")
        return

    print("\nTesting Brotli compression...")

    # Make request with brotli encoding
    response = client.get("/test-text", headers={"Accept-Encoding": "br"})

    # Check that response is compressed with brotli
    if "Content-Encoding" in response.headers and response.headers["Content-Encoding"] == "br":
        print("✅ Response is compressed with Brotli")

        # The TestClient automatically decompresses the response
        # So we can just check the content directly
        try:
            data = response.json()
            if "text" in data and data["text"] == "x" * 10000:
                print("✅ Content is correctly compressed and decompressed")
            else:
                print("❌ Content is not correctly decompressed")
                print(f"Content: {data}")
        except Exception as e:
            print(f"❌ Error parsing content: {e}")
            print(f"Content: {response.content[:100]}...")
    else:
        print(f"❌ Response is not compressed with Brotli: {response.headers.get('Content-Encoding', 'none')}")


def run_all_tests():
    """Run all compression tests."""
    print("Running compression middleware tests...")

    # Create test app and client
    app = create_test_app()
    client = TestClient(app)

    # Run tests
    test_compression_large_response(client)
    test_compression_small_response(client)
    test_compression_excluded_media_type(client)
    test_compression_excluded_path(client)
    test_compression_brotli(client)

    print("\nAll tests completed!")


if __name__ == "__main__":
    run_all_tests()
