"""
Fraud alert generation functionality.

This module provides the FraudAlertGenerator class for creating realistic
sample fraud alerts in the database.
"""

import uuid
import json
import random
import logging
from datetime import datetime, timedelta, UTC
from typing import Dict, Any, List
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from .base import BaseGenerator

logger = logging.getLogger(__name__)


class FraudAlertGenerator(BaseGenerator):
    """Generator for creating sample fraud alerts."""

    def __init__(self, engine):
        """Initialize the fraud alert generator."""
        super().__init__(engine)
        self._setup_alert_data()

    def _setup_alert_data(self):
        """Set up alert types and other constants."""
        self.alert_types = [
            "suspicious_user", "suspicious_item", "suspicious_auction",
            "suspicious_rental", "suspicious_login", "suspicious_payment"
        ]
        
        self.statuses = ["new", "investigating", "resolved", "false_positive"]
        
        self.trigger_types = [
            "unusual_location", "rapid_transactions", "multiple_accounts",
            "suspicious_pattern", "unusual_behavior", "high_risk_score",
            "blacklisted_ip", "device_fingerprint_mismatch"
        ]

    def generate_fraud_alerts(self, batch_size: int = 5) -> Dict[str, Any]:
        """
        Generate a batch of fraud alerts and add them to the database.

        Args:
            batch_size: Number of fraud alerts to generate in this batch

        Returns:
            Dictionary with generation results
        """
        with self.engine.connect() as conn:
            try:
                # Get all user IDs
                user_ids = [row[0] for row in conn.execute(text("SELECT id FROM users")).fetchall()]

                # Get all item IDs
                item_ids = [row[0] for row in conn.execute(text("SELECT id FROM items")).fetchall()]

                # Get all auction IDs
                auction_ids = [row[0] for row in conn.execute(text("SELECT id FROM auctions")).fetchall()]

                if not user_ids:
                    logger.warning("No users found for fraud alert generation.")
                    return {"count": 0, "message": "No users available"}

                # Prepare batch of fraud alerts to insert
                alerts_to_insert = []
                current_time = datetime.now(UTC)

                # Generate fraud alerts
                for i in range(batch_size):
                    alert_id = str(uuid.uuid4())

                    # Randomly select alert type and associated entity
                    alert_type = random.choice(self.alert_types)
                    
                    # Assign entities based on alert type
                    user_id = None
                    item_id = None
                    auction_id = None
                    
                    if alert_type in ["suspicious_user", "suspicious_login"]:
                        user_id = random.choice(user_ids)
                    elif alert_type == "suspicious_item" and item_ids:
                        item_id = random.choice(item_ids)
                    elif alert_type == "suspicious_auction" and auction_ids:
                        auction_id = random.choice(auction_ids)
                    elif alert_type in ["suspicious_rental", "suspicious_payment"]:
                        user_id = random.choice(user_ids)

                    # Generate risk score and severity
                    risk_score = round(random.uniform(0.1, 0.9), 2)
                    severity = self._calculate_severity(risk_score)

                    # Generate status and resolution details
                    status = random.choices(
                        self.statuses, 
                        weights=[0.4, 0.3, 0.2, 0.1]
                    )[0]
                    
                    resolved_at = current_time if status in ["resolved", "false_positive"] else None
                    resolved_by = random.choice(user_ids) if resolved_at else None

                    # Generate alert details
                    details = self._generate_alert_details(alert_type, risk_score, current_time)

                    # Add fraud alert to batch
                    alerts_to_insert.append({
                        "id": alert_id,
                        "user_id": user_id,
                        "item_id": item_id,
                        "auction_id": auction_id,
                        "alert_type": alert_type,
                        "severity": severity,
                        "risk_score": risk_score,
                        "details": json.dumps(details),
                        "status": status,
                        "created_at": current_time - timedelta(days=random.randint(1, 7)),
                        "resolved_at": resolved_at,
                        "resolved_by": resolved_by
                    })

                # Execute batch insert
                if alerts_to_insert:
                    conn.execute(text("""
                    INSERT INTO fraud_alerts (
                        id, user_id, item_id, auction_id, alert_type, severity,
                        risk_score, details, status, created_at, resolved_at, resolved_by
                    ) VALUES (
                        :id, :user_id, :item_id, :auction_id, :alert_type, :severity,
                        :risk_score, :details, :status, :created_at, :resolved_at, :resolved_by
                    )
                    """), alerts_to_insert)

                conn.commit()
                logger.info(f"Added {len(alerts_to_insert)} fraud alerts to the database.")
                
                return {
                    "count": len(alerts_to_insert),
                    "alert_types": list(set(alert["alert_type"] for alert in alerts_to_insert)),
                    "severity_distribution": self._get_severity_distribution(alerts_to_insert)
                }

            except SQLAlchemyError as e:
                conn.rollback()
                logger.error(f"Database error adding fraud alerts: {e}")
                raise
            except Exception as e:
                conn.rollback()
                logger.error(f"Error adding fraud alerts: {e}")
                raise

    def _calculate_severity(self, risk_score: float) -> str:
        """Calculate severity based on risk score."""
        if risk_score < 0.3:
            return "low"
        elif risk_score < 0.6:
            return "medium"
        elif risk_score < 0.8:
            return "high"
        else:
            return "critical"

    def _generate_alert_details(self, alert_type: str, risk_score: float, 
                              current_time: datetime) -> Dict[str, Any]:
        """Generate detailed information for the alert."""
        details = {
            "ip_address": f"192.168.{random.randint(1, 255)}.{random.randint(1, 255)}",
            "device_id": f"device_{uuid.uuid4().hex[:8]}",
            "timestamp": (current_time - timedelta(hours=random.randint(1, 24))).isoformat(),
            "confidence": risk_score,
            "triggers": random.sample(self.trigger_types, k=random.randint(1, 3))
        }

        # Add alert-type specific details
        if alert_type == "suspicious_login":
            details.update({
                "login_attempts": random.randint(3, 10),
                "failed_attempts": random.randint(1, 5),
                "unusual_location": random.choice([True, False])
            })
        elif alert_type == "suspicious_payment":
            details.update({
                "payment_amount": round(random.uniform(100, 5000), 2),
                "payment_method": random.choice(["credit_card", "bank_transfer", "digital_wallet"]),
                "velocity_check": random.choice(["failed", "warning", "passed"])
            })
        elif alert_type in ["suspicious_item", "suspicious_auction"]:
            details.update({
                "price_anomaly": random.choice([True, False]),
                "description_flags": random.randint(0, 3),
                "image_analysis": random.choice(["suspicious", "clean", "inconclusive"])
            })

        return details

    def _get_severity_distribution(self, alerts: List[Dict[str, Any]]) -> Dict[str, int]:
        """Get distribution of severity levels in the generated alerts."""
        distribution = {"low": 0, "medium": 0, "high": 0, "critical": 0}
        for alert in alerts:
            distribution[alert["severity"]] += 1
        return distribution
