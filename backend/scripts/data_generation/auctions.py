"""
Auction generation functionality.

This module provides the AuctionGenerator class for creating realistic
sample auctions in the database.
"""

import uuid
import random
import logging
from datetime import datetime, timedelta, UTC
from typing import Dict, Any
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from .base import BaseGenerator

logger = logging.getLogger(__name__)


class AuctionGenerator(BaseGenerator):
    """Generator for creating sample auctions."""

    def generate_auctions(self, batch_size: int = 5) -> Dict[str, Any]:
        """
        Generate a batch of auctions and add them to the database.

        Args:
            batch_size: Number of auctions to generate in this batch

        Returns:
            Dictionary with generation results
        """
        with self.engine.connect() as conn:
            try:
                # Get all item IDs that are not already in auctions or rentals
                item_ids = [row[0] for row in conn.execute(text("""
                    SELECT i.id FROM items i
                    LEFT JOIN auctions a ON i.id = a.item_id
                    LEFT JOIN rentals r ON i.id = r.item_id
                    WHERE a.id IS NULL AND r.id IS NULL
                    LIMIT :limit
                """), {"limit": batch_size}).fetchall()]

                if not item_ids:
                    logger.warning("No available items found for new auctions.")
                    return {"count": 0, "message": "No available items"}

                # Get all item details in a single query for better performance
                items_data = {}
                if item_ids:
                    items_result = conn.execute(text("""
                        SELECT id, owner_id, name, daily_price
                        FROM items
                        WHERE id IN :item_ids
                    """), {"item_ids": tuple(item_ids)}).fetchall()

                    for item in items_result:
                        items_data[item.id] = {
                            "owner_id": item.owner_id,
                            "name": item.name,
                            "daily_price": item.daily_price
                        }

                # Prepare batch of auctions to insert
                auctions_to_insert = []
                current_time = datetime.now(UTC)

                # Generate auctions for available items
                for item_id in item_ids:
                    auction_id = str(uuid.uuid4())

                    # Get item details from our cached data
                    item_data = items_data[item_id]
                    owner_id = item_data["owner_id"]
                    item_name = item_data["name"]
                    daily_price = item_data["daily_price"]

                    # Generate auction details
                    start_time = current_time + timedelta(hours=random.randint(1, 48))
                    duration = random.randint(3, 7)  # 3 to 7 days
                    end_time = start_time + timedelta(days=duration)

                    # Rental period starts after auction ends
                    rental_start_date = end_time + timedelta(days=1)
                    rental_duration = random.randint(3, 14)  # 3 to 14 days
                    rental_end_date = rental_start_date + timedelta(days=rental_duration)

                    # Set reserve price and minimum increment
                    reserve_price = daily_price * rental_duration * 0.8  # 80% of normal rental price
                    min_increment = max(5.0, reserve_price * 0.05)  # 5% of reserve price or $5, whichever is higher

                    # Add auction to batch
                    auctions_to_insert.append({
                        "id": auction_id,
                        "item_id": item_id,
                        "owner_id": owner_id,
                        "title": f"Auction for {item_name}",
                        "description": f"Bid on this {item_name} for a {rental_duration}-day rental starting on {rental_start_date.strftime('%Y-%m-%d')}.",
                        "start_time": start_time,
                        "end_time": end_time,
                        "reserve_price": reserve_price,
                        "min_increment": min_increment,
                        "current_highest_bid": 0.0,
                        "current_highest_bidder_id": None,
                        "status": "upcoming" if start_time > current_time else "active",
                        "rental_start_date": rental_start_date,
                        "rental_end_date": rental_end_date,
                        "created_at": current_time,
                        "updated_at": current_time
                    })

                # Execute batch insert
                if auctions_to_insert:
                    conn.execute(text("""
                    INSERT INTO auctions (
                        id, item_id, owner_id, title, description, start_time, end_time,
                        reserve_price, min_increment, current_highest_bid, current_highest_bidder_id,
                        status, rental_start_date, rental_end_date, created_at, updated_at
                    ) VALUES (
                        :id, :item_id, :owner_id, :title, :description, :start_time, :end_time,
                        :reserve_price, :min_increment, :current_highest_bid, :current_highest_bidder_id,
                        :status, :rental_start_date, :rental_end_date, :created_at, :updated_at
                    )
                    """), auctions_to_insert)

                conn.commit()
                logger.info(f"Added {len(auctions_to_insert)} auctions to the database.")
                
                return {
                    "count": len(auctions_to_insert),
                    "items_used": len(item_ids)
                }

            except SQLAlchemyError as e:
                conn.rollback()
                logger.error(f"Database error adding auctions: {e}")
                raise
            except Exception as e:
                conn.rollback()
                logger.error(f"Error adding auctions: {e}")
                raise
