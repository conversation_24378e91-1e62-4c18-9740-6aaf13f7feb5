"""
Bid generation functionality.

This module provides the BidGenerator class for creating realistic
sample bids in the database.
"""

import uuid
import random
import logging
from datetime import datetime, timedelta, UTC
from typing import Dict, Any
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from .base import BaseGenerator

logger = logging.getLogger(__name__)


class BidGenerator(BaseGenerator):
    """Generator for creating sample bids."""

    def generate_bids(self, batch_size: int = 15) -> Dict[str, Any]:
        """
        Generate a batch of bids and add them to the database.

        Args:
            batch_size: Number of bids to generate in this batch

        Returns:
            Dictionary with generation results
        """
        with self.engine.connect() as conn:
            try:
                # Get all user IDs
                user_ids = [row[0] for row in conn.execute(text("SELECT id FROM users")).fetchall()]

                # Get active auctions
                auctions = conn.execute(text("""
                    SELECT id, owner_id, reserve_price, min_increment, current_highest_bid
                    FROM auctions
                    WHERE status = 'active'
                """)).fetchall()

                if not auctions:
                    logger.warning("No active auctions found for new bids.")
                    return {"count": 0, "message": "No active auctions"}

                # Dictionary to track highest bids for each auction
                auction_highest_bids = {}

                # Prepare batch of bids to insert
                bids_to_insert = []
                auction_updates = []
                outbid_updates = []

                # Generate bids
                for i in range(batch_size):
                    bid_id = str(uuid.uuid4())

                    # Select a random auction
                    auction = random.choice(auctions)
                    auction_id = auction.id
                    owner_id = auction.owner_id
                    reserve_price = auction.reserve_price
                    min_increment = auction.min_increment
                    current_highest_bid = auction.current_highest_bid

                    # Get a bidder (not the owner)
                    available_bidders = [uid for uid in user_ids if uid != owner_id]
                    if not available_bidders:
                        continue
                    
                    bidder_id = random.choice(available_bidders)

                    # Get the current highest bid (either from our tracking dictionary or from the database)
                    tracked_highest_bid = auction_highest_bids.get(auction_id, current_highest_bid)
                    highest_bid = max(tracked_highest_bid, current_highest_bid)

                    # Calculate bid amount
                    if highest_bid < reserve_price:
                        # First bid should be at least the reserve price
                        bid_amount = reserve_price + (random.random() * min_increment)
                    else:
                        # Subsequent bids should be higher than current highest bid
                        bid_amount = highest_bid + (random.random() * min_increment * 2) + min_increment

                    bid_amount = round(bid_amount, 2)
                    placed_at = datetime.now(UTC) - timedelta(minutes=random.randint(1, 60))
                    current_time = datetime.now(UTC)

                    # Add bid to batch
                    bids_to_insert.append({
                        "id": bid_id,
                        "auction_id": auction_id,
                        "bidder_id": bidder_id,
                        "amount": bid_amount,
                        "status": "active",
                        "placed_at": placed_at,
                        "created_at": current_time,
                        "updated_at": current_time
                    })

                    # If this is the highest bid, prepare auction update
                    if bid_amount > current_highest_bid:
                        auction_updates.append({
                            "bid_amount": bid_amount,
                            "bidder_id": bidder_id,
                            "updated_at": current_time,
                            "auction_id": auction_id
                        })

                        # If there was a previous highest bid, prepare outbid update
                        if current_highest_bid > 0:
                            outbid_updates.append({
                                "updated_at": current_time,
                                "auction_id": auction_id,
                                "bid_id": bid_id
                            })

                        # Update our tracking dictionary
                        auction_highest_bids[auction_id] = bid_amount

                # Execute batch inserts and updates
                if bids_to_insert:
                    conn.execute(text("""
                    INSERT INTO bids (
                        id, auction_id, bidder_id, amount, status, placed_at, created_at, updated_at
                    ) VALUES (
                        :id, :auction_id, :bidder_id, :amount, :status, :placed_at, :created_at, :updated_at
                    )
                    """), bids_to_insert)

                # Update auctions with highest bids
                for update in auction_updates:
                    conn.execute(text("""
                    UPDATE auctions
                    SET current_highest_bid = :bid_amount,
                        current_highest_bidder_id = :bidder_id,
                        updated_at = :updated_at
                    WHERE id = :auction_id
                    """), update)

                # Update previous highest bids to outbid status
                for update in outbid_updates:
                    conn.execute(text("""
                    UPDATE bids
                    SET status = 'outbid', updated_at = :updated_at
                    WHERE auction_id = :auction_id AND status = 'active' AND id != :bid_id
                    """), update)

                conn.commit()
                logger.info(f"Added {len(bids_to_insert)} bids to the database.")
                
                return {
                    "count": len(bids_to_insert),
                    "auction_updates": len(auction_updates),
                    "outbid_updates": len(outbid_updates)
                }

            except SQLAlchemyError as e:
                conn.rollback()
                logger.error(f"Database error adding bids: {e}")
                raise
            except Exception as e:
                conn.rollback()
                logger.error(f"Error adding bids: {e}")
                raise
