"""
Agreement generation functionality.

This module provides the AgreementGenerator class for creating realistic
sample agreements in the database.
"""

import uuid
import random
import logging
from datetime import datetime, timedelta, UTC
from typing import Dict, Any
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from .base import BaseGenerator

logger = logging.getLogger(__name__)


class AgreementGenerator(BaseGenerator):
    """Generator for creating sample agreements."""

    def generate_agreements(self, batch_size: int = 10) -> Dict[str, Any]:
        """
        Generate a batch of agreements and add them to the database.

        Args:
            batch_size: Number of agreements to generate in this batch

        Returns:
            Dictionary with generation results
        """
        with self.engine.connect() as conn:
            try:
                # Get all rentals that don't have agreements yet
                rental_data = conn.execute(text("""
                    SELECT r.id, r.owner_id, r.renter_id, r.item_id, r.start_date, r.end_date, r.status, i.name
                    FROM rentals r
                    JOIN items i ON r.item_id = i.id
                    LEFT JOIN agreements a ON r.id = a.rental_id
                    WHERE a.id IS NULL
                    LIMIT :limit
                """), {"limit": batch_size}).fetchall()

                if not rental_data:
                    logger.warning("No available rentals found for new agreements.")
                    return {"count": 0, "message": "No available rentals"}

                # Prepare batch of agreements to insert
                agreements_to_insert = []
                current_time = datetime.now(UTC)

                # Generate agreements for rentals
                for rental in rental_data:
                    rental_id = rental.id
                    owner_id = rental.owner_id
                    renter_id = rental.renter_id
                    item_name = rental.name
                    start_date = rental.start_date
                    end_date = rental.end_date
                    rental_status = rental.status

                    # Calculate rental duration in days
                    rental_duration = (end_date - start_date).days

                    # Generate agreement content
                    agreement_content = self._generate_agreement_content(
                        item_name, start_date, end_date, rental_duration
                    )

                    # Determine agreement status based on rental status
                    agreement_status = self._determine_agreement_status(rental_status)

                    # Add agreement to batch
                    agreements_to_insert.append({
                        "id": str(uuid.uuid4()),
                        "rental_id": rental_id,
                        "owner_id": owner_id,
                        "renter_id": renter_id,
                        "agreement_type": "standard",
                        "content": agreement_content,
                        "status": agreement_status,
                        "created_at": current_time - timedelta(days=random.randint(1, 5)),
                        "updated_at": current_time
                    })

                # Execute batch insert
                if agreements_to_insert:
                    conn.execute(text("""
                    INSERT INTO agreements (
                        id, rental_id, owner_id, renter_id, agreement_type,
                        content, status, created_at, updated_at
                    ) VALUES (
                        :id, :rental_id, :owner_id, :renter_id, :agreement_type,
                        :content, :status, :created_at, :updated_at
                    )
                    """), agreements_to_insert)

                conn.commit()
                logger.info(f"Added {len(agreements_to_insert)} agreements to the database.")
                
                return {
                    "count": len(agreements_to_insert),
                    "rentals_processed": len(rental_data)
                }

            except SQLAlchemyError as e:
                conn.rollback()
                logger.error(f"Database error adding agreements: {e}")
                raise
            except Exception as e:
                conn.rollback()
                logger.error(f"Error adding agreements: {e}")
                raise

    def _generate_agreement_content(self, item_name: str, start_date: datetime, 
                                  end_date: datetime, rental_duration: int) -> str:
        """
        Generate agreement content for a rental.

        Args:
            item_name: Name of the item being rented
            start_date: Rental start date
            end_date: Rental end date
            rental_duration: Duration in days

        Returns:
            Generated agreement content
        """
        agreement_content = f"Rental Agreement for {item_name}\n\n"
        agreement_content += f"This agreement is made between the owner and the renter for the rental of {item_name}.\n"
        agreement_content += f"Rental period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')} ({rental_duration} days)\n\n"
        agreement_content += "Terms and Conditions:\n"
        agreement_content += "1. The renter agrees to pay the rental fee in full before the rental period begins.\n"
        agreement_content += "2. The renter agrees to return the item in the same condition as received.\n"
        agreement_content += "3. The owner reserves the right to charge additional fees for damages.\n"
        agreement_content += "4. The renter agrees to use the item for its intended purpose only.\n"
        agreement_content += "5. Late returns may incur additional charges.\n"
        agreement_content += "6. The renter is responsible for any loss or theft during the rental period.\n"
        
        return agreement_content

    def _determine_agreement_status(self, rental_status: str) -> str:
        """
        Determine agreement status based on rental status.

        Args:
            rental_status: Current rental status

        Returns:
            Appropriate agreement status
        """
        if rental_status == "pending":
            return "draft"
        elif rental_status == "confirmed":
            return "pending"
        else:
            return "active"
