"""
Core data generation functionality.

This module provides the main DataGenerator class that orchestrates
all data generation operations.
"""

import time
import logging
from typing import Dict, Any, Optional
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import settings

# Configure logging
logger = logging.getLogger(__name__)

# Import tqdm for progress bars
try:
    from tqdm import tqdm
except ImportError:
    # If tqdm is not installed, create a simple replacement
    def tqdm(iterable, *args, **kwargs):
        return iterable


class DataGenerator:
    """Main data generator class that orchestrates all data generation operations."""

    def __init__(self):
        """Initialize the data generator with database connection."""
        self.engine = create_engine(
            settings.DATABASE_URL,
            poolclass=QueuePool,
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=1800  # Recycle connections after 30 minutes
        )

    def add_data_with_progress(self, func, count: int, description: str):
        """
        Execute a data addition function with progress reporting.

        Args:
            func: Function to execute
            count: Number of times to execute
            description: Description for logging
        """
        start_time = time.time()
        logger.info(f"Adding {count} {description}...")

        try:
            for i in range(count):
                func()
                # Log progress for large batches
                if (i + 1) % 10 == 0 or i + 1 == count:
                    elapsed = time.time() - start_time
                    logger.info(f"Progress: {i + 1}/{count} {description} added ({elapsed:.2f}s elapsed)")
        except SQLAlchemyError as e:
            logger.error(f"Database error adding {description}: {e}")
            raise
        except Exception as e:
            logger.error(f"Error adding {description}: {e}")
            raise

    def generate_all_data(self, config: Dict[str, int]) -> Dict[str, Any]:
        """
        Generate all types of data based on configuration.

        Args:
            config: Dictionary with counts for each data type

        Returns:
            Dictionary with generation results and statistics
        """
        from .items import ItemGenerator
        from .auctions import AuctionGenerator
        from .bids import BidGenerator
        from .rentals import RentalGenerator
        from .agreements import AgreementGenerator
        from .fraud_alerts import FraudAlertGenerator

        results = {}
        total_start_time = time.time()

        try:
            logger.info("Starting comprehensive data generation...")

            # Initialize generators
            item_gen = ItemGenerator(self.engine)
            auction_gen = AuctionGenerator(self.engine)
            bid_gen = BidGenerator(self.engine)
            rental_gen = RentalGenerator(self.engine)
            agreement_gen = AgreementGenerator(self.engine)
            fraud_gen = FraudAlertGenerator(self.engine)

            # Generate items
            if config.get("items", 0) > 0:
                start_time = time.time()
                results["items"] = item_gen.generate_items(config["items"])
                results["items"]["time_taken"] = time.time() - start_time

            # Generate auctions
            if config.get("auctions", 0) > 0:
                start_time = time.time()
                auction_batches = config["auctions"] // 5 + (1 if config["auctions"] % 5 > 0 else 0)
                self.add_data_with_progress(
                    auction_gen.generate_auctions,
                    auction_batches,
                    "auction batches"
                )
                results["auctions"] = {
                    "requested": config["auctions"],
                    "batches": auction_batches,
                    "time_taken": time.time() - start_time
                }

            # Generate bids
            if config.get("bids", 0) > 0:
                start_time = time.time()
                bid_batches = config["bids"] // 15 + (1 if config["bids"] % 15 > 0 else 0)
                self.add_data_with_progress(
                    bid_gen.generate_bids,
                    bid_batches,
                    "bid batches"
                )
                results["bids"] = {
                    "requested": config["bids"],
                    "batches": bid_batches,
                    "time_taken": time.time() - start_time
                }

            # Generate rentals
            if config.get("rentals", 0) > 0:
                start_time = time.time()
                rental_batches = config["rentals"] // 10 + (1 if config["rentals"] % 10 > 0 else 0)
                self.add_data_with_progress(
                    rental_gen.generate_rentals,
                    rental_batches,
                    "rental batches"
                )
                results["rentals"] = {
                    "requested": config["rentals"],
                    "batches": rental_batches,
                    "time_taken": time.time() - start_time
                }

            # Generate agreements
            if config.get("agreements", 0) > 0:
                start_time = time.time()
                agreement_batches = config["agreements"] // 10 + (1 if config["agreements"] % 10 > 0 else 0)
                self.add_data_with_progress(
                    agreement_gen.generate_agreements,
                    agreement_batches,
                    "agreement batches"
                )
                results["agreements"] = {
                    "requested": config["agreements"],
                    "batches": agreement_batches,
                    "time_taken": time.time() - start_time
                }

            # Generate fraud alerts
            if config.get("fraud_alerts", 0) > 0:
                start_time = time.time()
                fraud_batches = config["fraud_alerts"] // 5 + (1 if config["fraud_alerts"] % 5 > 0 else 0)
                self.add_data_with_progress(
                    fraud_gen.generate_fraud_alerts,
                    fraud_batches,
                    "fraud alert batches"
                )
                results["fraud_alerts"] = {
                    "requested": config["fraud_alerts"],
                    "batches": fraud_batches,
                    "time_taken": time.time() - start_time
                }

            total_elapsed = time.time() - total_start_time
            results["total_time"] = total_elapsed

            logger.info(f"Successfully generated all data in {total_elapsed:.2f} seconds!")
            return results

        except Exception as e:
            logger.error(f"Error during data generation: {e}")
            raise

    def calculate_proportional_config(self, base_count: int) -> Dict[str, int]:
        """
        Calculate proportional data counts based on a base item count.

        Args:
            base_count: Base number of items to generate

        Returns:
            Dictionary with calculated counts for each data type
        """
        return {
            "items": base_count,
            "auctions": max(1, int(base_count * 0.2)),      # 20% of items become auctions
            "bids": max(1, int(base_count * 0.6)),          # 3 bids per auction on average
            "rentals": max(1, int(base_count * 0.4)),       # 40% of items get rented
            "agreements": max(1, int(base_count * 0.4)),    # One agreement per rental
            "fraud_alerts": max(1, int(base_count * 0.1))   # 10% of items get fraud alerts
        }


# Create singleton instance
data_generator = DataGenerator()
