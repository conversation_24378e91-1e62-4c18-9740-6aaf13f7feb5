"""
Rental generation functionality.

This module provides the RentalGenerator class for creating realistic
sample rentals in the database.
"""

import uuid
import random
import logging
from datetime import datetime, timedelta, UTC
from typing import Dict, Any
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from .base import BaseGenerator

logger = logging.getLogger(__name__)


class RentalGenerator(BaseGenerator):
    """Generator for creating sample rentals."""

    def generate_rentals(self, batch_size: int = 10) -> Dict[str, Any]:
        """
        Generate a batch of rentals and add them to the database.

        Args:
            batch_size: Number of rentals to generate in this batch

        Returns:
            Dictionary with generation results
        """
        with self.engine.connect() as conn:
            try:
                # Get all user IDs
                user_ids = [row[0] for row in conn.execute(text("SELECT id FROM users")).fetchall()]

                # Get all item IDs that are not already in rentals
                item_ids = [row[0] for row in conn.execute(text("""
                    SELECT i.id FROM items i
                    LEFT JOIN rentals r ON i.id = r.item_id
                    WHERE r.id IS NULL
                    LIMIT :limit
                """), {"limit": batch_size}).fetchall()]

                if not item_ids:
                    logger.warning("No available items found for new rentals.")
                    return {"count": 0, "message": "No available items"}

                # Get all item details in a single query for better performance
                items_data = {}
                if item_ids:
                    items_result = conn.execute(text("""
                        SELECT id, owner_id, daily_price
                        FROM items
                        WHERE id IN :item_ids
                    """), {"item_ids": tuple(item_ids)}).fetchall()

                    for item in items_result:
                        items_data[item.id] = {
                            "owner_id": item.owner_id,
                            "daily_price": item.daily_price
                        }

                # Prepare batch of rentals to insert
                rentals_to_insert = []
                current_time = datetime.now(UTC)

                # Generate rentals for available items
                for item_id in item_ids:
                    rental_id = str(uuid.uuid4())

                    # Get item details from our cached data
                    item_data = items_data[item_id]
                    owner_id = item_data["owner_id"]
                    daily_price = item_data["daily_price"]

                    # Get a renter (not the owner)
                    available_renters = [uid for uid in user_ids if uid != owner_id]
                    if not available_renters:
                        continue
                    
                    renter_id = random.choice(available_renters)

                    # Generate random dates
                    start_date = current_time + timedelta(days=random.randint(1, 14))
                    duration = random.randint(3, 14)  # 3 to 14 days
                    end_date = start_date + timedelta(days=duration)
                    total_price = daily_price * duration

                    # Determine status based on dates
                    if start_date > current_time + timedelta(days=7):
                        status = "pending"
                        payment_status = "pending"
                        deposit_paid = False
                    elif start_date > current_time:
                        status = "confirmed"
                        payment_status = "paid"
                        deposit_paid = True
                    elif end_date > current_time:
                        status = "active"
                        payment_status = "paid"
                        deposit_paid = True
                    else:
                        status = "completed"
                        payment_status = "paid"
                        deposit_paid = True

                    # Add rental to batch
                    rentals_to_insert.append({
                        "id": rental_id,
                        "item_id": item_id,
                        "renter_id": renter_id,
                        "owner_id": owner_id,
                        "start_date": start_date,
                        "end_date": end_date,
                        "total_price": total_price,
                        "status": status,
                        "payment_status": payment_status,
                        "payment_intent_id": f"pi_{uuid.uuid4()}" if payment_status == "paid" else None,
                        "notes": f"Notes for rental of item {item_id}" if random.random() < 0.5 else None,
                        "deposit_paid": deposit_paid,
                        "deposit_returned": status == "completed" and random.random() < 0.8,
                        "created_at": current_time - timedelta(days=random.randint(1, 10)),
                        "updated_at": current_time
                    })

                # Execute batch insert
                if rentals_to_insert:
                    conn.execute(text("""
                    INSERT INTO rentals (
                        id, item_id, renter_id, owner_id, start_date, end_date,
                        total_price, status, payment_status, payment_intent_id, notes,
                        deposit_paid, deposit_returned, created_at, updated_at
                    ) VALUES (
                        :id, :item_id, :renter_id, :owner_id, :start_date, :end_date,
                        :total_price, :status, :payment_status, :payment_intent_id, :notes,
                        :deposit_paid, :deposit_returned, :created_at, :updated_at
                    )
                    """), rentals_to_insert)

                conn.commit()
                logger.info(f"Added {len(rentals_to_insert)} rentals to the database.")
                
                return {
                    "count": len(rentals_to_insert),
                    "items_used": len(item_ids)
                }

            except SQLAlchemyError as e:
                conn.rollback()
                logger.error(f"Database error adding rentals: {e}")
                raise
            except Exception as e:
                conn.rollback()
                logger.error(f"Error adding rentals: {e}")
                raise
