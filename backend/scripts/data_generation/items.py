"""
Item generation functionality.

This module provides the ItemGenerator class for creating realistic
sample items in the database.
"""

import time
import uuid
import json
import random
import logging
from datetime import datetime, timedelta, UTC
from typing import Dict, List, Any
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError

from .base import BaseGenerator

logger = logging.getLogger(__name__)


class ItemGenerator(BaseGenerator):
    """Generator for creating sample items."""

    def __init__(self, engine):
        """Initialize the item generator."""
        super().__init__(engine)
        self._setup_item_data()

    def _setup_item_data(self):
        """Set up item categories, data, and other constants."""
        # Define categories and subcategories
        self.categories = {
            "Electronics": ["Smartphones", "Laptops", "Cameras", "Audio Equipment", "Gaming Consoles"],
            "Furniture": ["Sofas", "Tables", "Chairs", "Beds", "Storage"],
            "Vehicles": ["Cars", "Motorcycles", "Bicycles", "Scooters", "Boats"],
            "Tools": ["Power Tools", "Hand Tools", "Garden Tools", "Construction Equipment", "Measuring Tools"],
            "Sports": ["Fitness Equipment", "Outdoor Gear", "Water Sports", "Winter Sports", "Team Sports"]
        }

        # Define conditions
        self.conditions = ["new", "like_new", "good", "fair", "poor"]

        # Define locations
        self.locations = [
            "New York", "Los Angeles", "Chicago", "Houston", "Phoenix", 
            "Philadelphia", "San Antonio", "San Diego", "Dallas", "San Jose"
        ]

        # Sample image URLs (placeholder)
        self.image_urls = [
            "https://images.unsplash.com/photo-1505740420928-5e560c06d30e",
            "https://images.unsplash.com/photo-1523275335684-37898b6baf30",
            "https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f",
            "https://images.unsplash.com/photo-1485955900006-10f4d324d411",
            "https://images.unsplash.com/photo-1572635196237-14b3f281503f",
            "https://images.unsplash.com/photo-1581235720704-06d3acfcb36f",
            "https://images.unsplash.com/photo-1542291026-7eec264c27ff",
            "https://images.unsplash.com/photo-1560343090-f0409e92791a",
            "https://images.unsplash.com/photo-1496181133206-80ce9b88a853",
            "https://images.unsplash.com/photo-1507764923504-cd90bf7da772"
        ]

        # Item names and descriptions by category
        self.item_data = {
            "Electronics": [
                {"name": "iPhone 13 Pro", "description": "Latest iPhone model with 256GB storage and excellent camera."},
                {"name": "MacBook Pro 16", "description": "Powerful laptop with M1 Pro chip, perfect for professionals."},
                {"name": "Sony A7 III", "description": "Full-frame mirrorless camera with excellent low-light performance."},
                {"name": "Bose QuietComfort 45", "description": "Premium noise-cancelling headphones for immersive audio."},
                {"name": "PlayStation 5", "description": "Next-gen gaming console with fast SSD and ray tracing."}
            ],
            "Furniture": [
                {"name": "Leather Sectional Sofa", "description": "Comfortable L-shaped sofa with genuine leather upholstery."},
                {"name": "Solid Oak Dining Table", "description": "Beautiful handcrafted dining table that seats 6 people."},
                {"name": "Ergonomic Office Chair", "description": "Adjustable chair with lumbar support for all-day comfort."},
                {"name": "Queen Memory Foam Mattress", "description": "Premium mattress that adapts to your body for better sleep."},
                {"name": "Bookshelf with Storage", "description": "Modern bookshelf with additional storage compartments."}
            ],
            "Vehicles": [
                {"name": "Tesla Model 3", "description": "Electric sedan with autopilot features and long range."},
                {"name": "Harley-Davidson Street Glide", "description": "Powerful touring motorcycle with classic styling."},
                {"name": "Trek Domane SL 6", "description": "Carbon fiber road bike with electronic shifting."},
                {"name": "Xiaomi Mi Electric Scooter", "description": "Foldable electric scooter with 30km range."},
                {"name": "Sea-Doo GTI SE", "description": "3-person watercraft with 170hp engine for fun on the water."}
            ],
            "Tools": [
                {"name": "DeWalt Cordless Drill Set", "description": "20V MAX drill with multiple attachments and batteries."},
                {"name": "Craftsman Tool Set", "description": "Comprehensive 450-piece mechanics tool set with case."},
                {"name": "Honda Gas Lawn Mower", "description": "Self-propelled mower with variable speed control."},
                {"name": "Hilti Concrete Breaker", "description": "Professional-grade demolition hammer for construction."},
                {"name": "Fluke Digital Multimeter", "description": "Precision measuring tool for electrical work."}
            ],
            "Sports": [
                {"name": "Peloton Bike+", "description": "Interactive exercise bike with live and on-demand classes."},
                {"name": "Coleman 8-Person Tent", "description": "Spacious tent for family camping trips."},
                {"name": "Inflatable Stand-Up Paddleboard", "description": "Stable SUP board with paddle and pump included."},
                {"name": "Burton Custom Flying V Snowboard", "description": "All-mountain snowboard for intermediate to advanced riders."},
                {"name": "Wilson NCAA Official Basketball", "description": "Regulation size and weight basketball for indoor/outdoor use."}
            ]
        }

    def _generate_item_attributes(self, category: str) -> Dict[str, Any]:
        """Generate random attributes based on category."""
        if category == "Electronics":
            return {
                "brand": random.choice(["Apple", "Samsung", "Sony", "LG", "Dell"]),
                "color": random.choice(["Black", "White", "Silver", "Blue", "Red"]),
                "warranty": random.choice(["1 year", "2 years", "None"])
            }
        elif category == "Furniture":
            return {
                "material": random.choice(["Wood", "Leather", "Fabric", "Metal", "Glass"]),
                "color": random.choice(["Brown", "Black", "White", "Gray", "Beige"]),
                "assembly_required": random.choice(["Yes", "No"])
            }
        elif category == "Vehicles":
            return {
                "make": random.choice(["Toyota", "Honda", "Ford", "BMW", "Tesla"]),
                "year": random.randint(2015, 2023),
                "mileage": random.randint(0, 50000)
            }
        elif category == "Tools":
            return {
                "brand": random.choice(["DeWalt", "Milwaukee", "Bosch", "Makita", "Craftsman"]),
                "power_source": random.choice(["Cordless", "Corded", "Gas", "Manual"]),
                "weight": f"{random.randint(1, 20)} lbs"
            }
        elif category == "Sports":
            return {
                "brand": random.choice(["Nike", "Adidas", "Under Armour", "Wilson", "Coleman"]),
                "size": random.choice(["Small", "Medium", "Large", "One Size"]),
                "skill_level": random.choice(["Beginner", "Intermediate", "Advanced"])
            }
        return {}

    def _calculate_pricing(self, value: int) -> Dict[str, float]:
        """Calculate pricing based on item value."""
        daily_price = round(value * 0.01, 2)  # 1% of value per day
        weekly_price = round(daily_price * 6, 2)  # 6 days price for 7 days
        monthly_price = round(weekly_price * 3.5, 2)  # 3.5 weeks price for a month
        deposit_amount = round(value * 0.2, 2)  # 20% of value as deposit
        
        return {
            "daily_price": daily_price,
            "weekly_price": weekly_price,
            "monthly_price": monthly_price,
            "deposit_amount": deposit_amount
        }

    def generate_items(self, count: int = 25) -> Dict[str, Any]:
        """
        Generate items and add them to the database.

        Args:
            count: Number of items to generate

        Returns:
            Dictionary with generation results
        """
        start_time = time.time()

        with self.engine.connect() as conn:
            try:
                # Get all user IDs
                user_ids = [row[0] for row in conn.execute(text("SELECT id FROM users")).fetchall()]
                
                if not user_ids:
                    raise ValueError("No users found in database. Please add users first.")

                # Calculate total items to insert based on count parameter
                total_items = min(count, len(self.item_data) * 5)  # Limit to available item data
                items_per_category = total_items // len(self.item_data)

                logger.info(f"Preparing to insert {total_items} items ({items_per_category} per category)")

                # Prepare batch of items to insert
                items_to_insert = []

                # Generate items for each category
                for category, items in self.item_data.items():
                    for item in items[:items_per_category]:  # Limit items per category
                        item_id = str(uuid.uuid4())
                        subcategory = random.choice(self.categories[category])
                        condition = random.choice(self.conditions)
                        owner_id = random.choice(user_ids)
                        location = random.choice(self.locations)

                        # Generate random prices
                        value = random.randint(100, 2000)
                        pricing = self._calculate_pricing(value)

                        # Generate random image URLs (2-4 images per item)
                        num_images = random.randint(2, 4)
                        images = random.sample(self.image_urls, num_images)

                        # Generate random attributes based on category
                        attributes = self._generate_item_attributes(category)

                        items_to_insert.append({
                            "id": item_id,
                            "name": item["name"],
                            "description": item["description"],
                            "category": category,
                            "subcategory": subcategory,
                            "condition": condition,
                            "owner_id": owner_id,
                            "value": value,
                            "daily_price": pricing["daily_price"],
                            "weekly_price": pricing["weekly_price"],
                            "monthly_price": pricing["monthly_price"],
                            "deposit_amount": pricing["deposit_amount"],
                            "location": location,
                            "is_available": True,
                            "is_featured": random.random() < 0.2,  # 20% chance of being featured
                            "is_verified": random.random() < 0.5,  # 50% chance of being verified
                            "images": json.dumps(images),
                            "attributes": json.dumps(attributes),
                            "created_at": datetime.now(UTC) - timedelta(days=random.randint(1, 30)),
                            "updated_at": datetime.now(UTC)
                        })

                # Execute batch insert using executemany for better performance
                conn.execute(text("""
                INSERT INTO items (
                    id, name, description, category, subcategory, condition, owner_id,
                    value, daily_price, weekly_price, monthly_price, deposit_amount,
                    location, is_available, is_featured, is_verified, images, attributes,
                    created_at, updated_at
                ) VALUES (
                    :id, :name, :description, :category, :subcategory, :condition, :owner_id,
                    :value, :daily_price, :weekly_price, :monthly_price, :deposit_amount,
                    :location, :is_available, :is_featured, :is_verified, :images, :attributes,
                    :created_at, :updated_at
                )
                """), items_to_insert)

                # Commit the transaction
                conn.commit()

                # Calculate and log performance metrics
                end_time = time.time()
                elapsed_time = end_time - start_time
                items_per_second = len(items_to_insert) / elapsed_time

                logger.info(f"Added {len(items_to_insert)} items to the database in {elapsed_time:.2f} seconds ({items_per_second:.2f} items/sec)")
                
                return {
                    "count": len(items_to_insert),
                    "time_taken": elapsed_time,
                    "items_per_second": items_per_second
                }

            except SQLAlchemyError as e:
                conn.rollback()
                logger.error(f"Database error adding items: {e}")
                raise
            except Exception as e:
                conn.rollback()
                logger.error(f"Error adding items: {e}")
                raise
