"""
Base generator class for data generation modules.

This module provides the BaseGenerator class that contains common
functionality shared across all data generators.
"""

import logging
from typing import Any
from sqlalchemy import Engine

logger = logging.getLogger(__name__)


class BaseGenerator:
    """Base class for all data generators."""

    def __init__(self, engine: Engine):
        """
        Initialize the base generator.

        Args:
            engine: SQLAlchemy engine for database connections
        """
        self.engine = engine

    def log_performance(self, operation: str, count: int, elapsed_time: float):
        """
        Log performance metrics for data generation operations.

        Args:
            operation: Name of the operation
            count: Number of items processed
            elapsed_time: Time taken in seconds
        """
        items_per_second = count / elapsed_time if elapsed_time > 0 else 0
        logger.info(
            f"{operation}: {count} items in {elapsed_time:.2f}s "
            f"({items_per_second:.2f} items/sec)"
        )

    def validate_prerequisites(self, required_tables: list) -> bool:
        """
        Validate that required tables have data before generation.

        Args:
            required_tables: List of table names that must have data

        Returns:
            True if all prerequisites are met

        Raises:
            ValueError: If prerequisites are not met
        """
        with self.engine.connect() as conn:
            for table in required_tables:
                result = conn.execute(f"SELECT COUNT(*) FROM {table}").scalar()
                if result == 0:
                    raise ValueError(f"Table '{table}' is empty. Please populate it first.")
        return True
