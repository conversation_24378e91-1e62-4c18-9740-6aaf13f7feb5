# Data Generation Package

This package provides modular data generation functionality for the RentUp application. It allows you to populate the database with realistic sample data for testing and development purposes.

## Architecture

The package is organized into specialized modules:

- **`core.py`** - Main orchestration and coordination
- **`base.py`** - Base class with common functionality
- **`items.py`** - Item generation with categories, pricing, and attributes
- **`auctions.py`** - Auction generation with realistic timing and pricing
- **`bids.py`** - Bid generation with proper auction mechanics
- **`rentals.py`** - Rental generation with status management
- **`agreements.py`** - Agreement generation linked to rentals
- **`fraud_alerts.py`** - Fraud alert generation with risk scoring

## Features

### Modular Design
- Each generator is independent and focused on a single entity type
- Shared functionality is abstracted into the base class
- Easy to extend with new generators

### Realistic Data
- Items have proper categories, subcategories, and attributes
- Pricing follows realistic market patterns
- Auctions have proper timing and bidding mechanics
- Rentals have appropriate status transitions
- Fraud alerts include detailed risk analysis

### Performance Optimized
- Batch operations for database efficiency
- Connection pooling for better resource management
- Progress tracking for long-running operations
- Comprehensive error handling and rollback

### Configurable
- Flexible configuration options
- Proportional data generation
- Customizable batch sizes
- Detailed logging and reporting

## Usage

### Basic Usage

```python
from data_generation import DataGenerator

# Initialize generator
generator = DataGenerator()

# Generate proportional data
config = generator.calculate_proportional_config(100)  # 100 items + proportional others
results = generator.generate_all_data(config)

# Generate specific amounts
config = {
    "items": 50,
    "auctions": 10,
    "bids": 30,
    "rentals": 20,
    "agreements": 20,
    "fraud_alerts": 5
}
results = generator.generate_all_data(config)
```

### Individual Generators

```python
from data_generation import ItemGenerator, AuctionGenerator

# Use individual generators
item_gen = ItemGenerator(engine)
results = item_gen.generate_items(count=25)

auction_gen = AuctionGenerator(engine)
results = auction_gen.generate_auctions(batch_size=5)
```

### Command Line Interface

```bash
# Generate proportional data
python add_more_data_refactored.py --all 100

# Generate specific amounts
python add_more_data_refactored.py --items 50 --auctions 10 --bids 30

# Use defaults
python add_more_data_refactored.py
```

## Data Relationships

The generators respect database relationships and constraints:

1. **Items** are created first as the foundation
2. **Auctions** reference available items (not already in auctions/rentals)
3. **Bids** reference active auctions and exclude item owners
4. **Rentals** reference available items and exclude item owners
5. **Agreements** reference existing rentals
6. **Fraud Alerts** reference existing users, items, and auctions

## Configuration Options

### Proportional Generation
When using `--all` or `calculate_proportional_config()`:
- 20% of items become auctions
- 60% of items get bids (3 per auction average)
- 40% of items get rented
- 40% of items get agreements (one per rental)
- 10% of items trigger fraud alerts

### Batch Sizes
Default batch sizes for optimal performance:
- Items: Variable (based on request)
- Auctions: 5 per batch
- Bids: 15 per batch
- Rentals: 10 per batch
- Agreements: 10 per batch
- Fraud Alerts: 5 per batch

## Error Handling

The package includes comprehensive error handling:
- Database transaction rollback on errors
- Detailed error logging
- Graceful degradation when data is unavailable
- Validation of prerequisites before generation

## Performance Considerations

- Uses connection pooling for database efficiency
- Batch operations minimize database round trips
- Progress tracking for long-running operations
- Memory-efficient data structures
- Optimized queries with proper indexing

## Extending the Package

To add a new generator:

1. Create a new module in the package
2. Inherit from `BaseGenerator`
3. Implement the generation logic
4. Add to `__init__.py` exports
5. Update the main `DataGenerator` class

Example:
```python
from .base import BaseGenerator

class NewEntityGenerator(BaseGenerator):
    def generate_entities(self, batch_size: int = 10):
        # Implementation here
        pass
```
