#!/usr/bin/env python3
"""
Script to generate a security compliance report for the RentUp backend.

This script:
1. Checks compliance with security standards (OWASP, GDPR, etc.)
2. Generates a compliance report in Markdown format
3. Provides recommendations for improving compliance
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import json
import datetime
import subprocess
from typing import Dict, List, Any, Optional, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Generate security compliance report for RentUp backend')
    parser.add_argument('--audit-file', default='security_audit_report.json', help='Security audit report file')
    parser.add_argument('--test-file', default='security_test_report.json', help='Security test report file')
    parser.add_argument('--output', default='security_compliance_report.md', help='Output file for compliance report')
    parser.add_argument('--standards', nargs='+', default=['owasp', 'gdpr', 'pci'], help='Security standards to check compliance with')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def load_report_file(file_path):
    """Load a report file."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading report file {file_path}: {e}")
        return None

def check_owasp_compliance(audit_results, test_results):
    """Check compliance with OWASP Top 10."""
    logger.info("Checking compliance with OWASP Top 10...")
    
    # OWASP Top 10 categories
    owasp_categories = {
        "A01:2021-Broken Access Control": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "A02:2021-Cryptographic Failures": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "A03:2021-Injection": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "A04:2021-Insecure Design": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "A05:2021-Security Misconfiguration": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "A06:2021-Vulnerable and Outdated Components": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "A07:2021-Identification and Authentication Failures": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "A08:2021-Software and Data Integrity Failures": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "A09:2021-Security Logging and Monitoring Failures": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "A10:2021-Server-Side Request Forgery": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        }
    }
    
    # Check audit results
    if audit_results and "results" in audit_results:
        # Check for broken access control issues
        if "api" in audit_results["results"]:
            api_issues = audit_results["results"]["api"].get("issues", [])
            
            for issue in api_issues:
                if "unauthenticated endpoints" in issue or "missing authentication" in issue:
                    owasp_categories["A01:2021-Broken Access Control"]["compliant"] = False
                    owasp_categories["A01:2021-Broken Access Control"]["issues"].append(issue)
                    owasp_categories["A01:2021-Broken Access Control"]["recommendations"].append("Add authentication to all API endpoints")
        
        # Check for cryptographic failures
        if "database" in audit_results["results"]:
            db_issues = audit_results["results"]["database"].get("issues", [])
            
            for issue in db_issues:
                if "SSL is not enabled" in issue:
                    owasp_categories["A02:2021-Cryptographic Failures"]["compliant"] = False
                    owasp_categories["A02:2021-Cryptographic Failures"]["issues"].append(issue)
                    owasp_categories["A02:2021-Cryptographic Failures"]["recommendations"].append("Enable SSL for database connections")
                
                if "Password encryption" in issue and "scram-sha-256" not in issue:
                    owasp_categories["A02:2021-Cryptographic Failures"]["compliant"] = False
                    owasp_categories["A02:2021-Cryptographic Failures"]["issues"].append(issue)
                    owasp_categories["A02:2021-Cryptographic Failures"]["recommendations"].append("Set password encryption to scram-sha-256")
        
        # Check for injection issues
        if "code" in audit_results["results"]:
            code_issues = audit_results["results"]["code"].get("issues", [])
            
            for issue in code_issues:
                if "SQL injection" in issue:
                    owasp_categories["A03:2021-Injection"]["compliant"] = False
                    owasp_categories["A03:2021-Injection"]["issues"].append(issue)
                    owasp_categories["A03:2021-Injection"]["recommendations"].append("Use parameterized queries instead of string formatting")
        
        # Check for security misconfiguration
        if "database" in audit_results["results"]:
            db_issues = audit_results["results"]["database"].get("issues", [])
            
            for issue in db_issues:
                if "Public schema has excessive permissions" in issue:
                    owasp_categories["A05:2021-Security Misconfiguration"]["compliant"] = False
                    owasp_categories["A05:2021-Security Misconfiguration"]["issues"].append(issue)
                    owasp_categories["A05:2021-Security Misconfiguration"]["recommendations"].append("Revoke excessive permissions from public schema")
        
        # Check for vulnerable and outdated components
        if "dependencies" in audit_results["results"]:
            dep_issues = audit_results["results"]["dependencies"].get("issues", [])
            
            for issue in dep_issues:
                if "Vulnerable dependency" in issue or "Outdated dependency" in issue:
                    owasp_categories["A06:2021-Vulnerable and Outdated Components"]["compliant"] = False
                    owasp_categories["A06:2021-Vulnerable and Outdated Components"]["issues"].append(issue)
                    owasp_categories["A06:2021-Vulnerable and Outdated Components"]["recommendations"].append("Update dependencies to secure versions")
        
        # Check for identification and authentication failures
        if "api" in audit_results["results"]:
            api_issues = audit_results["results"]["api"].get("issues", [])
            
            for issue in api_issues:
                if "Missing X-Content-Type-Options" in issue or "Missing X-Frame-Options" in issue:
                    owasp_categories["A07:2021-Identification and Authentication Failures"]["compliant"] = False
                    owasp_categories["A07:2021-Identification and Authentication Failures"]["issues"].append(issue)
                    owasp_categories["A07:2021-Identification and Authentication Failures"]["recommendations"].append("Add security headers to API responses")
    
    # Check test results
    if test_results and "results" in test_results:
        # Check for broken access control issues
        if "auth" in test_results["results"]:
            auth_tests = test_results["results"]["auth"].get("tests", [])
            
            for test in auth_tests:
                if test["result"] == "fail" and "Access Control" in test["name"]:
                    owasp_categories["A01:2021-Broken Access Control"]["compliant"] = False
                    owasp_categories["A01:2021-Broken Access Control"]["issues"].append(test["details"])
                    owasp_categories["A01:2021-Broken Access Control"]["recommendations"].append("Implement proper role-based access control")
        
        # Check for cryptographic failures
        if "data" in test_results["results"]:
            data_tests = test_results["results"]["data"].get("tests", [])
            
            for test in data_tests:
                if test["result"] == "fail" and "Password Hashing" in test["name"]:
                    owasp_categories["A02:2021-Cryptographic Failures"]["compliant"] = False
                    owasp_categories["A02:2021-Cryptographic Failures"]["issues"].append(test["details"])
                    owasp_categories["A02:2021-Cryptographic Failures"]["recommendations"].append("Implement secure password hashing with bcrypt")
    
    # Calculate compliance percentage
    compliant_categories = sum(1 for category in owasp_categories.values() if category["compliant"])
    compliance_percentage = compliant_categories / len(owasp_categories) * 100
    
    return {
        "standard": "OWASP Top 10 (2021)",
        "compliance_percentage": compliance_percentage,
        "categories": owasp_categories
    }

def check_gdpr_compliance(audit_results, test_results):
    """Check compliance with GDPR."""
    logger.info("Checking compliance with GDPR...")
    
    # GDPR categories
    gdpr_categories = {
        "Data Protection by Design and Default": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "Data Subject Rights": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "Consent Management": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "Data Breach Notification": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "Data Protection Impact Assessment": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        }
    }
    
    # Check audit results
    if audit_results and "results" in audit_results:
        # Check for data protection issues
        if "database" in audit_results["results"]:
            db_issues = audit_results["results"]["database"].get("issues", [])
            
            for issue in db_issues:
                if "Row-level security" in issue:
                    gdpr_categories["Data Protection by Design and Default"]["compliant"] = False
                    gdpr_categories["Data Protection by Design and Default"]["issues"].append(issue)
                    gdpr_categories["Data Protection by Design and Default"]["recommendations"].append("Implement row-level security for sensitive tables")
        
        # Check for environment variable issues
        if "environment" in audit_results["results"]:
            env_issues = audit_results["results"]["environment"].get("issues", [])
            
            for issue in env_issues:
                if "Sensitive environment variable" in issue:
                    gdpr_categories["Data Protection by Design and Default"]["compliant"] = False
                    gdpr_categories["Data Protection by Design and Default"]["issues"].append(issue)
                    gdpr_categories["Data Protection by Design and Default"]["recommendations"].append("Store sensitive information securely")
    
    # Check test results
    if test_results and "results" in test_results:
        # Check for data protection issues
        if "data" in test_results["results"]:
            data_tests = test_results["results"]["data"].get("tests", [])
            
            for test in data_tests:
                if test["result"] == "fail" and "pgcrypto Extension" in test["name"]:
                    gdpr_categories["Data Protection by Design and Default"]["compliant"] = False
                    gdpr_categories["Data Protection by Design and Default"]["issues"].append(test["details"])
                    gdpr_categories["Data Protection by Design and Default"]["recommendations"].append("Install pgcrypto extension for database encryption")
    
    # Calculate compliance percentage
    compliant_categories = sum(1 for category in gdpr_categories.values() if category["compliant"])
    compliance_percentage = compliant_categories / len(gdpr_categories) * 100
    
    return {
        "standard": "General Data Protection Regulation (GDPR)",
        "compliance_percentage": compliance_percentage,
        "categories": gdpr_categories
    }

def check_pci_compliance(audit_results, test_results):
    """Check compliance with PCI DSS."""
    logger.info("Checking compliance with PCI DSS...")
    
    # PCI DSS categories
    pci_categories = {
        "Build and Maintain a Secure Network and Systems": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "Protect Cardholder Data": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "Maintain a Vulnerability Management Program": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "Implement Strong Access Control Measures": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "Regularly Monitor and Test Networks": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        },
        "Maintain an Information Security Policy": {
            "compliant": True,
            "issues": [],
            "recommendations": []
        }
    }
    
    # Check audit results
    if audit_results and "results" in audit_results:
        # Check for network security issues
        if "database" in audit_results["results"]:
            db_issues = audit_results["results"]["database"].get("issues", [])
            
            for issue in db_issues:
                if "SSL is not enabled" in issue:
                    pci_categories["Build and Maintain a Secure Network and Systems"]["compliant"] = False
                    pci_categories["Build and Maintain a Secure Network and Systems"]["issues"].append(issue)
                    pci_categories["Build and Maintain a Secure Network and Systems"]["recommendations"].append("Enable SSL for database connections")
        
        # Check for data protection issues
        if "code" in audit_results["results"]:
            code_issues = audit_results["results"]["code"].get("issues", [])
            
            for issue in code_issues:
                if "hardcoded secret" in issue:
                    pci_categories["Protect Cardholder Data"]["compliant"] = False
                    pci_categories["Protect Cardholder Data"]["issues"].append(issue)
                    pci_categories["Protect Cardholder Data"]["recommendations"].append("Replace hardcoded secrets with environment variables")
        
        # Check for vulnerability management issues
        if "dependencies" in audit_results["results"]:
            dep_issues = audit_results["results"]["dependencies"].get("issues", [])
            
            for issue in dep_issues:
                if "Vulnerable dependency" in issue:
                    pci_categories["Maintain a Vulnerability Management Program"]["compliant"] = False
                    pci_categories["Maintain a Vulnerability Management Program"]["issues"].append(issue)
                    pci_categories["Maintain a Vulnerability Management Program"]["recommendations"].append("Update dependencies to secure versions")
        
        # Check for access control issues
        if "database" in audit_results["results"]:
            db_issues = audit_results["results"]["database"].get("issues", [])
            
            for issue in db_issues:
                if "has no password set" in issue or "Multiple superuser accounts" in issue:
                    pci_categories["Implement Strong Access Control Measures"]["compliant"] = False
                    pci_categories["Implement Strong Access Control Measures"]["issues"].append(issue)
                    pci_categories["Implement Strong Access Control Measures"]["recommendations"].append("Implement strong password policies and limit superuser accounts")
    
    # Check test results
    if test_results and "results" in test_results:
        # Check for network security issues
        if "api" in test_results["results"]:
            api_tests = test_results["results"]["api"].get("tests", [])
            
            for test in api_tests:
                if test["result"] == "fail" and "Security Header" in test["name"]:
                    pci_categories["Build and Maintain a Secure Network and Systems"]["compliant"] = False
                    pci_categories["Build and Maintain a Secure Network and Systems"]["issues"].append(test["details"])
                    pci_categories["Build and Maintain a Secure Network and Systems"]["recommendations"].append("Add security headers to API responses")
        
        # Check for access control issues
        if "auth" in test_results["results"]:
            auth_tests = test_results["results"]["auth"].get("tests", [])
            
            for test in auth_tests:
                if test["result"] == "fail" and "Authentication" in test["name"]:
                    pci_categories["Implement Strong Access Control Measures"]["compliant"] = False
                    pci_categories["Implement Strong Access Control Measures"]["issues"].append(test["details"])
                    pci_categories["Implement Strong Access Control Measures"]["recommendations"].append("Implement proper authentication and authorization")
    
    # Calculate compliance percentage
    compliant_categories = sum(1 for category in pci_categories.values() if category["compliant"])
    compliance_percentage = compliant_categories / len(pci_categories) * 100
    
    return {
        "standard": "Payment Card Industry Data Security Standard (PCI DSS)",
        "compliance_percentage": compliance_percentage,
        "categories": pci_categories
    }

def generate_compliance_report(compliance_results, output_file):
    """Generate a compliance report in Markdown format."""
    logger.info(f"Generating compliance report: {output_file}")
    
    # Calculate overall compliance percentage
    total_percentage = sum(result["compliance_percentage"] for result in compliance_results) / len(compliance_results)
    
    # Generate report
    report = f"""# Security Compliance Report for RentUp Backend

**Generated:** {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Executive Summary

Overall compliance: **{total_percentage:.1f}%**

"""
    
    # Add compliance summary for each standard
    for result in compliance_results:
        report += f"- {result['standard']}: **{result['compliance_percentage']:.1f}%**\n"
    
    report += """
## Compliance Details

"""
    
    # Add details for each standard
    for result in compliance_results:
        report += f"### {result['standard']}\n\n"
        report += f"Compliance: **{result['compliance_percentage']:.1f}%**\n\n"
        
        # Add details for each category
        for category, details in result["categories"].items():
            status = "✅ Compliant" if details["compliant"] else "❌ Non-compliant"
            report += f"#### {category}: {status}\n\n"
            
            if not details["compliant"]:
                report += "**Issues:**\n\n"
                
                for issue in details["issues"]:
                    report += f"- {issue}\n"
                
                report += "\n**Recommendations:**\n\n"
                
                for recommendation in details["recommendations"]:
                    report += f"- {recommendation}\n"
            
            report += "\n"
    
    report += """
## Recommendations Summary

"""
    
    # Add summary of all recommendations
    all_recommendations = []
    
    for result in compliance_results:
        for category, details in result["categories"].items():
            if not details["compliant"]:
                for recommendation in details["recommendations"]:
                    if recommendation not in all_recommendations:
                        all_recommendations.append(recommendation)
    
    for recommendation in all_recommendations:
        report += f"- {recommendation}\n"
    
    # Write report to file
    with open(output_file, "w") as f:
        f.write(report)
    
    logger.info(f"Compliance report generated: {output_file}")

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load audit results
    audit_results = load_report_file(args.audit_file)
    
    if not audit_results:
        logger.error(f"Could not load audit results from {args.audit_file}")
        return 1
    
    # Load test results
    test_results = load_report_file(args.test_file)
    
    if not test_results:
        logger.error(f"Could not load test results from {args.test_file}")
        return 1
    
    # Check compliance with security standards
    compliance_results = []
    
    for standard in args.standards:
        if standard.lower() == "owasp":
            compliance_results.append(check_owasp_compliance(audit_results, test_results))
        elif standard.lower() == "gdpr":
            compliance_results.append(check_gdpr_compliance(audit_results, test_results))
        elif standard.lower() == "pci":
            compliance_results.append(check_pci_compliance(audit_results, test_results))
        else:
            logger.warning(f"Unknown security standard: {standard}")
    
    # Generate compliance report
    generate_compliance_report(compliance_results, args.output)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
