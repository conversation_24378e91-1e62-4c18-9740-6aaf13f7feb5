#!/usr/bin/env python3
"""
Script to generate a summary report of backup verifications.

This script:
1. Collects backup verification reports
2. Analyzes success/failure rates
3. Generates a summary report
4. Sends the report via email
"""

import os
import sys
import argparse
import logging
import json
import datetime
import glob
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Generate backup verification report summary')
    parser.add_argument('--report-dir', default='/var/log/rentup', help='Directory containing verification reports')
    parser.add_argument('--days', type=int, default=7, help='Number of days to include in summary')
    parser.add_argument('--email', help='Email address to send report to')
    parser.add_argument('--smtp-server', default='localhost', help='SMTP server for sending email')
    parser.add_argument('--smtp-port', type=int, default=25, help='SMTP port')
    parser.add_argument('--smtp-user', help='SMTP username')
    parser.add_argument('--smtp-password', help='SMTP password')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def collect_verification_reports(report_dir: str, days: int) -> List[Dict[str, Any]]:
    """Collect verification reports from the specified directory."""
    try:
        logger.info(f"Collecting verification reports from {report_dir} for the last {days} days")
        
        # Calculate the cutoff date
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)
        
        # Find all verification report files
        report_files = glob.glob(os.path.join(report_dir, "backup_verification_*.json"))
        
        # Filter reports by date
        reports = []
        for report_file in report_files:
            try:
                # Extract date from filename
                filename = os.path.basename(report_file)
                date_str = filename.replace("backup_verification_", "").replace(".json", "")
                file_date = datetime.datetime.strptime(date_str, "%Y%m%d")
                
                # Skip files older than cutoff date
                if file_date < cutoff_date:
                    continue
                
                # Read report file
                with open(report_file, "r") as f:
                    report = json.load(f)
                    report["file_date"] = file_date.strftime("%Y-%m-%d")
                    reports.append(report)
            except Exception as e:
                logger.warning(f"Error processing report file {report_file}: {str(e)}")
        
        # Sort reports by date
        reports.sort(key=lambda x: x.get("file_date", ""))
        
        logger.info(f"Collected {len(reports)} verification reports")
        return reports
    except Exception as e:
        logger.error(f"Error collecting verification reports: {str(e)}")
        return []

def analyze_reports(reports: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze verification reports and generate summary statistics."""
    try:
        logger.info("Analyzing verification reports")
        
        # Initialize summary
        summary = {
            "total_reports": len(reports),
            "successful_reports": 0,
            "failed_reports": 0,
            "success_rate": 0,
            "table_counts": [],
            "row_counts": [],
            "constraint_counts": [],
            "index_counts": [],
            "sequence_counts": [],
            "custom_checks": {},
            "daily_status": {}
        }
        
        # Process each report
        for report in reports:
            # Update status counts
            status = report.get("status", "unknown")
            if status == "success":
                summary["successful_reports"] += 1
            else:
                summary["failed_reports"] += 1
            
            # Update daily status
            file_date = report.get("file_date", "unknown")
            summary["daily_status"][file_date] = status
            
            # Skip failed reports for detailed analysis
            if status != "success":
                continue
            
            # Extract summary data
            report_summary = report.get("summary", {})
            
            # Update counts
            summary["table_counts"].append(report_summary.get("table_count", 0))
            summary["row_counts"].append(report_summary.get("total_rows", 0))
            summary["constraint_counts"].append(report_summary.get("constraint_count", 0))
            summary["index_counts"].append(report_summary.get("index_count", 0))
            summary["sequence_counts"].append(report_summary.get("sequence_count", 0))
            
            # Update custom checks
            custom_checks = report_summary.get("custom_checks", {})
            for check, value in custom_checks.items():
                if check not in summary["custom_checks"]:
                    summary["custom_checks"][check] = []
                summary["custom_checks"][check].append(value)
        
        # Calculate success rate
        if summary["total_reports"] > 0:
            summary["success_rate"] = (summary["successful_reports"] / summary["total_reports"]) * 100
        
        # Calculate averages
        summary["avg_table_count"] = sum(summary["table_counts"]) / len(summary["table_counts"]) if summary["table_counts"] else 0
        summary["avg_row_count"] = sum(summary["row_counts"]) / len(summary["row_counts"]) if summary["row_counts"] else 0
        summary["avg_constraint_count"] = sum(summary["constraint_counts"]) / len(summary["constraint_counts"]) if summary["constraint_counts"] else 0
        summary["avg_index_count"] = sum(summary["index_counts"]) / len(summary["index_counts"]) if summary["index_counts"] else 0
        summary["avg_sequence_count"] = sum(summary["sequence_counts"]) / len(summary["sequence_counts"]) if summary["sequence_counts"] else 0
        
        # Calculate averages for custom checks
        summary["avg_custom_checks"] = {}
        for check, values in summary["custom_checks"].items():
            summary["avg_custom_checks"][check] = sum(values) / len(values) if values else 0
        
        logger.info("Analysis completed")
        return summary
    except Exception as e:
        logger.error(f"Error analyzing verification reports: {str(e)}")
        return {"error": str(e)}

def generate_report_text(summary: Dict[str, Any]) -> str:
    """Generate a text report from the summary data."""
    try:
        logger.info("Generating report text")
        
        # Create report text
        report = f"""
Backup Verification Summary Report
=================================

Generated: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
Period: Last {args.days} days

Summary Statistics
-----------------
Total Reports: {summary.get("total_reports", 0)}
Successful Reports: {summary.get("successful_reports", 0)}
Failed Reports: {summary.get("failed_reports", 0)}
Success Rate: {summary.get("success_rate", 0):.2f}%

Database Statistics (Averages)
-----------------------------
Tables: {summary.get("avg_table_count", 0):.2f}
Total Rows: {summary.get("avg_row_count", 0):.2f}
Constraints: {summary.get("avg_constraint_count", 0):.2f}
Indexes: {summary.get("avg_index_count", 0):.2f}
Sequences: {summary.get("avg_sequence_count", 0):.2f}

Custom Checks (Averages)
-----------------------
"""
        
        # Add custom checks
        for check, value in summary.get("avg_custom_checks", {}).items():
            report += f"{check}: {value:.2f}\n"
        
        # Add daily status
        report += """
Daily Status
-----------
"""
        
        for date, status in sorted(summary.get("daily_status", {}).items()):
            status_symbol = "✅" if status == "success" else "❌"
            report += f"{date}: {status_symbol} {status}\n"
        
        # Add footer
        report += """
=================================
This report was generated automatically. Please do not reply to this email.
"""
        
        logger.info("Report text generated")
        return report
    except Exception as e:
        logger.error(f"Error generating report text: {str(e)}")
        return f"Error generating report: {str(e)}"

def send_email_report(report_text: str, email: str, smtp_server: str, smtp_port: int, smtp_user: Optional[str] = None, smtp_password: Optional[str] = None) -> bool:
    """Send the report via email."""
    try:
        logger.info(f"Sending report to {email}")
        
        # Create message
        msg = MIMEMultipart()
        msg["From"] = "<EMAIL>"
        msg["To"] = email
        msg["Subject"] = f"RentUp Backup Verification Summary - {datetime.datetime.now().strftime('%Y-%m-%d')}"
        
        # Attach report text
        msg.attach(MIMEText(report_text, "plain"))
        
        # Connect to SMTP server
        if smtp_user and smtp_password:
            # Use authentication
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(smtp_user, smtp_password)
        else:
            # No authentication
            server = smtplib.SMTP(smtp_server, smtp_port)
        
        # Send email
        server.send_message(msg)
        server.quit()
        
        logger.info("Report sent successfully")
        return True
    except Exception as e:
        logger.error(f"Error sending email: {str(e)}")
        return False

def main():
    """Main function."""
    global args
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Collect verification reports
        reports = collect_verification_reports(args.report_dir, args.days)
        
        if not reports:
            logger.warning("No verification reports found")
            return 1
        
        # Analyze reports
        summary = analyze_reports(reports)
        
        if "error" in summary:
            logger.error(f"Analysis failed: {summary['error']}")
            return 1
        
        # Generate report text
        report_text = generate_report_text(summary)
        
        # Print report to stdout
        print(report_text)
        
        # Send email report if requested
        if args.email:
            if send_email_report(report_text, args.email, args.smtp_server, args.smtp_port, args.smtp_user, args.smtp_password):
                logger.info(f"Report sent to {args.email}")
            else:
                logger.error(f"Failed to send report to {args.email}")
                return 1
        
        logger.info("Backup report summary completed successfully")
        return 0
    except Exception as e:
        logger.error(f"Backup report summary failed: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
