#!/usr/bin/env python3
"""
Production Deployment Verification Script for RentUp Backend (2025)

This script verifies that all production optimizations and security measures
are properly implemented and functioning correctly.
"""

import sys
import os
import asyncio
import logging
import json
import time
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('deployment_verification.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ProductionVerifier:
    """Comprehensive production deployment verification."""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results = {}
        self.start_time = datetime.now()

    def log_check(self, check_name: str, success: bool, details: str = ""):
        """Log verification check results."""
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{check_name}: {status} {details}")
        self.results[check_name] = {"success": success, "details": details}

    async def verify_security_implementations(self) -> bool:
        """Verify security implementations."""
        logger.info("🔒 Verifying security implementations...")

        all_passed = True

        # Check if security modules exist
        security_modules = [
            "app/core/auth_config.py",
            "app/core/rate_limiting.py",
            "app/middleware/security.py",
            "app/core/validation.py",
            "app/core/audit.py"
        ]

        for module in security_modules:
            module_path = Path(__file__).parent.parent / module
            exists = module_path.exists()
            self.log_check(f"Security module: {module}", exists)
            if not exists:
                all_passed = False

        return all_passed

    async def verify_optimization_modules(self) -> bool:
        """Verify database optimization modules."""
        logger.info("⚡ Verifying optimization modules...")

        all_passed = True

        # Check if optimization modules exist
        optimization_modules = [
            "app/core/query_optimization.py",
            "app/core/join_optimization.py",
            "app/core/query_cache.py",
            "app/core/db_config.py",
            "app/core/database_connection.py",
            "app/services/db_optimization_service.py"
        ]

        for module in optimization_modules:
            # Check in both backend/app and main app directories
            module_path1 = Path(__file__).parent.parent / module
            module_path2 = Path(__file__).parent.parent.parent / module
            exists = module_path1.exists() or module_path2.exists()
            self.log_check(f"Optimization module: {module}", exists)
            if not exists:
                all_passed = False

        # Test optimization modules functionality
        try:
            sys.path.insert(0, str(Path(__file__).parent.parent))

            # Test query cache
            from app.core.query_cache import QueryCache, CacheStrategy
            cache = QueryCache(strategy=CacheStrategy.SIMPLE)
            cache_working = hasattr(cache, 'get') and hasattr(cache, 'set')
            self.log_check("Query cache functionality", cache_working)

            # Test query optimization
            from app.core.query_optimization import QueryPerformanceTracker
            tracker = QueryPerformanceTracker()
            tracker_working = hasattr(tracker, 'track_query')
            self.log_check("Query performance tracker", tracker_working)

            # Test database config
            from app.core.db_config import DatabaseConfig
            config = DatabaseConfig()
            config_working = hasattr(config, 'url')
            self.log_check("Database configuration", config_working)

        except ImportError as e:
            self.log_check("Optimization modules import", False, str(e))
            all_passed = False
        except Exception as e:
            self.log_check("Optimization modules functionality", False, str(e))
            all_passed = False

        return all_passed

    async def verify_docker_configuration(self) -> bool:
        """Verify Docker production configuration."""
        logger.info("🐳 Verifying Docker configuration...")

        all_passed = True

        # Check Docker files
        docker_files = [
            "Dockerfile.prod",
            "docker-compose.prod.yml",
            "requirements-prod.txt"
        ]

        for docker_file in docker_files:
            file_path = Path(__file__).parent.parent / docker_file
            exists = file_path.exists()
            self.log_check(f"Docker file: {docker_file}", exists)
            if not exists:
                all_passed = False

        # Check production startup script
        startup_script = Path(__file__).parent / "start-prod.sh"
        script_exists = startup_script.exists()
        self.log_check("Production startup script", script_exists)
        if not script_exists:
            all_passed = False

        return all_passed

    async def verify_api_endpoints(self) -> bool:
        """Verify API endpoints are working."""
        logger.info("🌐 Verifying API endpoints...")

        all_passed = True

        try:
            # Test health endpoint
            response = requests.get(f"{self.base_url}/api/v1/health", timeout=10)
            health_ok = response.status_code == 200
            self.log_check("Health endpoint", health_ok, f"Status: {response.status_code}")
            if not health_ok:
                all_passed = False

            # Test API documentation
            docs_response = requests.get(f"{self.base_url}/docs", timeout=10)
            docs_ok = docs_response.status_code == 200
            self.log_check("API documentation", docs_ok, f"Status: {docs_response.status_code}")
            if not docs_ok:
                all_passed = False

        except requests.exceptions.ConnectionError:
            self.log_check("API connectivity", False, "Cannot connect to API server")
            all_passed = False
        except Exception as e:
            self.log_check("API endpoints", False, str(e))
            all_passed = False

        return all_passed

    async def verify_security_headers(self) -> bool:
        """Verify security headers are present."""
        logger.info("🛡️ Verifying security headers...")

        all_passed = True

        try:
            response = requests.get(f"{self.base_url}/api/v1/health", timeout=10)
            headers = response.headers

            # Expected security headers
            expected_headers = [
                "X-Frame-Options",
                "X-Content-Type-Options",
                "X-XSS-Protection",
                "Referrer-Policy",
                "Content-Security-Policy"
            ]

            for header in expected_headers:
                header_present = header in headers
                self.log_check(f"Security header: {header}", header_present)
                if not header_present:
                    all_passed = False

        except Exception as e:
            self.log_check("Security headers verification", False, str(e))
            all_passed = False

        return all_passed

    async def verify_test_coverage(self) -> bool:
        """Verify test coverage and functionality."""
        logger.info("🧪 Verifying test coverage...")

        all_passed = True

        # Check test files
        test_files = [
            "tests/test_optimization_modules.py",
            "tests/test_database_optimizations.py"
        ]

        for test_file in test_files:
            # Check in both backend/tests and main tests directories
            file_path1 = Path(__file__).parent.parent / test_file
            file_path2 = Path(__file__).parent.parent.parent / test_file
            exists = file_path1.exists() or file_path2.exists()
            self.log_check(f"Test file: {test_file}", exists)
            if not exists:
                all_passed = False

        return all_passed

    async def verify_google_adk_readiness(self) -> bool:
        """Verify Google ADK migration readiness."""
        logger.info("🤖 Verifying Google ADK migration readiness...")

        all_passed = True

        # Check ADK migration files
        adk_files = [
            "adk_migration_output/agents_analysis.json",
            "adk_migration_output/migration_plan.json"
        ]

        for adk_file in adk_files:
            file_path = Path(__file__).parent.parent / adk_file
            exists = file_path.exists()
            self.log_check(f"ADK migration file: {adk_file}", exists)
            if not exists:
                all_passed = False

        return all_passed

    def generate_verification_report(self) -> Dict[str, Any]:
        """Generate comprehensive verification report."""
        duration = datetime.now() - self.start_time

        total_checks = len(self.results)
        passed_checks = sum(1 for result in self.results.values() if result["success"])
        failed_checks = total_checks - passed_checks

        report = {
            "verification_timestamp": datetime.now().isoformat(),
            "duration_seconds": duration.total_seconds(),
            "summary": {
                "total_checks": total_checks,
                "passed_checks": passed_checks,
                "failed_checks": failed_checks,
                "success_rate": (passed_checks / total_checks * 100) if total_checks > 0 else 0
            },
            "detailed_results": self.results,
            "production_ready": failed_checks == 0
        }

        return report

async def main():
    """Main verification function."""
    import argparse

    parser = argparse.ArgumentParser(description='RentUp Production Deployment Verification')
    parser.add_argument('--base-url', default='http://localhost:8000', help='Base URL for API testing')
    parser.add_argument('--output-file', default='verification_report.json', help='Output file for verification report')

    args = parser.parse_args()

    verifier = ProductionVerifier(base_url=args.base_url)

    logger.info("🚀 Starting RentUp Production Deployment Verification...")

    # Run all verification checks
    security_ok = await verifier.verify_security_implementations()
    optimization_ok = await verifier.verify_optimization_modules()
    docker_ok = await verifier.verify_docker_configuration()
    api_ok = await verifier.verify_api_endpoints()
    headers_ok = await verifier.verify_security_headers()
    tests_ok = await verifier.verify_test_coverage()
    adk_ok = await verifier.verify_google_adk_readiness()

    # Generate report
    report = verifier.generate_verification_report()

    # Save report
    with open(args.output_file, 'w') as f:
        json.dump(report, f, indent=2)

    # Summary
    logger.info(f"✅ Verification completed in {report['duration_seconds']:.2f} seconds")
    logger.info(f"📊 Results: {report['summary']['passed_checks']}/{report['summary']['total_checks']} checks passed")
    logger.info(f"📈 Success rate: {report['summary']['success_rate']:.1f}%")

    if report["production_ready"]:
        logger.info("🎉 🚀 PRODUCTION READY! All verification checks passed.")
    else:
        logger.error("❌ Production deployment verification failed. Check the report for details.")

    logger.info(f"📄 Detailed report saved to: {args.output_file}")

    return 0 if report["production_ready"] else 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
