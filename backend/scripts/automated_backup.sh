#!/bin/bash
#
# Automated backup script for RentUp backend
# This script:
# 1. Creates a full database backup
# 2. Backs up file storage
# 3. Backs up configuration
# 4. Sends notifications
# 5. Manages backup retention
#
# Usage: ./automated_backup.sh [--full|--transaction-log]
#

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_TYPE=${1:-"--full"}
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="/var/backups/rentup"
LOG_FILE="/var/log/rentup/backup_${TIMESTAMP}.log"
NOTIFICATION_EMAIL="<EMAIL>"
S3_BUCKET="rentup-backups"
SLACK_WEBHOOK_URL="*****************************************************************************"

# Load environment variables
if [ -f "${SCRIPT_DIR}/../.env" ]; then
    source "${SCRIPT_DIR}/../.env"
fi

# Create backup directory if it doesn't exist
mkdir -p "${BACKUP_DIR}"
mkdir -p "$(dirname "${LOG_FILE}")"

# Start logging
exec > >(tee -a "${LOG_FILE}") 2>&1

echo "=== RentUp Backup Started at $(date) ==="
echo "Backup type: ${BACKUP_TYPE}"

# Function to send notifications
send_notification() {
    local status=$1
    local message=$2
    
    # Send email notification
    if [ -n "${NOTIFICATION_EMAIL}" ]; then
        echo "Sending email notification to ${NOTIFICATION_EMAIL}"
        echo "${message}" | mail -s "RentUp Backup ${status}" "${NOTIFICATION_EMAIL}"
    fi
    
    # Send Slack notification
    if [ -n "${SLACK_WEBHOOK_URL}" ]; then
        echo "Sending Slack notification"
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"RentUp Backup ${status}: ${message}\"}" \
            "${SLACK_WEBHOOK_URL}"
    fi
}

# Function to handle errors
handle_error() {
    local error_message=$1
    echo "ERROR: ${error_message}"
    send_notification "FAILED" "${error_message}"
    exit 1
}

# Backup database
backup_database() {
    echo "Starting database backup..."
    
    if [ "${BACKUP_TYPE}" == "--full" ]; then
        echo "Performing full database backup"
        python3 "${SCRIPT_DIR}/backup_database.py" \
            --backup-dir "${BACKUP_DIR}/database" \
            --encrypt \
            --upload \
            --bucket-name "${S3_BUCKET}" \
            --retention-days 30 \
            --verbose || handle_error "Database backup failed"
    elif [ "${BACKUP_TYPE}" == "--transaction-log" ]; then
        echo "Performing transaction log backup"
        # Add transaction log backup command here
        # This will depend on your specific database setup
        echo "Transaction log backup not implemented yet"
    else
        handle_error "Unknown backup type: ${BACKUP_TYPE}"
    fi
    
    echo "Database backup completed"
}

# Backup file storage
backup_file_storage() {
    echo "Starting file storage backup..."
    
    # Define file storage paths
    FILE_STORAGE_DIR="/var/lib/rentup/files"
    FILE_STORAGE_BACKUP="${BACKUP_DIR}/files/${TIMESTAMP}"
    
    # Create backup directory
    mkdir -p "${FILE_STORAGE_BACKUP}"
    
    # Backup files
    rsync -avz --delete "${FILE_STORAGE_DIR}/" "${FILE_STORAGE_BACKUP}/" || handle_error "File storage backup failed"
    
    # Upload to S3
    if [ -n "${S3_BUCKET}" ]; then
        echo "Uploading file storage backup to S3"
        aws s3 sync "${FILE_STORAGE_BACKUP}" "s3://${S3_BUCKET}/file_storage/${TIMESTAMP}" \
            --delete || handle_error "File storage upload to S3 failed"
    fi
    
    echo "File storage backup completed"
}

# Backup configuration
backup_configuration() {
    echo "Starting configuration backup..."
    
    # Define configuration paths
    CONFIG_DIR="${SCRIPT_DIR}/../config"
    CONFIG_BACKUP="${BACKUP_DIR}/config/${TIMESTAMP}"
    
    # Create backup directory
    mkdir -p "${CONFIG_BACKUP}"
    
    # Backup configuration files
    cp -r "${CONFIG_DIR}"/* "${CONFIG_BACKUP}/" || handle_error "Configuration backup failed"
    
    # Upload to S3
    if [ -n "${S3_BUCKET}" ]; then
        echo "Uploading configuration backup to S3"
        aws s3 sync "${CONFIG_BACKUP}" "s3://${S3_BUCKET}/config/${TIMESTAMP}" \
            --delete || handle_error "Configuration upload to S3 failed"
    fi
    
    echo "Configuration backup completed"
}

# Cleanup old backups
cleanup_old_backups() {
    echo "Cleaning up old backups..."
    
    # Keep the last 30 days of full backups
    find "${BACKUP_DIR}/database" -type d -mtime +30 -exec rm -rf {} \; 2>/dev/null || true
    
    # Keep the last 7 days of transaction log backups
    find "${BACKUP_DIR}/transaction_logs" -type f -mtime +7 -exec rm -f {} \; 2>/dev/null || true
    
    # Keep the last 30 days of file storage backups
    find "${BACKUP_DIR}/files" -type d -mtime +30 -exec rm -rf {} \; 2>/dev/null || true
    
    # Keep the last 30 days of configuration backups
    find "${BACKUP_DIR}/config" -type d -mtime +30 -exec rm -rf {} \; 2>/dev/null || true
    
    echo "Cleanup completed"
}

# Validate backups
validate_backups() {
    echo "Validating backups..."
    
    # Add validation logic here
    # For example, check if backup files exist and have expected size
    
    # For database backups, you might want to test restore in a sandbox environment
    
    echo "Validation completed"
}

# Main backup process
main() {
    # Record start time
    start_time=$(date +%s)
    
    # Perform backups
    if [ "${BACKUP_TYPE}" == "--full" ]; then
        backup_database
        backup_file_storage
        backup_configuration
    elif [ "${BACKUP_TYPE}" == "--transaction-log" ]; then
        backup_database
    fi
    
    # Cleanup old backups
    cleanup_old_backups
    
    # Validate backups
    validate_backups
    
    # Record end time and calculate duration
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    # Send success notification
    send_notification "SUCCESS" "Backup completed successfully in ${duration} seconds"
    
    echo "=== RentUp Backup Completed at $(date) ==="
    echo "Duration: ${duration} seconds"
}

# Run main function
main
