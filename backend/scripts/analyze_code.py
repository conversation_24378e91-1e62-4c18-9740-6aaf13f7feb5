#!/usr/bin/env python
"""
Code Analysis and Optimization Script for RentUp Backend.

This script analyzes the backend code for potential issues and optimizations,
including:
- Unused imports
- Unused variables
- Inefficient code patterns
- Memory leaks
- Security issues
- Performance bottlenecks
"""

import os
import sys
import re
import ast
import logging
import argparse
import subprocess
from typing import Dict, List, Any, Tuple, Set
from pathlib import Path
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"code_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Analyze and optimize RentUp backend code')
    parser.add_argument('--src-dir', type=str, default='app',
                        help='Source directory to analyze')
    parser.add_argument('--output-dir', type=str, default='code_analysis_results',
                        help='Directory to save results')
    parser.add_argument('--fix', action='store_true',
                        help='Automatically fix issues where possible')
    parser.add_argument('--skip-tests', action='store_true',
                        help='Skip running tests')
    return parser.parse_args()

def run_command(command: List[str]) -> Tuple[str, str, int]:
    """Run a command and return stdout, stderr, and return code."""
    process = subprocess.Popen(
        command,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True
    )
    stdout, stderr = process.communicate()
    return stdout, stderr, process.returncode

def run_linters(src_dir: str, output_dir: str):
    """Run various linters and save the results."""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Run flake8
    logger.info("Running flake8...")
    stdout, stderr, rc = run_command(['flake8', src_dir])
    with open(f"{output_dir}/flake8_results.txt", 'w') as f:
        f.write(stdout)
    logger.info(f"Flake8 found {stdout.count(os.linesep)} issues")
    
    # Run pylint
    logger.info("Running pylint...")
    stdout, stderr, rc = run_command(['pylint', src_dir])
    with open(f"{output_dir}/pylint_results.txt", 'w') as f:
        f.write(stdout)
    
    # Run bandit for security issues
    logger.info("Running bandit...")
    stdout, stderr, rc = run_command(['bandit', '-r', src_dir])
    with open(f"{output_dir}/bandit_results.txt", 'w') as f:
        f.write(stdout)
    
    # Run mypy for type checking
    logger.info("Running mypy...")
    stdout, stderr, rc = run_command(['mypy', src_dir])
    with open(f"{output_dir}/mypy_results.txt", 'w') as f:
        f.write(stdout)

def find_unused_imports(src_dir: str) -> Dict[str, List[str]]:
    """Find unused imports in Python files."""
    unused_imports = {}
    
    for root, _, files in os.walk(src_dir):
        for file in files:
            if not file.endswith('.py'):
                continue
            
            file_path = os.path.join(root, file)
            
            try:
                with open(file_path, 'r') as f:
                    file_content = f.read()
                
                tree = ast.parse(file_content)
                
                # Get all imports
                imports = []
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for name in node.names:
                            imports.append(name.name)
                    elif isinstance(node, ast.ImportFrom):
                        module = node.module
                        for name in node.names:
                            if name.name == '*':
                                continue
                            if module:
                                imports.append(f"{module}.{name.name}")
                            else:
                                imports.append(name.name)
                
                # Get all names used in the file
                used_names = set()
                for node in ast.walk(tree):
                    if isinstance(node, ast.Name):
                        used_names.add(node.id)
                    elif isinstance(node, ast.Attribute):
                        if isinstance(node.value, ast.Name):
                            used_names.add(f"{node.value.id}.{node.attr}")
                
                # Find unused imports
                file_unused_imports = []
                for imp in imports:
                    parts = imp.split('.')
                    if parts[0] not in used_names and imp not in used_names:
                        # Check if it's a module import (e.g., import os)
                        if len(parts) == 1 or (parts[0] + '.' + parts[1]) not in used_names:
                            file_unused_imports.append(imp)
                
                if file_unused_imports:
                    unused_imports[file_path] = file_unused_imports
            
            except Exception as e:
                logger.error(f"Error analyzing {file_path}: {e}")
    
    return unused_imports

def find_inefficient_patterns(src_dir: str) -> Dict[str, List[Dict[str, Any]]]:
    """Find inefficient code patterns."""
    inefficient_patterns = {}
    
    # Patterns to look for
    patterns = [
        {
            'name': 'List comprehension instead of for loop',
            'regex': r'result\s*=\s*\[\]\s*\n\s*for .+ in .+:\s*\n\s*result\.append\(.+\)',
            'suggestion': 'Use list comprehension: result = [expr for item in iterable]'
        },
        {
            'name': 'Inefficient string concatenation',
            'regex': r'(s|string|text|result)\s*\+=\s*.+\s*\n\s*(s|string|text|result)\s*\+=',
            'suggestion': 'Use str.join() or f-strings for multiple concatenations'
        },
        {
            'name': 'Multiple database queries in loop',
            'regex': r'for .+ in .+:\s*\n\s*.*(query|db\.|session\.|execute|cursor)',
            'suggestion': 'Use a single query with IN clause or JOIN instead of querying in a loop'
        },
        {
            'name': 'Redundant list/dict creation',
            'regex': r'(list|dict)\((list|dict)\(.+\)\)',
            'suggestion': 'Avoid redundant conversions'
        },
        {
            'name': 'Inefficient exception handling',
            'regex': r'try:\s*\n\s*.+\s*\n\s*except Exception:',
            'suggestion': 'Catch specific exceptions instead of generic Exception'
        }
    ]
    
    for root, _, files in os.walk(src_dir):
        for file in files:
            if not file.endswith('.py'):
                continue
            
            file_path = os.path.join(root, file)
            
            try:
                with open(file_path, 'r') as f:
                    file_content = f.read()
                
                file_issues = []
                
                for pattern in patterns:
                    matches = re.finditer(pattern['regex'], file_content, re.MULTILINE)
                    for match in matches:
                        file_issues.append({
                            'pattern': pattern['name'],
                            'line': file_content.count('\n', 0, match.start()) + 1,
                            'suggestion': pattern['suggestion'],
                            'code': match.group(0)
                        })
                
                if file_issues:
                    inefficient_patterns[file_path] = file_issues
            
            except Exception as e:
                logger.error(f"Error analyzing {file_path}: {e}")
    
    return inefficient_patterns

def find_security_issues(src_dir: str) -> Dict[str, List[Dict[str, Any]]]:
    """Find security issues in the code."""
    security_issues = {}
    
    # Patterns to look for
    patterns = [
        {
            'name': 'SQL Injection',
            'regex': r'execute\(\s*f["\']',
            'suggestion': 'Use parameterized queries instead of string formatting'
        },
        {
            'name': 'Hardcoded credentials',
            'regex': r'(password|secret|key|token)\s*=\s*["\'][^"\']+["\']',
            'suggestion': 'Use environment variables or a secure vault for credentials'
        },
        {
            'name': 'Insecure hash function',
            'regex': r'(md5|sha1)\(',
            'suggestion': 'Use secure hash functions like SHA-256 or bcrypt'
        },
        {
            'name': 'Debug mode in production',
            'regex': r'debug\s*=\s*True',
            'suggestion': 'Disable debug mode in production'
        },
        {
            'name': 'Insecure deserialization',
            'regex': r'pickle\.loads\(',
            'suggestion': 'Avoid using pickle for deserialization from untrusted sources'
        }
    ]
    
    for root, _, files in os.walk(src_dir):
        for file in files:
            if not file.endswith('.py'):
                continue
            
            file_path = os.path.join(root, file)
            
            try:
                with open(file_path, 'r') as f:
                    file_content = f.read()
                
                file_issues = []
                
                for pattern in patterns:
                    matches = re.finditer(pattern['regex'], file_content, re.MULTILINE)
                    for match in matches:
                        file_issues.append({
                            'pattern': pattern['name'],
                            'line': file_content.count('\n', 0, match.start()) + 1,
                            'suggestion': pattern['suggestion'],
                            'code': match.group(0)
                        })
                
                if file_issues:
                    security_issues[file_path] = file_issues
            
            except Exception as e:
                logger.error(f"Error analyzing {file_path}: {e}")
    
    return security_issues

def main():
    """Main function."""
    args = parse_args()
    
    # Create output directory
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # Run linters
    run_linters(args.src_dir, args.output_dir)
    
    # Find unused imports
    logger.info("Finding unused imports...")
    unused_imports = find_unused_imports(args.src_dir)
    with open(f"{args.output_dir}/unused_imports.txt", 'w') as f:
        for file_path, imports in unused_imports.items():
            f.write(f"{file_path}:\n")
            for imp in imports:
                f.write(f"  {imp}\n")
            f.write("\n")
    logger.info(f"Found unused imports in {len(unused_imports)} files")
    
    # Find inefficient patterns
    logger.info("Finding inefficient code patterns...")
    inefficient_patterns = find_inefficient_patterns(args.src_dir)
    with open(f"{args.output_dir}/inefficient_patterns.txt", 'w') as f:
        for file_path, issues in inefficient_patterns.items():
            f.write(f"{file_path}:\n")
            for issue in issues:
                f.write(f"  Line {issue['line']}: {issue['pattern']}\n")
                f.write(f"    Code: {issue['code']}\n")
                f.write(f"    Suggestion: {issue['suggestion']}\n")
            f.write("\n")
    logger.info(f"Found inefficient patterns in {len(inefficient_patterns)} files")
    
    # Find security issues
    logger.info("Finding security issues...")
    security_issues = find_security_issues(args.src_dir)
    with open(f"{args.output_dir}/security_issues.txt", 'w') as f:
        for file_path, issues in security_issues.items():
            f.write(f"{file_path}:\n")
            for issue in issues:
                f.write(f"  Line {issue['line']}: {issue['pattern']}\n")
                f.write(f"    Code: {issue['code']}\n")
                f.write(f"    Suggestion: {issue['suggestion']}\n")
            f.write("\n")
    logger.info(f"Found security issues in {len(security_issues)} files")
    
    # Run tests if not skipped
    if not args.skip_tests:
        logger.info("Running tests...")
        stdout, stderr, rc = run_command(['pytest', '-xvs'])
        with open(f"{args.output_dir}/test_results.txt", 'w') as f:
            f.write(stdout)
            f.write("\n\n")
            f.write(stderr)
        logger.info(f"Tests completed with return code {rc}")
    
    logger.info(f"Analysis complete. Results saved to {args.output_dir}/")

if __name__ == "__main__":
    main()
