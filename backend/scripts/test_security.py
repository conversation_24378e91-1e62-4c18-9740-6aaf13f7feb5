#!/usr/bin/env python3
"""
Script to test security measures implemented in the RentUp backend.

This script:
1. Tests database security measures
2. Tests API security measures
3. Tests authentication and authorization
4. Tests data protection measures
5. Generates a security test report
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import json
import subprocess
import re
import datetime
import requests
from typing import Dict, List, Any, Optional, Tuple

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import psycopg2
from psycopg2 import sql
import jwt
from sqlalchemy import text

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    from app.core.security import get_password_hash, verify_password, create_access_token
    from app.api.deps import get_current_user
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Test security measures in RentUp backend')
    parser.add_argument('--database', action='store_true', help='Test database security')
    parser.add_argument('--api', action='store_true', help='Test API security')
    parser.add_argument('--auth', action='store_true', help='Test authentication and authorization')
    parser.add_argument('--data', action='store_true', help='Test data protection')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    parser.add_argument('--api-url', default='http://localhost:8000', help='URL of the API server')
    parser.add_argument('--output', default='security_test_report.json', help='Output file for test report')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def get_connection_params():
    """Get database connection parameters."""
    if NEW_MODULES:
        # Use db_config
        db_host = db_config.POSTGRES_SERVER
        db_port = db_config.POSTGRES_PORT
        db_user = db_config.POSTGRES_USER
        db_password = db_config.POSTGRES_PASSWORD
        db_name = db_config.POSTGRES_DB
    else:
        # Use settings
        db_url = settings.DATABASE_URL
        # Parse DATABASE_URL
        match = re.match(r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
        if match:
            db_user, db_password, db_host, db_port, db_name = match.groups()
            db_port = int(db_port)
        else:
            logger.error("Could not parse DATABASE_URL")
            return None
    
    return {
        "host": db_host,
        "port": db_port,
        "user": db_user,
        "password": db_password,
        "dbname": db_name
    }

def test_database_security():
    """Test database security measures."""
    logger.info("Testing database security...")
    
    results = {
        "status": "success",
        "tests": [],
        "issues": []
    }
    
    try:
        # Get database connection parameters
        conn_params = get_connection_params()
        
        if not conn_params:
            results["status"] = "error"
            results["issues"].append("Could not get database connection parameters")
            return results
        
        # Connect to database
        conn = psycopg2.connect(**conn_params)
        
        # Test SSL connection
        with conn.cursor() as cur:
            cur.execute("SHOW ssl")
            ssl_enabled = cur.fetchone()[0] == "on"
            
            results["tests"].append({
                "name": "SSL Connection",
                "result": "pass" if ssl_enabled else "fail",
                "details": "SSL is enabled" if ssl_enabled else "SSL is not enabled"
            })
            
            if not ssl_enabled:
                results["issues"].append("SSL is not enabled for database connections")
        
        # Test password encryption
        with conn.cursor() as cur:
            cur.execute("SHOW password_encryption")
            password_encryption = cur.fetchone()[0]
            
            results["tests"].append({
                "name": "Password Encryption",
                "result": "pass" if password_encryption == "scram-sha-256" else "fail",
                "details": f"Password encryption is set to {password_encryption}"
            })
            
            if password_encryption != "scram-sha-256":
                results["issues"].append(f"Password encryption is set to {password_encryption}, which is not the strongest option")
        
        # Test public schema permissions
        with conn.cursor() as cur:
            cur.execute("SELECT nspname, nspacl FROM pg_namespace WHERE nspname = 'public'")
            public_schema = cur.fetchone()
            
            if public_schema and public_schema[1] is not None:
                excessive_permissions = 'everyone=UC' in public_schema[1]
                
                results["tests"].append({
                    "name": "Public Schema Permissions",
                    "result": "pass" if not excessive_permissions else "fail",
                    "details": "Public schema has appropriate permissions" if not excessive_permissions else "Public schema has excessive permissions"
                })
                
                if excessive_permissions:
                    results["issues"].append("Public schema has excessive permissions")
            else:
                results["tests"].append({
                    "name": "Public Schema Permissions",
                    "result": "warning",
                    "details": "Could not determine public schema permissions"
                })
        
        # Test row-level security
        with conn.cursor() as cur:
            cur.execute("""
                SELECT c.relname, c.relrowsecurity
                FROM pg_class c
                JOIN pg_namespace n ON c.relnamespace = n.oid
                WHERE c.relkind = 'r'
                AND n.nspname = 'public'
                AND c.relname IN ('users', 'items', 'rentals', 'payments', 'messages')
            """)
            tables = cur.fetchall()
            
            for table_name, has_rls in tables:
                results["tests"].append({
                    "name": f"Row-Level Security for {table_name}",
                    "result": "pass" if has_rls else "fail",
                    "details": f"Row-level security is enabled for {table_name}" if has_rls else f"Row-level security is not enabled for {table_name}"
                })
                
                if not has_rls:
                    results["issues"].append(f"Row-level security is not enabled for {table_name}")
        
        # Test database roles
        with conn.cursor() as cur:
            cur.execute("""
                SELECT rolname
                FROM pg_roles
                WHERE rolname IN ('rentup_readonly', 'rentup_readwrite', 'rentup_admin')
            """)
            roles = [row[0] for row in cur.fetchall()]
            
            expected_roles = ['rentup_readonly', 'rentup_readwrite', 'rentup_admin']
            missing_roles = [role for role in expected_roles if role not in roles]
            
            results["tests"].append({
                "name": "Database Roles",
                "result": "pass" if not missing_roles else "fail",
                "details": "All expected roles exist" if not missing_roles else f"Missing roles: {', '.join(missing_roles)}"
            })
            
            if missing_roles:
                results["issues"].append(f"Missing database roles: {', '.join(missing_roles)}")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"Error testing database security: {e}")
        results["status"] = "error"
        results["issues"].append(f"Error testing database security: {str(e)}")
    
    return results

def test_api_security(api_url):
    """Test API security measures."""
    logger.info("Testing API security...")
    
    results = {
        "status": "success",
        "tests": [],
        "issues": []
    }
    
    try:
        # Test security headers
        response = requests.get(f"{api_url}/docs", verify=False)
        
        # Expected security headers
        expected_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "Content-Security-Policy": None,  # Any value is acceptable
            "Strict-Transport-Security": None,  # Any value is acceptable
            "X-XSS-Protection": None,  # Any value is acceptable
            "Referrer-Policy": None  # Any value is acceptable
        }
        
        for header, expected_value in expected_headers.items():
            has_header = header in response.headers
            
            if expected_value is None:
                # Just check if header exists
                results["tests"].append({
                    "name": f"Security Header: {header}",
                    "result": "pass" if has_header else "fail",
                    "details": f"{header} header is present" if has_header else f"{header} header is missing"
                })
                
                if not has_header:
                    results["issues"].append(f"Missing security header: {header}")
            else:
                # Check if header has expected value
                has_expected_value = has_header and response.headers[header] == expected_value
                
                results["tests"].append({
                    "name": f"Security Header: {header}",
                    "result": "pass" if has_expected_value else "fail",
                    "details": f"{header} header has expected value" if has_expected_value else f"{header} header is missing or has unexpected value"
                })
                
                if not has_expected_value:
                    if has_header:
                        results["issues"].append(f"Unexpected value for {header} header: {response.headers[header]}")
                    else:
                        results["issues"].append(f"Missing security header: {header}")
        
        # Test CORS configuration
        response = requests.options(f"{api_url}/docs", headers={"Origin": "https://example.com"}, verify=False)
        
        has_cors_headers = "Access-Control-Allow-Origin" in response.headers
        
        results["tests"].append({
            "name": "CORS Configuration",
            "result": "pass" if has_cors_headers else "fail",
            "details": "CORS headers are present" if has_cors_headers else "CORS headers are missing"
        })
        
        if not has_cors_headers:
            results["issues"].append("Missing CORS headers")
        
        # Test authentication requirement
        response = requests.get(f"{api_url}/api/v1/users/me", verify=False)
        
        requires_auth = response.status_code == 401
        
        results["tests"].append({
            "name": "Authentication Requirement",
            "result": "pass" if requires_auth else "fail",
            "details": "API requires authentication" if requires_auth else "API does not require authentication"
        })
        
        if not requires_auth:
            results["issues"].append("API does not require authentication")
        
    except Exception as e:
        logger.error(f"Error testing API security: {e}")
        results["status"] = "error"
        results["issues"].append(f"Error testing API security: {str(e)}")
    
    return results

def test_authentication_authorization(api_url):
    """Test authentication and authorization."""
    logger.info("Testing authentication and authorization...")
    
    results = {
        "status": "success",
        "tests": [],
        "issues": []
    }
    
    try:
        # Test login endpoint
        login_data = {
            "username": "<EMAIL>",
            "password": "password123"
        }
        
        response = requests.post(f"{api_url}/api/v1/login/access-token", data=login_data, verify=False)
        
        has_login_endpoint = response.status_code != 404
        
        results["tests"].append({
            "name": "Login Endpoint",
            "result": "pass" if has_login_endpoint else "fail",
            "details": "Login endpoint exists" if has_login_endpoint else "Login endpoint does not exist"
        })
        
        if not has_login_endpoint:
            results["issues"].append("Login endpoint does not exist")
        
        # Test token validation
        if has_login_endpoint and response.status_code == 200:
            token = response.json().get("access_token")
            
            if token:
                # Test accessing protected endpoint with token
                response = requests.get(
                    f"{api_url}/api/v1/users/me",
                    headers={"Authorization": f"Bearer {token}"},
                    verify=False
                )
                
                token_works = response.status_code == 200
                
                results["tests"].append({
                    "name": "Token Validation",
                    "result": "pass" if token_works else "fail",
                    "details": "Token is validated correctly" if token_works else "Token validation fails"
                })
                
                if not token_works:
                    results["issues"].append("Token validation fails")
                
                # Test token expiration
                if NEW_MODULES:
                    # Create an expired token
                    from datetime import datetime, timedelta
                    
                    expire_time = datetime.utcnow() - timedelta(minutes=1)
                    expired_token = create_access_token(
                        subject="<EMAIL>",
                        expires_delta=timedelta(minutes=-1)
                    )
                    
                    # Test accessing protected endpoint with expired token
                    response = requests.get(
                        f"{api_url}/api/v1/users/me",
                        headers={"Authorization": f"Bearer {expired_token}"},
                        verify=False
                    )
                    
                    rejects_expired = response.status_code == 401
                    
                    results["tests"].append({
                        "name": "Token Expiration",
                        "result": "pass" if rejects_expired else "fail",
                        "details": "Expired tokens are rejected" if rejects_expired else "Expired tokens are accepted"
                    })
                    
                    if not rejects_expired:
                        results["issues"].append("Expired tokens are accepted")
        
        # Test role-based access control
        if NEW_MODULES:
            # Create tokens for different roles
            admin_token = create_access_token(
                subject="<EMAIL>",
                expires_delta=timedelta(minutes=30),
                scopes=["admin"]
            )
            
            user_token = create_access_token(
                subject="<EMAIL>",
                expires_delta=timedelta(minutes=30),
                scopes=["user"]
            )
            
            # Test accessing admin endpoint with admin token
            response = requests.get(
                f"{api_url}/api/v1/admin/users",
                headers={"Authorization": f"Bearer {admin_token}"},
                verify=False
            )
            
            admin_access = response.status_code != 403
            
            results["tests"].append({
                "name": "Admin Access Control",
                "result": "pass" if admin_access else "fail",
                "details": "Admin can access admin endpoints" if admin_access else "Admin cannot access admin endpoints"
            })
            
            if not admin_access:
                results["issues"].append("Admin cannot access admin endpoints")
            
            # Test accessing admin endpoint with user token
            response = requests.get(
                f"{api_url}/api/v1/admin/users",
                headers={"Authorization": f"Bearer {user_token}"},
                verify=False
            )
            
            user_restricted = response.status_code == 403
            
            results["tests"].append({
                "name": "User Access Control",
                "result": "pass" if user_restricted else "fail",
                "details": "User cannot access admin endpoints" if user_restricted else "User can access admin endpoints"
            })
            
            if not user_restricted:
                results["issues"].append("User can access admin endpoints")
        
    except Exception as e:
        logger.error(f"Error testing authentication and authorization: {e}")
        results["status"] = "error"
        results["issues"].append(f"Error testing authentication and authorization: {str(e)}")
    
    return results

def test_data_protection():
    """Test data protection measures."""
    logger.info("Testing data protection...")
    
    results = {
        "status": "success",
        "tests": [],
        "issues": []
    }
    
    try:
        # Test password hashing
        if NEW_MODULES:
            # Hash a password
            password = "test_password"
            hashed_password = get_password_hash(password)
            
            # Verify the password
            is_verified = verify_password(password, hashed_password)
            
            results["tests"].append({
                "name": "Password Hashing",
                "result": "pass" if is_verified else "fail",
                "details": "Password hashing works correctly" if is_verified else "Password hashing fails"
            })
            
            if not is_verified:
                results["issues"].append("Password hashing fails")
            
            # Check if hashed password contains algorithm identifier
            has_algorithm = "$2b$" in hashed_password  # bcrypt
            
            results["tests"].append({
                "name": "Password Hashing Algorithm",
                "result": "pass" if has_algorithm else "fail",
                "details": "Password is hashed with bcrypt" if has_algorithm else "Password is not hashed with bcrypt"
            })
            
            if not has_algorithm:
                results["issues"].append("Password is not hashed with bcrypt")
        
        # Test database encryption
        conn_params = get_connection_params()
        
        if conn_params:
            conn = psycopg2.connect(**conn_params)
            
            with conn.cursor() as cur:
                # Check if pgcrypto extension is installed
                cur.execute("""
                    SELECT 1
                    FROM pg_extension
                    WHERE extname = 'pgcrypto'
                """)
                has_pgcrypto = cur.fetchone() is not None
                
                results["tests"].append({
                    "name": "pgcrypto Extension",
                    "result": "pass" if has_pgcrypto else "fail",
                    "details": "pgcrypto extension is installed" if has_pgcrypto else "pgcrypto extension is not installed"
                })
                
                if not has_pgcrypto:
                    results["issues"].append("pgcrypto extension is not installed")
            
            conn.close()
        
        # Test environment variable security
        env_file = ".env"
        
        if os.path.exists(env_file):
            # Check file permissions
            file_stat = os.stat(env_file)
            file_mode = file_stat.st_mode & 0o777
            
            has_secure_permissions = file_mode <= 0o600
            
            results["tests"].append({
                "name": "Environment File Permissions",
                "result": "pass" if has_secure_permissions else "fail",
                "details": f"Environment file has secure permissions: {oct(file_mode)[2:]}" if has_secure_permissions else f"Environment file has insecure permissions: {oct(file_mode)[2:]}"
            })
            
            if not has_secure_permissions:
                results["issues"].append(f"Environment file has insecure permissions: {oct(file_mode)[2:]}")
        
    except Exception as e:
        logger.error(f"Error testing data protection: {e}")
        results["status"] = "error"
        results["issues"].append(f"Error testing data protection: {str(e)}")
    
    return results

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Determine which tests to run
    run_database = args.database or args.all
    run_api = args.api or args.all
    run_auth = args.auth or args.all
    run_data = args.data or args.all
    
    # If no tests specified, show help
    if not (run_database or run_api or run_auth or run_data):
        logger.info("No tests specified. Use --all or specify individual tests.")
        return 1
    
    # Initialize test results
    test_results = {
        "timestamp": datetime.datetime.now().isoformat(),
        "summary": {
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "warnings": 0,
            "errors": 0
        },
        "results": {}
    }
    
    # Run tests
    if run_database:
        test_results["results"]["database"] = test_database_security()
        
        # Update summary
        for test in test_results["results"]["database"]["tests"]:
            test_results["summary"]["total_tests"] += 1
            
            if test["result"] == "pass":
                test_results["summary"]["passed"] += 1
            elif test["result"] == "fail":
                test_results["summary"]["failed"] += 1
            elif test["result"] == "warning":
                test_results["summary"]["warnings"] += 1
        
        if test_results["results"]["database"]["status"] == "error":
            test_results["summary"]["errors"] += 1
    
    if run_api:
        test_results["results"]["api"] = test_api_security(args.api_url)
        
        # Update summary
        for test in test_results["results"]["api"]["tests"]:
            test_results["summary"]["total_tests"] += 1
            
            if test["result"] == "pass":
                test_results["summary"]["passed"] += 1
            elif test["result"] == "fail":
                test_results["summary"]["failed"] += 1
            elif test["result"] == "warning":
                test_results["summary"]["warnings"] += 1
        
        if test_results["results"]["api"]["status"] == "error":
            test_results["summary"]["errors"] += 1
    
    if run_auth:
        test_results["results"]["auth"] = test_authentication_authorization(args.api_url)
        
        # Update summary
        for test in test_results["results"]["auth"]["tests"]:
            test_results["summary"]["total_tests"] += 1
            
            if test["result"] == "pass":
                test_results["summary"]["passed"] += 1
            elif test["result"] == "fail":
                test_results["summary"]["failed"] += 1
            elif test["result"] == "warning":
                test_results["summary"]["warnings"] += 1
        
        if test_results["results"]["auth"]["status"] == "error":
            test_results["summary"]["errors"] += 1
    
    if run_data:
        test_results["results"]["data"] = test_data_protection()
        
        # Update summary
        for test in test_results["results"]["data"]["tests"]:
            test_results["summary"]["total_tests"] += 1
            
            if test["result"] == "pass":
                test_results["summary"]["passed"] += 1
            elif test["result"] == "fail":
                test_results["summary"]["failed"] += 1
            elif test["result"] == "warning":
                test_results["summary"]["warnings"] += 1
        
        if test_results["results"]["data"]["status"] == "error":
            test_results["summary"]["errors"] += 1
    
    # Save test results to file
    with open(args.output, "w") as f:
        json.dump(test_results, f, indent=2)
    
    logger.info(f"Security tests completed. Results saved to {args.output}")
    logger.info(f"Summary: {test_results['summary']['passed']} passed, {test_results['summary']['failed']} failed, {test_results['summary']['warnings']} warnings, {test_results['summary']['errors']} errors")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
