#!/usr/bin/env python3
"""
PostgreSQL database setup script for RentUp.

This script sets up the PostgreSQL database for RentUp:
1. Creates the database user if it doesn't exist
2. Creates the database if it doesn't exist
3. Grants necessary permissions to the user

Usage:
    python setup_postgres.py [--drop] [--user USER] [--password PASSWORD] [--db DB]

Options:
    --drop      Drop the database and user if they exist
    --user      Database username (default: rentup)
    --password  Database password (default: rentup_secure_password)
    --db        Database name (default: rentup)
    --host      Database host (default: localhost)
    --port      Database port (default: 5432)
"""

import sys
import os
import argparse
import logging
import psycopg2
from psycopg2 import sql
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Set up PostgreSQL database for RentUp')
    parser.add_argument('--drop', action='store_true', help='Drop the database and user if they exist')
    parser.add_argument('--user', default='rentup', help='Database username')
    parser.add_argument('--password', default='rentup_secure_password', help='Database password')
    parser.add_argument('--db', default='rentup', help='Database name')
    parser.add_argument('--host', default='localhost', help='Database host')
    parser.add_argument('--port', default=5432, type=int, help='Database port')
    return parser.parse_args()

def connect_to_postgres(host, port, user='postgres', password='postgres', database='postgres'):
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        logger.info(f"Connected to PostgreSQL database at {host}:{port}/{database}")
        return conn
    except psycopg2.Error as e:
        logger.error(f"Error connecting to PostgreSQL database: {e}")
        raise

def user_exists(conn, username):
    """Check if a user exists."""
    with conn.cursor() as cur:
        cur.execute("SELECT 1 FROM pg_roles WHERE rolname = %s", (username,))
        return cur.fetchone() is not None

def database_exists(conn, dbname):
    """Check if a database exists."""
    with conn.cursor() as cur:
        cur.execute("SELECT 1 FROM pg_database WHERE datname = %s", (dbname,))
        return cur.fetchone() is not None

def create_user(conn, username, password):
    """Create a database user."""
    try:
        with conn.cursor() as cur:
            # Create user if it doesn't exist
            if not user_exists(conn, username):
                cur.execute(
                    sql.SQL("CREATE USER {} WITH ENCRYPTED PASSWORD {}").format(
                        sql.Identifier(username), sql.Literal(password)
                    )
                )
                logger.info(f"Created user: {username}")
            else:
                # Update password for existing user
                cur.execute(
                    sql.SQL("ALTER USER {} WITH ENCRYPTED PASSWORD {}").format(
                        sql.Identifier(username), sql.Literal(password)
                    )
                )
                logger.info(f"Updated password for user: {username}")
    except psycopg2.Error as e:
        logger.error(f"Error creating user {username}: {e}")
        raise

def drop_user(conn, username):
    """Drop a database user."""
    try:
        with conn.cursor() as cur:
            if user_exists(conn, username):
                cur.execute(sql.SQL("DROP USER {}").format(sql.Identifier(username)))
                logger.info(f"Dropped user: {username}")
    except psycopg2.Error as e:
        logger.error(f"Error dropping user {username}: {e}")
        raise

def create_database(conn, dbname, owner):
    """Create a database."""
    try:
        with conn.cursor() as cur:
            # Create database if it doesn't exist
            if not database_exists(conn, dbname):
                cur.execute(
                    sql.SQL("CREATE DATABASE {} WITH OWNER = {}").format(
                        sql.Identifier(dbname), sql.Identifier(owner)
                    )
                )
                logger.info(f"Created database: {dbname}")
            else:
                # Change owner if database already exists
                cur.execute(
                    sql.SQL("ALTER DATABASE {} OWNER TO {}").format(
                        sql.Identifier(dbname), sql.Identifier(owner)
                    )
                )
                logger.info(f"Changed owner of database {dbname} to {owner}")
    except psycopg2.Error as e:
        logger.error(f"Error creating database {dbname}: {e}")
        raise

def drop_database(conn, dbname):
    """Drop a database."""
    try:
        with conn.cursor() as cur:
            if database_exists(conn, dbname):
                # Terminate all connections to the database
                cur.execute(
                    """
                    SELECT pg_terminate_backend(pg_stat_activity.pid)
                    FROM pg_stat_activity
                    WHERE pg_stat_activity.datname = %s
                    AND pid <> pg_backend_pid()
                    """,
                    (dbname,)
                )
                
                # Drop the database
                cur.execute(sql.SQL("DROP DATABASE {}").format(sql.Identifier(dbname)))
                logger.info(f"Dropped database: {dbname}")
    except psycopg2.Error as e:
        logger.error(f"Error dropping database {dbname}: {e}")
        raise

def grant_privileges(conn, dbname, username):
    """Grant privileges on database to user."""
    try:
        with conn.cursor() as cur:
            cur.execute(
                sql.SQL("GRANT ALL PRIVILEGES ON DATABASE {} TO {}").format(
                    sql.Identifier(dbname), sql.Identifier(username)
                )
            )
            logger.info(f"Granted privileges on {dbname} to {username}")
    except psycopg2.Error as e:
        logger.error(f"Error granting privileges on {dbname} to {username}: {e}")
        raise

def main():
    """Main function."""
    args = parse_args()
    
    # Connect to PostgreSQL
    conn = connect_to_postgres(args.host, args.port)
    
    try:
        # Drop database and user if requested
        if args.drop:
            drop_database(conn, args.db)
            drop_user(conn, args.user)
        
        # Create user and database
        create_user(conn, args.user, args.password)
        create_database(conn, args.db, args.user)
        grant_privileges(conn, args.db, args.user)
        
        logger.info(f"PostgreSQL setup completed successfully for database {args.db}")
        
        # Print connection string for reference
        conn_string = f"postgresql://{args.user}:{args.password}@{args.host}:{args.port}/{args.db}"
        logger.info(f"Connection string: {conn_string}")
        
    except Exception as e:
        logger.error(f"Error setting up PostgreSQL: {e}")
        sys.exit(1)
    finally:
        conn.close()

if __name__ == "__main__":
    main()
