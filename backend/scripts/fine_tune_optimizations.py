#!/usr/bin/env python3
"""
Script to fine-tune database optimizations based on monitoring data.

This script:
1. Analyzes monitoring data
2. Adjusts connection pool parameters
3. Tunes cache TTL based on data volatility
4. Optimizes query plan cache parameters
5. Balances read replica load
6. Provides recommendations for further optimization
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import json
import datetime
from typing import Dict, List, Any, Optional, Tuple, Set

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    from app.core.query_cache import query_cache, CACHE_CONFIG as QUERY_CACHE_CONFIG
    from app.core.query_plan_cache import CACHE_CONFIG as PLAN_CACHE_CONFIG
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Fine-tune database optimizations')
    parser.add_argument('--monitoring-dir', default='monitoring', help='Directory containing monitoring data')
    parser.add_argument('--apply', action='store_true', help='Apply recommended changes')
    parser.add_argument('--output', default='optimization_recommendations.json', help='Output file for recommendations')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def load_monitoring_data(monitoring_dir):
    """Load monitoring data from files."""
    monitoring_data = {}
    
    # Load each monitoring data type
    for data_type in ["connection_pool", "query_performance", "cache_performance", 
                      "replica_lag", "shard_balance", "system_resources"]:
        file_path = os.path.join(monitoring_dir, f"{data_type}.json")
        
        if os.path.exists(file_path):
            try:
                with open(file_path, "r") as f:
                    monitoring_data[data_type] = json.load(f)
            except Exception as e:
                logger.error(f"Error loading {data_type} data: {e}")
                monitoring_data[data_type] = []
        else:
            monitoring_data[data_type] = []
    
    return monitoring_data

def analyze_connection_pool(monitoring_data):
    """Analyze connection pool usage and recommend optimizations."""
    connection_pool_data = monitoring_data.get("connection_pool", [])
    
    if not connection_pool_data:
        return {
            "status": "no_data",
            "message": "No connection pool monitoring data available"
        }
    
    # Calculate average and peak usage
    total_size = 0
    total_checkedout = 0
    total_overflow = 0
    peak_checkedout = 0
    peak_overflow = 0
    
    for data in connection_pool_data:
        if data.get("status") == "success":
            size = data.get("size", 0)
            checkedout = data.get("checkedout", 0)
            overflow = data.get("overflow", 0)
            
            total_size += size
            total_checkedout += checkedout
            total_overflow += overflow
            
            peak_checkedout = max(peak_checkedout, checkedout)
            peak_overflow = max(peak_overflow, overflow)
    
    if not connection_pool_data:
        return {
            "status": "no_data",
            "message": "No valid connection pool monitoring data available"
        }
    
    avg_size = total_size / len(connection_pool_data)
    avg_checkedout = total_checkedout / len(connection_pool_data)
    avg_overflow = total_overflow / len(connection_pool_data)
    
    # Calculate utilization
    utilization = avg_checkedout / avg_size if avg_size > 0 else 0
    peak_utilization = peak_checkedout / avg_size if avg_size > 0 else 0
    
    # Generate recommendations
    recommendations = []
    
    if peak_utilization > 0.9:
        # High peak utilization - increase pool size
        recommended_size = int(avg_size * 1.5)
        recommendations.append({
            "parameter": "pool_size",
            "current_value": avg_size,
            "recommended_value": recommended_size,
            "reason": f"High peak utilization ({peak_utilization:.2f})"
        })
    elif peak_utilization < 0.5 and avg_size > 10:
        # Low peak utilization - decrease pool size
        recommended_size = max(10, int(avg_size * 0.8))
        recommendations.append({
            "parameter": "pool_size",
            "current_value": avg_size,
            "recommended_value": recommended_size,
            "reason": f"Low peak utilization ({peak_utilization:.2f})"
        })
    
    if peak_overflow > 0:
        # Overflow used - increase max_overflow
        recommended_overflow = int(peak_overflow * 1.5)
        recommendations.append({
            "parameter": "max_overflow",
            "current_value": avg_overflow,
            "recommended_value": recommended_overflow,
            "reason": f"Overflow connections used (peak: {peak_overflow})"
        })
    
    return {
        "status": "success",
        "avg_size": avg_size,
        "avg_checkedout": avg_checkedout,
        "avg_overflow": avg_overflow,
        "peak_checkedout": peak_checkedout,
        "peak_overflow": peak_overflow,
        "utilization": utilization,
        "peak_utilization": peak_utilization,
        "recommendations": recommendations
    }

def analyze_cache_performance(monitoring_data):
    """Analyze cache performance and recommend optimizations."""
    cache_performance_data = monitoring_data.get("cache_performance", [])
    
    if not cache_performance_data:
        return {
            "status": "no_data",
            "message": "No cache performance monitoring data available"
        }
    
    # Extract query cache statistics
    query_cache_stats = []
    plan_cache_stats = []
    
    for data in cache_performance_data:
        if data.get("status") == "success":
            query_cache = data.get("query_cache", {})
            plan_cache = data.get("plan_cache", {})
            
            if query_cache:
                query_cache_stats.append(query_cache)
            
            if plan_cache:
                plan_cache_stats.append(plan_cache)
    
    # Analyze query cache
    query_cache_recommendations = []
    
    if query_cache_stats:
        # Calculate average hit ratio
        total_hit_ratio = 0
        for stats in query_cache_stats:
            hit_ratio = stats.get("hit_ratio", 0)
            total_hit_ratio += hit_ratio
        
        avg_hit_ratio = total_hit_ratio / len(query_cache_stats) if query_cache_stats else 0
        
        # Generate recommendations
        if avg_hit_ratio < 0.5:
            # Low hit ratio - increase TTL
            current_ttl = getattr(QUERY_CACHE_CONFIG, "ttl", 3600) if NEW_MODULES else 3600
            recommended_ttl = current_ttl * 2
            
            query_cache_recommendations.append({
                "parameter": "ttl",
                "current_value": current_ttl,
                "recommended_value": recommended_ttl,
                "reason": f"Low hit ratio ({avg_hit_ratio:.2f})"
            })
    
    # Analyze plan cache
    plan_cache_recommendations = []
    
    if plan_cache_stats:
        # Calculate average hit ratio
        total_hit_ratio = 0
        for stats in plan_cache_stats:
            hit_ratio = stats.get("hit_ratio", 0)
            total_hit_ratio += hit_ratio
        
        avg_hit_ratio = total_hit_ratio / len(plan_cache_stats) if plan_cache_stats else 0
        
        # Generate recommendations
        if avg_hit_ratio < 0.5:
            # Low hit ratio - adjust min_execution_time
            current_min_time = getattr(PLAN_CACHE_CONFIG, "min_execution_time", 0.01) if NEW_MODULES else 0.01
            recommended_min_time = current_min_time / 2
            
            plan_cache_recommendations.append({
                "parameter": "min_execution_time",
                "current_value": current_min_time,
                "recommended_value": recommended_min_time,
                "reason": f"Low hit ratio ({avg_hit_ratio:.2f})"
            })
    
    return {
        "status": "success",
        "query_cache": {
            "avg_hit_ratio": avg_hit_ratio if query_cache_stats else None,
            "recommendations": query_cache_recommendations
        },
        "plan_cache": {
            "avg_hit_ratio": avg_hit_ratio if plan_cache_stats else None,
            "recommendations": plan_cache_recommendations
        }
    }

def analyze_query_performance(monitoring_data):
    """Analyze query performance and recommend optimizations."""
    query_performance_data = monitoring_data.get("query_performance", [])
    
    if not query_performance_data:
        return {
            "status": "no_data",
            "message": "No query performance monitoring data available"
        }
    
    # Extract slow queries
    slow_queries = {}
    
    for data in query_performance_data:
        if data.get("status") == "success":
            for query_data in data.get("slow_queries", []):
                query = query_data.get("query", "")
                
                if query not in slow_queries:
                    slow_queries[query] = []
                
                slow_queries[query].append({
                    "calls": query_data.get("calls", 0),
                    "total_time": query_data.get("total_time", 0),
                    "mean_time": query_data.get("mean_time", 0),
                    "rows": query_data.get("rows", 0)
                })
    
    # Analyze slow queries
    query_recommendations = []
    
    for query, stats_list in slow_queries.items():
        # Calculate average statistics
        total_calls = 0
        total_time = 0
        total_mean_time = 0
        total_rows = 0
        
        for stats in stats_list:
            total_calls += stats.get("calls", 0)
            total_time += stats.get("total_time", 0)
            total_mean_time += stats.get("mean_time", 0)
            total_rows += stats.get("rows", 0)
        
        avg_calls = total_calls / len(stats_list) if stats_list else 0
        avg_time = total_time / len(stats_list) if stats_list else 0
        avg_mean_time = total_mean_time / len(stats_list) if stats_list else 0
        avg_rows = total_rows / len(stats_list) if stats_list else 0
        
        # Generate recommendations
        if "JOIN" in query.upper():
            query_recommendations.append({
                "query": query[:100] + "..." if len(query) > 100 else query,
                "avg_mean_time": avg_mean_time,
                "avg_calls": avg_calls,
                "recommendation": "Optimize JOIN operation",
                "reason": "Slow JOIN query"
            })
        elif "ORDER BY" in query.upper():
            query_recommendations.append({
                "query": query[:100] + "..." if len(query) > 100 else query,
                "avg_mean_time": avg_mean_time,
                "avg_calls": avg_calls,
                "recommendation": "Add index for ORDER BY columns",
                "reason": "Slow ORDER BY query"
            })
        elif "GROUP BY" in query.upper():
            query_recommendations.append({
                "query": query[:100] + "..." if len(query) > 100 else query,
                "avg_mean_time": avg_mean_time,
                "avg_calls": avg_calls,
                "recommendation": "Add index for GROUP BY columns",
                "reason": "Slow GROUP BY query"
            })
        elif avg_calls > 100:
            query_recommendations.append({
                "query": query[:100] + "..." if len(query) > 100 else query,
                "avg_mean_time": avg_mean_time,
                "avg_calls": avg_calls,
                "recommendation": "Add query caching",
                "reason": "Frequently executed query"
            })
    
    return {
        "status": "success",
        "slow_query_count": len(slow_queries),
        "recommendations": query_recommendations
    }

def apply_recommendations(recommendations, db):
    """Apply recommended optimizations."""
    if not NEW_MODULES:
        logger.error("Cannot apply recommendations: optimization modules not available")
        return False
    
    # Apply connection pool recommendations
    connection_pool_recs = recommendations.get("connection_pool", {}).get("recommendations", [])
    
    for rec in connection_pool_recs:
        parameter = rec.get("parameter")
        value = rec.get("recommended_value")
        
        if parameter == "pool_size":
            db_config.pool_size = value
            logger.info(f"Updated connection pool size to {value}")
        elif parameter == "max_overflow":
            db_config.max_overflow = value
            logger.info(f"Updated connection pool max_overflow to {value}")
    
    # Apply query cache recommendations
    query_cache_recs = recommendations.get("cache_performance", {}).get("query_cache", {}).get("recommendations", [])
    
    for rec in query_cache_recs:
        parameter = rec.get("parameter")
        value = rec.get("recommended_value")
        
        if parameter == "ttl":
            QUERY_CACHE_CONFIG["ttl"] = value
            logger.info(f"Updated query cache TTL to {value}")
    
    # Apply plan cache recommendations
    plan_cache_recs = recommendations.get("cache_performance", {}).get("plan_cache", {}).get("recommendations", [])
    
    for rec in plan_cache_recs:
        parameter = rec.get("parameter")
        value = rec.get("recommended_value")
        
        if parameter == "min_execution_time":
            PLAN_CACHE_CONFIG["min_execution_time"] = value
            logger.info(f"Updated plan cache min_execution_time to {value}")
    
    return True

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load monitoring data
    monitoring_data = load_monitoring_data(args.monitoring_dir)
    
    # Analyze monitoring data
    recommendations = {
        "timestamp": datetime.datetime.now().isoformat(),
        "connection_pool": analyze_connection_pool(monitoring_data),
        "cache_performance": analyze_cache_performance(monitoring_data),
        "query_performance": analyze_query_performance(monitoring_data)
    }
    
    # Save recommendations to file
    with open(args.output, "w") as f:
        json.dump(recommendations, f, indent=2)
    
    logger.info(f"Saved optimization recommendations to {args.output}")
    
    # Apply recommendations if requested
    if args.apply:
        with session_scope() as db:
            if apply_recommendations(recommendations, db):
                logger.info("Applied optimization recommendations")
            else:
                logger.error("Failed to apply optimization recommendations")
    
    # Print summary
    logger.info("Optimization Recommendations Summary:")
    
    connection_pool_recs = recommendations.get("connection_pool", {}).get("recommendations", [])
    logger.info(f"  Connection Pool: {len(connection_pool_recs)} recommendations")
    for rec in connection_pool_recs:
        logger.info(f"    {rec.get('parameter')}: {rec.get('current_value')} -> {rec.get('recommended_value')} ({rec.get('reason')})")
    
    query_cache_recs = recommendations.get("cache_performance", {}).get("query_cache", {}).get("recommendations", [])
    logger.info(f"  Query Cache: {len(query_cache_recs)} recommendations")
    for rec in query_cache_recs:
        logger.info(f"    {rec.get('parameter')}: {rec.get('current_value')} -> {rec.get('recommended_value')} ({rec.get('reason')})")
    
    plan_cache_recs = recommendations.get("cache_performance", {}).get("plan_cache", {}).get("recommendations", [])
    logger.info(f"  Plan Cache: {len(plan_cache_recs)} recommendations")
    for rec in plan_cache_recs:
        logger.info(f"    {rec.get('parameter')}: {rec.get('current_value')} -> {rec.get('recommended_value')} ({rec.get('reason')})")
    
    query_recs = recommendations.get("query_performance", {}).get("recommendations", [])
    logger.info(f"  Query Performance: {len(query_recs)} recommendations")
    for rec in query_recs:
        logger.info(f"    {rec.get('recommendation')} ({rec.get('reason')})")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
