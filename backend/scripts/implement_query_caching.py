#!/usr/bin/env python3
"""
Script to implement query caching for RentUp.

This script:
1. Identifies frequently accessed data in the application
2. Implements caching for these queries
3. Adds cache invalidation mechanisms
4. Measures cache hit rates and performance gains
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import re
from typing import Dict, List, Set, Tuple, Any, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text, inspect, MetaData, Table, Column, ForeignKey
from sqlalchemy.orm import Session

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our new modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    from app.core.query_cache import query_cache, CacheStrategy, cached_query
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Implement query caching for RentUp')
    parser.add_argument('--analyze', action='store_true', help='Analyze query patterns')
    parser.add_argument('--implement', action='store_true', help='Implement caching')
    parser.add_argument('--test', action='store_true', help='Test caching performance')
    parser.add_argument('--all', action='store_true', help='Perform all steps')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def get_slow_queries(db: Session) -> List[Dict[str, Any]]:
    """
    Get slow queries from the database.
    
    Args:
        db: Database session
    
    Returns:
        List of slow query information
    """
    query = """
    SELECT
        query,
        calls,
        total_time,
        mean_time,
        rows
    FROM
        pg_stat_statements
    WHERE
        mean_time > 10  -- 10ms threshold
    ORDER BY
        mean_time DESC
    LIMIT 20
    """
    
    try:
        result = db.execute(text(query))
        
        slow_queries = []
        for row in result:
            slow_queries.append({
                "query": row.query,
                "calls": row.calls,
                "total_time": row.total_time,
                "mean_time": row.mean_time,
                "rows": row.rows
            })
        
        return slow_queries
    except Exception as e:
        logger.error(f"Error getting slow queries: {str(e)}")
        logger.info("Make sure pg_stat_statements extension is enabled")
        return []

def get_frequent_queries(db: Session) -> List[Dict[str, Any]]:
    """
    Get frequently executed queries from the database.
    
    Args:
        db: Database session
    
    Returns:
        List of frequent query information
    """
    query = """
    SELECT
        query,
        calls,
        total_time,
        mean_time,
        rows
    FROM
        pg_stat_statements
    WHERE
        calls > 100  -- 100 calls threshold
    ORDER BY
        calls DESC
    LIMIT 20
    """
    
    try:
        result = db.execute(text(query))
        
        frequent_queries = []
        for row in result:
            frequent_queries.append({
                "query": row.query,
                "calls": row.calls,
                "total_time": row.total_time,
                "mean_time": row.mean_time,
                "rows": row.rows
            })
        
        return frequent_queries
    except Exception as e:
        logger.error(f"Error getting frequent queries: {str(e)}")
        logger.info("Make sure pg_stat_statements extension is enabled")
        return []

def extract_tables_from_query(query: str) -> Set[str]:
    """
    Extract table names from a SQL query.
    
    Args:
        query: SQL query string
    
    Returns:
        Set of table names
    """
    tables = set()
    
    # Look for FROM and JOIN clauses
    parts = query.split()
    for i, part in enumerate(parts):
        if part.upper() in ("FROM", "JOIN") and i + 1 < len(parts):
            table = parts[i + 1].strip('",\'`[];()')
            if table and not table.upper() in ("SELECT", "WHERE", "GROUP", "ORDER", "HAVING", "LIMIT"):
                tables.add(table)
    
    return tables

def analyze_query_patterns(db: Session) -> Dict[str, Any]:
    """
    Analyze query patterns in the database.
    
    Args:
        db: Database session
    
    Returns:
        Dictionary of analysis results
    """
    results = {
        "slow_queries": [],
        "frequent_queries": [],
        "cacheable_tables": set(),
        "recommendations": []
    }
    
    # Get slow queries
    slow_queries = get_slow_queries(db)
    results["slow_queries"] = slow_queries
    
    # Get frequent queries
    frequent_queries = get_frequent_queries(db)
    results["frequent_queries"] = frequent_queries
    
    # Identify cacheable tables
    cacheable_tables = set()
    
    for query_info in slow_queries + frequent_queries:
        query = query_info["query"]
        tables = extract_tables_from_query(query)
        cacheable_tables.update(tables)
    
    results["cacheable_tables"] = cacheable_tables
    
    # Generate recommendations
    for table in cacheable_tables:
        results["recommendations"].append({
            "table": table,
            "recommendation": f"Implement caching for queries on {table}",
            "ttl": 300 if table in ("users", "items") else 3600  # 5 minutes for users/items, 1 hour for others
        })
    
    return results

def implement_caching(db: Session, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Implement caching based on analysis results.
    
    Args:
        db: Database session
        analysis_results: Analysis results
    
    Returns:
        Dictionary of implementation results
    """
    results = {
        "cached_tables": [],
        "cache_invalidation": [],
        "implementation_details": []
    }
    
    # Check if we have the query_cache module
    if not NEW_MODULES:
        logger.error("Query cache module not available")
        return results
    
    # Set up caching for recommended tables
    for recommendation in analysis_results["recommendations"]:
        table = recommendation["table"]
        ttl = recommendation["ttl"]
        
        # Add to results
        results["cached_tables"].append({
            "table": table,
            "ttl": ttl
        })
        
        # Add cache invalidation trigger
        results["cache_invalidation"].append({
            "table": table,
            "events": ["INSERT", "UPDATE", "DELETE"],
            "action": f"EXECUTE FUNCTION invalidate_cache('{table}')"
        })
        
        # Add implementation details
        results["implementation_details"].append({
            "table": table,
            "decorator": f"@cached_query(ttl={ttl})",
            "strategy": "SMART"
        })
    
    return results

def test_caching_performance(db: Session, implementation_results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Test caching performance.
    
    Args:
        db: Database session
        implementation_results: Implementation results
    
    Returns:
        Dictionary of test results
    """
    results = {
        "performance_gains": [],
        "cache_hit_rates": {},
        "overall_improvement": 0.0
    }
    
    # Check if we have the query_cache module
    if not NEW_MODULES:
        logger.error("Query cache module not available")
        return results
    
    # Test each cached table
    for cached_table in implementation_results["cached_tables"]:
        table = cached_table["table"]
        
        # Create a test query
        query = f"SELECT * FROM {table} LIMIT 100"
        
        # Measure uncached performance
        start_time = time.time()
        uncached_result = db.execute(text(query))
        uncached_rows = list(uncached_result)
        uncached_time = time.time() - start_time
        
        # Clear cache
        query_cache.invalidate_by_table(table)
        
        # Define a cached function
        @cached_query(ttl=60, strategy=CacheStrategy.SMART)
        def execute_cached_query(query_str):
            result = db.execute(text(query_str))
            return list(result)
        
        # Measure cached performance (first call - cache miss)
        start_time = time.time()
        cached_result_1 = execute_cached_query(query)
        cache_miss_time = time.time() - start_time
        
        # Measure cached performance (second call - cache hit)
        start_time = time.time()
        cached_result_2 = execute_cached_query(query)
        cache_hit_time = time.time() - start_time
        
        # Calculate improvement
        miss_improvement = (uncached_time - cache_miss_time) / uncached_time * 100
        hit_improvement = (uncached_time - cache_hit_time) / uncached_time * 100
        
        # Add to results
        results["performance_gains"].append({
            "table": table,
            "uncached_time": uncached_time,
            "cache_miss_time": cache_miss_time,
            "cache_hit_time": cache_hit_time,
            "miss_improvement": miss_improvement,
            "hit_improvement": hit_improvement
        })
        
        # Get cache hit rate
        cache_stats = query_cache.get_stats()
        results["cache_hit_rates"][table] = cache_stats["hit_ratio"]
    
    # Calculate overall improvement
    if results["performance_gains"]:
        total_hit_improvement = sum(item["hit_improvement"] for item in results["performance_gains"])
        results["overall_improvement"] = total_hit_improvement / len(results["performance_gains"])
    
    return results

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Determine what to do
    do_analyze = args.analyze or args.all
    do_implement = args.implement or args.all
    do_test = args.test or args.all
    
    # If no actions specified, show help
    if not (do_analyze or do_implement or do_test):
        logger.info("No actions specified. Use --analyze, --implement, --test, or --all")
        return 1
    
    with session_scope() as db:
        # Analyze query patterns
        if do_analyze:
            logger.info("Analyzing query patterns...")
            analysis_results = analyze_query_patterns(db)
            
            logger.info(f"Found {len(analysis_results['slow_queries'])} slow queries")
            logger.info(f"Found {len(analysis_results['frequent_queries'])} frequent queries")
            logger.info(f"Identified {len(analysis_results['cacheable_tables'])} cacheable tables")
            
            for recommendation in analysis_results["recommendations"]:
                logger.info(f"  {recommendation['recommendation']} (TTL: {recommendation['ttl']}s)")
        else:
            # Default analysis results
            analysis_results = {
                "recommendations": [
                    {"table": "users", "ttl": 300},
                    {"table": "items", "ttl": 300},
                    {"table": "rentals", "ttl": 3600},
                    {"table": "auctions", "ttl": 3600},
                    {"table": "bids", "ttl": 3600}
                ]
            }
        
        # Implement caching
        if do_implement:
            logger.info("Implementing caching...")
            implementation_results = implement_caching(db, analysis_results)
            
            logger.info(f"Implemented caching for {len(implementation_results['cached_tables'])} tables")
            for cached_table in implementation_results["cached_tables"]:
                logger.info(f"  {cached_table['table']} (TTL: {cached_table['ttl']}s)")
        else:
            # Default implementation results
            implementation_results = {
                "cached_tables": [
                    {"table": "users", "ttl": 300},
                    {"table": "items", "ttl": 300},
                    {"table": "rentals", "ttl": 3600},
                    {"table": "auctions", "ttl": 3600},
                    {"table": "bids", "ttl": 3600}
                ]
            }
        
        # Test caching performance
        if do_test and NEW_MODULES:
            logger.info("Testing caching performance...")
            test_results = test_caching_performance(db, implementation_results)
            
            logger.info("Performance gains:")
            for gain in test_results["performance_gains"]:
                logger.info(f"  {gain['table']}:")
                logger.info(f"    Uncached: {gain['uncached_time']:.6f}s")
                logger.info(f"    Cache miss: {gain['cache_miss_time']:.6f}s ({gain['miss_improvement']:.2f}% improvement)")
                logger.info(f"    Cache hit: {gain['cache_hit_time']:.6f}s ({gain['hit_improvement']:.2f}% improvement)")
            
            logger.info(f"Overall improvement: {test_results['overall_improvement']:.2f}%")
    
    logger.info("Query caching implementation completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
