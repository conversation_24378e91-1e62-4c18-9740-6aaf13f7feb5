# Database Optimization Scripts

This directory contains scripts for optimizing the RentUp database performance.

## Overview

These scripts are designed to improve database performance by:

1. Setting up and configuring PostgreSQL
2. Optimizing JOIN operations
3. Implementing query caching
4. Testing and measuring performance improvements

## Scripts

### 1. `setup_postgres.py`

Sets up the PostgreSQL database for RentUp.

```bash
# Create the database and user
python setup_postgres.py

# Drop and recreate the database and user
python setup_postgres.py --drop

# Customize database settings
python setup_postgres.py --user custom_user --password custom_password --db custom_db
```

### 2. `test_db_connection.py`

Tests the database connection and performs basic queries.

```bash
# Test the database connection
python test_db_connection.py

# Test with custom connection parameters
python test_db_connection.py --host localhost --port 5432 --user rentup --password rentup_secure_password --db rentup

# Enable verbose output
python test_db_connection.py --verbose
```

### 3. `optimize_joins.py`

Identifies and optimizes complex JOIN operations in the codebase.

```bash
# Analyze JOIN operations
python optimize_joins.py --analyze

# Create indexes for JOIN operations
python optimize_joins.py --create-indexes

# Test JOIN operation performance
python optimize_joins.py --test

# Perform all optimization steps
python optimize_joins.py --all
```

### 4. `implement_query_caching.py`

Implements query caching for frequently accessed data.

```bash
# Analyze query patterns
python implement_query_caching.py --analyze

# Implement caching
python implement_query_caching.py --implement

# Test caching performance
python implement_query_caching.py --test

# Perform all steps
python implement_query_caching.py --all
```

### 5. `apply_optimizations.py`

Applies database optimizations to the RentUp codebase and measures performance improvements.

```bash
# Test User model optimizations
python apply_optimizations.py --test-user

# Test Item model optimizations
python apply_optimizations.py --test-item

# Test all model optimizations
python apply_optimizations.py --test-all
```

## Usage

### Setting Up the Database

1. Install PostgreSQL if not already installed:

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install postgresql postgresql-contrib

# macOS with Homebrew
brew install postgresql
```

2. Set up the database:

```bash
python setup_postgres.py
```

3. Test the database connection:

```bash
python test_db_connection.py
```

### Optimizing JOIN Operations

1. Analyze JOIN operations:

```bash
python optimize_joins.py --analyze
```

2. Create indexes for JOIN operations:

```bash
python optimize_joins.py --create-indexes
```

3. Test JOIN operation performance:

```bash
python optimize_joins.py --test
```

### Implementing Query Caching

1. Analyze query patterns:

```bash
python implement_query_caching.py --analyze
```

2. Implement caching:

```bash
python implement_query_caching.py --implement
```

3. Test caching performance:

```bash
python implement_query_caching.py --test
```

### Applying Optimizations

1. Test optimizations:

```bash
python apply_optimizations.py --test-all
```

## Performance Monitoring

The scripts generate performance metrics that can be used to monitor the effectiveness of the optimizations:

- JOIN operation performance before and after optimization
- Query caching hit rates and performance improvements
- Overall database performance improvements

## Troubleshooting

### Database Connection Issues

If you encounter database connection issues:

1. Verify PostgreSQL is running:

```bash
# Ubuntu/Debian
sudo systemctl status postgresql

# macOS with Homebrew
brew services list
```

2. Check connection parameters:

```bash
python test_db_connection.py --verbose
```

3. Ensure the database and user exist:

```bash
sudo -u postgres psql -c "\l"  # List databases
sudo -u postgres psql -c "\du"  # List users
```

### Optimization Issues

If optimizations are not working as expected:

1. Check if the optimization modules are available:

```python
from app.services.db_optimization_service import OPTIMIZATION_MODULES_AVAILABLE
print(f"Optimization modules available: {OPTIMIZATION_MODULES_AVAILABLE}")
```

2. Verify the database indexes:

```bash
python optimize_joins.py --analyze
```

3. Check the cache hit rates:

```bash
python implement_query_caching.py --test
```

## Further Reading

For more information on database optimizations, see:

- [Database Optimizations Documentation](../docs/optimization/database_optimizations.md)
- [Query Batching Documentation](../docs/optimization/query_batching.md)
- [Redis Caching Documentation](../docs/optimization/redis_caching.md)
