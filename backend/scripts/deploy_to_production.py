#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to automate the deployment of RentUp backend to production.

This script:
1. Validates the production environment
2. Sets up the database infrastructure
3. Configures read replicas and sharding
4. Deploys the application
5. Sets up monitoring
6. Performs post-deployment tests
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import json
import subprocess
import shutil
from typing import Dict, List, Any, Optional, Tuple

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Deploy RentUp backend to production')
    parser.add_argument('--env-file', default='.env.prod', help='Environment file for production')
    parser.add_argument('--skip-db-setup', action='store_true', help='Skip database setup')
    parser.add_argument('--skip-app-deploy', action='store_true', help='Skip application deployment')
    parser.add_argument('--skip-monitoring', action='store_true', help='Skip monitoring setup')
    parser.add_argument('--docker-compose-file', default='docker-compose.prod.yml', help='Docker Compose file for production')
    parser.add_argument('--kubernetes', action='store_true', help='Deploy to Kubernetes instead of Docker Compose')
    parser.add_argument('--k8s-namespace', default='rentup', help='Kubernetes namespace')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def load_env_file(env_file):
    """Load environment variables from file."""
    env_vars = {}
    
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    key, value = line.split('=', 1)
                    env_vars[key] = value
    
    return env_vars

def validate_environment(env_vars):
    """Validate the production environment."""
    logger.info("Validating production environment...")
    
    required_vars = [
        "POSTGRES_SERVER",
        "POSTGRES_USER",
        "POSTGRES_PASSWORD",
        "POSTGRES_DB"
    ]
    
    missing_vars = [var for var in required_vars if var not in env_vars]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
        return False
    
    # Check if PostgreSQL is accessible
    try:
        result = subprocess.run([
            "psql",
            f"postgresql://{env_vars['POSTGRES_USER']}:{env_vars['POSTGRES_PASSWORD']}@{env_vars['POSTGRES_SERVER']}/{env_vars['POSTGRES_DB']}",
            "-c", "SELECT 1"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to connect to PostgreSQL: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"Failed to connect to PostgreSQL: {e}")
        return False
    
    logger.info("Environment validation successful")
    return True

def setup_database(env_vars):
    """Set up the database infrastructure."""
    logger.info("Setting up database infrastructure...")
    
    # Run database setup script
    try:
        result = subprocess.run([
            "python", "backend/scripts/setup_postgres.py",
            "--host", env_vars["POSTGRES_SERVER"],
            "--user", env_vars["POSTGRES_USER"],
            "--password", env_vars["POSTGRES_PASSWORD"],
            "--db", env_vars["POSTGRES_DB"]
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to set up database: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"Failed to set up database: {e}")
        return False
    
    # Set up read replicas if enabled
    if env_vars.get("READ_REPLICAS_ENABLED", "").lower() == "true":
        logger.info("Setting up read replicas...")
        
        try:
            result = subprocess.run([
                "python", "backend/scripts/setup_read_replicas.py",
                "--primary-host", env_vars["POSTGRES_SERVER"],
                "--primary-user", env_vars["POSTGRES_USER"],
                "--primary-password", env_vars["POSTGRES_PASSWORD"],
                "--primary-db", env_vars["POSTGRES_DB"],
                "--replica-count", env_vars.get("READ_REPLICA_COUNT", "2"),
                "--update-config"
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to set up read replicas: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Failed to set up read replicas: {e}")
            return False
    
    # Set up database sharding if enabled
    if env_vars.get("SHARDING_ENABLED", "").lower() == "true":
        logger.info("Setting up database sharding...")
        
        try:
            result = subprocess.run([
                "python", "backend/scripts/setup_database_sharding.py",
                "--shard-count", env_vars.get("SHARD_COUNT", "4"),
                "--update-config"
            ], capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to set up database sharding: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"Failed to set up database sharding: {e}")
            return False
    
    # Optimize full-text search
    logger.info("Optimizing full-text search...")
    
    try:
        result = subprocess.run([
            "python", "backend/scripts/optimize_full_text_search.py",
            "--all"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to optimize full-text search: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"Failed to optimize full-text search: {e}")
        return False
    
    logger.info("Database setup successful")
    return True

def deploy_application(env_vars, args):
    """Deploy the application."""
    logger.info("Deploying application...")
    
    if args.kubernetes:
        return deploy_to_kubernetes(env_vars, args)
    else:
        return deploy_with_docker_compose(env_vars, args)

def deploy_with_docker_compose(env_vars, args):
    """Deploy the application using Docker Compose."""
    logger.info(f"Deploying with Docker Compose using {args.docker_compose_file}...")
    
    # Export environment variables
    for key, value in env_vars.items():
        os.environ[key] = value
    
    # Deploy with Docker Compose
    try:
        result = subprocess.run([
            "docker-compose",
            "-f", args.docker_compose_file,
            "up", "-d"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Failed to deploy with Docker Compose: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"Failed to deploy with Docker Compose: {e}")
        return False
    
    logger.info("Application deployment with Docker Compose successful")
    return True

def deploy_to_kubernetes(env_vars, args):
    """Deploy the application to Kubernetes."""
    logger.info(f"Deploying to Kubernetes namespace {args.k8s_namespace}...")
    
    # Create namespace if it doesn't exist
    try:
        subprocess.run([
            "kubectl", "create", "namespace", args.k8s_namespace,
            "--dry-run=client", "-o", "yaml"
        ], capture_output=True, text=True, check=True)
        
        subprocess.run([
            "kubectl", "apply", "-f", "-"
        ], input=result.stdout, text=True, check=True)
    except Exception as e:
        logger.warning(f"Failed to create namespace (it may already exist): {e}")
    
    # Create database secrets
    try:
        secrets = {
            "db_host": env_vars["POSTGRES_SERVER"],
            "db_user": env_vars["POSTGRES_USER"],
            "db_password": env_vars["POSTGRES_PASSWORD"],
            "db_name": env_vars["POSTGRES_DB"]
        }
        
        # Create secrets YAML
        secrets_yaml = f"""
apiVersion: v1
kind: Secret
metadata:
  name: rentup-db-secrets
  namespace: {args.k8s_namespace}
type: Opaque
data:
  db_host: {secrets['db_host'].encode('utf-8').hex()}
  db_user: {secrets['db_user'].encode('utf-8').hex()}
  db_password: {secrets['db_password'].encode('utf-8').hex()}
  db_name: {secrets['db_name'].encode('utf-8').hex()}
"""
        
        # Apply secrets
        subprocess.run([
            "kubectl", "apply", "-f", "-"
        ], input=secrets_yaml, text=True, check=True)
    except Exception as e:
        logger.error(f"Failed to create database secrets: {e}")
        return False
    
    # Deploy application
    try:
        # Create deployment YAML
        deployment_yaml = f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rentup-backend
  namespace: {args.k8s_namespace}
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rentup-backend
  template:
    metadata:
      labels:
        app: rentup-backend
    spec:
      containers:
      - name: rentup-backend
        image: rentup/backend:latest
        ports:
        - containerPort: 8000
        env:
        - name: POSTGRES_SERVER
          valueFrom:
            secretKeyRef:
              name: rentup-db-secrets
              key: db_host
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: rentup-db-secrets
              key: db_user
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: rentup-db-secrets
              key: db_password
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: rentup-db-secrets
              key: db_name
        - name: READ_REPLICAS_ENABLED
          value: "{env_vars.get('READ_REPLICAS_ENABLED', 'true')}"
        - name: READ_REPLICA_COUNT
          value: "{env_vars.get('READ_REPLICA_COUNT', '2')}"
        - name: SHARDING_ENABLED
          value: "{env_vars.get('SHARDING_ENABLED', 'true')}"
        - name: SHARD_COUNT
          value: "{env_vars.get('SHARD_COUNT', '4')}"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
"""
        
        # Apply deployment
        subprocess.run([
            "kubectl", "apply", "-f", "-"
        ], input=deployment_yaml, text=True, check=True)
        
        # Create service YAML
        service_yaml = f"""
apiVersion: v1
kind: Service
metadata:
  name: rentup-backend
  namespace: {args.k8s_namespace}
spec:
  selector:
    app: rentup-backend
  ports:
  - port: 80
    targetPort: 8000
  type: LoadBalancer
"""
        
        # Apply service
        subprocess.run([
            "kubectl", "apply", "-f", "-"
        ], input=service_yaml, text=True, check=True)
    except Exception as e:
        logger.error(f"Failed to deploy to Kubernetes: {e}")
        return False
    
    logger.info("Application deployment to Kubernetes successful")
    return True

def setup_monitoring(env_vars, args):
    """Set up monitoring."""
    logger.info("Setting up monitoring...")
    
    # Create monitoring directory
    os.makedirs("monitoring", exist_ok=True)
    
    # Start monitoring script
    try:
        # Create systemd service file
        service_file = """[Unit]
Description=RentUp Database Monitoring
After=network.target

[Service]
ExecStart=/usr/bin/python3 /path/to/backend/scripts/monitor_database_performance.py --interval 60 --output-dir /path/to/monitoring
Restart=always
User=rentup
Group=rentup
Environment=PATH=/usr/bin:/usr/local/bin
WorkingDirectory=/path/to/backend

[Install]
WantedBy=multi-user.target
"""
        
        # Write service file
        with open("rentup-monitoring.service", "w") as f:
            f.write(service_file)
        
        logger.info("Created systemd service file: rentup-monitoring.service")
        logger.info("To install the service:")
        logger.info("1. Copy the service file to /etc/systemd/system/")
        logger.info("2. Update paths in the service file")
        logger.info("3. Run: sudo systemctl daemon-reload")
        logger.info("4. Run: sudo systemctl enable rentup-monitoring.service")
        logger.info("5. Run: sudo systemctl start rentup-monitoring.service")
        
        # Set up fine-tuning cron job
        cron_job = "0 0 * * 0 python /path/to/backend/scripts/fine_tune_optimizations.py --monitoring-dir /path/to/monitoring --apply >> /var/log/rentup/fine_tune.log 2>&1"
        
        logger.info("To set up fine-tuning cron job:")
        logger.info(f"1. Run: (crontab -l ; echo '{cron_job}') | crontab -")
        logger.info("2. Update paths in the cron job")
    except Exception as e:
        logger.error(f"Failed to set up monitoring: {e}")
        return False
    
    logger.info("Monitoring setup successful")
    return True

def run_post_deployment_tests(env_vars):
    """Run post-deployment tests."""
    logger.info("Running post-deployment tests...")
    
    try:
        result = subprocess.run([
            "python", "backend/scripts/run_comprehensive_tests.py",
            "--all"
        ], capture_output=True, text=True)
        
        if result.returncode != 0:
            logger.error(f"Post-deployment tests failed: {result.stderr}")
            return False
    except Exception as e:
        logger.error(f"Failed to run post-deployment tests: {e}")
        return False
    
    logger.info("Post-deployment tests successful")
    return True

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load environment variables
    env_vars = load_env_file(args.env_file)
    
    # Validate environment
    if not validate_environment(env_vars):
        logger.error("Environment validation failed")
        return 1
    
    # Set up database
    if not args.skip_db_setup:
        if not setup_database(env_vars):
            logger.error("Database setup failed")
            return 1
    
    # Deploy application
    if not args.skip_app_deploy:
        if not deploy_application(env_vars, args):
            logger.error("Application deployment failed")
            return 1
    
    # Set up monitoring
    if not args.skip_monitoring:
        if not setup_monitoring(env_vars, args):
            logger.error("Monitoring setup failed")
            return 1
    
    # Run post-deployment tests
    if not run_post_deployment_tests(env_vars):
        logger.warning("Post-deployment tests failed")
        # Continue anyway, as this is not critical
    
    logger.info("Deployment completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
