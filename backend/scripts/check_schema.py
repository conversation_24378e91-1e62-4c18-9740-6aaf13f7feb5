#!/usr/bin/env python3
"""
Script to check the database schema.
"""

from sqlalchemy import create_engine, text

def main():
    """
    Check the database schema.
    """
    # Connect to the database
    engine = create_engine('postgresql://rentup:rentup_secure_password@localhost:5432/rentup')
    conn = engine.connect()

    # Check the agreements table schema
    print("Agreements Table Columns:")
    result = conn.execute(text("SELECT column_name FROM information_schema.columns WHERE table_name = 'agreements'"))
    for row in result:
        print(f"- {row[0]}")

    # Check the items table schema
    print("\nItems Table Columns:")
    result = conn.execute(text("SELECT column_name FROM information_schema.columns WHERE table_name = 'items'"))
    for row in result:
        print(f"- {row[0]}")

    # Check the auctions table schema
    print("\nAuctions Table Columns:")
    result = conn.execute(text("SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = 'auctions'"))
    for row in result:
        print(f"- {row[0]}: {row[1]} (nullable: {row[2]})")

    # Close the connection
    conn.close()

if __name__ == "__main__":
    main()
