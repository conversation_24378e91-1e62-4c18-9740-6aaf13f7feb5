#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to verify RentUp database backups.

This script:
1. Downloads a backup from cloud storage (if needed)
2. Restores the backup to a temporary database
3. Runs validation queries
4. Generates a verification report
"""

import os
import sys
import argparse
import logging
import subprocess
import datetime
import tempfile
import json
import boto3
import psycopg2
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import from our modules
try:
    from app.core.db_config import db_config
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    NEW_MODULES = False

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Verify RentUp database backup')
    parser.add_argument('--backup-file', help='Path to backup file')
    parser.add_argument('--cloud-backup', action='store_true', help='Verify backup from cloud storage')
    parser.add_argument('--cloud-provider', choices=['aws', 'gcp', 'azure'], default='aws', help='Cloud provider')
    parser.add_argument('--bucket-name', help='Cloud storage bucket name')
    parser.add_argument('--backup-key', help='Cloud storage key/path to backup file')
    parser.add_argument('--encrypted', action='store_true', help='Backup is encrypted')
    parser.add_argument('--compressed', action='store_true', help='Backup is compressed')
    parser.add_argument('--temp-db-name', default='rentup_verify', help='Temporary database name for verification')
    parser.add_argument('--report-file', default='backup_verification_report.json', help='Output file for verification report')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def get_db_connection_params():
    """Get database connection parameters."""
    if NEW_MODULES:
        # Use db_config
        db_host = db_config.POSTGRES_SERVER
        db_port = db_config.POSTGRES_PORT
        db_user = db_config.POSTGRES_USER
        db_password = db_config.POSTGRES_PASSWORD
        db_name = db_config.POSTGRES_DB
    else:
        # Use settings
        db_url = settings.DATABASE_URL
        # Parse DATABASE_URL
        import re
        match = re.match(r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
        if match:
            db_user, db_password, db_host, db_port, db_name = match.groups()
            db_port = int(db_port)
        else:
            logger.error("Could not parse DATABASE_URL")
            return None
    
    return {
        "host": db_host,
        "port": db_port,
        "user": db_user,
        "password": db_password,
        "dbname": db_name
    }

def download_from_cloud(cloud_provider: str, bucket_name: str, backup_key: str) -> str:
    """Download backup file from cloud storage."""
    if cloud_provider == "aws":
        return download_from_aws(bucket_name, backup_key)
    elif cloud_provider == "gcp":
        return download_from_gcp(bucket_name, backup_key)
    elif cloud_provider == "azure":
        return download_from_azure(bucket_name, backup_key)
    else:
        logger.error(f"Unsupported cloud provider: {cloud_provider}")
        return None

def download_from_aws(bucket_name: str, backup_key: str) -> str:
    """Download backup file from AWS S3."""
    try:
        logger.info(f"Downloading backup file from AWS S3: s3://{bucket_name}/{backup_key}")
        s3 = boto3.client('s3')
        
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(backup_key)[1])
        temp_file.close()
        
        # Download the file
        s3.download_file(bucket_name, backup_key, temp_file.name)
        
        logger.info(f"Backup file downloaded to: {temp_file.name}")
        return temp_file.name
    except Exception as e:
        logger.error(f"Download from AWS S3 failed: {str(e)}")
        return None

def download_from_gcp(bucket_name: str, backup_key: str) -> str:
    """Download backup file from Google Cloud Storage."""
    try:
        logger.info(f"Downloading backup file from Google Cloud Storage: gs://{bucket_name}/{backup_key}")
        
        # Import GCP libraries
        try:
            from google.cloud import storage
        except ImportError:
            logger.error("Google Cloud Storage library not installed. Run: pip install google-cloud-storage")
            return None
        
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(backup_key)[1])
        temp_file.close()
        
        # Download the file
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(backup_key)
        blob.download_to_filename(temp_file.name)
        
        logger.info(f"Backup file downloaded to: {temp_file.name}")
        return temp_file.name
    except Exception as e:
        logger.error(f"Download from Google Cloud Storage failed: {str(e)}")
        return None

def download_from_azure(bucket_name: str, backup_key: str) -> str:
    """Download backup file from Azure Blob Storage."""
    try:
        logger.info(f"Downloading backup file from Azure Blob Storage: {bucket_name}/{backup_key}")
        
        # Import Azure libraries
        try:
            from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient
        except ImportError:
            logger.error("Azure Blob Storage library not installed. Run: pip install azure-storage-blob")
            return None
        
        # Get connection string from environment
        connect_str = os.environ.get('AZURE_STORAGE_CONNECTION_STRING')
        if not connect_str:
            logger.error("Azure Storage connection string not found in environment variables")
            return None
        
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(backup_key)[1])
        temp_file.close()
        
        # Download the file
        blob_service_client = BlobServiceClient.from_connection_string(connect_str)
        blob_client = blob_service_client.get_blob_client(container=bucket_name, blob=backup_key)
        
        with open(temp_file.name, "wb") as download_file:
            download_file.write(blob_client.download_blob().readall())
        
        logger.info(f"Backup file downloaded to: {temp_file.name}")
        return temp_file.name
    except Exception as e:
        logger.error(f"Download from Azure Blob Storage failed: {str(e)}")
        return None

def decrypt_backup(backup_file: str) -> str:
    """Decrypt the backup file using GPG."""
    decrypted_file = backup_file.replace('.gpg', '')
    
    try:
        logger.info(f"Decrypting backup file to {decrypted_file}")
        
        cmd = [
            "gpg",
            "--output", decrypted_file,
            "--decrypt", backup_file
        ]
        
        subprocess.run(cmd, check=True, capture_output=True)
        
        # Remove the encrypted file
        os.remove(backup_file)
        logger.info(f"Backup file decrypted: {decrypted_file}")
        return decrypted_file
    except subprocess.CalledProcessError as e:
        logger.error(f"Decryption failed: {e.stderr}")
        raise

def decompress_backup(backup_file: str) -> str:
    """Decompress the backup file using gunzip."""
    decompressed_file = backup_file.replace('.gz', '')
    
    try:
        logger.info(f"Decompressing backup file to {decompressed_file}")
        
        with open(backup_file, 'rb') as f_in:
            with subprocess.Popen(['gunzip', '-c'], stdin=subprocess.PIPE, stdout=subprocess.PIPE) as proc:
                stdout, stderr = proc.communicate(f_in.read())
                if proc.returncode != 0:
                    raise subprocess.CalledProcessError(proc.returncode, 'gunzip')
                with open(decompressed_file, 'wb') as f_out:
                    f_out.write(stdout)
        
        # Remove the compressed file
        os.remove(backup_file)
        logger.info(f"Backup file decompressed: {decompressed_file}")
        return decompressed_file
    except Exception as e:
        logger.error(f"Decompression failed: {str(e)}")
        raise

def create_temp_database(db_params: Dict[str, str], temp_db_name: str) -> Dict[str, str]:
    """Create a temporary database for verification."""
    try:
        logger.info(f"Creating temporary database {temp_db_name}")
        
        # Set environment variables for psql
        env = os.environ.copy()
        env["PGPASSWORD"] = db_params["password"]
        
        # Connect to postgres database to create the temporary database
        cmd = [
            "psql",
            "-h", db_params["host"],
            "-p", str(db_params["port"]),
            "-U", db_params["user"],
            "-d", "postgres",
            "-c", f"DROP DATABASE IF EXISTS {temp_db_name}; CREATE DATABASE {temp_db_name};"
        ]
        
        subprocess.run(cmd, env=env, check=True, capture_output=True)
        logger.info(f"Temporary database {temp_db_name} created")
        
        # Create a new db_params dict with the temporary database name
        temp_db_params = db_params.copy()
        temp_db_params["dbname"] = temp_db_name
        
        return temp_db_params
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to create temporary database: {e.stderr}")
        raise

def restore_to_temp_database(backup_file: str, temp_db_params: Dict[str, str]) -> bool:
    """Restore the backup to a temporary database."""
    try:
        logger.info(f"Restoring backup to temporary database {temp_db_params['dbname']}")
        
        # Set environment variables for pg_restore
        env = os.environ.copy()
        env["PGPASSWORD"] = temp_db_params["password"]
        
        # Create pg_restore command
        cmd = [
            "pg_restore",
            "-h", temp_db_params["host"],
            "-p", str(temp_db_params["port"]),
            "-U", temp_db_params["user"],
            "-d", temp_db_params["dbname"],
            "-v",  # Verbose
            backup_file
        ]
        
        subprocess.run(cmd, env=env, check=True, capture_output=True)
        logger.info(f"Backup restored to temporary database {temp_db_params['dbname']}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to restore backup: {e.stderr}")
        return False

def run_validation_queries(temp_db_params: Dict[str, str]) -> Dict[str, Any]:
    """Run validation queries against the temporary database."""
    try:
        logger.info(f"Running validation queries against {temp_db_params['dbname']}")
        
        # Connect to the database
        conn = psycopg2.connect(
            host=temp_db_params["host"],
            port=temp_db_params["port"],
            user=temp_db_params["user"],
            password=temp_db_params["password"],
            dbname=temp_db_params["dbname"]
        )
        
        # Create a cursor
        cur = conn.cursor()
        
        # Initialize results
        results = {
            "tables": {},
            "constraints": {},
            "indexes": {},
            "sequences": {},
            "custom_checks": {}
        }
        
        # Check tables
        cur.execute("""
            SELECT table_name, 
                   (SELECT count(*) FROM information_schema.columns WHERE table_name=t.table_name) as column_count,
                   (SELECT count(*) FROM pg_stat_user_tables WHERE relname=t.table_name) as row_count
            FROM information_schema.tables t
            WHERE table_schema='public'
            ORDER BY table_name
        """)
        
        for table_name, column_count, row_count in cur.fetchall():
            results["tables"][table_name] = {
                "column_count": column_count,
                "row_count": row_count
            }
        
        # Check constraints
        cur.execute("""
            SELECT tc.table_name, tc.constraint_name, tc.constraint_type
            FROM information_schema.table_constraints tc
            WHERE tc.constraint_schema='public'
            ORDER BY tc.table_name, tc.constraint_name
        """)
        
        for table_name, constraint_name, constraint_type in cur.fetchall():
            if table_name not in results["constraints"]:
                results["constraints"][table_name] = []
            
            results["constraints"][table_name].append({
                "name": constraint_name,
                "type": constraint_type
            })
        
        # Check indexes
        cur.execute("""
            SELECT tablename, indexname
            FROM pg_indexes
            WHERE schemaname='public'
            ORDER BY tablename, indexname
        """)
        
        for table_name, index_name in cur.fetchall():
            if table_name not in results["indexes"]:
                results["indexes"][table_name] = []
            
            results["indexes"][table_name].append(index_name)
        
        # Check sequences
        cur.execute("""
            SELECT sequence_name, last_value
            FROM information_schema.sequences s
            JOIN pg_sequences ps ON s.sequence_name = ps.sequencename
            WHERE s.sequence_schema='public'
            ORDER BY sequence_name
        """)
        
        for sequence_name, last_value in cur.fetchall():
            results["sequences"][sequence_name] = last_value
        
        # Custom checks
        
        # Check for users
        cur.execute("SELECT COUNT(*) FROM users")
        results["custom_checks"]["user_count"] = cur.fetchone()[0]
        
        # Check for items
        cur.execute("SELECT COUNT(*) FROM items")
        results["custom_checks"]["item_count"] = cur.fetchone()[0]
        
        # Check for rentals
        cur.execute("SELECT COUNT(*) FROM rentals")
        results["custom_checks"]["rental_count"] = cur.fetchone()[0]
        
        # Close cursor and connection
        cur.close()
        conn.close()
        
        logger.info("Validation queries completed")
        return results
    except Exception as e:
        logger.error(f"Validation queries failed: {str(e)}")
        return {"error": str(e)}

def drop_temp_database(db_params: Dict[str, str], temp_db_name: str) -> bool:
    """Drop the temporary database."""
    try:
        logger.info(f"Dropping temporary database {temp_db_name}")
        
        # Set environment variables for psql
        env = os.environ.copy()
        env["PGPASSWORD"] = db_params["password"]
        
        # Connect to postgres database to drop the temporary database
        cmd = [
            "psql",
            "-h", db_params["host"],
            "-p", str(db_params["port"]),
            "-U", db_params["user"],
            "-d", "postgres",
            "-c", f"DROP DATABASE IF EXISTS {temp_db_name};"
        ]
        
        subprocess.run(cmd, env=env, check=True, capture_output=True)
        logger.info(f"Temporary database {temp_db_name} dropped")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to drop temporary database: {e.stderr}")
        return False

def generate_verification_report(validation_results: Dict[str, Any], backup_file: str, report_file: str) -> bool:
    """Generate a verification report."""
    try:
        logger.info(f"Generating verification report: {report_file}")
        
        # Create report data
        report = {
            "timestamp": datetime.datetime.now().isoformat(),
            "backup_file": backup_file,
            "validation_results": validation_results,
            "summary": {
                "table_count": len(validation_results.get("tables", {})),
                "total_rows": sum(table.get("row_count", 0) for table in validation_results.get("tables", {}).values()),
                "constraint_count": sum(len(constraints) for constraints in validation_results.get("constraints", {}).values()),
                "index_count": sum(len(indexes) for indexes in validation_results.get("indexes", {}).values()),
                "sequence_count": len(validation_results.get("sequences", {})),
                "custom_checks": validation_results.get("custom_checks", {})
            },
            "status": "success" if "error" not in validation_results else "error"
        }
        
        # Write report to file
        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Verification report generated: {report_file}")
        return True
    except Exception as e:
        logger.error(f"Failed to generate verification report: {str(e)}")
        return False

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Get database connection parameters
    db_params = get_db_connection_params()
    
    if not db_params:
        logger.error("Could not get database connection parameters")
        return 1
    
    backup_file = None
    
    try:
        # Get backup file
        if args.cloud_backup:
            if not args.bucket_name or not args.backup_key:
                logger.error("Bucket name and backup key are required for cloud verification")
                return 1
            
            backup_file = download_from_cloud(args.cloud_provider, args.bucket_name, args.backup_key)
            
            if not backup_file:
                logger.error("Failed to download backup from cloud storage")
                return 1
        else:
            if not args.backup_file:
                logger.error("Backup file path is required for local verification")
                return 1
            
            backup_file = args.backup_file
            
            if not os.path.exists(backup_file):
                logger.error(f"Backup file not found: {backup_file}")
                return 1
        
        # Decrypt backup if encrypted
        if args.encrypted:
            backup_file = decrypt_backup(backup_file)
        
        # Decompress backup if compressed
        if args.compressed:
            backup_file = decompress_backup(backup_file)
        
        # Create temporary database
        temp_db_params = create_temp_database(db_params, args.temp_db_name)
        
        # Restore backup to temporary database
        if not restore_to_temp_database(backup_file, temp_db_params):
            logger.error("Failed to restore backup to temporary database")
            return 1
        
        # Run validation queries
        validation_results = run_validation_queries(temp_db_params)
        
        # Generate verification report
        if not generate_verification_report(validation_results, backup_file, args.report_file):
            logger.error("Failed to generate verification report")
            return 1
        
        # Drop temporary database
        drop_temp_database(db_params, args.temp_db_name)
        
        logger.info("Backup verification completed successfully")
        return 0
    except Exception as e:
        logger.error(f"Backup verification failed: {str(e)}")
        return 1
    finally:
        # Clean up temporary files
        if args.cloud_backup and backup_file and os.path.exists(backup_file):
            os.remove(backup_file)

if __name__ == "__main__":
    sys.exit(main())
