#!/usr/bin/env python3
"""
Simple test script for compression middleware.

This script creates a minimal FastAPI app with compression middleware
and tests that responses are properly compressed.
"""

import os
import sys
import gzip
import json
from fastapi import FastAPI, Response
from fastapi.testclient import TestClient
import uvicorn

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Create a simple FastAPI app
app = FastAPI()

# Add routes
@app.get("/large")
def get_large():
    """Return a large response that should be compressed."""
    return {"text": "x" * 10000}

@app.get("/small")
def get_small():
    """Return a small response that should not be compressed."""
    return {"text": "small"}

@app.get("/binary")
def get_binary():
    """Return a binary response with a media type that should be excluded."""
    return Response(content=b"\x00" * 10000, media_type="image/jpeg")

# Add compression middleware
@app.middleware("http")
async def compression_middleware(request, call_next):
    """Simple compression middleware."""
    # Get the response
    response = await call_next(request)

    # Check if response should be compressed
    if (
        "accept-encoding" in request.headers
        and "gzip" in request.headers["accept-encoding"]
        and "content-encoding" not in response.headers
        and response.headers.get("content-type", "").startswith("application/json")
    ):
        # Get the response body
        body = b""
        async for chunk in response.body_iterator:
            body += chunk

        # Only compress if response is large enough
        if len(body) > 500:
            # Compress the body
            compressed_body = gzip.compress(body)

            # Create a new response with compressed body
            headers = dict(response.headers)
            headers["content-encoding"] = "gzip"
            headers["content-length"] = str(len(compressed_body))

            return Response(
                content=compressed_body,
                status_code=response.status_code,
                headers=headers,
                media_type=response.media_type
            )

    return response

# Test the app
def test_app():
    """Test the app with compression."""
    client = TestClient(app)

    # Test large response
    print("\nTesting large response...")
    response = client.get("/large", headers={"Accept-Encoding": "gzip"})
    print(f"Headers: {response.headers}")
    print(f"Content-Encoding: {response.headers.get('Content-Encoding')}")
    print(f"Content-Length: {response.headers.get('Content-Length')}")

    # Verify content
    data = response.json()
    if "text" in data and data["text"] == "x" * 10000:
        print("✅ Content is correct")
    else:
        print("❌ Content is incorrect")

    # Test small response
    print("\nTesting small response...")
    response = client.get("/small", headers={"Accept-Encoding": "gzip"})
    print(f"Headers: {response.headers}")
    print(f"Content-Encoding: {response.headers.get('Content-Encoding')}")

    # Verify content
    try:
        data = response.json()
        if "text" in data and data["text"] == "small":
            print("✅ Content is correct")
        else:
            print("❌ Content is incorrect")
    except Exception as e:
        print(f"❌ Error parsing content: {e}")
        print(f"Content: {response.content}")

    # Test binary response
    print("\nTesting binary response...")
    response = client.get("/binary", headers={"Accept-Encoding": "gzip"})
    print(f"Headers: {response.headers}")
    print(f"Content-Encoding: {response.headers.get('Content-Encoding')}")
    print(f"Content-Type: {response.headers.get('Content-Type')}")
    print(f"Content-Length: {response.headers.get('Content-Length')}")

    # Verify content length
    try:
        if len(response.content) == 10000:
            print("✅ Content length is correct")
        else:
            print(f"❌ Content length is incorrect: {len(response.content)}")
    except Exception as e:
        print(f"❌ Error checking content length: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "serve":
        # Run the server
        uvicorn.run(app, host="127.0.0.1", port=8000)
    else:
        # Run the tests
        test_app()
