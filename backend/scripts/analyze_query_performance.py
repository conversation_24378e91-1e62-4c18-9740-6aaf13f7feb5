#!/usr/bin/env python3
"""
Script to analyze database query performance and identify slow queries.

This script connects to the PostgreSQL database and analyzes query performance
using pg_stat_statements extension. It identifies slow queries, suggests indexes,
and provides recommendations for query optimization.

Usage:
    python analyze_query_performance.py [--limit N] [--min-calls N] [--min-time N]

Options:
    --limit N       Limit the number of queries to analyze (default: 20)
    --min-calls N   Minimum number of calls for a query to be analyzed (default: 10)
    --min-time N    Minimum average execution time in ms (default: 50)
"""

import sys
import time
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import json
from datetime import datetime

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('query_performance.log')
    ]
)
logger = logging.getLogger(__name__)

def check_pg_stat_statements(engine) -> bool:
    """
    Check if pg_stat_statements extension is installed and enabled.

    Args:
        engine: SQLAlchemy engine

    Returns:
        True if pg_stat_statements is available, False otherwise
    """
    try:
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT COUNT(*) FROM pg_extension WHERE extname = 'pg_stat_statements'
            """)).scalar()
            
            if result == 0:
                logger.warning("pg_stat_statements extension is not installed")
                return False
            
            # Check if it's enabled
            result = conn.execute(text("""
                SELECT setting FROM pg_settings WHERE name = 'pg_stat_statements.track'
            """)).scalar()
            
            if not result or result == 'none':
                logger.warning("pg_stat_statements extension is installed but not enabled")
                return False
            
            return True
    except SQLAlchemyError as e:
        logger.error(f"Error checking pg_stat_statements: {e}")
        return False

def enable_pg_stat_statements(engine) -> bool:
    """
    Try to enable pg_stat_statements extension if it's not already enabled.

    Args:
        engine: SQLAlchemy engine

    Returns:
        True if successful, False otherwise
    """
    try:
        with engine.connect() as conn:
            # Check if extension exists
            result = conn.execute(text("""
                SELECT COUNT(*) FROM pg_extension WHERE extname = 'pg_stat_statements'
            """)).scalar()
            
            if result == 0:
                # Try to create extension
                conn.execute(text("CREATE EXTENSION pg_stat_statements"))
                logger.info("Created pg_stat_statements extension")
            
            # Enable tracking
            conn.execute(text("ALTER SYSTEM SET pg_stat_statements.track = 'all'"))
            conn.execute(text("ALTER SYSTEM SET pg_stat_statements.max = 10000"))
            conn.execute(text("SELECT pg_reload_conf()"))
            
            logger.info("Enabled pg_stat_statements extension")
            return True
    except SQLAlchemyError as e:
        logger.error(f"Error enabling pg_stat_statements: {e}")
        logger.error("You may need to install the extension or have superuser privileges")
        return False

def get_slow_queries(engine, limit: int = 20, min_calls: int = 10, min_time: float = 50.0) -> List[Dict[str, Any]]:
    """
    Get slow queries from pg_stat_statements.

    Args:
        engine: SQLAlchemy engine
        limit: Maximum number of queries to return
        min_calls: Minimum number of calls for a query to be included
        min_time: Minimum average execution time in ms

    Returns:
        List of slow queries with execution statistics
    """
    try:
        with engine.connect() as conn:
            result = conn.execute(text(f"""
                SELECT
                    query,
                    calls,
                    total_time,
                    min_time,
                    max_time,
                    mean_time,
                    stddev_time,
                    rows,
                    shared_blks_hit,
                    shared_blks_read,
                    shared_blks_dirtied,
                    shared_blks_written,
                    local_blks_hit,
                    local_blks_read,
                    local_blks_dirtied,
                    local_blks_written,
                    temp_blks_read,
                    temp_blks_written
                FROM pg_stat_statements
                WHERE calls >= :min_calls
                AND mean_time >= :min_time
                AND query NOT LIKE 'EXPLAIN%'
                AND query NOT LIKE '%pg_stat_statements%'
                ORDER BY total_time DESC
                LIMIT :limit
            """), {
                "min_calls": min_calls,
                "min_time": min_time,
                "limit": limit
            })
            
            slow_queries = []
            for row in result:
                slow_queries.append({
                    "query": row.query,
                    "calls": row.calls,
                    "total_time": row.total_time,
                    "min_time": row.min_time,
                    "max_time": row.max_time,
                    "mean_time": row.mean_time,
                    "stddev_time": row.stddev_time,
                    "rows": row.rows,
                    "shared_blks_hit": row.shared_blks_hit,
                    "shared_blks_read": row.shared_blks_read,
                    "shared_blks_dirtied": row.shared_blks_dirtied,
                    "shared_blks_written": row.shared_blks_written,
                    "local_blks_hit": row.local_blks_hit,
                    "local_blks_read": row.local_blks_read,
                    "local_blks_dirtied": row.local_blks_dirtied,
                    "local_blks_written": row.local_blks_written,
                    "temp_blks_read": row.temp_blks_read,
                    "temp_blks_written": row.temp_blks_written
                })
            
            return slow_queries
    except SQLAlchemyError as e:
        logger.error(f"Error getting slow queries: {e}")
        return []

def get_query_execution_plan(engine, query: str) -> List[Dict[str, Any]]:
    """
    Get execution plan for a query using EXPLAIN ANALYZE.

    Args:
        engine: SQLAlchemy engine
        query: SQL query to analyze

    Returns:
        Execution plan as a list of dictionaries
    """
    try:
        with engine.connect() as conn:
            # Clean up the query
            query = query.strip()
            if query.endswith(';'):
                query = query[:-1]
            
            # Add EXPLAIN ANALYZE
            explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
            
            result = conn.execute(text(explain_query))
            plan = result.scalar()
            
            if isinstance(plan, str):
                plan = json.loads(plan)
            
            return plan
    except SQLAlchemyError as e:
        logger.error(f"Error getting execution plan: {e}")
        return []

def analyze_query_performance(engine, limit: int = 20, min_calls: int = 10, min_time: float = 50.0) -> Dict[str, Any]:
    """
    Analyze query performance and generate recommendations.

    Args:
        engine: SQLAlchemy engine
        limit: Maximum number of queries to analyze
        min_calls: Minimum number of calls for a query to be analyzed
        min_time: Minimum average execution time in ms

    Returns:
        Analysis results with recommendations
    """
    # Check if pg_stat_statements is available
    if not check_pg_stat_statements(engine):
        if not enable_pg_stat_statements(engine):
            logger.error("Cannot analyze query performance without pg_stat_statements")
            return {
                "error": "pg_stat_statements extension is not available",
                "recommendations": [
                    "Install pg_stat_statements extension",
                    "Add pg_stat_statements to shared_preload_libraries in postgresql.conf",
                    "Run CREATE EXTENSION pg_stat_statements; as a superuser"
                ]
            }
    
    # Get slow queries
    slow_queries = get_slow_queries(engine, limit, min_calls, min_time)
    
    if not slow_queries:
        logger.info("No slow queries found")
        return {
            "slow_queries": [],
            "recommendations": []
        }
    
    # Analyze each slow query
    analyzed_queries = []
    recommendations = set()
    
    for query_data in slow_queries:
        query = query_data["query"]
        
        # Skip queries that are too complex for EXPLAIN
        if "WITH" in query.upper() and "RECURSIVE" in query.upper():
            logger.warning(f"Skipping complex recursive query: {query[:100]}...")
            continue
        
        try:
            # Get execution plan
            plan = get_query_execution_plan(engine, query)
            
            # Extract recommendations from plan
            query_recommendations = extract_recommendations_from_plan(plan)
            
            analyzed_queries.append({
                "query": query,
                "statistics": query_data,
                "plan": plan,
                "recommendations": query_recommendations
            })
            
            # Add to global recommendations
            recommendations.update(query_recommendations)
        except Exception as e:
            logger.error(f"Error analyzing query: {e}")
            logger.error(f"Query: {query}")
    
    return {
        "timestamp": datetime.now().isoformat(),
        "analyzed_queries": analyzed_queries,
        "recommendations": list(recommendations)
    }

def extract_recommendations_from_plan(plan: List[Dict[str, Any]]) -> List[str]:
    """
    Extract recommendations from an execution plan.

    Args:
        plan: Execution plan from EXPLAIN ANALYZE

    Returns:
        List of recommendations
    """
    recommendations = []
    
    # Extract plan nodes
    if not plan or not isinstance(plan, list) or not plan[0].get('Plan'):
        return recommendations
    
    plan_node = plan[0].get('Plan', {})
    
    # Check for sequential scans on large tables
    if extract_sequential_scans(plan_node):
        recommendations.append("Add indexes to avoid sequential scans on large tables")
    
    # Check for hash joins without indexes
    if extract_hash_joins(plan_node):
        recommendations.append("Add indexes to improve hash join performance")
    
    # Check for high I/O operations
    if extract_high_io_operations(plan_node):
        recommendations.append("Optimize queries to reduce I/O operations")
    
    # Check for temporary file usage
    if extract_temp_file_usage(plan_node):
        recommendations.append("Increase work_mem to avoid temporary file usage")
    
    return recommendations

def extract_sequential_scans(node: Dict[str, Any]) -> bool:
    """
    Extract sequential scans from a plan node.

    Args:
        node: Plan node

    Returns:
        True if sequential scans are found, False otherwise
    """
    if node.get('Node Type') == 'Seq Scan' and node.get('Actual Rows', 0) > 1000:
        return True
    
    # Recursively check child nodes
    for child_key in ['Plans', 'Plan']:
        child_nodes = node.get(child_key, [])
        if isinstance(child_nodes, list):
            for child in child_nodes:
                if extract_sequential_scans(child):
                    return True
    
    return False

def extract_hash_joins(node: Dict[str, Any]) -> bool:
    """
    Extract hash joins from a plan node.

    Args:
        node: Plan node

    Returns:
        True if hash joins without indexes are found, False otherwise
    """
    if node.get('Node Type') == 'Hash Join' and node.get('Actual Rows', 0) > 1000:
        # Check if inner relation uses sequential scan
        inner_plan = node.get('Plans', [{}])[1] if len(node.get('Plans', [])) > 1 else {}
        if inner_plan.get('Node Type') == 'Seq Scan':
            return True
    
    # Recursively check child nodes
    for child_key in ['Plans', 'Plan']:
        child_nodes = node.get(child_key, [])
        if isinstance(child_nodes, list):
            for child in child_nodes:
                if extract_hash_joins(child):
                    return True
    
    return False

def extract_high_io_operations(node: Dict[str, Any]) -> bool:
    """
    Extract high I/O operations from a plan node.

    Args:
        node: Plan node

    Returns:
        True if high I/O operations are found, False otherwise
    """
    # Check for high shared block reads
    if node.get('Shared Read Blocks', 0) > 10000:
        return True
    
    # Check for high shared block writes
    if node.get('Shared Written Blocks', 0) > 1000:
        return True
    
    # Recursively check child nodes
    for child_key in ['Plans', 'Plan']:
        child_nodes = node.get(child_key, [])
        if isinstance(child_nodes, list):
            for child in child_nodes:
                if extract_high_io_operations(child):
                    return True
    
    return False

def extract_temp_file_usage(node: Dict[str, Any]) -> bool:
    """
    Extract temporary file usage from a plan node.

    Args:
        node: Plan node

    Returns:
        True if temporary file usage is found, False otherwise
    """
    # Check for temporary file usage
    if node.get('Temporary File Size', 0) > 0:
        return True
    
    # Recursively check child nodes
    for child_key in ['Plans', 'Plan']:
        child_nodes = node.get(child_key, [])
        if isinstance(child_nodes, list):
            for child in child_nodes:
                if extract_temp_file_usage(child):
                    return True
    
    return False

def main():
    """Main function to analyze query performance."""
    parser = argparse.ArgumentParser(description="Analyze database query performance")
    parser.add_argument("--limit", type=int, default=20, help="Limit the number of queries to analyze")
    parser.add_argument("--min-calls", type=int, default=10, help="Minimum number of calls for a query to be analyzed")
    parser.add_argument("--min-time", type=float, default=50.0, help="Minimum average execution time in ms")
    parser.add_argument("--output", type=str, help="Output file for analysis results (JSON format)")
    
    args = parser.parse_args()
    
    logger.info("Starting query performance analysis...")
    start_time = time.time()
    
    # Create engine
    engine = create_engine(settings.DATABASE_URL)
    
    try:
        # Analyze query performance
        analysis = analyze_query_performance(
            engine=engine,
            limit=args.limit,
            min_calls=args.min_calls,
            min_time=args.min_time
        )
        
        # Log results
        elapsed_time = time.time() - start_time
        logger.info(f"Query performance analysis completed in {elapsed_time:.2f} seconds")
        
        if "error" in analysis:
            logger.error(f"Error: {analysis['error']}")
            for recommendation in analysis.get("recommendations", []):
                logger.info(f"Recommendation: {recommendation}")
            sys.exit(1)
        
        # Print summary
        logger.info(f"Analyzed {len(analysis.get('analyzed_queries', []))} slow queries")
        
        for recommendation in analysis.get("recommendations", []):
            logger.info(f"Recommendation: {recommendation}")
        
        # Save to file if requested
        if args.output:
            with open(args.output, "w") as f:
                json.dump(analysis, f, indent=2)
            logger.info(f"Analysis results saved to {args.output}")
    
    except Exception as e:
        logger.error(f"Error analyzing query performance: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
