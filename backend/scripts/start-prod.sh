#!/bin/bash
# Production Startup Script for RentUp Backend (2025)
# Optimized for security, performance, and reliability

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Configuration
export PYTHONPATH=/app
export PYTHONUNBUFFERED=1
export PYTHONDONTWRITEBYTECODE=1

# Default values
WORKERS=${WORKERS:-4}
MAX_REQUESTS=${MAX_REQUESTS:-1000}
TIMEOUT=${TIMEOUT:-30}
BIND=${BIND:-0.0.0.0:8000}
LOG_LEVEL=${LOG_LEVEL:-info}

log "🚀 Starting RentUp Backend Production Server..."

# Pre-flight checks
log "🔍 Running pre-flight checks..."

# Check if required environment variables are set
required_vars=("DATABASE_URL" "SECRET_KEY")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        error "Required environment variable $var is not set"
        exit 1
    fi
done

# Check database connectivity
log "🔌 Checking database connectivity..."
python3 -c "
import asyncio
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
import os

try:
    engine = create_engine(os.getenv('DATABASE_URL'))
    with engine.connect() as conn:
        result = conn.execute(text('SELECT 1'))
        print('✅ Database connection successful')
except OperationalError as e:
    print(f'❌ Database connection failed: {e}')
    sys.exit(1)
except Exception as e:
    print(f'❌ Unexpected error: {e}')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    error "Database connectivity check failed"
    exit 1
fi

# Check Redis connectivity (if Redis URL is provided)
if [ ! -z "$REDIS_URL" ]; then
    log "🔌 Checking Redis connectivity..."
    python3 -c "
import redis
import sys
import os

try:
    r = redis.from_url(os.getenv('REDIS_URL'))
    r.ping()
    print('✅ Redis connection successful')
except Exception as e:
    print(f'❌ Redis connection failed: {e}')
    sys.exit(1)
"
    
    if [ $? -ne 0 ]; then
        warning "Redis connectivity check failed, continuing without cache"
    fi
fi

# Run database migrations
log "🔄 Running database migrations..."
alembic upgrade head

if [ $? -ne 0 ]; then
    error "Database migration failed"
    exit 1
fi

# Create necessary directories
log "📁 Creating necessary directories..."
mkdir -p /app/logs /app/uploads /app/temp

# Set proper permissions
chmod 755 /app/logs /app/uploads /app/temp

# Validate application configuration
log "⚙️ Validating application configuration..."
python3 -c "
import sys
sys.path.insert(0, '/app')

try:
    from app.core.config import settings
    print('✅ Configuration validation successful')
    print(f'   Environment: {settings.ENVIRONMENT}')
    print(f'   Debug mode: {settings.DEBUG}')
    print(f'   CORS origins: {len(settings.CORS_ORIGINS)} configured')
except Exception as e:
    print(f'❌ Configuration validation failed: {e}')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    error "Configuration validation failed"
    exit 1
fi

# Health check endpoint test
log "🏥 Testing health check endpoint..."
python3 -c "
import sys
sys.path.insert(0, '/app')

try:
    from app.main import app
    from fastapi.testclient import TestClient
    
    client = TestClient(app)
    response = client.get('/api/v1/health')
    
    if response.status_code == 200:
        print('✅ Health check endpoint working')
    else:
        print(f'❌ Health check failed with status {response.status_code}')
        sys.exit(1)
except Exception as e:
    print(f'❌ Health check test failed: {e}')
    sys.exit(1)
"

if [ $? -ne 0 ]; then
    error "Health check endpoint test failed"
    exit 1
fi

# Calculate optimal worker count based on CPU cores
if [ "$WORKERS" = "auto" ]; then
    CPU_CORES=$(nproc)
    WORKERS=$((CPU_CORES * 2 + 1))
    log "🔧 Auto-calculated workers: $WORKERS (based on $CPU_CORES CPU cores)"
fi

# Security checks
log "🔒 Running security checks..."

# Check if running as root (should not be)
if [ "$EUID" -eq 0 ]; then
    error "Application should not run as root user"
    exit 1
fi

# Check file permissions
if [ ! -w "/app/logs" ]; then
    error "Cannot write to logs directory"
    exit 1
fi

success "All pre-flight checks passed!"

# Start the application
log "🎯 Starting Gunicorn server..."
log "   Workers: $WORKERS"
log "   Bind: $BIND"
log "   Timeout: $TIMEOUT"
log "   Max requests: $MAX_REQUESTS"
log "   Log level: $LOG_LEVEL"

# Trap signals for graceful shutdown
trap 'log "🛑 Received shutdown signal, stopping server..."; kill -TERM $PID; wait $PID' TERM INT

# Start Gunicorn with optimized settings
exec gunicorn app.main:app \
    --workers $WORKERS \
    --worker-class uvicorn.workers.UvicornWorker \
    --bind $BIND \
    --access-logfile /app/logs/access.log \
    --error-logfile /app/logs/error.log \
    --log-level $LOG_LEVEL \
    --worker-connections 1000 \
    --max-requests $MAX_REQUESTS \
    --max-requests-jitter 100 \
    --timeout $TIMEOUT \
    --keep-alive 2 \
    --preload \
    --enable-stdio-inheritance \
    --capture-output &

PID=$!
wait $PID
