#!/usr/bin/env python3
"""
Refactored script to add more sample data to the database.

This script uses the modular data generation package to populate
the PostgreSQL database with realistic sample data for testing
and development purposes.

Usage:
    python add_more_data_refactored.py --items 50 --auctions 10 --bids 30 --rentals 20 --agreements 20 --fraud-alerts 10
    python add_more_data_refactored.py --all 100  # Generate 100 items and proportional other data
    python add_more_data_refactored.py --help     # Show help message
"""

import sys
import argparse
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from data_generation import DataGenerator

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('data_generation.log')
    ]
)
logger = logging.getLogger(__name__)


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Add sample data to the RentUp database")

    # Add arguments for each data type
    parser.add_argument("--items", type=int, help="Number of items to add")
    parser.add_argument("--auctions", type=int, help="Number of auctions to add")
    parser.add_argument("--bids", type=int, help="Number of bids to add")
    parser.add_argument("--rentals", type=int, help="Number of rentals to add")
    parser.add_argument("--agreements", type=int, help="Number of agreements to add")
    parser.add_argument("--fraud-alerts", type=int, help="Number of fraud alerts to add")
    parser.add_argument("--all", type=int, help="Add proportional data for all types")

    return parser.parse_args()


def build_config(args):
    """
    Build configuration dictionary from command line arguments.

    Args:
        args: Parsed command line arguments

    Returns:
        Dictionary with data generation configuration
    """
    if args.all:
        # Use proportional calculation for all data types
        generator = DataGenerator()
        return generator.calculate_proportional_config(args.all)
    else:
        # Use specified values or defaults
        return {
            "items": args.items or 25,
            "auctions": args.auctions or 5,
            "bids": args.bids or 15,
            "rentals": args.rentals or 10,
            "agreements": args.agreements or 10,
            "fraud_alerts": args.fraud_alerts or 5
        }


def print_summary(results):
    """
    Print a summary of the data generation results.

    Args:
        results: Dictionary with generation results
    """
    logger.info("=" * 60)
    logger.info("DATA GENERATION SUMMARY")
    logger.info("=" * 60)
    
    total_time = results.get("total_time", 0)
    logger.info(f"Total execution time: {total_time:.2f} seconds")
    logger.info("")
    
    for data_type, result in results.items():
        if data_type == "total_time":
            continue
            
        if isinstance(result, dict):
            count = result.get("count", 0)
            time_taken = result.get("time_taken", 0)
            logger.info(f"{data_type.title()}: {count} items in {time_taken:.2f}s")
            
            # Print additional details if available
            if "items_per_second" in result:
                logger.info(f"  Rate: {result['items_per_second']:.2f} items/sec")
            if "batches" in result:
                logger.info(f"  Batches: {result['batches']}")
        else:
            logger.info(f"{data_type.title()}: {result}")
    
    logger.info("=" * 60)


def main():
    """Main function to orchestrate data generation."""
    try:
        # Parse command line arguments
        args = parse_arguments()
        
        # Build configuration
        config = build_config(args)
        
        logger.info("Starting data generation with configuration:")
        for key, value in config.items():
            logger.info(f"  {key}: {value}")
        logger.info("")
        
        # Initialize data generator
        generator = DataGenerator()
        
        # Generate all data
        results = generator.generate_all_data(config)
        
        # Print summary
        print_summary(results)
        
        logger.info("Data generation completed successfully!")
        
    except KeyboardInterrupt:
        logger.warning("Data generation interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error during data generation: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
