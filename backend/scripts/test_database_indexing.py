#!/usr/bin/env python3
"""
Script to test database indexing.

This script demonstrates the use of the database indexing script
to apply recommended indexes to the database.
"""

import sys
import os
import time
import logging
from pathlib import Path

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from app.core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('database_indexing_test.log')
    ]
)
logger = logging.getLogger(__name__)

def get_existing_indexes(engine):
    """
    Get existing indexes from the database.

    Args:
        engine: SQLAlchemy engine

    Returns:
        Dictionary of tables and their indexes
    """
    with engine.connect() as conn:
        result = conn.execute(text("""
            SELECT
                t.relname AS table_name,
                i.relname AS index_name,
                array_agg(a.attname) AS column_names,
                ix.indisunique AS is_unique,
                am.amname AS index_type,
                pg_get_indexdef(ix.indexrelid) AS index_definition
            FROM
                pg_index ix
                JOIN pg_class i ON i.oid = ix.indexrelid
                JOIN pg_class t ON t.oid = ix.indrelid
                JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
                JOIN pg_am am ON am.oid = i.relam
            WHERE
                t.relkind = 'r'
                AND t.relname NOT LIKE 'pg_%'
                AND t.relname NOT LIKE 'sql_%'
            GROUP BY
                t.relname, i.relname, ix.indisunique, am.amname, ix.indexrelid
            ORDER BY
                t.relname, i.relname
        """))

        indexes_by_table = {}
        for row in result:
            table_name = row.table_name
            if table_name not in indexes_by_table:
                indexes_by_table[table_name] = []
            
            indexes_by_table[table_name].append({
                "index_name": row.index_name,
                "column_names": row.column_names,
                "is_unique": row.is_unique,
                "index_type": row.index_type,
                "index_definition": row.index_definition
            })
        
        return indexes_by_table

def create_test_index(engine, table_name, column_name, index_name=None, is_unique=False):
    """
    Create a test index on a table.

    Args:
        engine: SQLAlchemy engine
        table_name: Name of the table
        column_name: Name of the column
        index_name: Name of the index (optional)
        is_unique: Whether the index is unique

    Returns:
        True if successful, False otherwise
    """
    if not index_name:
        index_name = f"idx_{table_name}_{column_name}"
    
    unique_str = "UNIQUE " if is_unique else ""
    
    create_statement = f"CREATE {unique_str}INDEX IF NOT EXISTS {index_name} ON {table_name} ({column_name})"
    
    try:
        with engine.begin() as conn:
            conn.execute(text(create_statement))
            logger.info(f"Created index: {index_name} on {table_name}({column_name})")
            return True
    except SQLAlchemyError as e:
        logger.error(f"Error creating index {index_name}: {e}")
        return False

def drop_test_index(engine, index_name):
    """
    Drop a test index.

    Args:
        engine: SQLAlchemy engine
        index_name: Name of the index

    Returns:
        True if successful, False otherwise
    """
    drop_statement = f"DROP INDEX IF EXISTS {index_name}"
    
    try:
        with engine.begin() as conn:
            conn.execute(text(drop_statement))
            logger.info(f"Dropped index: {index_name}")
            return True
    except SQLAlchemyError as e:
        logger.error(f"Error dropping index {index_name}: {e}")
        return False

def test_index_performance(engine, table_name, column_name, value):
    """
    Test the performance of a query with and without an index.

    Args:
        engine: SQLAlchemy engine
        table_name: Name of the table
        column_name: Name of the column
        value: Value to search for

    Returns:
        Dictionary with performance results
    """
    # Query without index
    query = f"SELECT * FROM {table_name} WHERE {column_name} = :value"
    
    # Drop index if it exists
    drop_test_index(engine, f"idx_{table_name}_{column_name}")
    
    # Measure performance without index
    start_time = time.time()
    try:
        with engine.connect() as conn:
            result = conn.execute(text(query), {"value": value})
            rows = result.fetchall()
            without_index_time = time.time() - start_time
            logger.info(f"Query without index took {without_index_time:.6f} seconds, returned {len(rows)} rows")
    except SQLAlchemyError as e:
        logger.error(f"Error executing query without index: {e}")
        without_index_time = None
    
    # Create index
    create_test_index(engine, table_name, column_name)
    
    # Measure performance with index
    start_time = time.time()
    try:
        with engine.connect() as conn:
            result = conn.execute(text(query), {"value": value})
            rows = result.fetchall()
            with_index_time = time.time() - start_time
            logger.info(f"Query with index took {with_index_time:.6f} seconds, returned {len(rows)} rows")
    except SQLAlchemyError as e:
        logger.error(f"Error executing query with index: {e}")
        with_index_time = None
    
    # Calculate improvement
    if without_index_time and with_index_time:
        improvement = (without_index_time - with_index_time) / without_index_time * 100
        logger.info(f"Index improved query performance by {improvement:.2f}%")
    else:
        improvement = None
    
    # Drop index
    drop_test_index(engine, f"idx_{table_name}_{column_name}")
    
    return {
        "without_index_time": without_index_time,
        "with_index_time": with_index_time,
        "improvement": improvement
    }

def main():
    """Main function to test database indexing."""
    logger.info("Starting database indexing test...")
    
    # Create engine
    engine = create_engine(settings.DATABASE_URL)
    
    try:
        # Get existing indexes
        indexes = get_existing_indexes(engine)
        
        # Print existing indexes
        logger.info(f"Found {sum(len(indexes[table]) for table in indexes)} indexes in {len(indexes)} tables")
        
        for table, table_indexes in indexes.items():
            logger.info(f"Table {table} has {len(table_indexes)} indexes:")
            for index in table_indexes:
                logger.info(f"  - {index['index_name']} ({', '.join(index['column_names'])})")
        
        # Test creating and dropping an index
        logger.info("Testing index creation and deletion...")
        create_test_index(engine, "users", "email", "test_idx_users_email", True)
        drop_test_index(engine, "test_idx_users_email")
        
        # Test index performance (if there's data in the database)
        logger.info("Testing index performance...")
        try:
            test_index_performance(engine, "users", "is_active", True)
        except Exception as e:
            logger.error(f"Error testing index performance: {e}")
        
        logger.info("Database indexing test completed successfully")
    except Exception as e:
        logger.error(f"Error testing database indexing: {e}")

if __name__ == "__main__":
    main()
