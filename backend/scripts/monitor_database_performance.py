#!/usr/bin/env python3
"""
Script for continuous monitoring of database performance in RentUp.

This script:
1. Monitors database connection pool usage
2. Tracks query execution times
3. Monitors cache hit rates
4. Checks replication lag
5. Monitors shard balance
6. Generates performance reports
7. Sends alerts for performance issues
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import json
import datetime
import signal
import threading
from typing import Dict, List, Any, Optional, Tuple, Set

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text, func, inspect
import psutil

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    from app.core.query_cache import query_cache
    from app.core.read_replica import REPLICA_HEALTH
    from app.core.query_plan_cache import get_cache_stats as get_plan_cache_stats
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Monitor database performance')
    parser.add_argument('--interval', type=int, default=60, help='Monitoring interval in seconds')
    parser.add_argument('--output-dir', default='monitoring', help='Output directory for monitoring data')
    parser.add_argument('--alert-threshold', type=float, default=1.0, help='Alert threshold for slow queries in seconds')
    parser.add_argument('--max-replica-lag', type=float, default=30.0, help='Maximum acceptable replica lag in seconds')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

class DatabaseMonitor:
    """Database performance monitor."""
    
    def __init__(self, args):
        """Initialize the monitor."""
        self.args = args
        self.interval = args.interval
        self.output_dir = args.output_dir
        self.alert_threshold = args.alert_threshold
        self.max_replica_lag = args.max_replica_lag
        self.verbose = args.verbose
        self.running = False
        self.monitoring_thread = None
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
        
        # Initialize monitoring data
        self.monitoring_data = {
            "connection_pool": [],
            "query_performance": [],
            "cache_performance": [],
            "replica_lag": [],
            "shard_balance": [],
            "system_resources": []
        }
        
        # Set up signal handlers
        signal.signal(signal.SIGINT, self.handle_signal)
        signal.signal(signal.SIGTERM, self.handle_signal)
    
    def handle_signal(self, signum, frame):
        """Handle signals to gracefully stop monitoring."""
        logger.info(f"Received signal {signum}, stopping monitoring...")
        self.stop()
    
    def start(self):
        """Start monitoring."""
        if self.running:
            logger.warning("Monitoring is already running")
            return
        
        self.running = True
        self.monitoring_thread = threading.Thread(target=self.monitor_loop)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
        logger.info(f"Started database monitoring with interval {self.interval} seconds")
        
        try:
            # Keep the main thread alive
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received, stopping monitoring...")
            self.stop()
    
    def stop(self):
        """Stop monitoring."""
        self.running = False
        
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        # Save final monitoring data
        self.save_monitoring_data()
        
        logger.info("Stopped database monitoring")
    
    def monitor_loop(self):
        """Main monitoring loop."""
        while self.running:
            try:
                # Collect monitoring data
                self.collect_monitoring_data()
                
                # Save monitoring data periodically
                self.save_monitoring_data()
                
                # Sleep until next monitoring interval
                time.sleep(self.interval)
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.interval)
    
    def collect_monitoring_data(self):
        """Collect monitoring data."""
        timestamp = datetime.datetime.now().isoformat()
        
        with session_scope() as db:
            # Monitor connection pool
            connection_pool_data = self.monitor_connection_pool(db)
            self.monitoring_data["connection_pool"].append({
                "timestamp": timestamp,
                **connection_pool_data
            })
            
            # Monitor query performance
            query_performance_data = self.monitor_query_performance(db)
            self.monitoring_data["query_performance"].append({
                "timestamp": timestamp,
                **query_performance_data
            })
            
            # Monitor cache performance
            cache_performance_data = self.monitor_cache_performance(db)
            self.monitoring_data["cache_performance"].append({
                "timestamp": timestamp,
                **cache_performance_data
            })
            
            # Monitor replica lag
            replica_lag_data = self.monitor_replica_lag(db)
            self.monitoring_data["replica_lag"].append({
                "timestamp": timestamp,
                **replica_lag_data
            })
            
            # Monitor shard balance
            shard_balance_data = self.monitor_shard_balance(db)
            self.monitoring_data["shard_balance"].append({
                "timestamp": timestamp,
                **shard_balance_data
            })
            
            # Monitor system resources
            system_resources_data = self.monitor_system_resources()
            self.monitoring_data["system_resources"].append({
                "timestamp": timestamp,
                **system_resources_data
            })
        
        # Log monitoring data if verbose
        if self.verbose:
            logger.info(f"Collected monitoring data at {timestamp}")
            logger.info(f"Connection pool: {connection_pool_data}")
            logger.info(f"Query performance: {query_performance_data}")
            logger.info(f"Cache performance: {cache_performance_data}")
            logger.info(f"Replica lag: {replica_lag_data}")
            logger.info(f"Shard balance: {shard_balance_data}")
            logger.info(f"System resources: {system_resources_data}")
    
    def save_monitoring_data(self):
        """Save monitoring data to files."""
        # Save each monitoring data type to a separate file
        for data_type, data in self.monitoring_data.items():
            if data:
                file_path = os.path.join(self.output_dir, f"{data_type}.json")
                with open(file_path, "w") as f:
                    json.dump(data, f, indent=2)
        
        # Save latest monitoring data to a separate file
        latest_data = {
            data_type: data[-1] if data else None
            for data_type, data in self.monitoring_data.items()
        }
        
        latest_file_path = os.path.join(self.output_dir, "latest.json")
        with open(latest_file_path, "w") as f:
            json.dump(latest_data, f, indent=2)
    
    def monitor_connection_pool(self, db):
        """Monitor database connection pool."""
        if not NEW_MODULES:
            return {"status": "not_available"}
        
        try:
            # Get connection pool statistics
            pool = engine.pool
            
            return {
                "status": "success",
                "size": pool.size(),
                "checkedin": pool.checkedin(),
                "checkedout": pool.checkedout(),
                "overflow": pool.overflow(),
                "timeout": getattr(pool, "_timeout", None),
                "recycle": getattr(pool, "_recycle", None)
            }
        except Exception as e:
            logger.error(f"Error monitoring connection pool: {e}")
            return {"status": "error", "message": str(e)}
    
    def monitor_query_performance(self, db):
        """Monitor query performance."""
        try:
            # Get query statistics from pg_stat_statements
            query = """
                SELECT
                    query,
                    calls,
                    total_time,
                    mean_time,
                    rows
                FROM
                    pg_stat_statements
                ORDER BY
                    mean_time DESC
                LIMIT 10
            """
            
            try:
                result = db.execute(text(query))
                slow_queries = []
                
                for row in result:
                    if row.mean_time > self.alert_threshold * 1000:  # Convert to milliseconds
                        slow_queries.append({
                            "query": row.query,
                            "calls": row.calls,
                            "total_time": row.total_time,
                            "mean_time": row.mean_time,
                            "rows": row.rows
                        })
                
                return {
                    "status": "success",
                    "slow_queries": slow_queries,
                    "slow_query_count": len(slow_queries)
                }
            except Exception as e:
                logger.warning(f"Error getting query statistics: {e}")
                logger.info("Make sure pg_stat_statements extension is enabled")
                
                # Fall back to basic query statistics
                return {
                    "status": "limited",
                    "message": "pg_stat_statements not available"
                }
        except Exception as e:
            logger.error(f"Error monitoring query performance: {e}")
            return {"status": "error", "message": str(e)}
    
    def monitor_cache_performance(self, db):
        """Monitor cache performance."""
        if not NEW_MODULES:
            return {"status": "not_available"}
        
        try:
            # Get query cache statistics
            query_cache_stats = query_cache.get_stats() if hasattr(query_cache, "get_stats") else {}
            
            # Get query plan cache statistics
            plan_cache_stats = get_plan_cache_stats() if "get_plan_cache_stats" in globals() else {}
            
            return {
                "status": "success",
                "query_cache": query_cache_stats,
                "plan_cache": plan_cache_stats
            }
        except Exception as e:
            logger.error(f"Error monitoring cache performance: {e}")
            return {"status": "error", "message": str(e)}
    
    def monitor_replica_lag(self, db):
        """Monitor replica lag."""
        if not NEW_MODULES:
            return {"status": "not_available"}
        
        try:
            # Get replica health information
            replica_health = REPLICA_HEALTH if "REPLICA_HEALTH" in globals() else {}
            
            # Check for excessive lag
            excessive_lag = []
            for replica_id, health in replica_health.items():
                lag = health.get("lag", 0)
                if lag > self.max_replica_lag:
                    excessive_lag.append({
                        "replica_id": replica_id,
                        "lag": lag
                    })
            
            return {
                "status": "success",
                "replica_health": replica_health,
                "excessive_lag": excessive_lag,
                "excessive_lag_count": len(excessive_lag)
            }
        except Exception as e:
            logger.error(f"Error monitoring replica lag: {e}")
            return {"status": "error", "message": str(e)}
    
    def monitor_shard_balance(self, db):
        """Monitor shard balance."""
        if not NEW_MODULES:
            return {"status": "not_available"}
        
        try:
            # Get shard balance information
            # This is a placeholder - in a real implementation, we would query each shard
            # to get row counts for key tables
            
            return {
                "status": "not_implemented"
            }
        except Exception as e:
            logger.error(f"Error monitoring shard balance: {e}")
            return {"status": "error", "message": str(e)}
    
    def monitor_system_resources(self):
        """Monitor system resources."""
        try:
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Get memory usage
            memory = psutil.virtual_memory()
            
            # Get disk usage
            disk = psutil.disk_usage('/')
            
            return {
                "status": "success",
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk.percent
            }
        except Exception as e:
            logger.error(f"Error monitoring system resources: {e}")
            return {"status": "error", "message": str(e)}

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create and start monitor
    monitor = DatabaseMonitor(args)
    monitor.start()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
