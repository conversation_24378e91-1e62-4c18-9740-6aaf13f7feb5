#!/usr/bin/env python3
"""
Simple test script for database optimization utilities.
"""

import sys
import os
from pathlib import Path
import unittest
from unittest.mock import MagicMock, patch
import time

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from app.core.query_optimization import QueryPerformanceTracker
from app.core.index_optimization import IndexDefinition, IndexType

class TestQueryOptimization(unittest.TestCase):
    """Test cases for query optimization utilities."""
    
    def test_query_performance_tracker(self):
        """Test the QueryPerformanceTracker class."""
        tracker = QueryPerformanceTracker()
        
        # Record some queries
        tracker.record_query("test_query_1", 0.1, 10)
        tracker.record_query("test_query_1", 0.2, 20)
        tracker.record_query("test_query_2", 0.05, 5)
        
        # Check statistics
        self.assertIn("test_query_1", tracker.query_stats)
        self.assertIn("test_query_2", tracker.query_stats)
        
        self.assertEqual(tracker.query_stats["test_query_1"]["count"], 2)
        self.assertEqual(tracker.query_stats["test_query_1"]["total_time"], 0.3)
        self.assertEqual(tracker.query_stats["test_query_1"]["avg_time"], 0.15)
        self.assertEqual(tracker.query_stats["test_query_1"]["total_rows"], 30)
        self.assertEqual(tracker.query_stats["test_query_1"]["avg_rows"], 15)
        
        # Test slow queries
        slow_queries = tracker.get_slow_queries(threshold_seconds=0.1)
        self.assertEqual(len(slow_queries), 1)
        self.assertEqual(slow_queries[0]["query_name"], "test_query_1")
        
        # Test reset
        tracker.reset_statistics()
        self.assertEqual(len(tracker.query_stats), 0)
    
    def test_index_definition(self):
        """Test the IndexDefinition class."""
        # Create an index definition
        index_def = IndexDefinition(
            table_name="test_items",
            column_names=["name", "price"],
            index_name="idx_test_items_name_price",
            index_type=IndexType.BTREE,
            unique=True
        )
        
        # Check properties
        self.assertEqual(index_def.table_name, "test_items")
        self.assertEqual(index_def.column_names, ["name", "price"])
        self.assertEqual(index_def.index_name, "idx_test_items_name_price")
        self.assertEqual(index_def.index_type, IndexType.BTREE)
        self.assertTrue(index_def.unique)
        
        # Check SQL statement
        create_statement = index_def.get_create_statement()
        self.assertIn("CREATE UNIQUE INDEX", create_statement)
        self.assertIn("ON test_items", create_statement)
        self.assertIn("(name, price)", create_statement)
    
    def test_auto_index_name(self):
        """Test auto-generated index names."""
        # Create an index definition without a name
        index_def = IndexDefinition(
            table_name="items",
            column_names=["category", "is_available"],
            unique=False
        )
        
        # Check auto-generated name
        self.assertEqual(index_def.index_name, "idx_items_category_is_available")
        
        # Create a unique index definition without a name
        unique_index_def = IndexDefinition(
            table_name="users",
            column_names=["email"],
            unique=True
        )
        
        # Check auto-generated name for unique index
        self.assertEqual(unique_index_def.index_name, "uidx_users_email")

def main():
    """Run the tests."""
    unittest.main()

if __name__ == "__main__":
    main()
