#!/usr/bin/env python3
"""
Script to implement and test query plan caching for RentUp.

This script:
1. Enables query plan caching for the application
2. Tests query plan caching with various queries
3. Measures performance improvements
4. Provides recommendations for query plan caching configuration
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import json
from typing import Dict, List, Any, Optional, Tuple

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text, func, desc
from sqlalchemy.orm import Session

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database import engine, session_scope
    from app.core.query_plan_cache import (
        CACHE_CONFIG, get_query_plan, cache_query_plan, get_cached_query_plan,
        record_query_execution, clear_query_plan_cache, get_cache_stats,
        QueryPlanKey
    )
    from app.models.user import User
    from app.models.item import Item
    from app.models.rental import Rental
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Implement and test query plan caching for RentUp')
    parser.add_argument('--enable', action='store_true', help='Enable query plan caching')
    parser.add_argument('--test', action='store_true', help='Test query plan caching')
    parser.add_argument('--benchmark', action='store_true', help='Benchmark query plan caching')
    parser.add_argument('--clear-cache', action='store_true', help='Clear query plan cache')
    parser.add_argument('--max-size', type=int, help='Maximum cache size')
    parser.add_argument('--ttl', type=int, help='Cache TTL in seconds')
    parser.add_argument('--min-execution-time', type=float, help='Minimum execution time to cache')
    parser.add_argument('--all', action='store_true', help='Perform all actions')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def update_cache_config(args):
    """Update query plan cache configuration."""
    if not NEW_MODULES:
        logger.error("Query plan cache module not available")
        return

    logger.info("Updating query plan cache configuration...")

    # Update cache configuration
    if args.max_size is not None:
        CACHE_CONFIG["max_size"] = args.max_size
        logger.info(f"Set max_size to {args.max_size}")

    if args.ttl is not None:
        CACHE_CONFIG["ttl"] = args.ttl
        logger.info(f"Set ttl to {args.ttl}")

    if args.min_execution_time is not None:
        CACHE_CONFIG["min_execution_time"] = args.min_execution_time
        logger.info(f"Set min_execution_time to {args.min_execution_time}")

    # Enable cache
    if args.enable or args.all:
        CACHE_CONFIG["enabled"] = True
        logger.info("Enabled query plan cache")

    # Clear cache
    if args.clear_cache or args.all:
        clear_query_plan_cache()
        logger.info("Cleared query plan cache")

    # Log current configuration
    logger.info(f"Current cache configuration: {CACHE_CONFIG}")

def test_query_plan_caching(db):
    """Test query plan caching."""
    if not NEW_MODULES:
        logger.error("Query plan cache module not available")
        return

    logger.info("Testing query plan caching...")

    # Test queries
    test_queries = [
        {
            "name": "Get all users",
            "query": db.query(User),
            "params": {}
        },
        {
            "name": "Get user by ID",
            "query": db.query(User).filter(User.id == "user1"),
            "params": {}
        },
        {
            "name": "Get items by category",
            "query": db.query(Item).filter(Item.category == "furniture"),
            "params": {}
        },
        {
            "name": "Get rentals by status",
            "query": db.query(Rental).filter(Rental.status == "active"),
            "params": {}
        },
        {
            "name": "Get items with complex filter",
            "query": db.query(Item).filter(
                Item.category == "furniture",
                Item.is_available == True,
                Item.daily_price < 100
            ),
            "params": {}
        },
        {
            "name": "Get items with sorting",
            "query": db.query(Item).order_by(desc(Item.created_at)),
            "params": {}
        },
        {
            "name": "Get items with pagination",
            "query": db.query(Item).offset(0).limit(10),
            "params": {}
        },
        {
            "name": "Get items with complex query",
            "query": """
                SELECT i.*, u.full_name as owner_name
                FROM items i
                JOIN users u ON i.owner_id = u.id
                WHERE i.category = :category
                AND i.is_available = :is_available
                ORDER BY i.daily_price ASC
                LIMIT :limit
            """,
            "params": {
                "category": "furniture",
                "is_available": True,
                "limit": 10
            }
        }
    ]

    # Test each query
    for test in test_queries:
        logger.info(f"Testing query: {test['name']}")

        # Create query plan key
        key = QueryPlanKey.from_query(test["query"], test["params"])

        # Get query plan
        plan = get_query_plan(db, test["query"], test["params"])

        if plan:
            # Cache plan
            cache_query_plan(key, plan)
            logger.info("Cached query plan")

            # Get cached plan
            cached_plan = get_cached_query_plan(key)

            if cached_plan:
                logger.info("Retrieved cached query plan successfully")
            else:
                logger.error("Failed to retrieve cached query plan")
        else:
            logger.warning("Failed to get query plan")

    # Log cache statistics
    stats = get_cache_stats()
    logger.info(f"Cache statistics: {stats}")

def benchmark_query_plan_caching(db):
    """Benchmark query plan caching."""
    if not NEW_MODULES:
        logger.error("Query plan cache module not available")
        return

    logger.info("Benchmarking query plan caching...")

    # Clear cache
    clear_query_plan_cache()

    # Test queries
    test_queries = [
        {
            "name": "Simple query",
            "query": db.query(User),
            "params": {},
            "iterations": 10
        },
        {
            "name": "Complex query",
            "query": """
                SELECT i.*, u.full_name as owner_name
                FROM items i
                JOIN users u ON i.owner_id = u.id
                WHERE i.category = :category
                AND i.is_available = :is_available
                ORDER BY i.daily_price ASC
                LIMIT :limit
            """,
            "params": {
                "category": "furniture",
                "is_available": True,
                "limit": 10
            },
            "iterations": 10
        }
    ]

    results = []

    # Test each query
    for test in test_queries:
        logger.info(f"Benchmarking query: {test['name']}")

        query = test["query"]
        params = test["params"]
        iterations = test["iterations"]

        # Create query plan key
        key = QueryPlanKey.from_query(query, params)

        # Test without cache
        CACHE_CONFIG["enabled"] = False

        uncached_times = []
        for i in range(iterations):
            start_time = time.time()

            if isinstance(query, str):
                db.execute(text(query), params)
            else:
                query.all()

            execution_time = time.time() - start_time
            uncached_times.append(execution_time)

        avg_uncached_time = sum(uncached_times) / len(uncached_times)
        logger.info(f"Average execution time without cache: {avg_uncached_time:.6f}s")

        # Test with cache
        CACHE_CONFIG["enabled"] = True

        # First execution (cache miss)
        start_time = time.time()

        if isinstance(query, str):
            db.execute(text(query), params)
        else:
            query.all()

        cache_miss_time = time.time() - start_time
        logger.info(f"Execution time with cache miss: {cache_miss_time:.6f}s")

        # Get query plan
        plan = get_query_plan(db, query, params)

        if plan:
            # Cache plan
            cache_query_plan(key, plan)

        # Subsequent executions (cache hits)
        cached_times = []
        for i in range(iterations):
            start_time = time.time()

            if isinstance(query, str):
                db.execute(text(query), params)
            else:
                query.all()

            execution_time = time.time() - start_time
            cached_times.append(execution_time)

            # Record execution time
            record_query_execution(key, execution_time)

        avg_cached_time = sum(cached_times) / len(cached_times)
        logger.info(f"Average execution time with cache: {avg_cached_time:.6f}s")

        # Calculate improvement
        improvement = (avg_uncached_time - avg_cached_time) / avg_uncached_time * 100
        logger.info(f"Improvement: {improvement:.2f}%")

        # Add to results
        results.append({
            "name": test["name"],
            "uncached_time": avg_uncached_time,
            "cache_miss_time": cache_miss_time,
            "cached_time": avg_cached_time,
            "improvement": improvement
        })

    # Log results
    logger.info("Benchmark results:")
    for result in results:
        logger.info(f"  {result['name']}:")
        logger.info(f"    Uncached time: {result['uncached_time']:.6f}s")
        logger.info(f"    Cache miss time: {result['cache_miss_time']:.6f}s")
        logger.info(f"    Cached time: {result['cached_time']:.6f}s")
        logger.info(f"    Improvement: {result['improvement']:.2f}%")

    # Save results to file
    with open("query_plan_cache_benchmark.json", "w") as f:
        json.dump(results, f, indent=2)

    logger.info("Benchmark results saved to query_plan_cache_benchmark.json")

def main():
    """Main function."""
    args = parse_args()

    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Check if query plan cache module is available
    if not NEW_MODULES:
        logger.error("Query plan cache module not available")
        return 1

    # Update cache configuration
    update_cache_config(args)

    with session_scope() as db:
        # Test query plan caching
        if args.test or args.all:
            test_query_plan_caching(db)

        # Benchmark query plan caching
        if args.benchmark or args.all:
            benchmark_query_plan_caching(db)

    logger.info("Query plan caching implementation completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
