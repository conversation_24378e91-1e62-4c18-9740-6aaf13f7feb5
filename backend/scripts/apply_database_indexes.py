#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to apply database indexes to improve query performance.

This script analyzes the database schema and applies recommended indexes
for frequently accessed fields, particularly those used in search queries,
filtering, or JOIN operations.

Usage:
    python apply_database_indexes.py [--dry-run] [--verbose]

Options:
    --dry-run   Show SQL statements without executing them
    --verbose   Show detailed information about each index
"""

import sys
import time
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.engine import Engine

from app.core.config import settings
from app.core.database import Base

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('database_indexing.log')
    ]
)
logger = logging.getLogger(__name__)

# Define index types
class IndexType:
    BTREE = "btree"  # Default for most indexes
    GIN = "gin"      # For text search and array operations
    GIST = "gist"    # For geometric data and range queries
    HASH = "hash"    # For equality comparisons
    BRIN = "brin"    # For large tables with ordered data

# Define index definition class
class IndexDefinition:
    def __init__(
        self,
        table_name: str,
        column_names: List[str],
        index_name: Optional[str] = None,
        index_type: str = IndexType.BTREE,
        unique: bool = False,
        where_clause: Optional[str] = None
    ):
        self.table_name = table_name
        self.column_names = column_names
        self.index_name = index_name or f"idx_{table_name}_{'_'.join(column_names)}"
        self.index_type = index_type
        self.unique = unique
        self.where_clause = where_clause

    def get_create_statement(self) -> str:
        """Generate SQL statement to create this index"""
        unique_str = "UNIQUE " if self.unique else ""
        columns_str = ", ".join(self.column_names)
        where_str = f" WHERE {self.where_clause}" if self.where_clause else ""
        
        return (
            f"CREATE {unique_str}INDEX IF NOT EXISTS {self.index_name} "
            f"ON {self.table_name} USING {self.index_type} ({columns_str}){where_str}"
        )

# Define recommended indexes for common queries
RECOMMENDED_INDEXES = [
    # Users table indexes
    IndexDefinition(
        table_name="users",
        column_names=["email"],
        unique=True,
        index_name="uidx_users_email"
    ),
    IndexDefinition(
        table_name="users",
        column_names=["verification_level", "is_active"],
        index_name="idx_users_verification_active"
    ),
    IndexDefinition(
        table_name="users",
        column_names=["is_active"],
        index_name="idx_users_is_active"
    ),
    IndexDefinition(
        table_name="users",
        column_names=["risk_score"],
        index_name="idx_users_risk_score"
    ),

    # Items table indexes
    IndexDefinition(
        table_name="items",
        column_names=["owner_id"],
        index_name="idx_items_owner_id"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["category", "is_available"],
        index_name="idx_items_category_available"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["subcategory", "is_available"],
        index_name="idx_items_subcategory_available"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["location", "is_available"],
        index_name="idx_items_location_available"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["daily_price"],
        index_name="idx_items_daily_price"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["created_at"],
        index_name="idx_items_created_at"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["name", "description"],
        index_type=IndexType.GIN,
        index_name="idx_items_text_search",
        where_clause="to_tsvector('english', name || ' ' || description) @@ to_tsquery('english', 'query')"
    ),

    # Rentals table indexes
    IndexDefinition(
        table_name="rentals",
        column_names=["item_id"],
        index_name="idx_rentals_item_id"
    ),
    IndexDefinition(
        table_name="rentals",
        column_names=["owner_id"],
        index_name="idx_rentals_owner_id"
    ),
    IndexDefinition(
        table_name="rentals",
        column_names=["renter_id"],
        index_name="idx_rentals_renter_id"
    ),
    IndexDefinition(
        table_name="rentals",
        column_names=["status", "start_date", "end_date"],
        index_name="idx_rentals_status_dates"
    ),

    # Auctions table indexes
    IndexDefinition(
        table_name="auctions",
        column_names=["item_id"],
        index_name="idx_auctions_item_id"
    ),
    IndexDefinition(
        table_name="auctions",
        column_names=["owner_id"],
        index_name="idx_auctions_owner_id"
    ),
    IndexDefinition(
        table_name="auctions",
        column_names=["status", "end_time"],
        index_name="idx_auctions_status_end_time"
    ),

    # Bids table indexes
    IndexDefinition(
        table_name="bids",
        column_names=["auction_id"],
        index_name="idx_bids_auction_id"
    ),
    IndexDefinition(
        table_name="bids",
        column_names=["bidder_id"],
        index_name="idx_bids_bidder_id"
    ),
    IndexDefinition(
        table_name="bids",
        column_names=["status"],
        index_name="idx_bids_status"
    ),

    # Agreements table indexes
    IndexDefinition(
        table_name="agreements",
        column_names=["rental_id"],
        index_name="idx_agreements_rental_id"
    ),
    IndexDefinition(
        table_name="agreements",
        column_names=["owner_id"],
        index_name="idx_agreements_owner_id"
    ),
    IndexDefinition(
        table_name="agreements",
        column_names=["renter_id"],
        index_name="idx_agreements_renter_id"
    ),
    IndexDefinition(
        table_name="agreements",
        column_names=["status"],
        index_name="idx_agreements_status"
    ),

    # Reviews table indexes
    IndexDefinition(
        table_name="reviews",
        column_names=["item_id"],
        index_name="idx_reviews_item_id"
    ),
    IndexDefinition(
        table_name="reviews",
        column_names=["reviewer_id"],
        index_name="idx_reviews_reviewer_id"
    ),
    IndexDefinition(
        table_name="reviews",
        column_names=["rating"],
        index_name="idx_reviews_rating"
    ),

    # Fraud alerts table indexes
    IndexDefinition(
        table_name="fraud_alerts",
        column_names=["user_id"],
        index_name="idx_fraud_alerts_user_id"
    ),
    IndexDefinition(
        table_name="fraud_alerts",
        column_names=["item_id"],
        index_name="idx_fraud_alerts_item_id"
    ),
    IndexDefinition(
        table_name="fraud_alerts",
        column_names=["status"],
        index_name="idx_fraud_alerts_status"
    ),
    IndexDefinition(
        table_name="fraud_alerts",
        column_names=["risk_score"],
        index_name="idx_fraud_alerts_risk_score"
    ),

    # Payments table indexes
    IndexDefinition(
        table_name="payments",
        column_names=["user_id"],
        index_name="idx_payments_user_id"
    ),
    IndexDefinition(
        table_name="payments",
        column_names=["rental_id"],
        index_name="idx_payments_rental_id"
    ),
    IndexDefinition(
        table_name="payments",
        column_names=["status"],
        index_name="idx_payments_status"
    ),
]

def get_existing_indexes(engine: Engine) -> Dict[str, List[Dict[str, Any]]]:
    """
    Get existing indexes from the database.

    Args:
        engine: SQLAlchemy engine

    Returns:
        Dictionary of tables and their indexes
    """
    with engine.connect() as conn:
        result = conn.execute(text("""
            SELECT
                t.relname AS table_name,
                i.relname AS index_name,
                array_agg(a.attname) AS column_names,
                ix.indisunique AS is_unique,
                am.amname AS index_type,
                pg_get_indexdef(ix.indexrelid) AS index_definition
            FROM
                pg_index ix
                JOIN pg_class i ON i.oid = ix.indexrelid
                JOIN pg_class t ON t.oid = ix.indrelid
                JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
                JOIN pg_am am ON am.oid = i.relam
            WHERE
                t.relkind = 'r'
                AND t.relname NOT LIKE 'pg_%'
                AND t.relname NOT LIKE 'sql_%'
            GROUP BY
                t.relname, i.relname, ix.indisunique, am.amname, ix.indexrelid
            ORDER BY
                t.relname, i.relname
        """))

        indexes_by_table = {}
        for row in result:
            table_name = row.table_name
            if table_name not in indexes_by_table:
                indexes_by_table[table_name] = []
            
            indexes_by_table[table_name].append({
                "index_name": row.index_name,
                "column_names": row.column_names,
                "is_unique": row.is_unique,
                "index_type": row.index_type,
                "index_definition": row.index_definition
            })
        
        return indexes_by_table

def apply_indexes(engine: Engine, dry_run: bool = False, verbose: bool = False) -> Tuple[int, int]:
    """
    Apply recommended indexes to the database.

    Args:
        engine: SQLAlchemy engine
        dry_run: If True, only show SQL statements without executing them
        verbose: If True, show detailed information about each index

    Returns:
        Tuple of (indexes_created, indexes_skipped)
    """
    # Get existing indexes
    existing_indexes = get_existing_indexes(engine)
    
    indexes_created = 0
    indexes_skipped = 0
    
    with engine.begin() as conn:
        for index_def in RECOMMENDED_INDEXES:
            # Check if index already exists
            table_indexes = existing_indexes.get(index_def.table_name, [])
            index_exists = False
            
            for existing_index in table_indexes:
                # Check if an index with the same name or same columns exists
                if (existing_index["index_name"] == index_def.index_name or
                    set(existing_index["column_names"]) == set(index_def.column_names)):
                    index_exists = True
                    break
            
            if index_exists:
                if verbose:
                    logger.info(f"Skipping existing index: {index_def.index_name} on {index_def.table_name}")
                indexes_skipped += 1
                continue
            
            # Create index
            create_statement = index_def.get_create_statement()
            
            if verbose or dry_run:
                logger.info(f"Creating index: {create_statement}")
            
            if not dry_run:
                try:
                    conn.execute(text(create_statement))
                    indexes_created += 1
                    logger.info(f"Created index: {index_def.index_name} on {index_def.table_name}")
                except SQLAlchemyError as e:
                    logger.error(f"Error creating index {index_def.index_name}: {e}")
    
    return indexes_created, indexes_skipped

def main():
    """Main function to apply database indexes."""
    parser = argparse.ArgumentParser(description="Apply database indexes to improve query performance")
    parser.add_argument("--dry-run", action="store_true", help="Show SQL statements without executing them")
    parser.add_argument("--verbose", action="store_true", help="Show detailed information about each index")
    
    args = parser.parse_args()
    
    logger.info("Starting database indexing...")
    start_time = time.time()
    
    # Create engine
    engine = create_engine(settings.DATABASE_URL)
    
    try:
        # Apply indexes
        indexes_created, indexes_skipped = apply_indexes(
            engine=engine,
            dry_run=args.dry_run,
            verbose=args.verbose
        )
        
        # Log results
        elapsed_time = time.time() - start_time
        logger.info(f"Database indexing completed in {elapsed_time:.2f} seconds")
        logger.info(f"Indexes created: {indexes_created}")
        logger.info(f"Indexes skipped: {indexes_skipped}")
        
        if args.dry_run:
            logger.info("This was a dry run. No changes were made to the database.")
    
    except Exception as e:
        logger.error(f"Error applying database indexes: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
