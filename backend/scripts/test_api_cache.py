#!/usr/bin/env python3
"""
Test script for API response caching.

This script tests the API response caching functionality by making
requests to various endpoints and verifying that responses are cached.
"""

import sys
import os
import time
import json
import argparse
import requests
import logging
from typing import Dict, Any, Optional

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_endpoint_caching(
    base_url: str,
    endpoint: str,
    auth_token: Optional[str] = None,
    params: Optional[Dict[str, Any]] = None,
    expected_cache_behavior: bool = True,
    num_requests: int = 3,
    delay: float = 0.5
) -> Dict[str, Any]:
    """
    Test caching for a specific endpoint.

    Args:
        base_url: Base URL of the API
        endpoint: Endpoint to test
        auth_token: Optional auth token for authenticated requests
        params: Optional query parameters
        expected_cache_behavior: Whether the endpoint is expected to be cached
        num_requests: Number of requests to make
        delay: Delay between requests in seconds

    Returns:
        Dictionary with test results
    """
    url = f"{base_url}{endpoint}"
    headers = {}
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"

    logger.info(f"Testing endpoint: {url}")

    # Make initial request
    start_time = time.time()
    # Add timeout to prevent hanging on unresponsive servers
    response = requests.get(url, headers=headers, params=params, timeout=10)
    first_request_time = time.time() - start_time

    if response.status_code != 200:
        logger.error(f"Error: {response.status_code} - {response.text}")
        return {
            "endpoint": endpoint,
            "status": "error",
            "status_code": response.status_code,
            "error": response.text
        }

    # Get initial response
    try:
        first_response = response.json()
    except json.JSONDecodeError:
        logger.error(f"Error parsing JSON response: {response.text}")
        return {
            "endpoint": endpoint,
            "status": "error",
            "error": "Invalid JSON response"
        }

    # Make additional requests
    request_times = [first_request_time]
    cached_responses = 0

    for i in range(1, num_requests):
        time.sleep(delay)

        start_time = time.time()
        # Add timeout to prevent hanging on unresponsive servers
        response = requests.get(url, headers=headers, params=params, timeout=10)
        request_time = time.time() - start_time
        request_times.append(request_time)

        if response.status_code != 200:
            logger.error(f"Error in request {i+1}: {response.status_code} - {response.text}")
            continue

        # Check if response is identical
        try:
            current_response = response.json()
            if current_response == first_response:
                cached_responses += 1
        except json.JSONDecodeError:
            logger.error(f"Error parsing JSON response in request {i+1}: {response.text}")

    # Calculate average request time
    avg_time = sum(request_times) / len(request_times)

    # Determine if caching is working
    is_cached = cached_responses == num_requests - 1

    # Check if caching behavior matches expected behavior
    status = "pass" if is_cached == expected_cache_behavior else "fail"

    result = {
        "endpoint": endpoint,
        "status": status,
        "is_cached": is_cached,
        "expected_cache_behavior": expected_cache_behavior,
        "num_requests": num_requests,
        "cached_responses": cached_responses,
        "first_request_time": first_request_time,
        "avg_time": avg_time,
        "request_times": request_times
    }

    logger.info(f"Result: {status} - Cached: {is_cached} - Avg time: {avg_time:.4f}s")

    return result


def test_cache_invalidation(
    base_url: str,
    endpoint: str,
    invalidation_endpoint: str,
    invalidation_data: Dict[str, Any],
    auth_token: Optional[str] = None,
    params: Optional[Dict[str, Any]] = None,
    num_requests: int = 2,
    delay: float = 0.5
) -> Dict[str, Any]:
    """
    Test cache invalidation.

    Args:
        base_url: Base URL of the API
        endpoint: Endpoint to test
        invalidation_endpoint: Endpoint to invalidate cache
        invalidation_data: Data to send to invalidation endpoint
        auth_token: Optional auth token for authenticated requests
        params: Optional query parameters
        num_requests: Number of requests to make before and after invalidation
        delay: Delay between requests in seconds

    Returns:
        Dictionary with test results
    """
    url = f"{base_url}{endpoint}"
    invalidation_url = f"{base_url}{invalidation_endpoint}"
    headers = {}
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"

    logger.info(f"Testing cache invalidation for endpoint: {url}")

    # Make initial requests to cache the response
    responses_before = []
    for i in range(num_requests):
        # Add timeout to prevent hanging on unresponsive servers
        response = requests.get(url, headers=headers, params=params, timeout=10)
        if response.status_code == 200:
            try:
                responses_before.append(response.json())
            except json.JSONDecodeError:
                logger.error(f"Error parsing JSON response: {response.text}")
                return {
                    "endpoint": endpoint,
                    "status": "error",
                    "error": "Invalid JSON response"
                }
        time.sleep(delay)

    # Check if responses are cached
    is_cached_before = all(r == responses_before[0] for r in responses_before)

    # Invalidate cache
    logger.info(f"Invalidating cache: {invalidation_url}")
    # Add timeout to prevent hanging on unresponsive servers
    invalidation_response = requests.post(
        invalidation_url,
        headers=headers,
        json=invalidation_data,
        timeout=10
    )

    if invalidation_response.status_code != 200:
        logger.error(f"Error invalidating cache: {invalidation_response.status_code} - {invalidation_response.text}")
        return {
            "endpoint": endpoint,
            "status": "error",
            "error": f"Cache invalidation failed: {invalidation_response.status_code}"
        }

    # Make requests after invalidation
    time.sleep(delay)
    responses_after = []
    for i in range(num_requests):
        # Add timeout to prevent hanging on unresponsive servers
        response = requests.get(url, headers=headers, params=params, timeout=10)
        if response.status_code == 200:
            try:
                responses_after.append(response.json())
            except json.JSONDecodeError:
                logger.error(f"Error parsing JSON response: {response.text}")
                return {
                    "endpoint": endpoint,
                    "status": "error",
                    "error": "Invalid JSON response"
                }
        time.sleep(delay)

    # Check if first response after invalidation is different from before
    first_response_different = responses_before[0] != responses_after[0]

    # Check if responses after invalidation are cached
    is_cached_after = all(r == responses_after[0] for r in responses_after)

    result = {
        "endpoint": endpoint,
        "status": "pass" if first_response_different else "fail",
        "is_cached_before": is_cached_before,
        "is_cached_after": is_cached_after,
        "first_response_different": first_response_different,
        "invalidation_status_code": invalidation_response.status_code
    }

    logger.info(f"Result: {result['status']} - First response different: {first_response_different}")

    return result


def main():
    """Main function to run the tests."""
    parser = argparse.ArgumentParser(description="Test API response caching")
    parser.add_argument("--base-url", default="http://localhost:8000", help="Base URL of the API")
    parser.add_argument("--auth-token", help="Auth token for authenticated requests")
    parser.add_argument("--admin-token", help="Admin token for cache management endpoints")

    args = parser.parse_args()

    base_url = args.base_url
    auth_token = args.auth_token
    admin_token = args.admin_token or auth_token

    # Define endpoints to test
    endpoints = [
        # Public endpoints (should be cached)
        {"endpoint": "/api/v1/items", "expected_cache_behavior": True},
        {"endpoint": "/api/v1/categories", "expected_cache_behavior": True},

        # Authenticated endpoints (should not be cached by default)
        {"endpoint": "/api/v1/users/me", "auth_token": auth_token, "expected_cache_behavior": False},

        # Endpoints with query parameters
        {"endpoint": "/api/v1/items", "params": {"limit": 5}, "expected_cache_behavior": True},
        {"endpoint": "/api/v1/items", "params": {"category": "Electronics"}, "expected_cache_behavior": True},

        # Endpoints with nocache parameter (should not be cached)
        {"endpoint": "/api/v1/items", "params": {"nocache": "true"}, "expected_cache_behavior": False},
    ]

    # Run tests
    results = []
    for endpoint_config in endpoints:
        result = test_endpoint_caching(
            base_url=base_url,
            endpoint=endpoint_config["endpoint"],
            auth_token=endpoint_config.get("auth_token"),
            params=endpoint_config.get("params"),
            expected_cache_behavior=endpoint_config["expected_cache_behavior"]
        )
        results.append(result)

    # Test cache invalidation
    if admin_token:
        invalidation_result = test_cache_invalidation(
            base_url=base_url,
            endpoint="/api/v1/items",
            invalidation_endpoint="/api/v1/cache/invalidate",
            invalidation_data={"pattern": "items"},
            auth_token=admin_token
        )
        results.append(invalidation_result)

    # Print summary
    logger.info("\nTest Summary:")
    passed = sum(1 for r in results if r["status"] == "pass")
    failed = sum(1 for r in results if r["status"] == "fail")
    errors = sum(1 for r in results if r["status"] == "error")

    logger.info(f"Passed: {passed}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Errors: {errors}")

    # Print detailed results
    logger.info("\nDetailed Results:")
    for i, result in enumerate(results):
        logger.info(f"{i+1}. {result['endpoint']} - {result['status']}")
        if result["status"] == "fail":
            if "expected_cache_behavior" in result:
                logger.info(f"   Expected cache behavior: {result['expected_cache_behavior']}")
                logger.info(f"   Actual cache behavior: {result['is_cached']}")
            elif "first_response_different" in result:
                logger.info(f"   First response different after invalidation: {result['first_response_different']}")

    # Return exit code based on results
    if failed > 0 or errors > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
