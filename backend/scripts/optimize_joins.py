#!/usr/bin/env python3
"""
Script to identify and optimize complex JOIN operations in the RentUp codebase.

This script:
1. Analyzes the database schema to identify relationships between tables
2. Identifies complex JOIN operations in the codebase
3. Creates indexes for foreign keys used in JOIN operations
4. Measures the performance of JOIN operations before and after optimization
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import re
from typing import Dict, List, Set, Tuple, Any, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text, inspect, MetaData, Table, Column, ForeignKey
from sqlalchemy.orm import Session

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database import engine, session_scope
    from app.core.join_optimization import Join<PERSON>attern, JoinType
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Optimize JOIN operations in RentUp')
    parser.add_argument('--analyze', action='store_true', help='Analyze JOIN operations')
    parser.add_argument('--create-indexes', action='store_true', help='Create indexes for JOIN operations')
    parser.add_argument('--test', action='store_true', help='Test JOIN operation performance')
    parser.add_argument('--all', action='store_true', help='Perform all optimization steps')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def get_foreign_keys(db: Session) -> List[Dict[str, Any]]:
    """
    Get all foreign keys in the database.

    Args:
        db: Database session

    Returns:
        List of foreign key information
    """
    query = """
    SELECT
        tc.table_schema,
        tc.constraint_name,
        tc.table_name,
        kcu.column_name,
        ccu.table_schema AS foreign_table_schema,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
    FROM
        information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
    ORDER BY tc.table_name, kcu.column_name
    """

    result = db.execute(text(query))

    foreign_keys = []
    for row in result:
        foreign_keys.append({
            "schema": row.table_schema,
            "constraint_name": row.constraint_name,
            "table_name": row.table_name,
            "column_name": row.column_name,
            "foreign_table_schema": row.foreign_table_schema,
            "foreign_table_name": row.foreign_table_name,
            "foreign_column_name": row.foreign_column_name
        })

    return foreign_keys

def get_existing_indexes(db: Session) -> List[Dict[str, Any]]:
    """
    Get all existing indexes in the database.

    Args:
        db: Database session

    Returns:
        List of index information
    """
    query = """
    SELECT
        t.relname AS table_name,
        i.relname AS index_name,
        a.attname AS column_name,
        ix.indisunique AS is_unique,
        am.amname AS index_type
    FROM
        pg_index ix
        JOIN pg_class i ON i.oid = ix.indexrelid
        JOIN pg_class t ON t.oid = ix.indrelid
        JOIN pg_attribute a ON a.attrelid = t.oid AND a.attnum = ANY(ix.indkey)
        JOIN pg_am am ON am.oid = i.relam
    WHERE
        t.relkind = 'r'
        AND t.relname NOT LIKE 'pg_%'
        AND t.relname NOT LIKE 'sql_%'
    ORDER BY t.relname, i.relname, a.attnum
    """

    result = db.execute(text(query))

    # Group by index name
    indexes = {}
    for row in result:
        index_name = row.index_name
        if index_name not in indexes:
            indexes[index_name] = {
                "table_name": row.table_name,
                "index_name": index_name,
                "is_unique": row.is_unique,
                "index_type": row.index_type,
                "columns": []
            }
        indexes[index_name]["columns"].append(row.column_name)

    return list(indexes.values())

def create_index(db: Session, table_name: str, column_name: str, index_name: Optional[str] = None) -> bool:
    """
    Create an index on a column.

    Args:
        db: Database session
        table_name: Table name
        column_name: Column name
        index_name: Optional index name

    Returns:
        True if index was created successfully, False otherwise
    """
    if index_name is None:
        index_name = f"idx_{table_name}_{column_name}"

    try:
        db.execute(text(f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({column_name})"))
        db.commit()
        logger.info(f"Created index {index_name} on {table_name}({column_name})")
        return True
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating index {index_name}: {str(e)}")
        return False

def analyze_join_operations(db: Session) -> List[JoinPattern]:
    """
    Analyze JOIN operations in the database.

    Args:
        db: Database session

    Returns:
        List of JOIN patterns
    """
    # Get foreign keys
    foreign_keys = get_foreign_keys(db)

    # Create JOIN patterns from foreign keys
    patterns = []
    for fk in foreign_keys:
        pattern = JoinPattern(
            left_table=fk["table_name"],
            right_table=fk["foreign_table_name"],
            join_type=JoinType.INNER,
            join_condition=f"{fk['table_name']}.{fk['column_name']} = {fk['foreign_table_name']}.{fk['foreign_column_name']}"
        )
        patterns.append(pattern)

    return patterns

def test_join_performance(db: Session, patterns: List[JoinPattern]) -> Dict[str, Dict[str, float]]:
    """
    Test the performance of JOIN operations.

    Args:
        db: Database session
        patterns: List of JOIN patterns

    Returns:
        Dictionary of performance results
    """
    results = {}

    for pattern in patterns:
        # Create a simple JOIN query
        query = f"""
        SELECT COUNT(*)
        FROM {pattern.left_table}
        {pattern.join_type} {pattern.right_table}
        ON {pattern.join_condition}
        """

        # Measure performance
        start_time = time.time()
        result = db.execute(text(query))
        count = result.scalar()
        execution_time = time.time() - start_time

        # Record results
        results[str(pattern)] = {
            "count": count,
            "execution_time": execution_time
        }

        logger.info(f"JOIN {pattern}: {count} rows in {execution_time:.6f}s")

    return results

def create_indexes_for_joins(db: Session, patterns: List[JoinPattern]) -> int:
    """
    Create indexes for JOIN operations.

    Args:
        db: Database session
        patterns: List of JOIN patterns

    Returns:
        Number of indexes created
    """
    # Get existing indexes
    existing_indexes = get_existing_indexes(db)
    existing_index_columns = set()

    for index in existing_indexes:
        for column in index["columns"]:
            existing_index_columns.add(f"{index['table_name']}.{column}")

    # Create indexes for foreign keys
    created_count = 0
    for pattern in patterns:
        # Extract table and column from join condition
        match = re.search(r'(\w+)\.(\w+)\s*=\s*(\w+)\.(\w+)', pattern.join_condition)
        if match:
            left_table, left_column, right_table, right_column = match.groups()

            # Check if indexes already exist
            left_key = f"{left_table}.{left_column}"
            right_key = f"{right_table}.{right_column}"

            if left_key not in existing_index_columns:
                if create_index(db, left_table, left_column):
                    created_count += 1
                    existing_index_columns.add(left_key)

            if right_key not in existing_index_columns:
                if create_index(db, right_table, right_column):
                    created_count += 1
                    existing_index_columns.add(right_key)

    return created_count

def main():
    """Main function."""
    args = parse_args()

    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Determine what to do
    do_analyze = args.analyze or args.all
    do_create_indexes = args.create_indexes or args.all
    do_test = args.test or args.all

    # If no actions specified, show help
    if not (do_analyze or do_create_indexes or do_test):
        logger.info("No actions specified. Use --analyze, --create-indexes, --test, or --all")
        return 1

    with session_scope() as db:
        # Analyze JOIN operations
        if do_analyze:
            logger.info("Analyzing JOIN operations...")
            patterns = analyze_join_operations(db)
            logger.info(f"Found {len(patterns)} JOIN patterns")

            for pattern in patterns:
                logger.info(f"  {pattern}")

        # Create indexes for JOIN operations
        if do_create_indexes:
            logger.info("Creating indexes for JOIN operations...")
            patterns = analyze_join_operations(db)
            created_count = create_indexes_for_joins(db, patterns)
            logger.info(f"Created {created_count} indexes")

        # Test JOIN performance
        if do_test:
            logger.info("Testing JOIN performance...")
            patterns = analyze_join_operations(db)
            results = test_join_performance(db, patterns)

            # Log results
            logger.info("JOIN performance results:")
            for pattern, stats in results.items():
                logger.info(f"  {pattern}: {stats['count']} rows in {stats['execution_time']:.6f}s")

    logger.info("JOIN optimization completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
