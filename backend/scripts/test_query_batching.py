#!/usr/bin/env python3
"""
Standalone test for the query batching system.

This script tests the query batching system without depending on the main app
or pytest infrastructure.
"""

import os
import sys
import time
import logging
from typing import List, TypeVar, Generic, Union
from collections import defaultdict
from unittest.mock import MagicMock
from sqlalchemy import Column, Integer, String, Float, Boolean, ForeignKey, create_engine, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session, Query

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Type variables for generic functions
T = TypeVar('T')
R = TypeVar('R')
M = TypeVar('M')  # Model type

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Define the query batching classes here to avoid dependencies
class BatchQuery(Generic[M]):
    """
    Batch query builder for efficient database operations.
    """

    def __init__(self, model_class: M, db: Session):
        """
        Initialize a batch query.
        """
        self.model_class = model_class
        self.db = db
        self.filters = []
        self.joins = []
        self.order_by = []
        self.group_by = []
        self.limit_val = None
        self.offset_val = None
        self.options_list = []
        self._query = None
        self.start_time = None

    def filter(self, *criterion):
        """
        Add filter criteria to the query.
        """
        self.filters.extend(criterion)
        return self

    def join(self, *props, **kwargs):
        """
        Add join clauses to the query.
        """
        self.joins.append((props, kwargs))
        return self

    def options(self, *opts):
        """
        Add query options like joinedload or selectinload.
        """
        self.options_list.extend(opts)
        return self

    def order_by(self, *criterion):
        """
        Add order by clauses to the query.
        """
        self.order_by.extend(criterion)
        return self

    def group_by(self, *criterion):
        """
        Add group by clauses to the query.
        """
        self.group_by.extend(criterion)
        return self

    def limit(self, limit_val):
        """
        Set limit for the query.
        """
        self.limit_val = limit_val
        return self

    def offset(self, offset_val):
        """
        Set offset for the query.
        """
        self.offset_val = offset_val
        return self

    def _build_query(self):
        """
        Build the SQLAlchemy query from the configured parameters.
        """
        if self._query is not None:
            return self._query

        # Start with base query
        query = self.db.query(self.model_class)

        # Apply joins
        for join_args, join_kwargs in self.joins:
            query = query.join(*join_args, **join_kwargs)

        # Apply filters
        for filter_criterion in self.filters:
            query = query.filter(filter_criterion)

        # Apply options
        for option in self.options_list:
            query = query.options(option)

        # Apply order by
        for order_criterion in self.order_by:
            query = query.order_by(order_criterion)

        # Apply group by
        for group_criterion in self.group_by:
            query = query.group_by(group_criterion)

        # Apply limit and offset
        if self.limit_val is not None:
            query = query.limit(self.limit_val)

        if self.offset_val is not None:
            query = query.offset(self.offset_val)

        self._query = query
        return query

    def all(self):
        """
        Execute the query and return all results.
        """
        self.start_time = time.time()
        query = self._build_query()
        result = query.all()
        execution_time = time.time() - self.start_time

        # Log query performance
        logger.debug(f"Batch query executed in {execution_time:.4f}s, returned {len(result)} results")

        return result

    def first(self):
        """
        Execute the query and return the first result.
        """
        self.start_time = time.time()
        query = self._build_query()
        result = query.first()
        execution_time = time.time() - self.start_time

        # Log query performance
        logger.debug(f"Batch query executed in {execution_time:.4f}s, returned first result")

        return result

    def count(self):
        """
        Execute the query and return the count of results.
        """
        self.start_time = time.time()
        query = self._build_query().with_entities(func.count())
        result = query.scalar()
        execution_time = time.time() - self.start_time

        # Log query performance
        logger.debug(f"Batch query count executed in {execution_time:.4f}s, returned {result}")

        return result


class QueryBatcher:
    """
    Query batcher for executing multiple queries in a single database round-trip.
    """

    def __init__(self, db: Session):
        """
        Initialize a query batcher.
        """
        self.db = db
        self.queries = []
        self.start_time = None

    def add_query(self, query: Union[Query, BatchQuery]):
        """
        Add a query to the batch.
        """
        if isinstance(query, BatchQuery):
            query = query._build_query()

        self.queries.append(query)
        return len(self.queries) - 1

    def execute(self):
        """
        Execute all queries in the batch.
        """
        self.start_time = time.time()

        results = []
        for query in self.queries:
            results.append(query.all())

        execution_time = time.time() - self.start_time

        # Log batch performance
        logger.debug(f"Executed batch of {len(self.queries)} queries in {execution_time:.4f}s")

        return results


def batch_get_by_ids(db: Session, model_class: M, ids: List[str], id_field=None):
    """
    Efficiently get multiple model instances by their IDs.
    """
    if not ids:
        return {}

    # Use provided ID field or default to 'id'
    if id_field is None:
        id_field = 'id'

    # Get the actual field object
    id_column = getattr(model_class, id_field)

    # Execute query
    start_time = time.time()
    instances = db.query(model_class).filter(id_column.in_(ids)).all()
    execution_time = time.time() - start_time

    # Log performance
    logger.debug(f"Batch get by IDs executed in {execution_time:.4f}s, fetched {len(instances)} of {len(ids)} items")

    # Create ID to instance mapping
    result = {getattr(instance, id_field): instance for instance in instances}

    return result


def batch_get_related(db: Session, model_class: M, parent_ids: List[str],
                     relationship_field: str, parent_id_field: str = 'id'):
    """
    Efficiently get related entities for multiple parent IDs.
    """
    if not parent_ids:
        return {}

    # Get the relationship field
    rel_field = getattr(model_class, relationship_field)

    # Execute query
    start_time = time.time()
    instances = db.query(model_class).filter(rel_field.in_(parent_ids)).all()
    execution_time = time.time() - start_time

    # Log performance
    logger.debug(f"Batch get related executed in {execution_time:.4f}s, fetched {len(instances)} items")

    # Group by parent ID
    result = defaultdict(list)
    for instance in instances:
        parent_id = getattr(instance, relationship_field)
        result[parent_id].append(instance)

    return result

# Create test models
Base = declarative_base()


class TestItem(Base):
    __tablename__ = "test_items"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    price = Column(Float, nullable=False)
    category = Column(String, nullable=True)
    is_available = Column(Boolean, default=True)
    owner_id = Column(String, ForeignKey("test_users.id"))


class TestUser(Base):
    __tablename__ = "test_users"

    id = Column(String, primary_key=True)
    email = Column(String, nullable=False)
    full_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)


def create_mock_db():
    """Create a mock database session."""
    db = MagicMock(spec=Session)

    # Create a fresh mock for each test
    # This allows tests to override the mock behavior

    # Default query results
    items = [
        TestItem(id="item1", name="Item 1", price=10.0, category="category1", owner_id="user1"),
        TestItem(id="item2", name="Item 2", price=20.0, category="category1", owner_id="user1"),
        TestItem(id="item3", name="Item 3", price=30.0, category="category2", owner_id="user2"),
        TestItem(id="item4", name="Item 4", price=40.0, category="category2", owner_id="user2"),
        TestItem(id="item5", name="Item 5", price=50.0, category="category3", owner_id="user3"),
    ]

    users = [
        TestUser(id="user1", email="<EMAIL>", full_name="User One"),
        TestUser(id="user2", email="<EMAIL>", full_name="User Two"),
        TestUser(id="user3", email="<EMAIL>", full_name="User Three"),
    ]

    # Create a new mock for the query method
    query_mock = MagicMock()

    # Configure the filter method
    filter_mock = MagicMock()
    filter_mock.all.return_value = items  # Default return value
    filter_mock.first.return_value = items[0] if items else None

    # Configure the query mock
    query_mock.filter.return_value = filter_mock
    query_mock.all.return_value = items
    query_mock.first.return_value = items[0] if items else None

    # Configure with_entities
    with_entities_mock = MagicMock()
    with_entities_mock.scalar.return_value = len(items)
    query_mock.with_entities.return_value = with_entities_mock

    # Set up the db.query method
    db.query.return_value = query_mock

    return db


def test_batch_query_all():
    """Test BatchQuery.all() method."""
    # Create mock database
    mock_db = create_mock_db()

    # Create batch query
    query = BatchQuery(TestItem, mock_db)

    # Execute query
    results = query.all()

    # Check results
    assert len(results) == 5
    assert results[0].id == "item1"
    assert results[1].id == "item2"

    # Verify mock calls
    mock_db.query.assert_called_once_with(TestItem)

    print("✅ test_batch_query_all passed")


def test_batch_query_filter():
    """Test BatchQuery.filter() method."""
    # Create mock database
    mock_db = create_mock_db()

    # Create batch query
    query = BatchQuery(TestItem, mock_db)

    # Add filter
    query.filter(TestItem.category == "category1")

    # Execute query
    results = query.all()

    # Check results
    assert len(results) == 5  # Mock returns all items

    # Verify mock calls
    mock_db.query.assert_called_once_with(TestItem)

    print("✅ test_batch_query_filter passed")


def test_query_batcher():
    """Test QueryBatcher."""
    # Create mock database
    mock_db = create_mock_db()

    # Create items and users for testing
    items = [
        TestItem(id="item1", name="Item 1"),
        TestItem(id="item2", name="Item 2"),
        TestItem(id="item3", name="Item 3"),
    ]

    users = [
        TestUser(id="user1", email="<EMAIL>"),
        TestUser(id="user2", email="<EMAIL>"),
    ]

    # Configure the mock to return different results for different models
    mock_db.query.side_effect = lambda model: MagicMock(
        all=MagicMock(return_value=items if model == TestItem else users),
        filter=MagicMock(
            return_value=MagicMock(
                all=MagicMock(return_value=items if model == TestItem else users)
            )
        )
    )

    # Create query batcher
    batcher = QueryBatcher(mock_db)

    # Add queries
    query1 = BatchQuery(TestItem, mock_db)
    query2 = BatchQuery(TestUser, mock_db)

    batcher.add_query(query1)
    batcher.add_query(query2)

    # Execute batch
    results = batcher.execute()

    # Check results
    assert len(results) == 2, f"Expected 2 result sets, got {len(results)}"
    assert len(results[0]) == len(items), f"Expected {len(items)} items, got {len(results[0])}"
    assert len(results[1]) == len(users), f"Expected {len(users)} users, got {len(results[1])}"

    # Verify mock calls
    assert mock_db.query.call_count >= 2, f"Expected at least 2 query calls, got {mock_db.query.call_count}"

    print("✅ test_query_batcher passed")


def test_batch_get_by_ids():
    """Test batch_get_by_ids function."""
    # Create mock database
    mock_db = create_mock_db()

    # Create test items with specific IDs
    items = [
        TestItem(id="item1", name="Item 1"),
        TestItem(id="item3", name="Item 3"),
    ]

    # Configure the mock to return our specific items
    mock_db.query.return_value.filter.return_value.all.return_value = items

    # Call the function
    result = batch_get_by_ids(mock_db, TestItem, ["item1", "item3"])

    # Check result - we expect exactly these two items
    assert len(result) == 2, f"Expected 2 items, got {len(result)}"
    assert "item1" in result, f"Expected 'item1' in result, got keys: {list(result.keys())}"
    assert "item3" in result, f"Expected 'item3' in result, got keys: {list(result.keys())}"

    # Check item properties
    if "item1" in result:
        assert result["item1"].name == "Item 1", f"Expected name 'Item 1', got {result['item1'].name}"
    if "item3" in result:
        assert result["item3"].name == "Item 3", f"Expected name 'Item 3', got {result['item3'].name}"

    # Verify mock calls
    mock_db.query.assert_called_with(TestItem)

    print("✅ test_batch_get_by_ids passed")


def test_batch_get_related():
    """Test batch_get_related function."""
    # Create mock database
    mock_db = create_mock_db()

    # Create test items with specific owner IDs
    items = [
        TestItem(id="item1", owner_id="user1"),
        TestItem(id="item2", owner_id="user1"),
        TestItem(id="item3", owner_id="user2"),
    ]

    # Configure the mock to return our specific items
    mock_db.query.return_value.filter.return_value.all.return_value = items

    # Call the function
    result = batch_get_related(mock_db, TestItem, ["user1", "user2"], "owner_id")

    # Check result
    assert len(result) == 2, f"Expected 2 users, got {len(result)}"
    assert "user1" in result, f"Expected 'user1' in result, got keys: {list(result.keys())}"
    assert "user2" in result, f"Expected 'user2' in result, got keys: {list(result.keys())}"

    # Check item counts per user
    if "user1" in result:
        assert len(result["user1"]) > 0, f"Expected items for user1, got {len(result['user1'])}"
    if "user2" in result:
        assert len(result["user2"]) > 0, f"Expected items for user2, got {len(result['user2'])}"

    # Verify mock calls
    mock_db.query.assert_called_with(TestItem)

    print("✅ test_batch_get_related passed")


def run_all_tests():
    """Run all tests."""
    print("Running query batching tests...")

    test_batch_query_all()
    test_batch_query_filter()
    test_query_batcher()
    test_batch_get_by_ids()
    test_batch_get_related()

    print("\nAll tests passed! ✅")


if __name__ == "__main__":
    run_all_tests()
