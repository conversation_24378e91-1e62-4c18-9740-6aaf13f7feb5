#!/usr/bin/env python
"""
Performance Optimization Script for RentUp Backend.

This script analyzes and optimizes the performance of the RentUp backend,
including:
- Caching optimization
- Database query optimization
- Memory usage optimization
- API response time optimization
- Concurrency optimization
"""

import os
import sys
import time
import logging
import argparse
import json
import requests
import statistics
from typing import Dict, List, Any, Tuple
from datetime import datetime
import concurrent.futures

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"performance_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Optimize RentUp backend performance')
    parser.add_argument('--api-url', type=str, default='http://localhost:8000',
                        help='API URL to test')
    parser.add_argument('--output-dir', type=str, default='performance_results',
                        help='Directory to save results')
    parser.add_argument('--concurrency', type=int, default=10,
                        help='Number of concurrent requests to simulate')
    parser.add_argument('--iterations', type=int, default=100,
                        help='Number of iterations for each endpoint')
    parser.add_argument('--auth-token', type=str,
                        help='Authentication token for API requests')
    return parser.parse_args()

def measure_endpoint_performance(url: str, auth_token: str = None, method: str = 'GET', 
                                data: Dict[str, Any] = None) -> Dict[str, Any]:
    """Measure the performance of an API endpoint."""
    headers = {}
    if auth_token:
        headers['Authorization'] = f"Bearer {auth_token}"
    
    try:
        start_time = time.time()
        
        if method == 'GET':
            response = requests.get(url, headers=headers, timeout=30)
        elif method == 'POST':
            headers['Content-Type'] = 'application/json'
            response = requests.post(url, headers=headers, json=data, timeout=30)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
        
        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        return {
            'url': url,
            'method': method,
            'status_code': response.status_code,
            'response_time': response_time,
            'response_size': len(response.content),
            'success': response.status_code < 400
        }
    
    except Exception as e:
        logger.error(f"Error measuring endpoint {url}: {e}")
        return {
            'url': url,
            'method': method,
            'status_code': 0,
            'response_time': 0,
            'response_size': 0,
            'success': False,
            'error': str(e)
        }

def test_endpoint_concurrency(url: str, concurrency: int, iterations: int, 
                             auth_token: str = None, method: str = 'GET',
                             data: Dict[str, Any] = None) -> List[Dict[str, Any]]:
    """Test an endpoint with concurrent requests."""
    results = []
    
    for i in range(iterations):
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = [
                executor.submit(measure_endpoint_performance, url, auth_token, method, data)
                for _ in range(concurrency)
            ]
            
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error in concurrent request: {e}")
    
    return results

def analyze_results(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze the performance test results."""
    if not results:
        return {}
    
    # Filter out failed requests
    successful_results = [r for r in results if r['success']]
    
    if not successful_results:
        return {
            'url': results[0]['url'],
            'method': results[0]['method'],
            'success_rate': 0,
            'total_requests': len(results),
            'successful_requests': 0,
            'failed_requests': len(results),
            'error': "All requests failed"
        }
    
    # Calculate statistics
    response_times = [r['response_time'] for r in successful_results]
    
    return {
        'url': results[0]['url'],
        'method': results[0]['method'],
        'success_rate': len(successful_results) / len(results) * 100,
        'total_requests': len(results),
        'successful_requests': len(successful_results),
        'failed_requests': len(results) - len(successful_results),
        'min_response_time': min(response_times),
        'max_response_time': max(response_times),
        'avg_response_time': statistics.mean(response_times),
        'median_response_time': statistics.median(response_times),
        'p95_response_time': statistics.quantiles(response_times, n=20)[18],  # 95th percentile
        'p99_response_time': statistics.quantiles(response_times, n=100)[98],  # 99th percentile
        'std_dev_response_time': statistics.stdev(response_times) if len(response_times) > 1 else 0,
        'avg_response_size': statistics.mean([r['response_size'] for r in successful_results])
    }

def suggest_optimizations(analysis: Dict[str, Any]) -> List[str]:
    """Suggest optimizations based on the performance analysis."""
    suggestions = []
    
    # Check response time
    if analysis.get('avg_response_time', 0) > 500:
        suggestions.append(f"High average response time ({analysis['avg_response_time']:.2f}ms). Consider optimizing database queries or adding caching.")
    
    # Check success rate
    if analysis.get('success_rate', 100) < 95:
        suggestions.append(f"Low success rate ({analysis['success_rate']:.2f}%). Investigate failed requests.")
    
    # Check response time variability
    if analysis.get('std_dev_response_time', 0) > 100:
        suggestions.append(f"High response time variability (std dev: {analysis['std_dev_response_time']:.2f}ms). Consider optimizing for consistent performance.")
    
    # Check p95 vs average
    if analysis.get('p95_response_time', 0) > 3 * analysis.get('avg_response_time', 0):
        suggestions.append(f"95th percentile response time ({analysis['p95_response_time']:.2f}ms) is much higher than average ({analysis['avg_response_time']:.2f}ms). Optimize for tail latency.")
    
    # Check response size
    if analysis.get('avg_response_size', 0) > 100000:  # 100KB
        suggestions.append(f"Large average response size ({analysis['avg_response_size'] / 1024:.2f}KB). Consider pagination or reducing payload size.")
    
    return suggestions

def main():
    """Main function."""
    args = parse_args()
    
    # Create output directory
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # Define endpoints to test
    endpoints = [
        {'url': f"{args.api_url}/api/v1/items", 'method': 'GET'},
        {'url': f"{args.api_url}/api/v1/items/search", 'method': 'POST', 'data': {'query': 'camera', 'limit': 10}},
        {'url': f"{args.api_url}/api/v1/categories", 'method': 'GET'},
        {'url': f"{args.api_url}/api/v1/recommendations/similar/1", 'method': 'GET'},
        {'url': f"{args.api_url}/api/v1/recommendations/trending", 'method': 'GET'},
        {'url': f"{args.api_url}/api/v1/health", 'method': 'GET'}
    ]
    
    # Test each endpoint
    all_results = {}
    all_analyses = {}
    
    for endpoint in endpoints:
        logger.info(f"Testing endpoint: {endpoint['url']} ({endpoint['method']})")
        
        results = test_endpoint_concurrency(
            endpoint['url'],
            args.concurrency,
            args.iterations,
            args.auth_token,
            endpoint['method'],
            endpoint.get('data')
        )
        
        analysis = analyze_results(results)
        suggestions = suggest_optimizations(analysis)
        
        all_results[endpoint['url']] = results
        all_analyses[endpoint['url']] = {
            'analysis': analysis,
            'suggestions': suggestions
        }
        
        logger.info(f"Completed testing {endpoint['url']}")
        logger.info(f"Average response time: {analysis.get('avg_response_time', 0):.2f}ms")
        logger.info(f"Success rate: {analysis.get('success_rate', 0):.2f}%")
        
        for suggestion in suggestions:
            logger.info(f"Suggestion: {suggestion}")
    
    # Save results
    with open(f"{args.output_dir}/performance_results.json", 'w') as f:
        json.dump(all_analyses, f, indent=2)
    
    # Generate summary report
    with open(f"{args.output_dir}/performance_summary.txt", 'w') as f:
        f.write("RentUp Backend Performance Summary\n")
        f.write("=================================\n\n")
        f.write(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"API URL: {args.api_url}\n")
        f.write(f"Concurrency: {args.concurrency}\n")
        f.write(f"Iterations: {args.iterations}\n\n")
        
        for url, data in all_analyses.items():
            analysis = data['analysis']
            suggestions = data['suggestions']
            
            f.write(f"Endpoint: {url} ({analysis.get('method', 'GET')})\n")
            f.write("-" * 80 + "\n")
            f.write(f"Success Rate: {analysis.get('success_rate', 0):.2f}%\n")
            f.write(f"Total Requests: {analysis.get('total_requests', 0)}\n")
            f.write(f"Successful Requests: {analysis.get('successful_requests', 0)}\n")
            f.write(f"Failed Requests: {analysis.get('failed_requests', 0)}\n\n")
            
            f.write("Response Time Statistics:\n")
            f.write(f"  Min: {analysis.get('min_response_time', 0):.2f}ms\n")
            f.write(f"  Max: {analysis.get('max_response_time', 0):.2f}ms\n")
            f.write(f"  Avg: {analysis.get('avg_response_time', 0):.2f}ms\n")
            f.write(f"  Median: {analysis.get('median_response_time', 0):.2f}ms\n")
            f.write(f"  95th Percentile: {analysis.get('p95_response_time', 0):.2f}ms\n")
            f.write(f"  99th Percentile: {analysis.get('p99_response_time', 0):.2f}ms\n")
            f.write(f"  Standard Deviation: {analysis.get('std_dev_response_time', 0):.2f}ms\n\n")
            
            f.write("Optimization Suggestions:\n")
            if suggestions:
                for suggestion in suggestions:
                    f.write(f"  - {suggestion}\n")
            else:
                f.write("  - No specific optimizations suggested.\n")
            
            f.write("\n\n")
    
    logger.info(f"Performance analysis complete. Results saved to {args.output_dir}/")

if __name__ == "__main__":
    main()
