#!/usr/bin/env python3
"""
Script to set up and configure database sharding for RentUp.

This script:
1. Sets up PostgreSQL database shards
2. Configures sharding for the application
3. Creates necessary tables and indexes on each shard
4. Tests the sharding setup
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import subprocess
import json
import hashlib
from typing import Dict, List, Any, Optional, Tuple

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import psycopg2
from psycopg2 import sql
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Set up database sharding for RentUp')
    parser.add_argument('--shard-count', type=int, default=4, help='Number of database shards')
    parser.add_argument('--base-port', type=int, default=5440, help='Base port for database shards')
    parser.add_argument('--docker', action='store_true', help='Use Docker for shard setup')
    parser.add_argument('--update-config', action='store_true', help='Update application configuration')
    parser.add_argument('--test', action='store_true', help='Test the sharding setup')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def connect_to_postgres(host, port, user, password, database='postgres'):
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        logger.info(f"Connected to PostgreSQL database at {host}:{port}/{database}")
        return conn
    except psycopg2.Error as e:
        logger.error(f"Error connecting to PostgreSQL database: {e}")
        raise

def setup_shard_with_docker(args, shard_id):
    """Set up a database shard using Docker."""
    shard_port = args.base_port + shard_id
    shard_name = f"rentup-shard-{shard_id}"
    shard_db = f"rentup_shard_{shard_id}"
    
    logger.info(f"Setting up database shard {shard_id} with Docker on port {shard_port}...")
    
    # Check if container already exists
    result = subprocess.run(
        ["docker", "ps", "-a", "--filter", f"name={shard_name}", "--format", "{{.Names}}"],
        capture_output=True,
        text=True
    )
    
    if shard_name in result.stdout:
        logger.info(f"Container {shard_name} already exists. Removing...")
        subprocess.run(["docker", "rm", "-f", shard_name])
    
    # Create shard container
    cmd = [
        "docker", "run", "-d",
        "--name", shard_name,
        "-e", "POSTGRES_USER=rentup",
        "-e", "POSTGRES_PASSWORD=rentup_secure_password",
        "-e", f"POSTGRES_DB={shard_db}",
        "-p", f"{shard_port}:5432",
        "postgres:14-alpine"
    ]
    
    subprocess.run(cmd)
    logger.info(f"Created shard container: {shard_name}")
    
    # Wait for container to start
    logger.info("Waiting for shard container to start...")
    time.sleep(10)
    
    return shard_port

def get_shard_tables():
    """Get tables that should be sharded."""
    return {
        # Tables sharded by user_id
        "users": {
            "shard_key": "id",
            "create_sql": """
                CREATE TABLE IF NOT EXISTS users (
                    id VARCHAR(255) PRIMARY KEY,
                    email VARCHAR(255) UNIQUE NOT NULL,
                    full_name VARCHAR(255) NOT NULL,
                    hashed_password VARCHAR(255) NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    is_superuser BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                );
                CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
            """
        },
        "items": {
            "shard_key": "owner_id",
            "create_sql": """
                CREATE TABLE IF NOT EXISTS items (
                    id VARCHAR(255) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    category VARCHAR(255),
                    subcategory VARCHAR(255),
                    daily_price NUMERIC(10, 2) NOT NULL,
                    owner_id VARCHAR(255) NOT NULL,
                    is_available BOOLEAN DEFAULT TRUE,
                    location VARCHAR(255),
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                );
                CREATE INDEX IF NOT EXISTS idx_items_owner_id ON items(owner_id);
                CREATE INDEX IF NOT EXISTS idx_items_category ON items(category, is_available);
            """
        },
        "rentals": {
            "shard_key": "owner_id",
            "create_sql": """
                CREATE TABLE IF NOT EXISTS rentals (
                    id VARCHAR(255) PRIMARY KEY,
                    item_id VARCHAR(255) NOT NULL,
                    owner_id VARCHAR(255) NOT NULL,
                    renter_id VARCHAR(255) NOT NULL,
                    start_date TIMESTAMP NOT NULL,
                    end_date TIMESTAMP NOT NULL,
                    total_price NUMERIC(10, 2) NOT NULL,
                    status VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                );
                CREATE INDEX IF NOT EXISTS idx_rentals_owner_id ON rentals(owner_id);
                CREATE INDEX IF NOT EXISTS idx_rentals_renter_id ON rentals(renter_id);
                CREATE INDEX IF NOT EXISTS idx_rentals_item_id ON rentals(item_id);
            """
        },
        "auctions": {
            "shard_key": "owner_id",
            "create_sql": """
                CREATE TABLE IF NOT EXISTS auctions (
                    id VARCHAR(255) PRIMARY KEY,
                    item_id VARCHAR(255) NOT NULL,
                    owner_id VARCHAR(255) NOT NULL,
                    start_price NUMERIC(10, 2) NOT NULL,
                    current_price NUMERIC(10, 2) NOT NULL,
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP NOT NULL,
                    status VARCHAR(50) NOT NULL,
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                );
                CREATE INDEX IF NOT EXISTS idx_auctions_owner_id ON auctions(owner_id);
                CREATE INDEX IF NOT EXISTS idx_auctions_item_id ON auctions(item_id);
            """
        }
    }

def get_global_tables():
    """Get tables that should be replicated across all shards."""
    return {
        "categories": {
            "create_sql": """
                CREATE TABLE IF NOT EXISTS categories (
                    id VARCHAR(255) PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    parent_id VARCHAR(255),
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                );
                CREATE INDEX IF NOT EXISTS idx_categories_parent_id ON categories(parent_id);
            """
        },
        "settings": {
            "create_sql": """
                CREATE TABLE IF NOT EXISTS settings (
                    id VARCHAR(255) PRIMARY KEY,
                    key VARCHAR(255) UNIQUE NOT NULL,
                    value TEXT,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP DEFAULT NOW()
                );
                CREATE INDEX IF NOT EXISTS idx_settings_key ON settings(key);
            """
        }
    }

def create_tables_on_shard(host, port, user, password, db, tables):
    """Create tables on a database shard."""
    try:
        conn = connect_to_postgres(host, port, user, password, db)
        
        with conn.cursor() as cur:
            for table_name, table_info in tables.items():
                logger.info(f"Creating table {table_name} on shard {db}")
                cur.execute(table_info["create_sql"])
        
        conn.close()
        return True
    except Exception as e:
        logger.error(f"Error creating tables on shard {db}: {e}")
        return False

def calculate_shard_id(shard_key, shard_count):
    """Calculate shard ID from a shard key."""
    if not shard_key:
        return 0
    
    # Use consistent hashing with SHA-256
    hash_value = int(hashlib.sha256(str(shard_key).encode()).hexdigest(), 16)
    return hash_value % shard_count

def test_sharding(args):
    """Test the sharding setup."""
    logger.info("Testing sharding setup...")
    
    # Test data
    test_users = [
        {"id": "user1", "email": "<EMAIL>", "full_name": "User One", "hashed_password": "hash1"},
        {"id": "user2", "email": "<EMAIL>", "full_name": "User Two", "hashed_password": "hash2"},
        {"id": "user3", "email": "<EMAIL>", "full_name": "User Three", "hashed_password": "hash3"},
        {"id": "user4", "email": "<EMAIL>", "full_name": "User Four", "hashed_password": "hash4"}
    ]
    
    # Calculate shard for each user
    for user in test_users:
        shard_id = calculate_shard_id(user["id"], args.shard_count)
        shard_port = args.base_port + shard_id
        
        logger.info(f"User {user['id']} should be on shard {shard_id} (port {shard_port})")
        
        try:
            # Connect to shard
            conn = connect_to_postgres("localhost", shard_port, "rentup", "rentup_secure_password", f"rentup_shard_{shard_id}")
            
            with conn.cursor() as cur:
                # Insert user
                cur.execute("""
                    INSERT INTO users (id, email, full_name, hashed_password)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (id) DO UPDATE SET
                    email = EXCLUDED.email,
                    full_name = EXCLUDED.full_name,
                    hashed_password = EXCLUDED.hashed_password
                """, (user["id"], user["email"], user["full_name"], user["hashed_password"]))
                
                # Verify user
                cur.execute("SELECT id, email FROM users WHERE id = %s", (user["id"],))
                result = cur.fetchone()
                
                if result:
                    logger.info(f"✅ User {user['id']} successfully stored and retrieved from shard {shard_id}")
                else:
                    logger.error(f"❌ Failed to retrieve user {user['id']} from shard {shard_id}")
            
            conn.close()
        
        except Exception as e:
            logger.error(f"Error testing shard {shard_id}: {e}")
    
    logger.info("Sharding test completed")

def update_application_config(args):
    """Update the application configuration to use sharding."""
    logger.info("Updating application configuration...")
    
    if NEW_MODULES:
        # Update db_config.py
        config_path = Path(__file__).parent.parent / "app" / "core" / "db_config.py"
        
        if not config_path.exists():
            logger.error(f"Configuration file not found: {config_path}")
            return
        
        with open(config_path, 'r') as f:
            config_content = f.read()
        
        # Update sharding settings
        config_content = config_content.replace(
            "SHARDING_ENABLED = False",
            "SHARDING_ENABLED = True"
        )
        
        config_content = config_content.replace(
            "SHARD_COUNT = 4",
            f"SHARD_COUNT = {args.shard_count}"
        )
        
        # Write updated configuration
        with open(config_path, 'w') as f:
            f.write(config_content)
        
        logger.info(f"Updated configuration file: {config_path}")
    
    else:
        # Update config.py
        config_path = Path(__file__).parent.parent / "app" / "core" / "config.py"
        
        if not config_path.exists():
            logger.error(f"Configuration file not found: {config_path}")
            return
        
        with open(config_path, 'r') as f:
            config_content = f.read()
        
        # Update sharding settings
        config_content = config_content.replace(
            "SHARDING_ENABLED: bool = False",
            "SHARDING_ENABLED: bool = True"
        )
        
        config_content = config_content.replace(
            "SHARD_COUNT: int = 4",
            f"SHARD_COUNT: int = {args.shard_count}"
        )
        
        # Write updated configuration
        with open(config_path, 'w') as f:
            f.write(config_content)
        
        logger.info(f"Updated configuration file: {config_path}")

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Set up shards
    if args.docker:
        logger.info(f"Setting up {args.shard_count} database shards with Docker...")
        
        shard_ports = []
        for shard_id in range(args.shard_count):
            shard_port = setup_shard_with_docker(args, shard_id)
            shard_ports.append(shard_port)
        
        # Create tables on each shard
        logger.info("Creating tables on each shard...")
        
        # Get tables
        shard_tables = get_shard_tables()
        global_tables = get_global_tables()
        all_tables = {**shard_tables, **global_tables}
        
        for shard_id in range(args.shard_count):
            shard_port = shard_ports[shard_id]
            shard_db = f"rentup_shard_{shard_id}"
            
            create_tables_on_shard(
                "localhost", shard_port, "rentup", "rentup_secure_password", shard_db, all_tables
            )
    else:
        logger.info("Manual shard setup required. Please follow the documentation.")
    
    # Test sharding
    if args.test:
        test_sharding(args)
    
    # Update application configuration
    if args.update_config:
        update_application_config(args)
    
    logger.info("Database sharding setup completed.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
