#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to apply database optimizations to the RentUp codebase.

This script:
1. Identifies complex queries in the codebase
2. Applies JOIN optimization, query caching, and other optimizations
3. Measures performance improvements
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import re
import json
from typing import Dict, List, Set, Tuple, Any, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text, inspect, MetaData, Table, Column, ForeignKey
from sqlalchemy.orm import Session

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our new modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    from app.services.db_optimization_service import (
        optimized_query, batch_get, batch_get_related
    )
    from app.models.user import User
    from app.models.item import Item
    from app.models.rental import Rental
    from app.models.auction import Auction
    from app.models.bid import Bid
    from app.models.agreement import Agreement
    from app.models.fraud_alert import FraudAlert
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Apply database optimizations to RentUp')
    parser.add_argument('--test-user', action='store_true', help='Test User model optimizations')
    parser.add_argument('--test-item', action='store_true', help='Test Item model optimizations')
    parser.add_argument('--test-rental', action='store_true', help='Test Rental model optimizations')
    parser.add_argument('--test-auction', action='store_true', help='Test Auction model optimizations')
    parser.add_argument('--test-all', action='store_true', help='Test all model optimizations')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def test_user_optimizations(db: Session) -> Dict[str, Any]:
    """
    Test optimizations for User model queries.
    
    Args:
        db: Database session
    
    Returns:
        Dictionary of test results
    """
    results = {
        "unoptimized": {},
        "optimized": {},
        "improvement": {}
    }
    
    if not NEW_MODULES:
        logger.error("New modules not available, skipping User model optimizations")
        return results
    
    logger.info("Testing User model optimizations...")
    
    # Test 1: Get all users
    logger.info("Test 1: Get all users")
    
    # Unoptimized query
    start_time = time.time()
    users = db.query(User).all()
    unoptimized_time = time.time() - start_time
    results["unoptimized"]["get_all_users"] = unoptimized_time
    logger.info(f"  Unoptimized: {len(users)} users in {unoptimized_time:.6f}s")
    
    # Optimized query
    start_time = time.time()
    users = optimized_query(db.query(User), db, User).optimize().all()
    optimized_time = time.time() - start_time
    results["optimized"]["get_all_users"] = optimized_time
    logger.info(f"  Optimized: {len(users)} users in {optimized_time:.6f}s")
    
    # Calculate improvement
    improvement = (unoptimized_time - optimized_time) / unoptimized_time * 100
    results["improvement"]["get_all_users"] = improvement
    logger.info(f"  Improvement: {improvement:.2f}%")
    
    # Test 2: Get users with owned items
    logger.info("Test 2: Get users with owned items")
    
    # Unoptimized query
    start_time = time.time()
    users = db.query(User).all()
    for user in users:
        _ = user.owned_items
    unoptimized_time = time.time() - start_time
    results["unoptimized"]["get_users_with_items"] = unoptimized_time
    logger.info(f"  Unoptimized: {len(users)} users with items in {unoptimized_time:.6f}s")
    
    # Optimized query
    start_time = time.time()
    users = optimized_query(db.query(User), db, User).optimize().with_relationships(["owned_items"]).all()
    optimized_time = time.time() - start_time
    results["optimized"]["get_users_with_items"] = optimized_time
    logger.info(f"  Optimized: {len(users)} users with items in {optimized_time:.6f}s")
    
    # Calculate improvement
    improvement = (unoptimized_time - optimized_time) / unoptimized_time * 100
    results["improvement"]["get_users_with_items"] = improvement
    logger.info(f"  Improvement: {improvement:.2f}%")
    
    # Test 3: Get users with pagination
    logger.info("Test 3: Get users with pagination")
    
    # Unoptimized query
    start_time = time.time()
    total_count = db.query(User).count()
    users = db.query(User).offset(0).limit(10).all()
    unoptimized_time = time.time() - start_time
    results["unoptimized"]["get_users_paginated"] = unoptimized_time
    logger.info(f"  Unoptimized: {len(users)} of {total_count} users in {unoptimized_time:.6f}s")
    
    # Optimized query
    start_time = time.time()
    users, total_count = optimized_query(db.query(User), db, User).optimize().paginate(page=1, page_size=10).get_page()
    optimized_time = time.time() - start_time
    results["optimized"]["get_users_paginated"] = optimized_time
    logger.info(f"  Optimized: {len(users)} of {total_count} users in {optimized_time:.6f}s")
    
    # Calculate improvement
    improvement = (unoptimized_time - optimized_time) / unoptimized_time * 100
    results["improvement"]["get_users_paginated"] = improvement
    logger.info(f"  Improvement: {improvement:.2f}%")
    
    # Test 4: Get users with caching
    logger.info("Test 4: Get users with caching")
    
    # First call (cache miss)
    start_time = time.time()
    users = optimized_query(db.query(User), db, User).optimize().cache(ttl=60).all()
    cache_miss_time = time.time() - start_time
    results["optimized"]["get_users_cache_miss"] = cache_miss_time
    logger.info(f"  Cache miss: {len(users)} users in {cache_miss_time:.6f}s")
    
    # Second call (cache hit)
    start_time = time.time()
    users = optimized_query(db.query(User), db, User).optimize().cache(ttl=60).all()
    cache_hit_time = time.time() - start_time
    results["optimized"]["get_users_cache_hit"] = cache_hit_time
    logger.info(f"  Cache hit: {len(users)} users in {cache_hit_time:.6f}s")
    
    # Calculate improvement
    improvement = (cache_miss_time - cache_hit_time) / cache_miss_time * 100
    results["improvement"]["get_users_cached"] = improvement
    logger.info(f"  Improvement: {improvement:.2f}%")
    
    return results

def test_item_optimizations(db: Session) -> Dict[str, Any]:
    """
    Test optimizations for Item model queries.
    
    Args:
        db: Database session
    
    Returns:
        Dictionary of test results
    """
    results = {
        "unoptimized": {},
        "optimized": {},
        "improvement": {}
    }
    
    if not NEW_MODULES:
        logger.error("New modules not available, skipping Item model optimizations")
        return results
    
    logger.info("Testing Item model optimizations...")
    
    # Test 1: Get items with filtering
    logger.info("Test 1: Get items with filtering")
    
    # Unoptimized query
    start_time = time.time()
    items = db.query(Item).filter(Item.is_available == True).all()
    unoptimized_time = time.time() - start_time
    results["unoptimized"]["get_available_items"] = unoptimized_time
    logger.info(f"  Unoptimized: {len(items)} available items in {unoptimized_time:.6f}s")
    
    # Optimized query
    start_time = time.time()
    items = optimized_query(db.query(Item).filter(Item.is_available == True), db, Item).optimize().all()
    optimized_time = time.time() - start_time
    results["optimized"]["get_available_items"] = optimized_time
    logger.info(f"  Optimized: {len(items)} available items in {optimized_time:.6f}s")
    
    # Calculate improvement
    improvement = (unoptimized_time - optimized_time) / unoptimized_time * 100
    results["improvement"]["get_available_items"] = improvement
    logger.info(f"  Improvement: {improvement:.2f}%")
    
    # Test 2: Get items with owner
    logger.info("Test 2: Get items with owner")
    
    # Unoptimized query
    start_time = time.time()
    items = db.query(Item).all()
    for item in items:
        _ = item.owner
    unoptimized_time = time.time() - start_time
    results["unoptimized"]["get_items_with_owner"] = unoptimized_time
    logger.info(f"  Unoptimized: {len(items)} items with owner in {unoptimized_time:.6f}s")
    
    # Optimized query
    start_time = time.time()
    items = optimized_query(db.query(Item), db, Item).optimize().with_relationships(["owner"]).all()
    optimized_time = time.time() - start_time
    results["optimized"]["get_items_with_owner"] = optimized_time
    logger.info(f"  Optimized: {len(items)} items with owner in {optimized_time:.6f}s")
    
    # Calculate improvement
    improvement = (unoptimized_time - optimized_time) / unoptimized_time * 100
    results["improvement"]["get_items_with_owner"] = improvement
    logger.info(f"  Improvement: {improvement:.2f}%")
    
    # Test 3: Batch get items by IDs
    logger.info("Test 3: Batch get items by IDs")
    
    # Get some item IDs
    item_ids = [item.id for item in db.query(Item).limit(10).all()]
    
    # Unoptimized query
    start_time = time.time()
    items = {}
    for item_id in item_ids:
        item = db.query(Item).filter(Item.id == item_id).first()
        if item:
            items[item_id] = item
    unoptimized_time = time.time() - start_time
    results["unoptimized"]["batch_get_items"] = unoptimized_time
    logger.info(f"  Unoptimized: {len(items)} items in {unoptimized_time:.6f}s")
    
    # Optimized query
    start_time = time.time()
    items = batch_get(db, Item, item_ids)
    optimized_time = time.time() - start_time
    results["optimized"]["batch_get_items"] = optimized_time
    logger.info(f"  Optimized: {len(items)} items in {optimized_time:.6f}s")
    
    # Calculate improvement
    improvement = (unoptimized_time - optimized_time) / unoptimized_time * 100
    results["improvement"]["batch_get_items"] = improvement
    logger.info(f"  Improvement: {improvement:.2f}%")
    
    return results

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Determine what to test
    test_user = args.test_user or args.test_all
    test_item = args.test_item or args.test_all
    test_rental = args.test_rental or args.test_all
    test_auction = args.test_auction or args.test_all
    
    # If no tests specified, show help
    if not (test_user or test_item or test_rental or test_auction):
        logger.info("No tests specified. Use --test-user, --test-item, --test-rental, --test-auction, or --test-all")
        return 1
    
    # Run tests
    results = {}
    
    with session_scope() as db:
        if test_user:
            results["user"] = test_user_optimizations(db)
        
        if test_item:
            results["item"] = test_item_optimizations(db)
        
        # TODO: Add tests for Rental and Auction models
    
    # Save results to file
    output_file = "optimization_results.json"
    with open(output_file, "w") as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Results saved to {output_file}")
    
    # Calculate overall improvement
    total_improvement = 0.0
    count = 0
    
    for model, model_results in results.items():
        for query, improvement in model_results.get("improvement", {}).items():
            total_improvement += improvement
            count += 1
    
    if count > 0:
        average_improvement = total_improvement / count
        logger.info(f"Average improvement: {average_improvement:.2f}%")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
