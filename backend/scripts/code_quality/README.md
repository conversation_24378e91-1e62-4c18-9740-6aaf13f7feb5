# Code Quality Tools

This directory contains tools for maintaining code quality in the RentUp backend.

## Redundancy Checker

The `check_redundancies.py` script analyzes the codebase for redundancies, including:

1. Similar files
2. Unused imports
3. Unused functions
4. Similar functions
5. Redundant code patterns

### Usage

```bash
# Basic usage
python check_redundancies.py --path backend

# With custom threshold (0.0-1.0)
python check_redundancies.py --path backend --threshold 0.7

# Exclude specific directories
python check_redundancies.py --path backend --exclude-dirs venv node_modules .git __pycache__

# Only include specific directories
python check_redundancies.py --path backend --include-only app/models app/routes

# Enable verbose output
python check_redundancies.py --path backend --verbose
```

### Incremental Processing

For large codebases, you can use incremental processing to analyze the code in batches:

```bash
# Enable incremental processing
python check_redundancies.py --path backend --incremental

# Resume from a specific file index
python check_redundancies.py --path backend --incremental --resume-from 100

# Set batch size
python check_redundancies.py --path backend --incremental --batch-size 200
```

### Performance Optimization

For large files, you can adjust the maximum file size and sample size:

```bash
# Set maximum file size for full comparison (in bytes)
python check_redundancies.py --path backend --max-file-size 50000

# Set sample size for large files (in bytes)
python check_redundancies.py --path backend --sample-size 3000
```

### Output

The script generates a JSON report with the following sections:

- `similar_files`: Files with similar content
- `unused_imports`: Unused imports in each file
- `unused_functions`: Unused functions in each file
- `similar_functions`: Functions with similar implementations
- `redundant_patterns`: Common redundant code patterns
- `summary`: Summary statistics
- `recommendations`: Recommendations for addressing redundancies
- `timestamp`: When the report was generated
- `scan_info`: Information about the scan

### Example Report

```json
{
  "similar_files": [
    {
      "file1": "backend/app/models/user.py",
      "file2": "backend/app/models/profile.py",
      "similarity": 0.85
    }
  ],
  "unused_imports": [
    {
      "file": "backend/app/routes/auth.py",
      "imports": ["datetime", "uuid"]
    }
  ],
  "unused_functions": [
    {
      "file": "backend/app/utils/helpers.py",
      "functions": ["format_date", "validate_email"]
    }
  ],
  "similar_functions": [
    {
      "file1": "backend/app/services/user_service.py",
      "function1": "get_user_by_id",
      "file2": "backend/app/services/profile_service.py",
      "function2": "get_profile_by_id",
      "similarity": 0.92
    }
  ],
  "redundant_patterns": [
    {
      "file": "backend/app/routes/users.py",
      "pattern": "Multiple session creation",
      "count": 3
    }
  ],
  "summary": {
    "similar_files_count": 1,
    "unused_imports_count": 2,
    "unused_functions_count": 2,
    "similar_functions_count": 1,
    "redundant_patterns_count": 1
  },
  "recommendations": {
    "similar_files": "Consider consolidating similar files or extracting common functionality into shared modules.",
    "unused_imports": "Remove unused imports to improve code clarity and reduce potential namespace conflicts.",
    "unused_functions": "Remove or document unused functions to improve code maintainability.",
    "similar_functions": "Consider refactoring similar functions into a single, more generic function.",
    "redundant_patterns": "Refactor redundant code patterns to improve maintainability and reduce the risk of inconsistent updates."
  },
  "timestamp": "2025-05-23 03:15:27",
  "scan_info": {
    "total_files_scanned": 5
  }
}
```

## Best Practices

1. **Run regularly**: Include redundancy checking in your development workflow
2. **Review reports**: Regularly review redundancy reports to identify areas for improvement
3. **Refactor incrementally**: Address redundancies incrementally to avoid breaking changes
4. **Document decisions**: Document why certain redundancies are kept if they serve a purpose
5. **Automate**: Consider automating redundancy checks in your CI/CD pipeline

Last Updated: 2025-05-23
