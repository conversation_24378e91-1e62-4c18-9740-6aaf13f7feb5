#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to check for redundancies in the RentUp backend codebase.

This script:
1. Identifies duplicate or similar files
2. Detects redundant code patterns
3. Finds unused imports and functions
4. Generates a report of potential redundancies
"""

import os
import sys
import re
import ast
import json
import time
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict
import difflib

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Check for redundancies in the RentUp backend codebase')
    parser.add_argument('--path', default='backend', help='Path to the backend directory')
    parser.add_argument('--output', default='redundancy_report.json', help='Output file for redundancy report')
    parser.add_argument('--threshold', type=float, default=0.8, help='Similarity threshold (0.0-1.0)')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    parser.add_argument('--max-file-size', type=int, default=100000, help='Maximum file size in bytes to compare in full')
    parser.add_argument('--sample-size', type=int, default=5000, help='Number of bytes to sample from large files')
    parser.add_argument('--exclude-dirs', nargs='+', default=['venv', 'node_modules', '.git', '__pycache__'],
                        help='Directories to exclude from analysis')
    parser.add_argument('--include-only', nargs='+', default=None,
                        help='Only include files in these directories (relative to path)')
    parser.add_argument('--incremental', action='store_true', help='Enable incremental processing')
    parser.add_argument('--resume-from', type=int, default=0, help='Resume from this file index (for incremental processing)')
    parser.add_argument('--batch-size', type=int, default=50, help='Number of files to process in each batch (for incremental processing)')
    return parser.parse_args()

def find_python_files(path: str, exclude_dirs: List[str] = None, include_only: List[str] = None) -> List[str]:
    """Find all Python files in the given path.

    Args:
        path: Base directory to search
        exclude_dirs: List of directory names to exclude
        include_only: List of subdirectories to include (relative to path)

    Returns:
        List of Python file paths
    """
    if exclude_dirs is None:
        exclude_dirs = []

    python_files = []

    # Convert path to absolute path for consistent comparisons
    abs_path = os.path.abspath(path)

    # If include_only is specified, create full paths
    include_paths = None
    if include_only:
        include_paths = [os.path.join(abs_path, subdir) for subdir in include_only]

    for root, dirs, files in os.walk(abs_path):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in exclude_dirs]

        # Skip if not in included directories
        if include_paths:
            # Check if current directory is in or under any of the include paths
            if not any(root == inc_path or root.startswith(inc_path + os.sep) for inc_path in include_paths):
                continue

        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))

    logger.info(f"Found {len(python_files)} Python files")
    return python_files

def get_file_content(file_path: str) -> str:
    """Get the content of a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return ""

def find_similar_files(files: List[str], threshold: float, max_file_size: int = 100000, sample_size: int = 5000) -> List[Tuple[str, str, float]]:
    """Find similar files based on content similarity.

    Args:
        files: List of file paths to compare
        threshold: Similarity threshold (0.0-1.0)
        max_file_size: Maximum file size in bytes to compare in full
        sample_size: Number of bytes to sample from large files
    """
    similar_files = []
    total_comparisons = (len(files) * (len(files) - 1)) // 2
    completed = 0

    logger.info(f"Total comparisons to perform: {total_comparisons}")

    for i, file1 in enumerate(files):
        # Get file size
        try:
            file1_size = os.path.getsize(file1)
        except Exception:
            file1_size = 0

        content1 = get_file_content(file1)
        if not content1:
            continue

        # Sample large files
        if file1_size > max_file_size:
            # Take beginning, middle and end samples
            begin = content1[:sample_size//3]
            middle_start = max(0, len(content1)//2 - sample_size//6)
            middle = content1[middle_start:middle_start + sample_size//3]
            end = content1[-sample_size//3:]
            content1_sample = begin + middle + end
        else:
            content1_sample = content1

        for j, file2 in enumerate(files[i+1:], i+1):
            completed += 1
            if completed % 1000 == 0 or completed == total_comparisons:
                logger.info(f"Progress: {completed}/{total_comparisons} comparisons ({(completed/total_comparisons)*100:.1f}%)")

            # Skip comparing files with very different sizes
            try:
                file2_size = os.path.getsize(file2)
                if file1_size > 0 and file2_size > 0:
                    size_ratio = min(file1_size, file2_size) / max(file1_size, file2_size)
                    if size_ratio < 0.5:  # Skip if files differ in size by more than 50%
                        continue
            except Exception:
                pass

            content2 = get_file_content(file2)
            if not content2:
                continue

            # Sample large files
            if file2_size > max_file_size:
                # Take beginning, middle and end samples
                begin = content2[:sample_size//3]
                middle_start = max(0, len(content2)//2 - sample_size//6)
                middle = content2[middle_start:middle_start + sample_size//3]
                end = content2[-sample_size//3:]
                content2_sample = begin + middle + end
            else:
                content2_sample = content2

            try:
                # Calculate similarity ratio with timeout
                similarity = difflib.SequenceMatcher(None, content1_sample, content2_sample).ratio()

                if similarity >= threshold:
                    # For high similarity based on samples, verify with full content if needed
                    if file1_size > max_file_size or file2_size > max_file_size:
                        # Use a smaller threshold for full content to avoid false negatives
                        full_threshold = max(0.7 * threshold, 0.5)
                        # Only compare first 50KB for very large files
                        compare_size = min(50000, min(len(content1), len(content2)))
                        full_similarity = difflib.SequenceMatcher(
                            None,
                            content1[:compare_size],
                            content2[:compare_size]
                        ).ratio()

                        if full_similarity >= full_threshold:
                            similar_files.append((file1, file2, full_similarity))
                    else:
                        similar_files.append((file1, file2, similarity))
            except Exception as e:
                logger.error(f"Error comparing {file1} and {file2}: {e}")

    return similar_files

class ImportVisitor(ast.NodeVisitor):
    """AST visitor to find imports in a Python file."""

    def __init__(self):
        self.imports = set()
        self.from_imports = defaultdict(set)

    def visit_Import(self, node):
        for name in node.names:
            self.imports.add(name.name)
        self.generic_visit(node)

    def visit_ImportFrom(self, node):
        if node.module:
            for name in node.names:
                self.from_imports[node.module].add(name.name)
        self.generic_visit(node)

class FunctionVisitor(ast.NodeVisitor):
    """AST visitor to find functions and their usage in a Python file."""

    def __init__(self):
        self.defined_functions = set()
        self.called_functions = set()
        self.function_definitions = {}

    def visit_FunctionDef(self, node):
        self.defined_functions.add(node.name)
        self.function_definitions[node.name] = ast.unparse(node)
        self.generic_visit(node)

    def visit_Call(self, node):
        if isinstance(node.func, ast.Name):
            self.called_functions.add(node.func.id)
        elif isinstance(node.func, ast.Attribute):
            self.called_functions.add(node.func.attr)
        self.generic_visit(node)

def find_unused_imports(file_path: str) -> Set[str]:
    """Find unused imports in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        tree = ast.parse(content)

        # Find imports
        import_visitor = ImportVisitor()
        import_visitor.visit(tree)

        # Find all names used in the file
        used_names = set()
        for node in ast.walk(tree):
            if isinstance(node, ast.Name):
                used_names.add(node.id)

        # Find unused imports
        unused_imports = set()
        for imp in import_visitor.imports:
            # Check if the import name itself is used
            if imp not in used_names:
                # Check if any part of the import is used (e.g., os.path)
                parts = imp.split('.')
                if parts[0] not in used_names:
                    unused_imports.add(imp)

        # Find unused from imports
        for module, names in import_visitor.from_imports.items():
            for name in names:
                if name != '*' and name not in used_names:
                    unused_imports.add(f"{module}.{name}")

        return unused_imports

    except Exception as e:
        logger.error(f"Error analyzing imports in {file_path}: {e}")
        return set()

def find_unused_functions(file_path: str) -> Set[str]:
    """Find unused functions in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        tree = ast.parse(content)

        # Find functions and their usage
        function_visitor = FunctionVisitor()
        function_visitor.visit(tree)

        # Find unused functions
        unused_functions = function_visitor.defined_functions - function_visitor.called_functions

        # Remove special methods (e.g., __init__)
        unused_functions = {f for f in unused_functions if not (f.startswith('__') and f.endswith('__'))}

        return unused_functions

    except Exception as e:
        logger.error(f"Error analyzing functions in {file_path}: {e}")
        return set()

def find_similar_functions(files: List[str], threshold: float) -> List[Tuple[str, str, str, str, float]]:
    """Find similar functions across files."""
    similar_functions = []
    all_functions = {}

    # Extract all functions
    for file_path in files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            tree = ast.parse(content)

            # Find functions
            function_visitor = FunctionVisitor()
            function_visitor.visit(tree)

            # Add functions to the global dictionary
            for func_name, func_def in function_visitor.function_definitions.items():
                all_functions[(file_path, func_name)] = func_def

        except Exception as e:
            logger.error(f"Error extracting functions from {file_path}: {e}")

    # Compare functions
    function_items = list(all_functions.items())
    for i, ((file1, func1), def1) in enumerate(function_items):
        for j, ((file2, func2), def2) in enumerate(function_items[i+1:], i+1):
            # Skip comparing functions in the same file
            if file1 == file2:
                continue

            # Calculate similarity ratio
            similarity = difflib.SequenceMatcher(None, def1, def2).ratio()

            if similarity >= threshold:
                similar_functions.append((file1, func1, file2, func2, similarity))

    return similar_functions

def find_redundant_code_patterns(files: List[str]) -> List[Dict[str, Any]]:
    """Find redundant code patterns across files."""
    redundant_patterns = []

    # Common redundant patterns to look for
    patterns = [
        (r'try:\s*([^}]*?)except\s+Exception\s+as\s+e:\s*([^}]*?)pass', 'Empty exception handler'),
        (r'if\s+__name__\s*==\s*[\'"]__main__[\'"]\s*:\s*main\(\)', 'Standard main function call'),
        (r'logging\.basicConfig\(.*?\)', 'Multiple logging configuration'),
        (r'session\s*=\s*Session\(.*?\)', 'Multiple session creation'),
        (r'engine\s*=\s*create_engine\(.*?\)', 'Multiple engine creation'),
        (r'app\s*=\s*FastAPI\(.*?\)', 'Multiple FastAPI instance creation'),
    ]

    for file_path in files:
        try:
            content = get_file_content(file_path)
            if not content:
                continue

            for pattern, description in patterns:
                matches = re.findall(pattern, content, re.DOTALL)
                if matches:
                    redundant_patterns.append({
                        'file': file_path,
                        'pattern': description,
                        'count': len(matches)
                    })

        except Exception as e:
            logger.error(f"Error finding redundant patterns in {file_path}: {e}")

    return redundant_patterns

def generate_report(
    similar_files: List[Tuple[str, str, float]],
    unused_imports: Dict[str, List[str]],
    unused_functions: Dict[str, List[str]],
    similar_functions: List[Tuple[str, str, str, str, float]],
    redundant_patterns: List[Dict[str, Any]],
    output_file: str
) -> None:
    """Generate a redundancy report."""
    logger.info(f"Generating redundancy report: {output_file}")

    # Create report data
    report = {
        'similar_files': [
            {
                'file1': file1,
                'file2': file2,
                'similarity': similarity
            }
            for file1, file2, similarity in similar_files
        ],
        'unused_imports': [
            {
                'file': file,
                'imports': imports if isinstance(imports, list) else list(imports)
            }
            for file, imports in unused_imports.items() if imports
        ],
        'unused_functions': [
            {
                'file': file,
                'functions': functions if isinstance(functions, list) else list(functions)
            }
            for file, functions in unused_functions.items() if functions
        ],
        'similar_functions': [
            {
                'file1': file1,
                'function1': func1,
                'file2': file2,
                'function2': func2,
                'similarity': similarity
            }
            for file1, func1, file2, func2, similarity in similar_functions
        ],
        'redundant_patterns': redundant_patterns,
        'summary': {
            'similar_files_count': len(similar_files),
            'unused_imports_count': sum(len(imports) for imports in unused_imports.values()),
            'unused_functions_count': sum(len(functions) for functions in unused_functions.values()),
            'similar_functions_count': len(similar_functions),
            'redundant_patterns_count': len(redundant_patterns)
        },
        'recommendations': {
            'similar_files': "Consider consolidating similar files or extracting common functionality into shared modules.",
            'unused_imports': "Remove unused imports to improve code clarity and reduce potential namespace conflicts.",
            'unused_functions': "Remove or document unused functions to improve code maintainability.",
            'similar_functions': "Consider refactoring similar functions into a single, more generic function.",
            'redundant_patterns': "Refactor redundant code patterns to improve maintainability and reduce the risk of inconsistent updates."
        },
        'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
        'scan_info': {
            'total_files_scanned': len(set([f[0] for f in similar_files] + [f[1] for f in similar_files] +
                                         list(unused_imports.keys()) + list(unused_functions.keys()) +
                                         [f[0] for f in similar_functions] + [f[2] for f in similar_functions] +
                                         [p['file'] for p in redundant_patterns if 'file' in p]))
        }
    }

    # Write report to file
    with open(output_file, 'w') as f:
        json.dump(report, f, indent=2)

    # Print summary to console
    logger.info("=== Redundancy Report Summary ===")
    logger.info(f"Similar files: {report['summary']['similar_files_count']}")
    logger.info(f"Unused imports: {report['summary']['unused_imports_count']}")
    logger.info(f"Unused functions: {report['summary']['unused_functions_count']}")
    logger.info(f"Similar functions: {report['summary']['similar_functions_count']}")
    logger.info(f"Redundant patterns: {report['summary']['redundant_patterns_count']}")
    logger.info(f"Total files scanned: {report['scan_info']['total_files_scanned']}")
    logger.info(f"Report saved to: {output_file}")

    logger.info(f"Redundancy report generated: {output_file}")

def save_intermediate_results(results: Dict[str, Any], filename: str) -> None:
    """Save intermediate results to a file."""
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2)
    logger.info(f"Saved intermediate results to {filename}")

def load_intermediate_results(filename: str) -> Dict[str, Any]:
    """Load intermediate results from a file."""
    if os.path.exists(filename):
        with open(filename, 'r') as f:
            results = json.load(f)
        logger.info(f"Loaded intermediate results from {filename}")
        return results
    return {
        "similar_files": [],
        "unused_imports": {},
        "unused_functions": {},
        "similar_functions": [],
        "redundant_patterns": [],
        "processed_files": 0
    }

def main():
    """Main function."""
    args = parse_args()

    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Find Python files
        logger.info(f"Finding Python files in {args.path}")
        python_files = find_python_files(
            args.path,
            exclude_dirs=args.exclude_dirs,
            include_only=args.include_only
        )

        # Intermediate results filename
        intermediate_file = f"{os.path.splitext(args.output)[0]}_intermediate.json"

        # Load intermediate results if incremental mode is enabled
        if args.incremental and os.path.exists(intermediate_file):
            results = load_intermediate_results(intermediate_file)
            start_idx = args.resume_from if args.resume_from > 0 else results.get("processed_files", 0)
            similar_files = results.get("similar_files", [])
            unused_imports = results.get("unused_imports", {})
            unused_functions = results.get("unused_functions", {})
            similar_functions = results.get("similar_functions", [])
            redundant_patterns = results.get("redundant_patterns", [])
        else:
            start_idx = 0
            similar_files = []
            unused_imports = {}
            unused_functions = {}
            similar_functions = []
            redundant_patterns = []

        # Process files in batches if incremental mode is enabled
        if args.incremental:
            end_idx = min(start_idx + args.batch_size, len(python_files))
            current_batch = python_files[start_idx:end_idx]
            logger.info(f"Processing batch of {len(current_batch)} files ({start_idx} to {end_idx-1})")
        else:
            current_batch = python_files

        # Find similar files
        if not args.incremental or not similar_files:
            logger.info("Finding similar files")
            batch_similar_files = find_similar_files(
                current_batch if args.incremental else python_files,
                args.threshold,
                max_file_size=args.max_file_size,
                sample_size=args.sample_size
            )
            similar_files.extend(batch_similar_files)
            logger.info(f"Found {len(batch_similar_files)} similar file pairs in current batch")

        # Find unused imports
        logger.info("Finding unused imports")
        for file in current_batch:
            if file not in unused_imports:
                unused = find_unused_imports(file)
                if unused:
                    unused_imports[file] = list(unused)  # Convert set to list for JSON serialization
        logger.info(f"Found unused imports in {len(unused_imports)} files")

        # Find unused functions
        logger.info("Finding unused functions")
        for file in current_batch:
            if file not in unused_functions:
                unused = find_unused_functions(file)
                if unused:
                    unused_functions[file] = list(unused)  # Convert set to list for JSON serialization
        logger.info(f"Found unused functions in {len(unused_functions)} files")

        # Find similar functions
        if not args.incremental or not similar_functions:
            logger.info("Finding similar functions")
            batch_similar_functions = find_similar_functions(
                current_batch if args.incremental else python_files,
                args.threshold
            )
            similar_functions.extend(batch_similar_functions)
            logger.info(f"Found {len(batch_similar_functions)} similar function pairs in current batch")

        # Find redundant code patterns
        if not args.incremental or not redundant_patterns:
            logger.info("Finding redundant code patterns")
            batch_redundant_patterns = find_redundant_code_patterns(
                current_batch if args.incremental else python_files
            )
            redundant_patterns.extend(batch_redundant_patterns)
            logger.info(f"Found {len(batch_redundant_patterns)} redundant code patterns in current batch")

        # Save intermediate results if incremental mode is enabled
        if args.incremental:
            save_intermediate_results({
                "similar_files": similar_files,
                "unused_imports": unused_imports,
                "unused_functions": unused_functions,
                "similar_functions": similar_functions,
                "redundant_patterns": redundant_patterns,
                "processed_files": end_idx
            }, intermediate_file)

            # If there are more files to process, exit with a message
            if end_idx < len(python_files):
                logger.info(f"Processed {end_idx}/{len(python_files)} files. Run again with --resume-from {end_idx} to continue.")
                return 0

        # Generate final report
        generate_report(
            similar_files,
            unused_imports,
            unused_functions,
            similar_functions,
            redundant_patterns,
            args.output
        )

        logger.info("Redundancy check completed successfully")
        return 0

    except KeyboardInterrupt:
        logger.info("Process interrupted by user")
        return 130
    except Exception as e:
        logger.error(f"Redundancy check failed: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
