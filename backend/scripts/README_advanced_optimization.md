# Advanced Database Optimization Scripts

This directory contains scripts for advanced database optimizations in the RentUp backend.

## Overview

These scripts implement advanced database optimizations to improve scalability, reliability, and performance:

1. **Read Replicas**: Set up and configure PostgreSQL read replicas for horizontal scaling of read operations
2. **Full-Text Search**: Optimize full-text search operations using PostgreSQL's tsvector and GIN indexes
3. **Database Sharding**: Configure and manage database sharding for horizontal scaling of write operations

## Scripts

### 1. `setup_read_replicas.py`

Sets up and configures PostgreSQL read replicas for RentUp.

```bash
# Set up read replicas with Docker
python setup_read_replicas.py --docker --test --update-config

# Set up read replicas manually
python setup_read_replicas.py --update-config

# Test replication
python setup_read_replicas.py --test

# Show help
python setup_read_replicas.py --help
```

#### Options

- `--primary-host`: Primary database host (default: localhost)
- `--primary-port`: Primary database port (default: 5432)
- `--primary-user`: Primary database user (default: rentup)
- `--primary-password`: Primary database password (default: rentup_secure_password)
- `--primary-db`: Primary database name (default: rentup)
- `--replica-count`: Number of read replicas to set up (default: 2)
- `--replica-base-port`: Base port for read replicas (default: 5433)
- `--docker`: Use Docker for replica setup
- `--update-config`: Update application configuration
- `--test`: Test the replication setup
- `--verbose`: Enable verbose output

### 2. `optimize_full_text_search.py`

Optimizes full-text search operations for RentUp.

```bash
# Create full-text search indexes and triggers
python optimize_full_text_search.py --create-indexes --create-triggers

# Benchmark search performance
python optimize_full_text_search.py --benchmark

# Do everything
python optimize_full_text_search.py --all

# Show help
python optimize_full_text_search.py --help
```

#### Options

- `--create-indexes`: Create full-text search indexes
- `--create-triggers`: Create triggers to update search vectors
- `--benchmark`: Benchmark search performance
- `--all`: Perform all optimization steps
- `--verbose`: Enable verbose output

### 3. `setup_database_sharding.py`

Sets up and configures database sharding for RentUp.

```bash
# Set up database sharding with Docker
python setup_database_sharding.py --docker --test --update-config

# Set up database sharding manually
python setup_database_sharding.py --update-config

# Test sharding
python setup_database_sharding.py --test

# Show help
python setup_database_sharding.py --help
```

#### Options

- `--shard-count`: Number of database shards (default: 4)
- `--docker`: Use Docker for shard setup
- `--update-config`: Update application configuration
- `--test`: Test the sharding setup
- `--verbose`: Enable verbose output

## Usage

### Setting Up Read Replicas

1. Make sure PostgreSQL is installed and running:

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install postgresql postgresql-contrib

# macOS with Homebrew
brew install postgresql
```

2. Set up read replicas:

```bash
# With Docker
python setup_read_replicas.py --docker --test --update-config

# Without Docker
python setup_read_replicas.py --update-config
```

3. Test the replication setup:

```bash
python setup_read_replicas.py --test
```

### Optimizing Full-Text Search

1. Create full-text search indexes and triggers:

```bash
python optimize_full_text_search.py --create-indexes --create-triggers
```

2. Benchmark search performance:

```bash
python optimize_full_text_search.py --benchmark
```

### Setting Up Database Sharding

1. Set up database sharding:

```bash
# With Docker
python setup_database_sharding.py --docker --test --update-config

# Without Docker
python setup_database_sharding.py --update-config
```

2. Test the sharding setup:

```bash
python setup_database_sharding.py --test
```

## Best Practices

### Read Replicas

- Use read replicas for read-heavy operations only
- Use the primary database for all write operations
- Monitor replication lag to ensure data consistency
- Set up automatic failover for production environments

### Full-Text Search

- Use tsvector and GIN indexes for efficient text search
- Use weighted search for better relevance ranking
- Update search vectors automatically using triggers
- Consider using a dedicated search service for very large datasets

### Database Sharding

- Choose appropriate shard keys for even data distribution
- Use consistent hashing for shard key mapping
- Minimize cross-shard queries
- Consider using a dedicated shard for global tables

## Troubleshooting

### Read Replica Issues

- Check replication status:

```bash
python setup_read_replicas.py --test
```

- Check PostgreSQL logs:

```bash
# Ubuntu/Debian
sudo tail -f /var/log/postgresql/postgresql-14-main.log

# Docker
docker logs rentup-replica-0
```

### Full-Text Search Issues

- Check if indexes are created:

```sql
SELECT indexname, indexdef FROM pg_indexes WHERE tablename = 'items';
```

- Check if triggers are created:

```sql
SELECT tgname, tgrelid::regclass FROM pg_trigger WHERE tgname LIKE '%search_vector%';
```

### Database Sharding Issues

- Check shard configuration:

```bash
python setup_database_sharding.py --test
```

- Check database connectivity:

```bash
psql -h localhost -p 5432 -U rentup -d rentup_shard_0
```

## Further Reading

For more information on advanced database optimizations, see:

- [Advanced Database Optimizations Documentation](../docs/optimization/advanced_database_optimizations.md)
- [PostgreSQL Replication Documentation](https://www.postgresql.org/docs/current/high-availability.html)
- [PostgreSQL Full-Text Search Documentation](https://www.postgresql.org/docs/current/textsearch.html)
- [Database Sharding Best Practices](https://www.postgresql.org/docs/current/ddl-partitioning.html)
