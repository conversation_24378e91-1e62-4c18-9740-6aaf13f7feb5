#!/usr/bin/env python3
"""
Script to test batch operations module.

This script demonstrates the use of the batch operations module
for performing batch database operations.
"""

import sys
import os
import time
import logging
import uuid
from datetime import datetime, UTC
from typing import List, Dict, Any

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session

from app.core.config import settings
from app.core.batch_operations import (
    BatchProcessor,
    batch_create,
    batch_update,
    batch_delete,
    batch_upsert
)
from app.models.user import User
from app.models.item import Item

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('batch_operations_test.log')
    ]
)
logger = logging.getLogger(__name__)

def create_test_users(count: int = 10) -> List[Dict[str, Any]]:
    """Create test user data."""
    return [
        {
            "id": str(uuid.uuid4()),
            "email": f"test_user_{i}@example.com",
            "hashed_password": f"hashed_password_{i}",
            "full_name": f"Test User {i}",
            "is_active": True,
            "is_superuser": False,
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC)
        }
        for i in range(count)
    ]

def create_test_items(users: List[Dict[str, Any]], count: int = 20) -> List[Dict[str, Any]]:
    """Create test item data."""
    return [
        {
            "id": str(uuid.uuid4()),
            "name": f"Test Item {i}",
            "description": f"Description for test item {i}",
            "category": "Electronics",
            "subcategory": "Computers",
            "condition": "Excellent",
            "owner_id": users[i % len(users)]["id"],
            "value": 1000 + i * 100,
            "daily_price": 10 + i,
            "weekly_price": 60 + i * 5,
            "monthly_price": 200 + i * 20,
            "deposit_amount": 200 + i * 20,
            "location": "New York",
            "is_available": True,
            "is_featured": i % 3 == 0,
            "is_verified": i % 2 == 0,
            "created_at": datetime.now(UTC),
            "updated_at": datetime.now(UTC)
        }
        for i in range(count)
    ]

def test_batch_processor_create(db: Session):
    """Test creating records in batches."""
    logger.info("Testing batch creation...")
    
    # Create test users
    users = create_test_users(10)
    
    # Create batch processor
    processor = BatchProcessor(db, User, batch_size=5)
    
    # Create users in batches
    start_time = time.time()
    result = processor.create(users)
    elapsed_time = time.time() - start_time
    
    # Check result
    logger.info(f"Created {result['successful_items']} users in {elapsed_time:.2f} seconds")
    logger.info(f"Failed items: {result['failed_items']}")
    
    if result['failed_items'] > 0:
        logger.error(f"Errors: {result['errors']}")
    
    # Check that users were created
    user_count = db.query(User).count()
    logger.info(f"Total users in database: {user_count}")
    
    return users

def test_batch_processor_update(db: Session, users: List[Dict[str, Any]]):
    """Test updating records in batches."""
    logger.info("Testing batch updates...")
    
    # Update users
    updates = [
        {
            "id": user["id"],
            "full_name": f"Updated User {i}"
        }
        for i, user in enumerate(users)
    ]
    
    # Create batch processor
    processor = BatchProcessor(db, User, batch_size=5)
    
    # Update users in batches
    start_time = time.time()
    result = processor.update(updates)
    elapsed_time = time.time() - start_time
    
    # Check result
    logger.info(f"Updated {result['successful_items']} users in {elapsed_time:.2f} seconds")
    logger.info(f"Failed items: {result['failed_items']}")
    
    if result['failed_items'] > 0:
        logger.error(f"Errors: {result['errors']}")
    
    # Check that users were updated
    updated_users = db.query(User).all()
    for i, user in enumerate(updated_users[:5]):
        logger.info(f"User {i}: {user.full_name}")

def test_batch_processor_delete(db: Session, users: List[Dict[str, Any]]):
    """Test deleting records in batches."""
    logger.info("Testing batch deletion...")
    
    # Delete half of the users
    ids_to_delete = [user["id"] for user in users[:5]]
    
    # Create batch processor
    processor = BatchProcessor(db, User, batch_size=5)
    
    # Delete users in batches
    start_time = time.time()
    result = processor.delete(ids_to_delete)
    elapsed_time = time.time() - start_time
    
    # Check result
    logger.info(f"Deleted {result['successful_items']} users in {elapsed_time:.2f} seconds")
    logger.info(f"Failed items: {result['failed_items']}")
    
    if result['failed_items'] > 0:
        logger.error(f"Errors: {result['errors']}")
    
    # Check that users were deleted
    user_count = db.query(User).count()
    logger.info(f"Total users in database: {user_count}")

def test_batch_item_operations(db: Session):
    """Test batch operations with items."""
    logger.info("Testing batch operations with items...")
    
    # Create test users
    users = create_test_users(5)
    
    # Create users first
    batch_create(db, User, users)
    
    # Create test items
    items = create_test_items(users, 20)
    
    # Create items in batches
    start_time = time.time()
    result = batch_create(db, Item, items)
    elapsed_time = time.time() - start_time
    
    # Check result
    logger.info(f"Created {result['successful_items']} items in {elapsed_time:.2f} seconds")
    logger.info(f"Failed items: {result['failed_items']}")
    
    if result['failed_items'] > 0:
        logger.error(f"Errors: {result['errors']}")
    
    # Check that items were created
    item_count = db.query(Item).count()
    logger.info(f"Total items in database: {item_count}")
    
    # Update items
    updates = [
        {
            "id": item["id"],
            "name": f"Updated Item {i}",
            "daily_price": item["daily_price"] * 1.1
        }
        for i, item in enumerate(items[:10])
    ]
    
    # Update items in batches
    start_time = time.time()
    result = batch_update(db, Item, updates)
    elapsed_time = time.time() - start_time
    
    # Check result
    logger.info(f"Updated {result['successful_items']} items in {elapsed_time:.2f} seconds")
    
    # Delete items
    ids_to_delete = [item["id"] for item in items[10:15]]
    
    # Delete items in batches
    start_time = time.time()
    result = batch_delete(db, Item, ids_to_delete)
    elapsed_time = time.time() - start_time
    
    # Check result
    logger.info(f"Deleted {result['successful_items']} items in {elapsed_time:.2f} seconds")
    
    # Check final item count
    item_count = db.query(Item).count()
    logger.info(f"Final item count: {item_count}")

def main():
    """Main function to test batch operations."""
    logger.info("Starting batch operations test...")
    
    # Create engine
    engine = create_engine(settings.DATABASE_URL)
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Test batch operations
        users = test_batch_processor_create(db)
        test_batch_processor_update(db, users)
        test_batch_processor_delete(db, users)
        test_batch_item_operations(db)
        
        logger.info("Batch operations test completed successfully")
    except Exception as e:
        logger.error(f"Error testing batch operations: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()
