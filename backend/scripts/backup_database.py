#!/usr/bin/env python3
"""
Script to backup the RentUp PostgreSQL database.

This script:
1. Creates a full database backup
2. Compresses the backup
3. Encrypts the backup (optional)
4. Uploads the backup to cloud storage (optional)
5. Manages backup retention
"""

import os
import sys
import argparse
import logging
import subprocess
import datetime
import shutil
import boto3
import json
import time
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import from our modules
try:
    from app.core.db_config import db_config
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    NEW_MODULES = False

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Backup RentUp PostgreSQL database')
    parser.add_argument('--backup-dir', default='backups', help='Directory to store backups')
    parser.add_argument('--encrypt', action='store_true', help='Encrypt the backup')
    parser.add_argument('--upload', action='store_true', help='Upload to cloud storage')
    parser.add_argument('--retention-days', type=int, default=30, help='Number of days to keep backups')
    parser.add_argument('--cloud-provider', choices=['aws', 'gcp', 'azure'], default='aws', help='Cloud provider for upload')
    parser.add_argument('--bucket-name', help='Cloud storage bucket name')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def get_db_connection_params():
    """Get database connection parameters."""
    if NEW_MODULES:
        # Use db_config
        db_host = db_config.POSTGRES_SERVER
        db_port = db_config.POSTGRES_PORT
        db_user = db_config.POSTGRES_USER
        db_password = db_config.POSTGRES_PASSWORD
        db_name = db_config.POSTGRES_DB
    else:
        # Use settings
        db_url = settings.DATABASE_URL
        # Parse DATABASE_URL
        import re
        match = re.match(r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
        if match:
            db_user, db_password, db_host, db_port, db_name = match.groups()
            db_port = int(db_port)
        else:
            logger.error("Could not parse DATABASE_URL")
            return None
    
    return {
        "host": db_host,
        "port": db_port,
        "user": db_user,
        "password": db_password,
        "dbname": db_name
    }

def create_backup_directory(backup_dir: str) -> str:
    """Create backup directory with timestamp."""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    full_backup_dir = os.path.join(backup_dir, timestamp)
    os.makedirs(full_backup_dir, exist_ok=True)
    logger.info(f"Created backup directory: {full_backup_dir}")
    return full_backup_dir

def create_database_backup(backup_dir: str, db_params: Dict[str, str]) -> str:
    """Create a database backup using pg_dump."""
    backup_file = os.path.join(backup_dir, f"{db_params['dbname']}.sql")
    
    # Set environment variables for pg_dump
    env = os.environ.copy()
    env["PGPASSWORD"] = db_params["password"]
    
    # Create pg_dump command
    cmd = [
        "pg_dump",
        "-h", db_params["host"],
        "-p", str(db_params["port"]),
        "-U", db_params["user"],
        "-F", "c",  # Custom format
        "-b",  # Include large objects
        "-v",  # Verbose
        "-f", backup_file,
        db_params["dbname"]
    ]
    
    try:
        logger.info(f"Starting database backup to {backup_file}")
        process = subprocess.run(cmd, env=env, check=True, capture_output=True, text=True)
        logger.info(f"Database backup completed: {backup_file}")
        return backup_file
    except subprocess.CalledProcessError as e:
        logger.error(f"Database backup failed: {e.stderr}")
        raise

def compress_backup(backup_file: str) -> str:
    """Compress the backup file using gzip."""
    compressed_file = f"{backup_file}.gz"
    
    try:
        logger.info(f"Compressing backup file to {compressed_file}")
        with open(backup_file, 'rb') as f_in:
            with subprocess.Popen(['gzip', '-c'], stdin=subprocess.PIPE, stdout=subprocess.PIPE) as proc:
                stdout, stderr = proc.communicate(f_in.read())
                if proc.returncode != 0:
                    raise subprocess.CalledProcessError(proc.returncode, 'gzip')
                with open(compressed_file, 'wb') as f_out:
                    f_out.write(stdout)
        
        # Remove the original file
        os.remove(backup_file)
        logger.info(f"Backup file compressed: {compressed_file}")
        return compressed_file
    except Exception as e:
        logger.error(f"Compression failed: {str(e)}")
        raise

def encrypt_backup(backup_file: str) -> str:
    """Encrypt the backup file using GPG."""
    encrypted_file = f"{backup_file}.gpg"
    
    # Check if GPG key exists
    try:
        subprocess.run(["gpg", "--list-keys"], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        logger.error("GPG key not found. Please set up GPG first.")
        raise
    
    try:
        logger.info(f"Encrypting backup file to {encrypted_file}")
        # Get recipient from environment or use default
        recipient = os.environ.get("BACKUP_GPG_RECIPIENT", "<EMAIL>")
        
        cmd = [
            "gpg",
            "--recipient", recipient,
            "--output", encrypted_file,
            "--encrypt", backup_file
        ]
        
        subprocess.run(cmd, check=True, capture_output=True)
        
        # Remove the original file
        os.remove(backup_file)
        logger.info(f"Backup file encrypted: {encrypted_file}")
        return encrypted_file
    except subprocess.CalledProcessError as e:
        logger.error(f"Encryption failed: {e.stderr}")
        raise

def upload_to_cloud(backup_file: str, cloud_provider: str, bucket_name: str) -> bool:
    """Upload the backup file to cloud storage."""
    if cloud_provider == "aws":
        return upload_to_aws(backup_file, bucket_name)
    elif cloud_provider == "gcp":
        return upload_to_gcp(backup_file, bucket_name)
    elif cloud_provider == "azure":
        return upload_to_azure(backup_file, bucket_name)
    else:
        logger.error(f"Unsupported cloud provider: {cloud_provider}")
        return False

def upload_to_aws(backup_file: str, bucket_name: str) -> bool:
    """Upload the backup file to AWS S3."""
    try:
        logger.info(f"Uploading backup file to AWS S3: {bucket_name}")
        s3 = boto3.client('s3')
        
        # Get the file name from the path
        file_name = os.path.basename(backup_file)
        
        # Upload the file
        s3.upload_file(
            backup_file,
            bucket_name,
            f"database_backups/{file_name}",
            ExtraArgs={'ServerSideEncryption': 'AES256'}
        )
        
        logger.info(f"Backup file uploaded to S3: s3://{bucket_name}/database_backups/{file_name}")
        return True
    except Exception as e:
        logger.error(f"Upload to AWS S3 failed: {str(e)}")
        return False

def upload_to_gcp(backup_file: str, bucket_name: str) -> bool:
    """Upload the backup file to Google Cloud Storage."""
    try:
        logger.info(f"Uploading backup file to Google Cloud Storage: {bucket_name}")
        
        # Import GCP libraries
        try:
            from google.cloud import storage
        except ImportError:
            logger.error("Google Cloud Storage library not installed. Run: pip install google-cloud-storage")
            return False
        
        # Get the file name from the path
        file_name = os.path.basename(backup_file)
        
        # Upload the file
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(f"database_backups/{file_name}")
        blob.upload_from_filename(backup_file)
        
        logger.info(f"Backup file uploaded to GCS: gs://{bucket_name}/database_backups/{file_name}")
        return True
    except Exception as e:
        logger.error(f"Upload to Google Cloud Storage failed: {str(e)}")
        return False

def upload_to_azure(backup_file: str, bucket_name: str) -> bool:
    """Upload the backup file to Azure Blob Storage."""
    try:
        logger.info(f"Uploading backup file to Azure Blob Storage: {bucket_name}")
        
        # Import Azure libraries
        try:
            from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient
        except ImportError:
            logger.error("Azure Blob Storage library not installed. Run: pip install azure-storage-blob")
            return False
        
        # Get the file name from the path
        file_name = os.path.basename(backup_file)
        
        # Get connection string from environment
        connect_str = os.environ.get('AZURE_STORAGE_CONNECTION_STRING')
        if not connect_str:
            logger.error("Azure Storage connection string not found in environment variables")
            return False
        
        # Upload the file
        blob_service_client = BlobServiceClient.from_connection_string(connect_str)
        blob_client = blob_service_client.get_blob_client(container=bucket_name, blob=f"database_backups/{file_name}")
        
        with open(backup_file, "rb") as data:
            blob_client.upload_blob(data)
        
        logger.info(f"Backup file uploaded to Azure: {bucket_name}/database_backups/{file_name}")
        return True
    except Exception as e:
        logger.error(f"Upload to Azure Blob Storage failed: {str(e)}")
        return False

def cleanup_old_backups(backup_dir: str, retention_days: int) -> None:
    """Remove backups older than retention_days."""
    try:
        logger.info(f"Cleaning up backups older than {retention_days} days")
        
        # Get current time
        now = time.time()
        
        # Get all backup directories
        for backup_folder in os.listdir(backup_dir):
            backup_path = os.path.join(backup_dir, backup_folder)
            
            # Skip if not a directory
            if not os.path.isdir(backup_path):
                continue
            
            # Get folder creation time
            folder_time = os.path.getctime(backup_path)
            
            # Check if older than retention period
            if (now - folder_time) // (24 * 3600) >= retention_days:
                logger.info(f"Removing old backup: {backup_path}")
                shutil.rmtree(backup_path)
    except Exception as e:
        logger.error(f"Cleanup failed: {str(e)}")

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Get database connection parameters
    db_params = get_db_connection_params()
    
    if not db_params:
        logger.error("Could not get database connection parameters")
        return 1
    
    try:
        # Create backup directory
        backup_dir = create_backup_directory(args.backup_dir)
        
        # Create database backup
        backup_file = create_database_backup(backup_dir, db_params)
        
        # Compress backup
        backup_file = compress_backup(backup_file)
        
        # Encrypt backup if requested
        if args.encrypt:
            backup_file = encrypt_backup(backup_file)
        
        # Upload to cloud if requested
        if args.upload:
            if not args.bucket_name:
                logger.error("Bucket name is required for cloud upload")
                return 1
            
            upload_success = upload_to_cloud(backup_file, args.cloud_provider, args.bucket_name)
            
            if not upload_success:
                logger.warning("Cloud upload failed, but backup was created successfully")
        
        # Cleanup old backups
        cleanup_old_backups(args.backup_dir, args.retention_days)
        
        logger.info("Backup process completed successfully")
        return 0
    except Exception as e:
        logger.error(f"Backup process failed: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
