#!/usr/bin/env python3
"""
Codebase analyzer for RentUp.

This script analyzes the RentUp codebase to identify:
- File dependencies and relationships
- Code duplication and redundancy
- Overlapping functionality
- Unused code and dead imports
- Architectural patterns and violations

Usage:
    python codebase_analyzer.py [--path PATH] [--output OUTPUT]

Options:
    --path PATH       Path to the codebase to analyze [default: .]
    --output OUTPUT   Path to output the analysis results [default: codebase_analysis.json]
"""

import os
import sys
import json
import re
import ast
import argparse
import logging
from typing import Dict, List, Set, Tuple, Any, Optional
from collections import defaultdict
import networkx as nx
from pathlib import Path
import difflib

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CodebaseAnalyzer:
    """
    Analyzer for Python codebases.
    """
    
    def __init__(self, path: str = '.'):
        """
        Initialize the analyzer.
        
        Args:
            path: Path to the codebase to analyze
        """
        self.path = Path(path).resolve()
        self.files = []
        self.imports = defaultdict(set)
        self.exports = defaultdict(set)
        self.dependencies = defaultdict(set)
        self.functions = defaultdict(list)
        self.classes = defaultdict(list)
        self.duplicates = []
        self.unused = []
        self.metrics = {}
        self.dependency_graph = nx.DiGraph()
    
    def analyze(self):
        """
        Analyze the codebase.
        """
        logger.info(f"Analyzing codebase at {self.path}")
        
        # Find all Python files
        self._find_files()
        
        # Parse each file
        for file_path in self.files:
            self._parse_file(file_path)
        
        # Build dependency graph
        self._build_dependency_graph()
        
        # Find duplicates
        self._find_duplicates()
        
        # Find unused code
        self._find_unused()
        
        # Calculate metrics
        self._calculate_metrics()
        
        logger.info(f"Analysis complete. Found {len(self.files)} files.")
    
    def _find_files(self):
        """
        Find all Python files in the codebase.
        """
        for root, _, files in os.walk(self.path):
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.path)
                    self.files.append(rel_path)
    
    def _parse_file(self, file_path: str):
        """
        Parse a Python file to extract imports, functions, and classes.
        
        Args:
            file_path: Path to the file to parse
        """
        abs_path = os.path.join(self.path, file_path)
        
        try:
            with open(abs_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse the file with ast
            tree = ast.parse(content)
            
            # Extract imports
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for name in node.names:
                        self.imports[file_path].add(name.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        self.imports[file_path].add(node.module)
            
            # Extract functions
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    self.functions[file_path].append({
                        'name': node.name,
                        'lineno': node.lineno,
                        'args': [arg.arg for arg in node.args.args],
                        'docstring': ast.get_docstring(node)
                    })
                elif isinstance(node, ast.ClassDef):
                    methods = []
                    for child in node.body:
                        if isinstance(child, ast.FunctionDef):
                            methods.append({
                                'name': child.name,
                                'lineno': child.lineno,
                                'args': [arg.arg for arg in child.args.args],
                                'docstring': ast.get_docstring(child)
                            })
                    
                    self.classes[file_path].append({
                        'name': node.name,
                        'lineno': node.lineno,
                        'methods': methods,
                        'docstring': ast.get_docstring(node)
                    })
            
            # Extract exports (names defined at module level)
            for node in ast.walk(tree):
                if isinstance(node, ast.Assign) and hasattr(node, 'targets'):
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            self.exports[file_path].add(target.id)
        
        except Exception as e:
            logger.error(f"Error parsing {file_path}: {e}")
    
    def _build_dependency_graph(self):
        """
        Build a dependency graph of the codebase.
        """
        # Add all files as nodes
        for file in self.files:
            self.dependency_graph.add_node(file)
        
        # Add dependencies as edges
        for file, imports in self.imports.items():
            for imp in imports:
                # Convert import to file path
                imp_parts = imp.split('.')
                
                # Try different possible file paths
                possible_paths = []
                for i in range(len(imp_parts)):
                    path = os.path.join(*imp_parts[:i+1])
                    possible_paths.append(f"{path}.py")
                    possible_paths.append(os.path.join(path, "__init__.py"))
                
                # Check if any of the possible paths exist
                for path in possible_paths:
                    if path in self.files:
                        self.dependencies[file].add(path)
                        self.dependency_graph.add_edge(file, path)
                        break
    
    def _find_duplicates(self):
        """
        Find duplicate code across the codebase.
        """
        # Extract function and method bodies
        function_bodies = {}
        
        for file_path in self.files:
            abs_path = os.path.join(self.path, file_path)
            
            try:
                with open(abs_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # Extract function bodies
                for func in self.functions[file_path]:
                    start_line = func['lineno'] - 1
                    end_line = len(lines)
                    
                    # Find the end of the function
                    indent = 0
                    for i, line in enumerate(lines[start_line:], start_line):
                        if i == start_line:
                            # Get the indentation of the function definition
                            indent = len(line) - len(line.lstrip())
                        elif line.strip() and len(line) - len(line.lstrip()) <= indent:
                            # Found a line with less or equal indentation
                            end_line = i
                            break
                    
                    body = ''.join(lines[start_line:end_line])
                    key = (file_path, func['name'])
                    function_bodies[key] = body
            
            except Exception as e:
                logger.error(f"Error extracting function bodies from {file_path}: {e}")
        
        # Compare function bodies for similarity
        compared = set()
        for (file1, func1), body1 in function_bodies.items():
            for (file2, func2), body2 in function_bodies.items():
                if (file1, func1) == (file2, func2):
                    continue
                
                # Skip if already compared
                if ((file1, func1), (file2, func2)) in compared or ((file2, func2), (file1, func1)) in compared:
                    continue
                
                compared.add(((file1, func1), (file2, func2)))
                
                # Calculate similarity
                similarity = difflib.SequenceMatcher(None, body1, body2).ratio()
                
                # If similarity is high, add to duplicates
                if similarity > 0.7:
                    self.duplicates.append({
                        'file1': file1,
                        'function1': func1,
                        'file2': file2,
                        'function2': func2,
                        'similarity': similarity
                    })
    
    def _find_unused(self):
        """
        Find unused code in the codebase.
        """
        # Find all defined functions and classes
        defined = set()
        for file_path in self.files:
            for func in self.functions[file_path]:
                defined.add((file_path, func['name']))
            
            for cls in self.classes[file_path]:
                defined.add((file_path, cls['name']))
        
        # Find all used functions and classes
        used = set()
        for file_path in self.files:
            abs_path = os.path.join(self.path, file_path)
            
            try:
                with open(abs_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find all names used in the file
                tree = ast.parse(content)
                for node in ast.walk(tree):
                    if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Load):
                        # This is a name being used
                        name = node.id
                        
                        # Check if this name is defined in any file
                        for def_file, def_name in defined:
                            if def_name == name:
                                used.add((def_file, def_name))
            
            except Exception as e:
                logger.error(f"Error finding used names in {file_path}: {e}")
        
        # Find unused definitions
        for def_file, def_name in defined:
            if (def_file, def_name) not in used and not def_name.startswith('_'):
                self.unused.append({
                    'file': def_file,
                    'name': def_name
                })
    
    def _calculate_metrics(self):
        """
        Calculate metrics about the codebase.
        """
        # Count lines of code
        total_lines = 0
        code_lines = 0
        comment_lines = 0
        blank_lines = 0
        
        for file_path in self.files:
            abs_path = os.path.join(self.path, file_path)
            
            try:
                with open(abs_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                total_lines += len(lines)
                
                for line in lines:
                    line = line.strip()
                    if not line:
                        blank_lines += 1
                    elif line.startswith('#'):
                        comment_lines += 1
                    else:
                        code_lines += 1
            
            except Exception as e:
                logger.error(f"Error counting lines in {file_path}: {e}")
        
        self.metrics = {
            'files': len(self.files),
            'functions': sum(len(funcs) for funcs in self.functions.values()),
            'classes': sum(len(classes) for classes in self.classes.values()),
            'total_lines': total_lines,
            'code_lines': code_lines,
            'comment_lines': comment_lines,
            'blank_lines': blank_lines,
            'duplicates': len(self.duplicates),
            'unused': len(self.unused)
        }
    
    def get_results(self) -> Dict[str, Any]:
        """
        Get the analysis results.
        
        Returns:
            Dictionary of analysis results
        """
        return {
            'files': self.files,
            'imports': {k: list(v) for k, v in self.imports.items()},
            'exports': {k: list(v) for k, v in self.exports.items()},
            'dependencies': {k: list(v) for k, v in self.dependencies.items()},
            'functions': dict(self.functions),
            'classes': dict(self.classes),
            'duplicates': self.duplicates,
            'unused': self.unused,
            'metrics': self.metrics
        }
    
    def save_results(self, output_path: str):
        """
        Save the analysis results to a file.
        
        Args:
            output_path: Path to save the results
        """
        results = self.get_results()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"Analysis results saved to {output_path}")


def main():
    """
    Main entry point.
    """
    parser = argparse.ArgumentParser(description='Analyze a Python codebase')
    parser.add_argument('--path', default='.', help='Path to the codebase to analyze')
    parser.add_argument('--output', default='codebase_analysis.json', help='Path to output the analysis results')
    
    args = parser.parse_args()
    
    analyzer = CodebaseAnalyzer(args.path)
    analyzer.analyze()
    analyzer.save_results(args.output)


if __name__ == '__main__':
    main()
