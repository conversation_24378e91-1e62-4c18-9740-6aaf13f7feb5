#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to restore the RentUp PostgreSQL database from a backup.

This script:
1. Downloads the backup from cloud storage (if needed)
2. Decrypts the backup (if encrypted)
3. Decompresses the backup
4. Restores the database from the backup
"""

import os
import sys
import argparse
import logging
import subprocess
import datetime
import shutil
import boto3
import json
import tempfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import from our modules
try:
    from app.core.db_config import db_config
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    NEW_MODULES = False

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Restore RentUp PostgreSQL database from backup')
    parser.add_argument('--backup-file', help='Path to backup file')
    parser.add_argument('--cloud-backup', action='store_true', help='Restore from cloud storage')
    parser.add_argument('--cloud-provider', choices=['aws', 'gcp', 'azure'], default='aws', help='Cloud provider')
    parser.add_argument('--bucket-name', help='Cloud storage bucket name')
    parser.add_argument('--backup-key', help='Cloud storage key/path to backup file')
    parser.add_argument('--encrypted', action='store_true', help='Backup is encrypted')
    parser.add_argument('--compressed', action='store_true', help='Backup is compressed')
    parser.add_argument('--drop-db', action='store_true', help='Drop database before restore')
    parser.add_argument('--create-db', action='store_true', help='Create database if it does not exist')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def get_db_connection_params():
    """Get database connection parameters."""
    if NEW_MODULES:
        # Use db_config
        db_host = db_config.POSTGRES_SERVER
        db_port = db_config.POSTGRES_PORT
        db_user = db_config.POSTGRES_USER
        db_password = db_config.POSTGRES_PASSWORD
        db_name = db_config.POSTGRES_DB
    else:
        # Use settings
        db_url = settings.DATABASE_URL
        # Parse DATABASE_URL
        import re
        match = re.match(r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
        if match:
            db_user, db_password, db_host, db_port, db_name = match.groups()
            db_port = int(db_port)
        else:
            logger.error("Could not parse DATABASE_URL")
            return None
    
    return {
        "host": db_host,
        "port": db_port,
        "user": db_user,
        "password": db_password,
        "dbname": db_name
    }

def download_from_cloud(cloud_provider: str, bucket_name: str, backup_key: str) -> str:
    """Download backup file from cloud storage."""
    if cloud_provider == "aws":
        return download_from_aws(bucket_name, backup_key)
    elif cloud_provider == "gcp":
        return download_from_gcp(bucket_name, backup_key)
    elif cloud_provider == "azure":
        return download_from_azure(bucket_name, backup_key)
    else:
        logger.error(f"Unsupported cloud provider: {cloud_provider}")
        return None

def download_from_aws(bucket_name: str, backup_key: str) -> str:
    """Download backup file from AWS S3."""
    try:
        logger.info(f"Downloading backup file from AWS S3: s3://{bucket_name}/{backup_key}")
        s3 = boto3.client('s3')
        
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(backup_key)[1])
        temp_file.close()
        
        # Download the file
        s3.download_file(bucket_name, backup_key, temp_file.name)
        
        logger.info(f"Backup file downloaded to: {temp_file.name}")
        return temp_file.name
    except Exception as e:
        logger.error(f"Download from AWS S3 failed: {str(e)}")
        return None

def download_from_gcp(bucket_name: str, backup_key: str) -> str:
    """Download backup file from Google Cloud Storage."""
    try:
        logger.info(f"Downloading backup file from Google Cloud Storage: gs://{bucket_name}/{backup_key}")
        
        # Import GCP libraries
        try:
            from google.cloud import storage
        except ImportError:
            logger.error("Google Cloud Storage library not installed. Run: pip install google-cloud-storage")
            return None
        
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(backup_key)[1])
        temp_file.close()
        
        # Download the file
        storage_client = storage.Client()
        bucket = storage_client.bucket(bucket_name)
        blob = bucket.blob(backup_key)
        blob.download_to_filename(temp_file.name)
        
        logger.info(f"Backup file downloaded to: {temp_file.name}")
        return temp_file.name
    except Exception as e:
        logger.error(f"Download from Google Cloud Storage failed: {str(e)}")
        return None

def download_from_azure(bucket_name: str, backup_key: str) -> str:
    """Download backup file from Azure Blob Storage."""
    try:
        logger.info(f"Downloading backup file from Azure Blob Storage: {bucket_name}/{backup_key}")
        
        # Import Azure libraries
        try:
            from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient
        except ImportError:
            logger.error("Azure Blob Storage library not installed. Run: pip install azure-storage-blob")
            return None
        
        # Get connection string from environment
        connect_str = os.environ.get('AZURE_STORAGE_CONNECTION_STRING')
        if not connect_str:
            logger.error("Azure Storage connection string not found in environment variables")
            return None
        
        # Create a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(backup_key)[1])
        temp_file.close()
        
        # Download the file
        blob_service_client = BlobServiceClient.from_connection_string(connect_str)
        blob_client = blob_service_client.get_blob_client(container=bucket_name, blob=backup_key)
        
        with open(temp_file.name, "wb") as download_file:
            download_file.write(blob_client.download_blob().readall())
        
        logger.info(f"Backup file downloaded to: {temp_file.name}")
        return temp_file.name
    except Exception as e:
        logger.error(f"Download from Azure Blob Storage failed: {str(e)}")
        return None

def decrypt_backup(backup_file: str) -> str:
    """Decrypt the backup file using GPG."""
    decrypted_file = backup_file.replace('.gpg', '')
    
    try:
        logger.info(f"Decrypting backup file to {decrypted_file}")
        
        cmd = [
            "gpg",
            "--output", decrypted_file,
            "--decrypt", backup_file
        ]
        
        subprocess.run(cmd, check=True, capture_output=True)
        
        # Remove the encrypted file
        os.remove(backup_file)
        logger.info(f"Backup file decrypted: {decrypted_file}")
        return decrypted_file
    except subprocess.CalledProcessError as e:
        logger.error(f"Decryption failed: {e.stderr}")
        raise

def decompress_backup(backup_file: str) -> str:
    """Decompress the backup file using gunzip."""
    decompressed_file = backup_file.replace('.gz', '')
    
    try:
        logger.info(f"Decompressing backup file to {decompressed_file}")
        
        with open(backup_file, 'rb') as f_in:
            with subprocess.Popen(['gunzip', '-c'], stdin=subprocess.PIPE, stdout=subprocess.PIPE) as proc:
                stdout, stderr = proc.communicate(f_in.read())
                if proc.returncode != 0:
                    raise subprocess.CalledProcessError(proc.returncode, 'gunzip')
                with open(decompressed_file, 'wb') as f_out:
                    f_out.write(stdout)
        
        # Remove the compressed file
        os.remove(backup_file)
        logger.info(f"Backup file decompressed: {decompressed_file}")
        return decompressed_file
    except Exception as e:
        logger.error(f"Decompression failed: {str(e)}")
        raise

def drop_database(db_params: Dict[str, str]) -> bool:
    """Drop the database if it exists."""
    try:
        logger.info(f"Dropping database {db_params['dbname']}")
        
        # Set environment variables for psql
        env = os.environ.copy()
        env["PGPASSWORD"] = db_params["password"]
        
        # Connect to postgres database to drop the target database
        cmd = [
            "psql",
            "-h", db_params["host"],
            "-p", str(db_params["port"]),
            "-U", db_params["user"],
            "-d", "postgres",
            "-c", f"DROP DATABASE IF EXISTS {db_params['dbname']};"
        ]
        
        subprocess.run(cmd, env=env, check=True, capture_output=True)
        logger.info(f"Database {db_params['dbname']} dropped")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to drop database: {e.stderr}")
        return False

def create_database(db_params: Dict[str, str]) -> bool:
    """Create the database if it doesn't exist."""
    try:
        logger.info(f"Creating database {db_params['dbname']}")
        
        # Set environment variables for psql
        env = os.environ.copy()
        env["PGPASSWORD"] = db_params["password"]
        
        # Connect to postgres database to create the target database
        cmd = [
            "psql",
            "-h", db_params["host"],
            "-p", str(db_params["port"]),
            "-U", db_params["user"],
            "-d", "postgres",
            "-c", f"CREATE DATABASE {db_params['dbname']};"
        ]
        
        subprocess.run(cmd, env=env, check=True, capture_output=True)
        logger.info(f"Database {db_params['dbname']} created")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to create database: {e.stderr}")
        return False

def restore_database(backup_file: str, db_params: Dict[str, str]) -> bool:
    """Restore the database from backup."""
    try:
        logger.info(f"Restoring database {db_params['dbname']} from {backup_file}")
        
        # Set environment variables for pg_restore
        env = os.environ.copy()
        env["PGPASSWORD"] = db_params["password"]
        
        # Create pg_restore command
        cmd = [
            "pg_restore",
            "-h", db_params["host"],
            "-p", str(db_params["port"]),
            "-U", db_params["user"],
            "-d", db_params["dbname"],
            "-v",  # Verbose
            backup_file
        ]
        
        subprocess.run(cmd, env=env, check=True, capture_output=True)
        logger.info(f"Database {db_params['dbname']} restored from {backup_file}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Database restore failed: {e.stderr}")
        return False

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Get database connection parameters
    db_params = get_db_connection_params()
    
    if not db_params:
        logger.error("Could not get database connection parameters")
        return 1
    
    try:
        # Get backup file
        backup_file = None
        
        if args.cloud_backup:
            if not args.bucket_name or not args.backup_key:
                logger.error("Bucket name and backup key are required for cloud restore")
                return 1
            
            backup_file = download_from_cloud(args.cloud_provider, args.bucket_name, args.backup_key)
            
            if not backup_file:
                logger.error("Failed to download backup from cloud storage")
                return 1
        else:
            if not args.backup_file:
                logger.error("Backup file path is required for local restore")
                return 1
            
            backup_file = args.backup_file
            
            if not os.path.exists(backup_file):
                logger.error(f"Backup file not found: {backup_file}")
                return 1
        
        # Decrypt backup if encrypted
        if args.encrypted:
            backup_file = decrypt_backup(backup_file)
        
        # Decompress backup if compressed
        if args.compressed:
            backup_file = decompress_backup(backup_file)
        
        # Drop database if requested
        if args.drop_db:
            if not drop_database(db_params):
                logger.error("Failed to drop database")
                return 1
        
        # Create database if requested
        if args.create_db:
            if not create_database(db_params):
                logger.error("Failed to create database")
                return 1
        
        # Restore database
        if not restore_database(backup_file, db_params):
            logger.error("Failed to restore database")
            return 1
        
        logger.info("Database restore completed successfully")
        return 0
    except Exception as e:
        logger.error(f"Restore process failed: {str(e)}")
        return 1
    finally:
        # Clean up temporary files
        if args.cloud_backup and backup_file and os.path.exists(backup_file):
            os.remove(backup_file)

if __name__ == "__main__":
    sys.exit(main())
