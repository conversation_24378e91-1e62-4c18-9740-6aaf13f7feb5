#!/usr/bin/env python
"""
Database Query Optimization Script for RentUp.

This script analyzes the database queries and suggests optimizations
such as adding indexes, denormalizing tables, or rewriting queries.
"""

import os
import sys
import logging
import argparse
import time
from datetime import datetime
from typing import Dict, List, Any, Tuple, Set

import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"query_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Optimize database queries for RentUp')
    parser.add_argument('--db-url', type=str, help='Database URL', 
                        default=os.environ.get('DATABASE_URL'))
    parser.add_argument('--min-exec-time', type=float, default=100.0,
                        help='Minimum execution time in ms to consider for optimization')
    parser.add_argument('--min-calls', type=int, default=10,
                        help='Minimum number of calls to consider for optimization')
    parser.add_argument('--output-dir', type=str, default='query_optimization_results',
                        help='Directory to save results')
    parser.add_argument('--create-indexes', action='store_true',
                        help='Automatically create suggested indexes')
    parser.add_argument('--dry-run', action='store_true',
                        help='Show what would be done without making changes')
    return parser.parse_args()

def connect_to_db(db_url: str):
    """Connect to the database."""
    try:
        conn = psycopg2.connect(db_url)
        logger.info("Connected to the database")
        return conn
    except Exception as e:
        logger.error(f"Error connecting to the database: {e}")
        sys.exit(1)

def get_slow_queries(conn, min_exec_time: float, min_calls: int) -> List[Dict[str, Any]]:
    """Get slow queries from pg_stat_statements."""
    query = """
    SELECT 
        query,
        calls,
        total_exec_time / calls as avg_exec_time,
        rows,
        shared_blks_hit,
        shared_blks_read,
        shared_blks_dirtied,
        shared_blks_written,
        temp_blks_read,
        temp_blks_written
    FROM pg_stat_statements
    WHERE calls >= %s
    AND total_exec_time / calls >= %s
    ORDER BY total_exec_time / calls DESC
    LIMIT 100;
    """
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute(query, (min_calls, min_exec_time))
            return cur.fetchall()
    except Exception as e:
        logger.error(f"Error getting slow queries: {e}")
        return []

def analyze_query(conn, query_text: str) -> Dict[str, Any]:
    """Analyze a query using EXPLAIN ANALYZE."""
    try:
        with conn.cursor() as cur:
            cur.execute(f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query_text}")
            result = cur.fetchone()[0]
            return result[0]
    except Exception as e:
        logger.error(f"Error analyzing query: {e}")
        return {}

def suggest_indexes(conn, query_text: str) -> List[str]:
    """Suggest indexes for a query using pg_hint_plan."""
    try:
        with conn.cursor() as cur:
            # Enable pg_hint_plan
            cur.execute("SET pg_hint_plan.enable_hint TO on")
            cur.execute("SET pg_hint_plan.debug_print TO on")
            cur.execute("SET client_min_messages TO log")
            
            # Get table information
            cur.execute(f"EXPLAIN {query_text}")
            
            # Extract table names from the query plan
            tables = set()
            for line in cur.fetchall():
                if "Seq Scan on" in line[0]:
                    table = line[0].split("Seq Scan on")[1].strip().split(" ")[0]
                    tables.add(table)
            
            # Suggest indexes for each table
            suggested_indexes = []
            for table in tables:
                # Get columns from the table
                cur.execute(f"SELECT column_name FROM information_schema.columns WHERE table_name = '{table}'")
                columns = [row[0] for row in cur.fetchall()]
                
                # Check if columns are used in WHERE, JOIN, or ORDER BY
                for column in columns:
                    if (f"{table}.{column}" in query_text or 
                        f"{column}" in query_text and table in query_text):
                        suggested_indexes.append(f"CREATE INDEX idx_{table}_{column} ON {table} ({column});")
            
            return suggested_indexes
    except Exception as e:
        logger.error(f"Error suggesting indexes: {e}")
        return []

def create_index(conn, index_sql: str, dry_run: bool = False) -> bool:
    """Create an index in the database."""
    try:
        if dry_run:
            logger.info(f"Would execute: {index_sql}")
            return True
        
        with conn.cursor() as cur:
            cur.execute(index_sql)
            conn.commit()
            logger.info(f"Created index: {index_sql}")
            return True
    except Exception as e:
        logger.error(f"Error creating index: {e}")
        conn.rollback()
        return False

def visualize_query_stats(queries: List[Dict[str, Any]], output_dir: str):
    """Create visualizations of query statistics."""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Convert to DataFrame
    df = pd.DataFrame(queries)
    
    # Plot execution time distribution
    plt.figure(figsize=(12, 6))
    sns.histplot(df['avg_exec_time'], bins=20)
    plt.title('Query Execution Time Distribution')
    plt.xlabel('Average Execution Time (ms)')
    plt.ylabel('Count')
    plt.savefig(f"{output_dir}/exec_time_distribution.png")
    
    # Plot top 10 slowest queries
    plt.figure(figsize=(12, 6))
    top_10 = df.nlargest(10, 'avg_exec_time')
    sns.barplot(x=top_10.index, y='avg_exec_time', data=top_10)
    plt.title('Top 10 Slowest Queries')
    plt.xlabel('Query Index')
    plt.ylabel('Average Execution Time (ms)')
    plt.xticks(rotation=45)
    plt.savefig(f"{output_dir}/top_10_slowest.png")
    
    # Plot calls vs execution time
    plt.figure(figsize=(12, 6))
    sns.scatterplot(x='calls', y='avg_exec_time', data=df)
    plt.title('Query Calls vs Execution Time')
    plt.xlabel('Number of Calls')
    plt.ylabel('Average Execution Time (ms)')
    plt.savefig(f"{output_dir}/calls_vs_exec_time.png")

def main():
    """Main function."""
    args = parse_args()
    
    if not args.db_url:
        logger.error("Database URL is required. Set DATABASE_URL environment variable or use --db-url")
        sys.exit(1)
    
    # Create output directory
    if not os.path.exists(args.output_dir):
        os.makedirs(args.output_dir)
    
    # Connect to the database
    conn = connect_to_db(args.db_url)
    
    # Get slow queries
    logger.info(f"Getting slow queries (min_exec_time={args.min_exec_time}ms, min_calls={args.min_calls})...")
    slow_queries = get_slow_queries(conn, args.min_exec_time, args.min_calls)
    logger.info(f"Found {len(slow_queries)} slow queries")
    
    if not slow_queries:
        logger.info("No slow queries found. Try lowering the min_exec_time or min_calls thresholds.")
        return
    
    # Visualize query statistics
    logger.info("Creating visualizations...")
    visualize_query_stats(slow_queries, args.output_dir)
    
    # Analyze each slow query
    results = []
    for i, query_data in enumerate(slow_queries):
        query_text = query_data['query']
        logger.info(f"Analyzing query {i+1}/{len(slow_queries)}: {query_text[:100]}...")
        
        # Analyze the query
        analysis = analyze_query(conn, query_text)
        
        # Suggest indexes
        suggested_indexes = suggest_indexes(conn, query_text)
        
        # Create indexes if requested
        if args.create_indexes and suggested_indexes:
            for index_sql in suggested_indexes:
                create_index(conn, index_sql, args.dry_run)
        
        results.append({
            'query': query_text,
            'avg_exec_time': query_data['avg_exec_time'],
            'calls': query_data['calls'],
            'rows': query_data['rows'],
            'analysis': analysis,
            'suggested_indexes': suggested_indexes
        })
    
    # Save results to file
    with open(f"{args.output_dir}/optimization_results.txt", 'w') as f:
        for i, result in enumerate(results):
            f.write(f"Query {i+1}:\n")
            f.write(f"Text: {result['query']}\n")
            f.write(f"Avg Execution Time: {result['avg_exec_time']:.2f}ms\n")
            f.write(f"Calls: {result['calls']}\n")
            f.write(f"Rows: {result['rows']}\n")
            f.write("Suggested Indexes:\n")
            for index in result['suggested_indexes']:
                f.write(f"  {index}\n")
            f.write("\n")
    
    logger.info(f"Results saved to {args.output_dir}/optimization_results.txt")
    
    # Close the database connection
    conn.close()

if __name__ == "__main__":
    main()
