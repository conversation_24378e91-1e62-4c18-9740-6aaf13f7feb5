#!/usr/bin/env python3
"""
Script to implement database security hardening for RentUp.

This script:
1. Enables SSL for database connections
2. Sets password encryption to SCRAM-SHA-256
3. Revokes excessive permissions from public schema
4. Implements row-level security for sensitive tables
5. Creates database roles with appropriate permissions
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import json
import subprocess
import re
import getpass
from typing import Dict, List, Any, Optional, Tuple

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import psycopg2
from psycopg2 import sql

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Implement database security hardening for RentUp')
    parser.add_argument('--host', help='Database host')
    parser.add_argument('--port', type=int, help='Database port')
    parser.add_argument('--user', help='Database user')
    parser.add_argument('--password', help='Database password')
    parser.add_argument('--db', help='Database name')
    parser.add_argument('--ssl', action='store_true', help='Enable SSL for database connections')
    parser.add_argument('--password-encryption', action='store_true', help='Set password encryption to SCRAM-SHA-256')
    parser.add_argument('--revoke-public', action='store_true', help='Revoke excessive permissions from public schema')
    parser.add_argument('--row-level-security', action='store_true', help='Implement row-level security for sensitive tables')
    parser.add_argument('--create-roles', action='store_true', help='Create database roles with appropriate permissions')
    parser.add_argument('--all', action='store_true', help='Implement all database security hardening')
    parser.add_argument('--dry-run', action='store_true', help='Show SQL statements without executing them')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def get_connection_params(args):
    """Get database connection parameters."""
    if args.host and args.port and args.user and args.db:
        # Use command line arguments
        db_host = args.host
        db_port = args.port
        db_user = args.user
        db_password = args.password or getpass.getpass(f"Password for {db_user}@{db_host}: ")
        db_name = args.db
    elif NEW_MODULES:
        # Use db_config
        db_host = db_config.POSTGRES_SERVER
        db_port = db_config.POSTGRES_PORT
        db_user = db_config.POSTGRES_USER
        db_password = db_config.POSTGRES_PASSWORD
        db_name = db_config.POSTGRES_DB
    else:
        # Use settings
        db_url = settings.DATABASE_URL
        # Parse DATABASE_URL
        match = re.match(r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
        if match:
            db_user, db_password, db_host, db_port, db_name = match.groups()
            db_port = int(db_port)
        else:
            logger.error("Could not parse DATABASE_URL")
            return None
    
    return {
        "host": db_host,
        "port": db_port,
        "user": db_user,
        "password": db_password,
        "dbname": db_name
    }

def enable_ssl(conn, args):
    """Enable SSL for database connections."""
    logger.info("Enabling SSL for database connections...")
    
    if args.dry_run:
        logger.info("SQL statements to enable SSL:")
        logger.info("ALTER SYSTEM SET ssl = on;")
        logger.info("ALTER SYSTEM SET ssl_cert_file = 'server.crt';")
        logger.info("ALTER SYSTEM SET ssl_key_file = 'server.key';")
        return True
    
    try:
        with conn.cursor() as cur:
            # Check if SSL is already enabled
            cur.execute("SHOW ssl")
            ssl_enabled = cur.fetchone()[0] == "on"
            
            if ssl_enabled:
                logger.info("SSL is already enabled")
                return True
            
            # Enable SSL
            cur.execute("ALTER SYSTEM SET ssl = on")
            cur.execute("ALTER SYSTEM SET ssl_cert_file = 'server.crt'")
            cur.execute("ALTER SYSTEM SET ssl_key_file = 'server.key'")
            conn.commit()
            
            logger.info("SSL enabled for database connections")
            logger.info("You will need to restart PostgreSQL for this to take effect")
            logger.info("Make sure server.crt and server.key files exist in the PostgreSQL data directory")
            
            return True
    except Exception as e:
        logger.error(f"Error enabling SSL: {e}")
        return False

def set_password_encryption(conn, args):
    """Set password encryption to SCRAM-SHA-256."""
    logger.info("Setting password encryption to SCRAM-SHA-256...")
    
    if args.dry_run:
        logger.info("SQL statements to set password encryption:")
        logger.info("ALTER SYSTEM SET password_encryption = 'scram-sha-256';")
        return True
    
    try:
        with conn.cursor() as cur:
            # Check if password encryption is already set to scram-sha-256
            cur.execute("SHOW password_encryption")
            password_encryption = cur.fetchone()[0]
            
            if password_encryption == "scram-sha-256":
                logger.info("Password encryption is already set to scram-sha-256")
                return True
            
            # Set password encryption to scram-sha-256
            cur.execute("ALTER SYSTEM SET password_encryption = 'scram-sha-256'")
            conn.commit()
            
            logger.info("Password encryption set to scram-sha-256")
            logger.info("You will need to restart PostgreSQL for this to take effect")
            logger.info("After restart, you should update all user passwords to use the new encryption")
            
            return True
    except Exception as e:
        logger.error(f"Error setting password encryption: {e}")
        return False

def revoke_public_permissions(conn, args):
    """Revoke excessive permissions from public schema."""
    logger.info("Revoking excessive permissions from public schema...")
    
    if args.dry_run:
        logger.info("SQL statements to revoke public permissions:")
        logger.info("REVOKE ALL ON SCHEMA public FROM PUBLIC;")
        logger.info("GRANT USAGE ON SCHEMA public TO PUBLIC;")
        return True
    
    try:
        with conn.cursor() as cur:
            # Revoke all permissions from public schema
            cur.execute("REVOKE ALL ON SCHEMA public FROM PUBLIC")
            
            # Grant usage permission to public schema
            cur.execute("GRANT USAGE ON SCHEMA public TO PUBLIC")
            
            conn.commit()
            
            logger.info("Revoked excessive permissions from public schema")
            
            return True
    except Exception as e:
        logger.error(f"Error revoking public permissions: {e}")
        return False

def implement_row_level_security(conn, args):
    """Implement row-level security for sensitive tables."""
    logger.info("Implementing row-level security for sensitive tables...")
    
    # Define sensitive tables and their RLS policies
    sensitive_tables = {
        "users": {
            "policy_name": "users_policy",
            "using_expr": "id = current_setting('app.current_user_id')::text",
            "with_check_expr": "id = current_setting('app.current_user_id')::text"
        },
        "items": {
            "policy_name": "items_policy",
            "using_expr": "owner_id = current_setting('app.current_user_id')::text OR is_public = true",
            "with_check_expr": "owner_id = current_setting('app.current_user_id')::text"
        },
        "rentals": {
            "policy_name": "rentals_policy",
            "using_expr": "owner_id = current_setting('app.current_user_id')::text OR renter_id = current_setting('app.current_user_id')::text",
            "with_check_expr": "owner_id = current_setting('app.current_user_id')::text OR renter_id = current_setting('app.current_user_id')::text"
        },
        "payments": {
            "policy_name": "payments_policy",
            "using_expr": "user_id = current_setting('app.current_user_id')::text",
            "with_check_expr": "user_id = current_setting('app.current_user_id')::text"
        },
        "messages": {
            "policy_name": "messages_policy",
            "using_expr": "sender_id = current_setting('app.current_user_id')::text OR receiver_id = current_setting('app.current_user_id')::text",
            "with_check_expr": "sender_id = current_setting('app.current_user_id')::text"
        }
    }
    
    if args.dry_run:
        logger.info("SQL statements to implement row-level security:")
        
        for table, policy in sensitive_tables.items():
            logger.info(f"-- For table {table}:")
            logger.info(f"ALTER TABLE {table} ENABLE ROW LEVEL SECURITY;")
            logger.info(f"CREATE POLICY {policy['policy_name']} ON {table}")
            logger.info(f"    USING ({policy['using_expr']})")
            logger.info(f"    WITH CHECK ({policy['with_check_expr']});")
        
        return True
    
    try:
        with conn.cursor() as cur:
            # Check if tables exist
            cur.execute("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
            """)
            existing_tables = [row[0] for row in cur.fetchall()]
            
            # Implement RLS for existing sensitive tables
            for table, policy in sensitive_tables.items():
                if table in existing_tables:
                    logger.info(f"Implementing row-level security for table {table}")
                    
                    # Enable row-level security
                    cur.execute(f"ALTER TABLE {table} ENABLE ROW LEVEL SECURITY")
                    
                    # Check if policy already exists
                    cur.execute(f"""
                        SELECT 1
                        FROM pg_policy
                        WHERE polname = '{policy['policy_name']}'
                    """)
                    policy_exists = cur.fetchone() is not None
                    
                    if policy_exists:
                        # Drop existing policy
                        cur.execute(f"DROP POLICY {policy['policy_name']} ON {table}")
                    
                    # Create policy
                    cur.execute(f"""
                        CREATE POLICY {policy['policy_name']} ON {table}
                        USING ({policy['using_expr']})
                        WITH CHECK ({policy['with_check_expr']})
                    """)
                    
                    logger.info(f"Row-level security implemented for table {table}")
                else:
                    logger.warning(f"Table {table} does not exist, skipping")
            
            # Create function to set current user ID
            cur.execute("""
                CREATE OR REPLACE FUNCTION set_current_user_id(user_id text)
                RETURNS void AS $$
                BEGIN
                    PERFORM set_config('app.current_user_id', user_id, false);
                END;
                $$ LANGUAGE plpgsql;
            """)
            
            conn.commit()
            
            logger.info("Row-level security implemented for sensitive tables")
            logger.info("Use set_current_user_id() function to set the current user ID in your application")
            
            return True
    except Exception as e:
        logger.error(f"Error implementing row-level security: {e}")
        return False

def create_database_roles(conn, args):
    """Create database roles with appropriate permissions."""
    logger.info("Creating database roles with appropriate permissions...")
    
    # Define roles and their permissions
    roles = {
        "rentup_readonly": {
            "description": "Read-only access to all tables",
            "permissions": [
                "GRANT USAGE ON SCHEMA public TO rentup_readonly",
                "GRANT SELECT ON ALL TABLES IN SCHEMA public TO rentup_readonly",
                "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO rentup_readonly"
            ]
        },
        "rentup_readwrite": {
            "description": "Read-write access to all tables",
            "permissions": [
                "GRANT USAGE ON SCHEMA public TO rentup_readwrite",
                "GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO rentup_readwrite",
                "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO rentup_readwrite"
            ]
        },
        "rentup_admin": {
            "description": "Administrative access to all tables",
            "permissions": [
                "GRANT ALL PRIVILEGES ON SCHEMA public TO rentup_admin",
                "GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO rentup_admin",
                "GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO rentup_admin",
                "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO rentup_admin",
                "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO rentup_admin"
            ]
        }
    }
    
    if args.dry_run:
        logger.info("SQL statements to create database roles:")
        
        for role, info in roles.items():
            logger.info(f"-- Create role {role} ({info['description']}):")
            logger.info(f"CREATE ROLE {role};")
            
            for permission in info['permissions']:
                logger.info(permission + ";")
        
        return True
    
    try:
        with conn.cursor() as cur:
            # Create roles and grant permissions
            for role, info in roles.items():
                logger.info(f"Creating role {role}")
                
                # Check if role already exists
                cur.execute(f"""
                    SELECT 1
                    FROM pg_roles
                    WHERE rolname = '{role}'
                """)
                role_exists = cur.fetchone() is not None
                
                if not role_exists:
                    # Create role
                    cur.execute(f"CREATE ROLE {role}")
                    logger.info(f"Role {role} created")
                else:
                    logger.info(f"Role {role} already exists")
                
                # Grant permissions
                for permission in info['permissions']:
                    cur.execute(permission)
                
                logger.info(f"Permissions granted to role {role}")
            
            conn.commit()
            
            logger.info("Database roles created with appropriate permissions")
            logger.info("To assign a role to a user, use: GRANT role_name TO user_name")
            
            return True
    except Exception as e:
        logger.error(f"Error creating database roles: {e}")
        return False

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Get database connection parameters
    conn_params = get_connection_params(args)
    
    if not conn_params:
        logger.error("Could not get database connection parameters")
        return 1
    
    # Connect to database
    try:
        conn = psycopg2.connect(**conn_params)
        conn.autocommit = False
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        return 1
    
    # Determine which hardening to implement
    run_ssl = args.ssl or args.all
    run_password_encryption = args.password_encryption or args.all
    run_revoke_public = args.revoke_public or args.all
    run_row_level_security = args.row_level_security or args.all
    run_create_roles = args.create_roles or args.all
    
    # If no hardening specified, show help
    if not (run_ssl or run_password_encryption or run_revoke_public or run_row_level_security or run_create_roles):
        logger.info("No hardening specified. Use --all or specify individual hardening.")
        return 1
    
    # Implement database security hardening
    results = {}
    
    if run_ssl:
        results["ssl"] = enable_ssl(conn, args)
    
    if run_password_encryption:
        results["password_encryption"] = set_password_encryption(conn, args)
    
    if run_revoke_public:
        results["revoke_public"] = revoke_public_permissions(conn, args)
    
    if run_row_level_security:
        results["row_level_security"] = implement_row_level_security(conn, args)
    
    if run_create_roles:
        results["create_roles"] = create_database_roles(conn, args)
    
    # Close database connection
    conn.close()
    
    # Print summary
    logger.info("Database security hardening summary:")
    
    for category, success in results.items():
        status = "Success" if success else "Failed"
        logger.info(f"  {category}: {status}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
