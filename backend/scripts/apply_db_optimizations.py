#!/usr/bin/env python3
"""
Database optimization script for RentUp.

This script applies various database optimizations including:
- Creating and updating indexes
- Analyzing tables for statistics
- Optimizing query performance
- Generating optimization reports

Usage:
    python apply_db_optimizations.py --analyze  # Analyze database and suggest optimizations
    python apply_db_optimizations.py --apply    # Apply recommended optimizations
    python apply_db_optimizations.py --report   # Generate optimization report
    python apply_db_optimizations.py --all      # Perform all optimization tasks
    python apply_db_optimizations.py --help     # Show help message
"""

import sys
import os
import argparse
import logging
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text, create_engine, inspect
from sqlalchemy.orm import sessionmaker

from app.core.config import settings
from app.core.index_optimization import (
    IndexDefinition, IndexType, get_existing_indexes,
    get_index_usage_stats, get_unused_indexes, create_index, drop_index
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"db_optimization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger(__name__)

# Define recommended indexes for common queries
RECOMMENDED_INDEXES = [
    # Users table indexes
    IndexDefinition(
        table_name="users",
        column_names=["email"],
        unique=True,
        index_name="uidx_users_email"
    ),
    IndexDefinition(
        table_name="users",
        column_names=["verification_level", "is_active"],
        index_name="idx_users_verification_active"
    ),

    # Items table indexes
    IndexDefinition(
        table_name="items",
        column_names=["owner_id"],
        index_name="idx_items_owner_id"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["category", "is_available"],
        index_name="idx_items_category_available"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["location", "is_available"],
        index_name="idx_items_location_available"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["daily_price"],
        index_name="idx_items_daily_price"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["created_at"],
        index_name="idx_items_created_at"
    ),
    IndexDefinition(
        table_name="items",
        column_names=["name", "description"],
        index_type=IndexType.GIN,
        index_name="idx_items_text_search"
    ),

    # Rentals table indexes
    IndexDefinition(
        table_name="rentals",
        column_names=["item_id"],
        index_name="idx_rentals_item_id"
    ),
    IndexDefinition(
        table_name="rentals",
        column_names=["owner_id"],
        index_name="idx_rentals_owner_id"
    ),
    IndexDefinition(
        table_name="rentals",
        column_names=["renter_id"],
        index_name="idx_rentals_renter_id"
    ),
    IndexDefinition(
        table_name="rentals",
        column_names=["status", "start_date", "end_date"],
        index_name="idx_rentals_status_dates"
    ),

    # Auctions table indexes
    IndexDefinition(
        table_name="auctions",
        column_names=["item_id"],
        index_name="idx_auctions_item_id"
    ),
    IndexDefinition(
        table_name="auctions",
        column_names=["owner_id"],
        index_name="idx_auctions_owner_id"
    ),
    IndexDefinition(
        table_name="auctions",
        column_names=["status", "end_time"],
        index_name="idx_auctions_status_end_time"
    ),

    # Bids table indexes
    IndexDefinition(
        table_name="bids",
        column_names=["auction_id"],
        index_name="idx_bids_auction_id"
    ),
    IndexDefinition(
        table_name="bids",
        column_names=["bidder_id"],
        index_name="idx_bids_bidder_id"
    ),

    # Agreements table indexes
    IndexDefinition(
        table_name="agreements",
        column_names=["rental_id"],
        index_name="idx_agreements_rental_id"
    ),
    IndexDefinition(
        table_name="agreements",
        column_names=["owner_id"],
        index_name="idx_agreements_owner_id"
    ),
    IndexDefinition(
        table_name="agreements",
        column_names=["renter_id"],
        index_name="idx_agreements_renter_id"
    ),
    IndexDefinition(
        table_name="agreements",
        column_names=["status"],
        index_name="idx_agreements_status"
    )
]

def get_db_connection():
    """
    Create a database connection.

    Returns:
        SQLAlchemy session
    """
    engine = create_engine(settings.get_database_url)
    Session = sessionmaker(bind=engine)
    return Session()

def analyze_database(db) -> Dict[str, Any]:
    """
    Analyze the database and return optimization recommendations.

    Args:
        db: SQLAlchemy session

    Returns:
        Dictionary with analysis results
    """
    results = {
        "existing_indexes": [],
        "missing_indexes": [],
        "unused_indexes": [],
        "table_statistics": {},
        "recommendations": []
    }

    # Get existing indexes
    results["existing_indexes"] = get_existing_indexes(db)

    # Get unused indexes
    results["unused_indexes"] = get_unused_indexes(db)

    # Check for missing recommended indexes
    existing_index_names = [idx["index_name"] for idx in results["existing_indexes"]]
    for recommended_index in RECOMMENDED_INDEXES:
        if recommended_index.index_name not in existing_index_names:
            results["missing_indexes"].append({
                "index_name": recommended_index.index_name,
                "table_name": recommended_index.table_name,
                "columns": recommended_index.column_names,
                "index_type": recommended_index.index_type,
                "is_unique": recommended_index.unique,
                "create_statement": recommended_index.get_create_statement()
            })

    # Get table statistics
    inspector = inspect(db.bind)
    for table_name in inspector.get_table_names():
        if table_name.startswith(('pg_', 'sql_', 'information_schema')):
            continue

        try:
            # Get row count - using parameterized query to prevent SQL injection
            count_result = db.execute(
                text("SELECT COUNT(*) FROM :table_name").bindparams(table_name=table_name)
            ).scalar()

            # Get table size - using parameterized query to prevent SQL injection
            size_result = db.execute(
                text("SELECT pg_size_pretty(pg_total_relation_size(:table_name))").bindparams(table_name=table_name)
            ).scalar()

            results["table_statistics"][table_name] = {
                "row_count": count_result,
                "table_size": size_result
            }
        except Exception as e:
            logger.warning(f"Error getting statistics for table {table_name}: {e}")

    # Generate recommendations
    if results["missing_indexes"]:
        results["recommendations"].append({
            "type": "create_indexes",
            "description": "Create missing recommended indexes",
            "indexes": results["missing_indexes"]
        })

    if results["unused_indexes"]:
        results["recommendations"].append({
            "type": "drop_indexes",
            "description": "Consider dropping unused indexes",
            "indexes": results["unused_indexes"]
        })

    # Check for tables that need VACUUM
    vacuum_results = db.execute(text("""
        SELECT relname, n_dead_tup, n_live_tup
        FROM pg_stat_user_tables
        WHERE n_dead_tup > 0
        ORDER BY n_dead_tup DESC
    """)).fetchall()

    if vacuum_results:
        results["recommendations"].append({
            "type": "vacuum",
            "description": "Run VACUUM on tables with dead tuples",
            "tables": [{"table_name": row[0], "dead_tuples": row[1], "live_tuples": row[2]} for row in vacuum_results]
        })

    return results

def apply_optimizations(db, analysis_results: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Apply database optimizations based on analysis results.

    Args:
        db: SQLAlchemy session
        analysis_results: Optional analysis results (will be generated if None)

    Returns:
        Dictionary with optimization results
    """
    if analysis_results is None:
        analysis_results = analyze_database(db)

    results = {
        "created_indexes": [],
        "dropped_indexes": [],
        "vacuumed_tables": [],
        "analyzed_tables": []
    }

    # Create missing indexes
    for index_info in analysis_results.get("missing_indexes", []):
        index_def = IndexDefinition(
            table_name=index_info["table_name"],
            column_names=index_info["columns"],
            index_name=index_info["index_name"],
            index_type=index_info.get("index_type", IndexType.BTREE),
            unique=index_info.get("is_unique", False)
        )

        if create_index(db, index_def):
            results["created_indexes"].append(index_info["index_name"])

    # VACUUM tables with dead tuples
    for rec in analysis_results.get("recommendations", []):
        if rec["type"] == "vacuum":
            for table_info in rec.get("tables", []):
                table_name = table_info["table_name"]
                try:
                    # Using parameterized query to prevent SQL injection
                    db.execute(text("VACUUM ANALYZE :table_name").bindparams(table_name=table_name))
                    results["vacuumed_tables"].append(table_name)
                    results["analyzed_tables"].append(table_name)
                    logger.info(f"Vacuumed and analyzed table: {table_name}")
                except Exception as e:
                    logger.error(f"Error vacuuming table {table_name}: {str(e)}")

    # ANALYZE all tables to update statistics
    inspector = inspect(db.bind)
    for table_name in inspector.get_table_names():
        if table_name.startswith(('pg_', 'sql_', 'information_schema')):
            continue

        if table_name not in results["analyzed_tables"]:
            try:
                # Using parameterized query to prevent SQL injection
                db.execute(text("ANALYZE :table_name").bindparams(table_name=table_name))
                results["analyzed_tables"].append(table_name)
                logger.info(f"Analyzed table: {table_name}")
            except Exception as e:
                logger.error(f"Error analyzing table {table_name}: {str(e)}")

    return results


def generate_report(analysis_results: Dict[str, Any], optimization_results: Optional[Dict[str, Any]] = None) -> str:
    """
    Generate a human-readable report of database analysis and optimizations.

    Args:
        analysis_results: Database analysis results
        optimization_results: Optional optimization results

    Returns:
        Report as a string
    """
    report = []
    report.append("=" * 80)
    report.append("RentUp Database Optimization Report")
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("=" * 80)
    report.append("")

    # Table statistics
    report.append("Table Statistics:")
    report.append("-" * 80)
    for table_name, stats in analysis_results.get("table_statistics", {}).items():
        report.append(f"Table: {table_name}")
        report.append(f"  Rows: {stats.get('row_count', 'N/A')}")
        report.append(f"  Size: {stats.get('table_size', 'N/A')}")
        report.append("")

    # Existing indexes
    report.append("Existing Indexes:")
    report.append("-" * 80)
    for idx in analysis_results.get("existing_indexes", []):
        report.append(f"Index: {idx['index_name']}")
        report.append(f"  Table: {idx['table_name']}")
        report.append(f"  Columns: {', '.join(idx['columns'])}")
        report.append(f"  Type: {idx['index_type']}")
        report.append(f"  Unique: {idx['is_unique']}")
        report.append("")

    # Missing recommended indexes
    report.append("Missing Recommended Indexes:")
    report.append("-" * 80)
    if analysis_results.get("missing_indexes"):
        for idx in analysis_results.get("missing_indexes", []):
            report.append(f"Index: {idx['index_name']}")
            report.append(f"  Table: {idx['table_name']}")
            report.append(f"  Columns: {', '.join(idx['columns'])}")
            report.append(f"  Type: {idx.get('index_type', 'btree')}")
            report.append(f"  Unique: {idx.get('is_unique', False)}")
            report.append(f"  Create Statement: {idx['create_statement']}")
            report.append("")
    else:
        report.append("No missing recommended indexes.")
        report.append("")

    # Unused indexes
    report.append("Unused Indexes:")
    report.append("-" * 80)
    if analysis_results.get("unused_indexes"):
        for idx in analysis_results.get("unused_indexes", []):
            report.append(f"Index: {idx['index_name']}")
            report.append(f"  Table: {idx['table_name']}")
            report.append(f"  Size: {idx['index_size']}")
            report.append("")
    else:
        report.append("No unused indexes found.")
        report.append("")

    # Recommendations
    report.append("Recommendations:")
    report.append("-" * 80)
    if analysis_results.get("recommendations"):
        for rec in analysis_results.get("recommendations", []):
            report.append(f"Type: {rec['type']}")
            report.append(f"Description: {rec['description']}")
            report.append("")
    else:
        report.append("No recommendations.")
        report.append("")

    # Optimization results
    if optimization_results:
        report.append("Applied Optimizations:")
        report.append("-" * 80)

        report.append("Created Indexes:")
        if optimization_results.get("created_indexes"):
            for idx_name in optimization_results.get("created_indexes", []):
                report.append(f"  - {idx_name}")
        else:
            report.append("  None")
        report.append("")

        report.append("Vacuumed Tables:")
        if optimization_results.get("vacuumed_tables"):
            for table_name in optimization_results.get("vacuumed_tables", []):
                report.append(f"  - {table_name}")
        else:
            report.append("  None")
        report.append("")

        report.append("Analyzed Tables:")
        if optimization_results.get("analyzed_tables"):
            for table_name in optimization_results.get("analyzed_tables", []):
                report.append(f"  - {table_name}")
        else:
            report.append("  None")
        report.append("")

    return "\n".join(report)


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description="Database optimization script for RentUp")
    parser.add_argument("--analyze", action="store_true", help="Analyze database and suggest optimizations")
    parser.add_argument("--apply", action="store_true", help="Apply recommended optimizations")
    parser.add_argument("--report", action="store_true", help="Generate optimization report")
    parser.add_argument("--all", action="store_true", help="Perform all optimization tasks")
    parser.add_argument("--output", type=str, help="Output file for report (default: stdout)")

    args = parser.parse_args()

    # Default to analyze if no action specified
    if not (args.analyze or args.apply or args.report or args.all):
        args.analyze = True

    # Connect to database
    db = get_db_connection()

    try:
        # Analyze database
        if args.analyze or args.all:
            logger.info("Analyzing database...")
            analysis_results = analyze_database(db)
            logger.info("Database analysis complete.")

            if args.output:
                with open(args.output, "w") as f:
                    json.dump(analysis_results, f, indent=2)
                logger.info(f"Analysis results saved to {args.output}")
            else:
                print(json.dumps(analysis_results, indent=2))

        # Apply optimizations
        optimization_results = None
        if args.apply or args.all:
            logger.info("Applying optimizations...")
            analysis_results = analyze_database(db)
            optimization_results = apply_optimizations(db, analysis_results)
            logger.info("Optimizations applied.")

            if args.output and not args.report:
                with open(args.output, "w") as f:
                    json.dump(optimization_results, f, indent=2)
                logger.info(f"Optimization results saved to {args.output}")
            elif not args.report:
                print(json.dumps(optimization_results, indent=2))

        # Generate report
        if args.report or args.all:
            logger.info("Generating report...")
            if not 'analysis_results' in locals():
                analysis_results = analyze_database(db)

            report = generate_report(analysis_results, optimization_results)

            if args.output:
                with open(args.output, "w") as f:
                    f.write(report)
                logger.info(f"Report saved to {args.output}")
            else:
                print(report)

            logger.info("Report generation complete.")

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        sys.exit(1)
    finally:
        db.close()


if __name__ == "__main__":
    main()