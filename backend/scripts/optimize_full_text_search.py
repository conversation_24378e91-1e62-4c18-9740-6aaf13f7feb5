#!/usr/bin/env python3
"""
Script to optimize full-text search operations for RentUp.

This script:
1. Creates and optimizes PostgreSQL full-text search indexes
2. Implements efficient search functions
3. Benchmarks search performance
4. Provides recommendations for further optimization
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import json
from typing import Dict, List, Any, Optional, Tuple

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

from sqlalchemy import text, Column, String, Integer, Float, Boolean, DateTime, ForeignKey, Table, MetaData
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import TSVECTOR
from sqlalchemy.schema import DDL
from sqlalchemy.ext.declarative import declarative_base

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database import engine, session_scope
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Optimize full-text search operations for RentUp')
    parser.add_argument('--create-indexes', action='store_true', help='Create full-text search indexes')
    parser.add_argument('--create-triggers', action='store_true', help='Create triggers to update search vectors')
    parser.add_argument('--benchmark', action='store_true', help='Benchmark search performance')
    parser.add_argument('--all', action='store_true', help='Perform all optimization steps')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def get_tables_for_search() -> List[Dict[str, Any]]:
    """
    Get tables that should have full-text search capabilities.

    Returns:
        List of table configurations
    """
    return [
        {
            "table_name": "items",
            "search_columns": ["name", "description", "category", "subcategory", "location"],
            "weights": {
                "name": "A",  # Highest weight
                "category": "B",
                "subcategory": "B",
                "description": "C",
                "location": "D"  # Lowest weight
            },
            "language": "english"
        },
        {
            "table_name": "users",
            "search_columns": ["full_name", "email", "bio", "location"],
            "weights": {
                "full_name": "A",
                "email": "B",
                "bio": "C",
                "location": "D"
            },
            "language": "english"
        },
        {
            "table_name": "agreements",
            "search_columns": ["title", "description", "terms"],
            "weights": {
                "title": "A",
                "description": "B",
                "terms": "C"
            },
            "language": "english"
        }
    ]

def create_search_vector_column(db, table_config: Dict[str, Any]) -> bool:
    """
    Create a search vector column for a table.

    Args:
        db: Database session
        table_config: Table configuration

    Returns:
        True if successful, False otherwise
    """
    table_name = table_config["table_name"]

    try:
        # Check if search vector column already exists
        check_query = text("""
            SELECT column_name
            FROM information_schema.columns
            WHERE table_name = :table_name AND column_name = 'search_vector'
        """)
        result = db.execute(check_query, {"table_name": table_name})

        if result.scalar():
            logger.info(f"Search vector column already exists for table {table_name}")
            return True

        # Create search vector column
        logger.info(f"Creating search vector column for table {table_name}")

        create_column_query = text(f"""
            ALTER TABLE {table_name} ADD COLUMN search_vector TSVECTOR;
        """)
        db.execute(create_column_query)

        # Create GIN index on search vector
        create_index_query = text(f"""
            CREATE INDEX idx_{table_name}_search_vector ON {table_name} USING GIN(search_vector);
        """)
        db.execute(create_index_query)

        db.commit()
        logger.info(f"Created search vector column and index for table {table_name}")
        return True

    except Exception as e:
        db.rollback()
        logger.error(f"Error creating search vector column for table {table_name}: {str(e)}")
        return False

def create_search_vector_update_trigger(db, table_config: Dict[str, Any]) -> bool:
    """
    Create a trigger to update the search vector column when data changes.

    Args:
        db: Database session
        table_config: Table configuration

    Returns:
        True if successful, False otherwise
    """
    table_name = table_config["table_name"]
    search_columns = table_config["search_columns"]
    weights = table_config["weights"]
    language = table_config["language"]

    try:
        # Check if trigger already exists
        check_query = text("""
            SELECT 1
            FROM pg_trigger
            WHERE tgname = :trigger_name
        """)
        result = db.execute(check_query, {"trigger_name": f"{table_name}_search_vector_update"})

        if result.scalar():
            logger.info(f"Search vector update trigger already exists for table {table_name}")
            return True

        # Create weighted search vector expression
        weighted_columns = []
        for column in search_columns:
            weight = weights.get(column, "D")  # Default to lowest weight
            weighted_columns.append(f"setweight(to_tsvector('{language}', coalesce(NEW.{column}, '')), '{weight}')")

        vector_expression = " || ".join(weighted_columns)

        # Create function for trigger
        function_query = text(f"""
            CREATE OR REPLACE FUNCTION {table_name}_search_vector_update_func() RETURNS trigger AS $$
            BEGIN
                NEW.search_vector = {vector_expression};
                RETURN NEW;
            END
            $$ LANGUAGE plpgsql;
        """)
        db.execute(function_query)

        # Create trigger
        trigger_query = text(f"""
            CREATE TRIGGER {table_name}_search_vector_update
            BEFORE INSERT OR UPDATE ON {table_name}
            FOR EACH ROW EXECUTE FUNCTION {table_name}_search_vector_update_func();
        """)
        db.execute(trigger_query)

        db.commit()
        logger.info(f"Created search vector update trigger for table {table_name}")
        return True

    except Exception as e:
        db.rollback()
        logger.error(f"Error creating search vector update trigger for table {table_name}: {str(e)}")
        return False

def update_existing_rows(db, table_config: Dict[str, Any]) -> bool:
    """
    Update search vector for existing rows.

    Args:
        db: Database session
        table_config: Table configuration

    Returns:
        True if successful, False otherwise
    """
    table_name = table_config["table_name"]
    search_columns = table_config["search_columns"]
    weights = table_config["weights"]
    language = table_config["language"]

    try:
        # Create weighted search vector expression
        weighted_columns = []
        for column in search_columns:
            weight = weights.get(column, "D")  # Default to lowest weight
            weighted_columns.append(f"setweight(to_tsvector('{language}', coalesce({column}, '')), '{weight}')")

        vector_expression = " || ".join(weighted_columns)

        # Update existing rows
        update_query = text(f"""
            UPDATE {table_name} SET search_vector = {vector_expression};
        """)
        db.execute(update_query)

        db.commit()
        logger.info(f"Updated search vector for existing rows in table {table_name}")
        return True

    except Exception as e:
        db.rollback()
        logger.error(f"Error updating search vector for existing rows in table {table_name}: {str(e)}")
        return False

def benchmark_search_performance(db, table_config: Dict[str, Any], query: str) -> Dict[str, Any]:
    """
    Benchmark search performance.

    Args:
        db: Database session
        table_config: Table configuration
        query: Search query

    Returns:
        Dictionary of benchmark results
    """
    table_name = table_config["table_name"]
    language = table_config["language"]

    results = {
        "table_name": table_name,
        "query": query,
        "regular_search": {},
        "tsvector_search": {},
        "improvement": {}
    }

    try:
        # Benchmark regular LIKE search
        like_conditions = []
        for column in table_config["search_columns"]:
            like_conditions.append(f"{column} ILIKE :query")

        like_query = text(f"""
            SELECT COUNT(*) FROM {table_name}
            WHERE {" OR ".join(like_conditions)}
        """)

        start_time = time.time()
        like_result = db.execute(like_query, {"query": f"%{query}%"})
        like_count = like_result.scalar()
        like_time = time.time() - start_time

        results["regular_search"] = {
            "count": like_count,
            "time": like_time
        }

        # Benchmark tsvector search
        tsvector_query = text(f"""
            SELECT COUNT(*) FROM {table_name}
            WHERE search_vector @@ plainto_tsquery('{language}', :query)
        """)

        start_time = time.time()
        tsvector_result = db.execute(tsvector_query, {"query": query})
        tsvector_count = tsvector_result.scalar()
        tsvector_time = time.time() - start_time

        results["tsvector_search"] = {
            "count": tsvector_count,
            "time": tsvector_time
        }

        # Calculate improvement
        if like_time > 0:
            improvement = (like_time - tsvector_time) / like_time * 100
            results["improvement"] = {
                "time_reduction": improvement,
                "speedup_factor": like_time / tsvector_time if tsvector_time > 0 else float("inf")
            }

        return results

    except Exception as e:
        logger.error(f"Error benchmarking search performance for table {table_name}: {str(e)}")
        return results

def main():
    """Main function."""
    args = parse_args()

    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Determine what to do
    create_indexes = args.create_indexes or args.all
    create_triggers = args.create_triggers or args.all
    benchmark = args.benchmark or args.all

    # If no actions specified, show help
    if not (create_indexes or create_triggers or benchmark):
        logger.info("No actions specified. Use --create-indexes, --create-triggers, --benchmark, or --all")
        return 1

    # Get tables for search
    tables = get_tables_for_search()

    with session_scope() as db:
        # Create search vector columns and indexes
        if create_indexes:
            logger.info("Creating search vector columns and indexes...")
            for table_config in tables:
                create_search_vector_column(db, table_config)

        # Create search vector update triggers
        if create_triggers:
            logger.info("Creating search vector update triggers...")
            for table_config in tables:
                create_search_vector_update_trigger(db, table_config)
                update_existing_rows(db, table_config)

        # Benchmark search performance
        if benchmark:
            logger.info("Benchmarking search performance...")
            benchmark_results = []

            # Sample queries for benchmarking
            sample_queries = [
                "modern furniture",
                "vintage leather sofa",
                "apartment new york",
                "camera rental",
                "power tools"
            ]

            for table_config in tables:
                for query in sample_queries:
                    result = benchmark_search_performance(db, table_config, query)
                    benchmark_results.append(result)

                    # Log results
                    logger.info(f"Benchmark for {table_config['table_name']}, query: '{query}'")
                    logger.info(f"  Regular search: {result['regular_search']['count']} results in {result['regular_search']['time']:.6f}s")
                    logger.info(f"  TSVector search: {result['tsvector_search']['count']} results in {result['tsvector_search']['time']:.6f}s")

                    if "improvement" in result:
                        logger.info(f"  Improvement: {result['improvement']['time_reduction']:.2f}% time reduction")
                        logger.info(f"  Speedup factor: {result['improvement']['speedup_factor']:.2f}x")

            # Save benchmark results to file
            output_file = "search_benchmark_results.json"
            with open(output_file, "w") as f:
                json.dump(benchmark_results, f, indent=2)

            logger.info(f"Benchmark results saved to {output_file}")

    logger.info("Full-text search optimization completed successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
