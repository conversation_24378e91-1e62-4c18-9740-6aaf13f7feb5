#!/usr/bin/env python3
"""
<PERSON>ript to perform a comprehensive security audit of the RentUp backend.

This script:
1. Checks database security configuration
2. Audits API endpoints for security vulnerabilities
3. Checks for secure coding practices
4. Verifies environment variable security
5. Checks for dependency vulnerabilities
6. Generates a security audit report
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import json
import subprocess
import re
import datetime
from typing import Dict, List, Any, Optional, Tuple, Set

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import psycopg2
from psycopg2 import sql
import requests
from sqlalchemy import text, inspect

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    from app.core.security import get_password_hash, verify_password
    from app.api.deps import get_current_user
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Perform security audit of RentUp backend')
    parser.add_argument('--database', action='store_true', help='Audit database security')
    parser.add_argument('--api', action='store_true', help='Audit API security')
    parser.add_argument('--code', action='store_true', help='Audit code security')
    parser.add_argument('--dependencies', action='store_true', help='Audit dependencies for vulnerabilities')
    parser.add_argument('--env', action='store_true', help='Audit environment variables')
    parser.add_argument('--all', action='store_true', help='Perform all audits')
    parser.add_argument('--output', default='security_audit_report.json', help='Output file for audit report')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def audit_database_security():
    """Audit database security configuration."""
    logger.info("Auditing database security...")
    
    results = {
        "status": "success",
        "issues": [],
        "recommendations": []
    }
    
    try:
        # Get database connection parameters
        if NEW_MODULES:
            db_host = db_config.POSTGRES_SERVER
            db_port = db_config.POSTGRES_PORT
            db_user = db_config.POSTGRES_USER
            db_password = db_config.POSTGRES_PASSWORD
            db_name = db_config.POSTGRES_DB
        else:
            db_url = settings.DATABASE_URL
            # Parse DATABASE_URL
            match = re.match(r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
            if match:
                db_user, db_password, db_host, db_port, db_name = match.groups()
            else:
                results["status"] = "error"
                results["issues"].append("Could not parse DATABASE_URL")
                return results
        
        # Connect to database
        conn = psycopg2.connect(
            host=db_host,
            port=db_port,
            user=db_user,
            password=db_password,
            dbname=db_name
        )
        
        # Check if SSL is enabled
        with conn.cursor() as cur:
            cur.execute("SHOW ssl")
            ssl_enabled = cur.fetchone()[0] == "on"
            
            if not ssl_enabled:
                results["issues"].append("SSL is not enabled for database connections")
                results["recommendations"].append("Enable SSL for database connections")
        
        # Check if password encryption is enabled
        with conn.cursor() as cur:
            cur.execute("SHOW password_encryption")
            password_encryption = cur.fetchone()[0]
            
            if password_encryption != "scram-sha-256":
                results["issues"].append(f"Password encryption is set to {password_encryption}, which is not the strongest option")
                results["recommendations"].append("Set password_encryption to scram-sha-256")
        
        # Check for public schema permissions
        with conn.cursor() as cur:
            cur.execute("SELECT nspname, nspacl FROM pg_namespace WHERE nspname = 'public'")
            public_schema = cur.fetchone()
            
            if public_schema and public_schema[1] is not None and 'everyone=UC' in public_schema[1]:
                results["issues"].append("Public schema has excessive permissions")
                results["recommendations"].append("Revoke excessive permissions from public schema: REVOKE ALL ON SCHEMA public FROM PUBLIC")
        
        # Check for weak user passwords
        with conn.cursor() as cur:
            cur.execute("SELECT usename, passwd FROM pg_shadow")
            users = cur.fetchall()
            
            for user, passwd in users:
                if passwd is None or passwd == '':
                    results["issues"].append(f"User {user} has no password set")
                    results["recommendations"].append(f"Set a strong password for user {user}")
                elif not passwd.startswith('SCRAM-SHA-256'):
                    results["issues"].append(f"User {user} is not using SCRAM-SHA-256 password encryption")
                    results["recommendations"].append(f"Update password for user {user} with SCRAM-SHA-256 encryption")
        
        # Check for superuser accounts
        with conn.cursor() as cur:
            cur.execute("SELECT usename FROM pg_user WHERE usesuper = true")
            superusers = [row[0] for row in cur.fetchall()]
            
            if len(superusers) > 1:
                results["issues"].append(f"Multiple superuser accounts found: {', '.join(superusers)}")
                results["recommendations"].append("Limit the number of superuser accounts to only what is necessary")
        
        # Check for row-level security
        with conn.cursor() as cur:
            cur.execute("""
                SELECT c.relname
                FROM pg_class c
                JOIN pg_namespace n ON c.relnamespace = n.oid
                WHERE c.relkind = 'r'
                AND n.nspname = 'public'
                AND NOT EXISTS (
                    SELECT 1 FROM pg_policy p WHERE p.polrelid = c.oid
                )
            """)
            tables_without_rls = [row[0] for row in cur.fetchall()]
            
            if tables_without_rls:
                results["issues"].append(f"Tables without row-level security: {', '.join(tables_without_rls)}")
                results["recommendations"].append("Consider implementing row-level security for sensitive tables")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"Error auditing database security: {e}")
        results["status"] = "error"
        results["issues"].append(f"Error auditing database security: {str(e)}")
    
    return results

def audit_api_security():
    """Audit API security configuration."""
    logger.info("Auditing API security...")
    
    results = {
        "status": "success",
        "issues": [],
        "recommendations": []
    }
    
    try:
        # Check for authentication on endpoints
        api_dir = Path(__file__).parent.parent / "app" / "api" / "v1"
        
        if not api_dir.exists():
            results["status"] = "error"
            results["issues"].append(f"API directory not found: {api_dir}")
            return results
        
        # Scan API endpoint files
        for api_file in api_dir.glob("*.py"):
            with open(api_file, "r") as f:
                content = f.read()
                
                # Check for unauthenticated endpoints
                if "APIRouter" in content and "dependencies" not in content:
                    results["issues"].append(f"API file {api_file.name} may have unauthenticated endpoints")
                    results["recommendations"].append(f"Add authentication dependencies to {api_file.name}")
                
                # Check for missing input validation
                if "APIRouter" in content and "Depends" in content and "validate" not in content.lower():
                    results["issues"].append(f"API file {api_file.name} may be missing input validation")
                    results["recommendations"].append(f"Add input validation to {api_file.name}")
                
                # Check for CORS configuration
                if "APIRouter" in content and "CORS" not in content:
                    results["issues"].append(f"API file {api_file.name} may be missing CORS configuration")
                    results["recommendations"].append(f"Add CORS configuration to {api_file.name}")
        
        # Check main.py for security headers
        main_file = Path(__file__).parent.parent / "app" / "main.py"
        
        if main_file.exists():
            with open(main_file, "r") as f:
                content = f.read()
                
                # Check for security headers
                if "X-Content-Type-Options" not in content:
                    results["issues"].append("Missing X-Content-Type-Options security header")
                    results["recommendations"].append("Add X-Content-Type-Options: nosniff header")
                
                if "X-Frame-Options" not in content:
                    results["issues"].append("Missing X-Frame-Options security header")
                    results["recommendations"].append("Add X-Frame-Options: DENY header")
                
                if "Content-Security-Policy" not in content:
                    results["issues"].append("Missing Content-Security-Policy header")
                    results["recommendations"].append("Add Content-Security-Policy header")
                
                if "Strict-Transport-Security" not in content:
                    results["issues"].append("Missing HTTP Strict Transport Security header")
                    results["recommendations"].append("Add Strict-Transport-Security header")
        
    except Exception as e:
        logger.error(f"Error auditing API security: {e}")
        results["status"] = "error"
        results["issues"].append(f"Error auditing API security: {str(e)}")
    
    return results

def audit_code_security():
    """Audit code for security vulnerabilities."""
    logger.info("Auditing code security...")
    
    results = {
        "status": "success",
        "issues": [],
        "recommendations": []
    }
    
    try:
        # Run bandit for Python security issues
        try:
            result = subprocess.run(
                ["bandit", "-r", "backend/app", "-f", "json"],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                bandit_results = json.loads(result.stdout)
                
                for issue in bandit_results.get("results", []):
                    results["issues"].append(f"Security issue in {issue['filename']}: {issue['issue_text']}")
                    results["recommendations"].append(f"Fix security issue in {issue['filename']}: {issue['issue_text']}")
        except Exception as e:
            logger.warning(f"Could not run bandit: {e}")
            results["issues"].append("Could not run bandit for Python security scanning")
            results["recommendations"].append("Install bandit: pip install bandit")
        
        # Check for hardcoded secrets
        app_dir = Path(__file__).parent.parent / "app"
        
        if not app_dir.exists():
            results["status"] = "error"
            results["issues"].append(f"App directory not found: {app_dir}")
            return results
        
        # Patterns for potential hardcoded secrets
        secret_patterns = [
            r'password\s*=\s*["\'](?!.*\$\{).*["\']',
            r'secret\s*=\s*["\'](?!.*\$\{).*["\']',
            r'token\s*=\s*["\'](?!.*\$\{).*["\']',
            r'key\s*=\s*["\'](?!.*\$\{).*["\']',
            r'api_key\s*=\s*["\'](?!.*\$\{).*["\']'
        ]
        
        # Scan Python files for hardcoded secrets
        for py_file in app_dir.glob("**/*.py"):
            with open(py_file, "r") as f:
                content = f.read()
                
                for pattern in secret_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    
                    if matches:
                        results["issues"].append(f"Potential hardcoded secret in {py_file.relative_to(app_dir.parent)}")
                        results["recommendations"].append(f"Replace hardcoded secrets in {py_file.relative_to(app_dir.parent)} with environment variables")
        
        # Check for SQL injection vulnerabilities
        for py_file in app_dir.glob("**/*.py"):
            with open(py_file, "r") as f:
                content = f.read()
                
                # Check for raw SQL queries without parameterization
                if "execute(" in content and "%" in content and "text(" not in content:
                    results["issues"].append(f"Potential SQL injection vulnerability in {py_file.relative_to(app_dir.parent)}")
                    results["recommendations"].append(f"Use parameterized queries in {py_file.relative_to(app_dir.parent)}")
        
    except Exception as e:
        logger.error(f"Error auditing code security: {e}")
        results["status"] = "error"
        results["issues"].append(f"Error auditing code security: {str(e)}")
    
    return results

def audit_dependencies():
    """Audit dependencies for vulnerabilities."""
    logger.info("Auditing dependencies for vulnerabilities...")
    
    results = {
        "status": "success",
        "issues": [],
        "recommendations": []
    }
    
    try:
        # Run safety check for Python dependencies
        try:
            result = subprocess.run(
                ["safety", "check", "--json"],
                capture_output=True,
                text=True
            )
            
            if result.returncode != 0:
                safety_results = json.loads(result.stdout)
                
                for issue in safety_results:
                    results["issues"].append(f"Vulnerable dependency: {issue['package_name']} {issue['installed_version']}")
                    results["recommendations"].append(f"Update {issue['package_name']} to a secure version")
        except Exception as e:
            logger.warning(f"Could not run safety: {e}")
            results["issues"].append("Could not run safety for dependency vulnerability scanning")
            results["recommendations"].append("Install safety: pip install safety")
        
        # Check for outdated dependencies
        try:
            result = subprocess.run(
                ["pip", "list", "--outdated", "--format=json"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                outdated = json.loads(result.stdout)
                
                for package in outdated:
                    results["issues"].append(f"Outdated dependency: {package['name']} {package['version']} (latest: {package['latest_version']})")
                    results["recommendations"].append(f"Update {package['name']} to {package['latest_version']}")
        except Exception as e:
            logger.warning(f"Could not check for outdated dependencies: {e}")
        
    except Exception as e:
        logger.error(f"Error auditing dependencies: {e}")
        results["status"] = "error"
        results["issues"].append(f"Error auditing dependencies: {str(e)}")
    
    return results

def audit_environment_variables():
    """Audit environment variables for security issues."""
    logger.info("Auditing environment variables...")
    
    results = {
        "status": "success",
        "issues": [],
        "recommendations": []
    }
    
    try:
        # Check for sensitive environment variables
        env_vars = os.environ
        
        sensitive_vars = [
            "PASSWORD",
            "SECRET",
            "TOKEN",
            "KEY",
            "API_KEY"
        ]
        
        for var in env_vars:
            for sensitive in sensitive_vars:
                if sensitive in var.upper() and env_vars[var]:
                    # Mask the value for security
                    value = env_vars[var]
                    masked_value = value[:3] + "*" * (len(value) - 6) + value[-3:] if len(value) > 6 else "***"
                    
                    results["issues"].append(f"Sensitive environment variable: {var}={masked_value}")
                    results["recommendations"].append(f"Store {var} securely and do not expose it")
        
        # Check for missing required environment variables
        required_vars = [
            "POSTGRES_SERVER",
            "POSTGRES_USER",
            "POSTGRES_PASSWORD",
            "POSTGRES_DB",
            "SECRET_KEY"
        ]
        
        for var in required_vars:
            if var not in env_vars or not env_vars[var]:
                results["issues"].append(f"Missing required environment variable: {var}")
                results["recommendations"].append(f"Set the {var} environment variable")
        
        # Check for environment variable files
        env_files = [
            ".env",
            ".env.local",
            ".env.development",
            ".env.production"
        ]
        
        for env_file in env_files:
            if os.path.exists(env_file):
                # Check file permissions
                file_stat = os.stat(env_file)
                file_mode = file_stat.st_mode & 0o777
                
                if file_mode > 0o600:
                    results["issues"].append(f"Environment file {env_file} has insecure permissions: {oct(file_mode)[2:]}")
                    results["recommendations"].append(f"Set secure permissions for {env_file}: chmod 600 {env_file}")
                
                # Check for sensitive variables in the file
                with open(env_file, "r") as f:
                    content = f.read()
                    
                    for sensitive in sensitive_vars:
                        pattern = rf'{sensitive}.*=.*'
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        
                        if matches:
                            results["issues"].append(f"Sensitive variables found in {env_file}")
                            results["recommendations"].append(f"Store sensitive variables securely, not in {env_file}")
        
    except Exception as e:
        logger.error(f"Error auditing environment variables: {e}")
        results["status"] = "error"
        results["issues"].append(f"Error auditing environment variables: {str(e)}")
    
    return results

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Determine which audits to run
    run_database = args.database or args.all
    run_api = args.api or args.all
    run_code = args.code or args.all
    run_dependencies = args.dependencies or args.all
    run_env = args.env or args.all
    
    # If no audits specified, show help
    if not (run_database or run_api or run_code or run_dependencies or run_env):
        logger.info("No audits specified. Use --all or specify individual audits.")
        return 1
    
    # Initialize audit results
    audit_results = {
        "timestamp": datetime.datetime.now().isoformat(),
        "summary": {
            "total_issues": 0,
            "high_severity": 0,
            "medium_severity": 0,
            "low_severity": 0
        },
        "results": {}
    }
    
    # Run audits
    if run_database:
        audit_results["results"]["database"] = audit_database_security()
        audit_results["summary"]["total_issues"] += len(audit_results["results"]["database"]["issues"])
    
    if run_api:
        audit_results["results"]["api"] = audit_api_security()
        audit_results["summary"]["total_issues"] += len(audit_results["results"]["api"]["issues"])
    
    if run_code:
        audit_results["results"]["code"] = audit_code_security()
        audit_results["summary"]["total_issues"] += len(audit_results["results"]["code"]["issues"])
    
    if run_dependencies:
        audit_results["results"]["dependencies"] = audit_dependencies()
        audit_results["summary"]["total_issues"] += len(audit_results["results"]["dependencies"]["issues"])
    
    if run_env:
        audit_results["results"]["environment"] = audit_environment_variables()
        audit_results["summary"]["total_issues"] += len(audit_results["results"]["environment"]["issues"])
    
    # Save audit results to file
    with open(args.output, "w") as f:
        json.dump(audit_results, f, indent=2)
    
    logger.info(f"Security audit completed. Found {audit_results['summary']['total_issues']} issues.")
    logger.info(f"Audit report saved to {args.output}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
