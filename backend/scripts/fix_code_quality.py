#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to automatically fix common code quality issues in the codebase.

This script:
1. Removes unused imports
2. Fixes whitespace issues
3. Fixes line length issues
4. Fixes indentation issues
"""

import os
import re
import sys
from pathlib import Path

# Add parent directory to path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Files to exclude from processing
EXCLUDE_FILES = [
    "__pycache__",
    ".git",
    ".vscode",
    "venv",
    "env",
    "node_modules",
]

def should_process_file(file_path):
    """
    Check if a file should be processed.
    
    Args:
        file_path: Path to the file
        
    Returns:
        True if the file should be processed, False otherwise
    """
    # Check if file is in excluded directories
    for exclude in EXCLUDE_FILES:
        if exclude in str(file_path):
            return False
    
    # Only process Python files
    return file_path.suffix == ".py"

def remove_unused_imports(content):
    """
    Remove unused imports from a Python file.
    
    Args:
        content: File content
        
    Returns:
        Updated file content
    """
    # Find all import statements
    import_pattern = r"^(from\s+[\w.]+\s+import\s+(?:\([\s\w,]+\)|[\s\w,]+)|import\s+(?:\([\s\w,]+\)|[\s\w,]+))"
    imports = re.findall(import_pattern, content, re.MULTILINE)
    
    # Find all used symbols
    used_symbols = set()
    for line in content.split("\n"):
        # Skip import lines
        if re.match(import_pattern, line):
            continue
        
        # Find all words in the line
        words = re.findall(r"\b\w+\b", line)
        used_symbols.update(words)
    
    # Process each import statement
    for import_stmt in imports:
        if import_stmt.startswith("from"):
            # Handle "from module import symbol" statements
            match = re.match(r"from\s+([\w.]+)\s+import\s+((?:\([\s\w,]+\)|[\s\w,]+))", import_stmt)
            if match:
                module = match.group(1)
                symbols_str = match.group(2).strip("() ")
                symbols = [s.strip() for s in symbols_str.split(",")]
                
                # Check if each symbol is used
                unused_symbols = []
                for symbol in symbols:
                    if symbol and symbol not in used_symbols:
                        unused_symbols.append(symbol)
                
                # Remove unused symbols
                if unused_symbols and len(unused_symbols) < len(symbols):
                    for symbol in unused_symbols:
                        content = re.sub(
                            rf"from\s+{re.escape(module)}\s+import\s+.*{re.escape(symbol)}.*",
                            lambda m: m.group(0).replace(symbol, "").replace(",,", ",").replace(", ,", ","),
                            content
                        )
                
                # Clean up empty imports
                content = re.sub(r"from\s+[\w.]+\s+import\s+,*\s*\n", "", content)
                content = re.sub(r"from\s+[\w.]+\s+import\s+\(\s*\)\s*\n", "", content)
    
    # Clean up whitespace
    content = re.sub(r"\n\n\n+", "\n\n", content)
    
    return content

def fix_whitespace(content):
    """
    Fix whitespace issues in a Python file.
    
    Args:
        content: File content
        
    Returns:
        Updated file content
    """
    # Remove trailing whitespace
    content = re.sub(r"[ \t]+$", "", content, flags=re.MULTILINE)
    
    # Ensure consistent blank lines between functions and classes
    content = re.sub(r"\n{3,}(def|class)", r"\n\n\1", content)
    
    # Remove blank lines with whitespace
    content = re.sub(r"\n[ \t]+\n", r"\n\n", content)
    
    return content

def fix_line_length(content, max_length=127):
    """
    Fix line length issues in a Python file.
    
    Args:
        content: File content
        max_length: Maximum line length
        
    Returns:
        Updated file content
    """
    lines = content.split("\n")
    fixed_lines = []
    
    for line in lines:
        if len(line) <= max_length:
            fixed_lines.append(line)
            continue
        
        # Try to break the line at a sensible point
        if "=" in line:
            # Break at assignment
            parts = line.split("=", 1)
            fixed_lines.append(f"{parts[0].rstrip()}=")
            fixed_lines.append(f"    {parts[1].lstrip()}")
        elif "," in line:
            # Break at comma
            last_comma = line.rfind(",", 0, max_length)
            if last_comma > 0:
                fixed_lines.append(line[:last_comma + 1])
                fixed_lines.append(f"    {line[last_comma + 1:].lstrip()}")
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    return "\n".join(fixed_lines)

def process_file(file_path):
    """
    Process a Python file to fix common code quality issues.
    
    Args:
        file_path: Path to the file
    """
    print(f"Processing {file_path}")
    
    # Read file content
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # Apply fixes
    new_content = content
    new_content = remove_unused_imports(new_content)
    new_content = fix_whitespace(new_content)
    new_content = fix_line_length(new_content)
    
    # Write updated content if changed
    if new_content != content:
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(new_content)
        print(f"  Fixed issues in {file_path}")

def main():
    """
    Main function.
    """
    # Get the app directory
    app_dir = Path(__file__).parent.parent / "app"
    
    # Process all Python files
    for root, dirs, files in os.walk(app_dir):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in EXCLUDE_FILES]
        
        for file in files:
            file_path = Path(root) / file
            if should_process_file(file_path):
                process_file(file_path)

if __name__ == "__main__":
    main()
