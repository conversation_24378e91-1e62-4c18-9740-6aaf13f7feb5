#!/usr/bin/env python3
"""
Scrip<PERSON> to set up and configure PostgreSQL read replicas for RentUp.

This script:
1. Sets up PostgreSQL read replicas
2. Configures replication between primary and replicas
3. Tests the replication setup
4. Updates the application configuration to use read replicas
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import time
import subprocess
import json
from typing import Dict, List, Any, Optional

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import psycopg2
from psycopg2 import sql
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Set up PostgreSQL read replicas for RentUp')
    parser.add_argument('--primary-host', default='localhost', help='Primary database host')
    parser.add_argument('--primary-port', type=int, default=5432, help='Primary database port')
    parser.add_argument('--primary-user', default='rentup', help='Primary database user')
    parser.add_argument('--primary-password', default='rentup_secure_password', help='Primary database password')
    parser.add_argument('--primary-db', default='rentup', help='Primary database name')
    parser.add_argument('--replica-count', type=int, default=2, help='Number of read replicas to set up')
    parser.add_argument('--replica-base-port', type=int, default=5433, help='Base port for read replicas')
    parser.add_argument('--docker', action='store_true', help='Use Docker for replica setup')
    parser.add_argument('--update-config', action='store_true', help='Update application configuration')
    parser.add_argument('--test', action='store_true', help='Test the replication setup')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def connect_to_postgres(host, port, user, password, database='postgres'):
    """Connect to PostgreSQL database."""
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        logger.info(f"Connected to PostgreSQL database at {host}:{port}/{database}")
        return conn
    except psycopg2.Error as e:
        logger.error(f"Error connecting to PostgreSQL database: {e}")
        raise

def setup_primary_for_replication(args):
    """Set up the primary database for replication."""
    logger.info("Setting up primary database for replication...")
    
    # Connect to primary database
    conn = connect_to_postgres(
        args.primary_host,
        args.primary_port,
        args.primary_user,
        args.primary_password,
        args.primary_db
    )
    
    try:
        with conn.cursor() as cur:
            # Check if the primary is already set up for replication
            cur.execute("SHOW wal_level")
            wal_level = cur.fetchone()[0]
            
            if wal_level != 'replica':
                logger.info("Configuring primary database for replication...")
                
                # Update postgresql.conf
                cur.execute("""
                ALTER SYSTEM SET wal_level = 'replica';
                ALTER SYSTEM SET max_wal_senders = 10;
                ALTER SYSTEM SET max_replication_slots = 10;
                """)
                
                # Create replication user if it doesn't exist
                cur.execute("SELECT 1 FROM pg_roles WHERE rolname = 'replicator'")
                if not cur.fetchone():
                    cur.execute("""
                    CREATE ROLE replicator WITH REPLICATION LOGIN PASSWORD 'replicator_password';
                    """)
                
                # Update pg_hba.conf to allow replication connections
                cur.execute("""
                ALTER SYSTEM SET listen_addresses = '*';
                """)
                
                logger.info("Primary database configured for replication. PostgreSQL restart required.")
                logger.info("Please add the following line to pg_hba.conf:")
                logger.info("host replication replicator all md5")
                
                # Prompt for restart
                if not args.docker:
                    restart = input("Restart PostgreSQL now? (y/n): ")
                    if restart.lower() == 'y':
                        logger.info("Restarting PostgreSQL...")
                        subprocess.run(["sudo", "systemctl", "restart", "postgresql"])
                        logger.info("PostgreSQL restarted.")
            else:
                logger.info("Primary database already configured for replication.")
            
            # Create replication slot for each replica
            for i in range(args.replica_count):
                slot_name = f"replica_{i}_slot"
                
                # Check if slot already exists
                cur.execute("SELECT 1 FROM pg_replication_slots WHERE slot_name = %s", (slot_name,))
                if not cur.fetchone():
                    cur.execute(f"SELECT pg_create_physical_replication_slot('{slot_name}')")
                    logger.info(f"Created replication slot: {slot_name}")
                else:
                    logger.info(f"Replication slot already exists: {slot_name}")
    
    finally:
        conn.close()

def setup_replica_with_docker(args, replica_id):
    """Set up a read replica using Docker."""
    replica_port = args.replica_base_port + replica_id
    replica_name = f"rentup-replica-{replica_id}"
    
    logger.info(f"Setting up read replica {replica_id} with Docker on port {replica_port}...")
    
    # Check if container already exists
    result = subprocess.run(
        ["docker", "ps", "-a", "--filter", f"name={replica_name}", "--format", "{{.Names}}"],
        capture_output=True,
        text=True
    )
    
    if replica_name in result.stdout:
        logger.info(f"Container {replica_name} already exists. Removing...")
        subprocess.run(["docker", "rm", "-f", replica_name])
    
    # Create replica container
    cmd = [
        "docker", "run", "-d",
        "--name", replica_name,
        "-e", f"POSTGRES_USER={args.primary_user}",
        "-e", f"POSTGRES_PASSWORD={args.primary_password}",
        "-e", f"POSTGRES_DB={args.primary_db}",
        "-p", f"{replica_port}:5432",
        "postgres:14-alpine"
    ]
    
    subprocess.run(cmd)
    logger.info(f"Created replica container: {replica_name}")
    
    # Wait for container to start
    logger.info("Waiting for replica container to start...")
    time.sleep(10)
    
    # Stop PostgreSQL in the container
    subprocess.run(["docker", "exec", replica_name, "pg_ctl", "-D", "/var/lib/postgresql/data", "stop", "-m", "fast"])
    
    # Clear data directory
    subprocess.run(["docker", "exec", replica_name, "rm", "-rf", "/var/lib/postgresql/data/*"])
    
    # Create recovery.conf
    recovery_conf = f"""
    primary_conninfo = 'host={args.primary_host} port={args.primary_port} user=replicator password=replicator_password'
    primary_slot_name = 'replica_{replica_id}_slot'
    """
    
    # Write recovery.conf to container
    subprocess.run(["docker", "exec", "-i", replica_name, "bash", "-c", f"echo '{recovery_conf}' > /var/lib/postgresql/data/recovery.conf"])
    
    # Set permissions
    subprocess.run(["docker", "exec", replica_name, "chown", "postgres:postgres", "/var/lib/postgresql/data/recovery.conf"])
    
    # Start PostgreSQL in the container
    subprocess.run(["docker", "exec", replica_name, "pg_ctl", "-D", "/var/lib/postgresql/data", "start"])
    
    logger.info(f"Read replica {replica_id} set up successfully on port {replica_port}")

def test_replication(args):
    """Test the replication setup."""
    logger.info("Testing replication setup...")
    
    # Connect to primary database
    primary_conn = connect_to_postgres(
        args.primary_host,
        args.primary_port,
        args.primary_user,
        args.primary_password,
        args.primary_db
    )
    
    try:
        # Create a test table
        with primary_conn.cursor() as cur:
            cur.execute("CREATE TABLE IF NOT EXISTS replication_test (id SERIAL PRIMARY KEY, test_data TEXT, created_at TIMESTAMP DEFAULT NOW())")
            cur.execute("INSERT INTO replication_test (test_data) VALUES (%s)", (f"Test data at {time.time()}",))
            
            # Get the inserted row
            cur.execute("SELECT id, test_data FROM replication_test ORDER BY id DESC LIMIT 1")
            primary_row = cur.fetchone()
            logger.info(f"Inserted row in primary: {primary_row}")
        
        # Wait for replication to occur
        logger.info("Waiting for replication to occur...")
        time.sleep(5)
        
        # Check each replica
        for i in range(args.replica_count):
            replica_port = args.replica_base_port + i
            
            try:
                # Connect to replica
                replica_conn = connect_to_postgres(
                    args.primary_host,  # Usually localhost
                    replica_port,
                    args.primary_user,
                    args.primary_password,
                    args.primary_db
                )
                
                try:
                    with replica_conn.cursor() as cur:
                        cur.execute("SELECT id, test_data FROM replication_test ORDER BY id DESC LIMIT 1")
                        replica_row = cur.fetchone()
                        
                        if replica_row and replica_row[0] == primary_row[0] and replica_row[1] == primary_row[1]:
                            logger.info(f"Replication successful for replica {i}")
                        else:
                            logger.error(f"Replication failed for replica {i}. Expected {primary_row}, got {replica_row}")
                finally:
                    replica_conn.close()
            
            except Exception as e:
                logger.error(f"Error connecting to replica {i}: {e}")
    
    finally:
        primary_conn.close()

def update_application_config(args):
    """Update the application configuration to use read replicas."""
    logger.info("Updating application configuration...")
    
    if NEW_MODULES:
        # Update db_config.py
        config_path = Path(__file__).parent.parent / "app" / "core" / "db_config.py"
        
        if not config_path.exists():
            logger.error(f"Configuration file not found: {config_path}")
            return
        
        with open(config_path, 'r') as f:
            config_content = f.read()
        
        # Update read replica settings
        config_content = config_content.replace(
            "READ_REPLICAS_ENABLED = False",
            "READ_REPLICAS_ENABLED = True"
        )
        
        config_content = config_content.replace(
            "READ_REPLICA_COUNT = 2",
            f"READ_REPLICA_COUNT = {args.replica_count}"
        )
        
        # Write updated configuration
        with open(config_path, 'w') as f:
            f.write(config_content)
        
        logger.info(f"Updated configuration file: {config_path}")
    
    else:
        # Update settings.py
        config_path = Path(__file__).parent.parent / "app" / "core" / "config.py"
        
        if not config_path.exists():
            logger.error(f"Configuration file not found: {config_path}")
            return
        
        with open(config_path, 'r') as f:
            config_content = f.read()
        
        # Update read replica settings
        config_content = config_content.replace(
            "READ_REPLICAS_ENABLED: bool = False",
            "READ_REPLICAS_ENABLED: bool = True"
        )
        
        config_content = config_content.replace(
            "READ_REPLICA_COUNT: int = 2",
            f"READ_REPLICA_COUNT: int = {args.replica_count}"
        )
        
        # Write updated configuration
        with open(config_path, 'w') as f:
            f.write(config_content)
        
        logger.info(f"Updated configuration file: {config_path}")

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Set up primary for replication
    setup_primary_for_replication(args)
    
    # Set up replicas
    if args.docker:
        for i in range(args.replica_count):
            setup_replica_with_docker(args, i)
    else:
        logger.info("Manual replica setup required. Please follow the documentation.")
    
    # Test replication
    if args.test:
        test_replication(args)
    
    # Update application configuration
    if args.update_config:
        update_application_config(args)
    
    logger.info("Read replica setup completed.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
