#!/usr/bin/env python3
"""
Script to implement security hardening for the RentUp backend based on audit results.

This script:
1. Reads security audit results
2. Implements database security hardening
3. Implements API security hardening
4. Implements code security fixes
5. Secures environment variables
6. Updates dependencies to secure versions
"""

import sys
import os
from pathlib import Path
import argparse
import logging
import json
import subprocess
import re
import datetime
import shutil
from typing import Dict, List, Any, Optional, Tuple, Set

# Add the parent directory to the path so we can import from app
sys.path.insert(0, str(Path(__file__).parent.parent))

import psycopg2
from psycopg2 import sql

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import from our modules
try:
    from app.core.db_config import db_config
    from app.core.database_connection import engine, session_scope
    NEW_MODULES = True
except ImportError:
    # Fall back to old modules
    from app.core.config import settings
    from app.core.database import engine, session_scope
    NEW_MODULES = False
    # Override the DATABASE_URL for this script
    settings.DATABASE_URL = "postgresql://rentup:rentup_secure_password@localhost:5432/rentup"

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Implement security hardening for RentUp backend')
    parser.add_argument('--audit-file', default='security_audit_report.json', help='Security audit report file')
    parser.add_argument('--database', action='store_true', help='Implement database security hardening')
    parser.add_argument('--api', action='store_true', help='Implement API security hardening')
    parser.add_argument('--code', action='store_true', help='Implement code security fixes')
    parser.add_argument('--env', action='store_true', help='Secure environment variables')
    parser.add_argument('--dependencies', action='store_true', help='Update dependencies to secure versions')
    parser.add_argument('--all', action='store_true', help='Implement all security hardening')
    parser.add_argument('--dry-run', action='store_true', help='Show changes without applying them')
    parser.add_argument('--backup', action='store_true', help='Create backups before making changes')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose output')
    return parser.parse_args()

def load_audit_results(audit_file):
    """Load security audit results from file."""
    try:
        with open(audit_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"Error loading audit results: {e}")
        return None

def backup_file(file_path):
    """Create a backup of a file."""
    backup_path = f"{file_path}.bak.{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"Error creating backup of {file_path}: {e}")
        return False

def harden_database_security(audit_results, args):
    """Implement database security hardening."""
    logger.info("Implementing database security hardening...")
    
    if not audit_results or "results" not in audit_results or "database" not in audit_results["results"]:
        logger.error("No database audit results found")
        return False
    
    database_results = audit_results["results"]["database"]
    issues = database_results.get("issues", [])
    recommendations = database_results.get("recommendations", [])
    
    if not issues:
        logger.info("No database security issues found")
        return True
    
    # Get database connection parameters
    if NEW_MODULES:
        db_host = db_config.POSTGRES_SERVER
        db_port = db_config.POSTGRES_PORT
        db_user = db_config.POSTGRES_USER
        db_password = db_config.POSTGRES_PASSWORD
        db_name = db_config.POSTGRES_DB
    else:
        db_url = settings.DATABASE_URL
        # Parse DATABASE_URL
        match = re.match(r'postgresql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)', db_url)
        if match:
            db_user, db_password, db_host, db_port, db_name = match.groups()
        else:
            logger.error("Could not parse DATABASE_URL")
            return False
    
    try:
        # Connect to database
        conn = psycopg2.connect(
            host=db_host,
            port=db_port,
            user=db_user,
            password=db_password,
            dbname=db_name
        )
        
        # Enable SSL if needed
        if "SSL is not enabled" in " ".join(issues):
            logger.info("Enabling SSL for database connections")
            
            if not args.dry_run:
                # This requires modifying postgresql.conf, which we can't do directly
                # Instead, provide instructions
                logger.info("To enable SSL, add the following to postgresql.conf:")
                logger.info("ssl = on")
                logger.info("ssl_cert_file = 'server.crt'")
                logger.info("ssl_key_file = 'server.key'")
                logger.info("Then restart PostgreSQL")
            else:
                logger.info("[DRY RUN] Would enable SSL for database connections")
        
        # Set password encryption to scram-sha-256
        if "Password encryption is set to" in " ".join(issues):
            logger.info("Setting password encryption to scram-sha-256")
            
            if not args.dry_run:
                with conn.cursor() as cur:
                    cur.execute("ALTER SYSTEM SET password_encryption = 'scram-sha-256'")
                    conn.commit()
                    logger.info("Password encryption set to scram-sha-256")
                    logger.info("You will need to restart PostgreSQL for this to take effect")
            else:
                logger.info("[DRY RUN] Would set password encryption to scram-sha-256")
        
        # Revoke excessive permissions from public schema
        if "Public schema has excessive permissions" in " ".join(issues):
            logger.info("Revoking excessive permissions from public schema")
            
            if not args.dry_run:
                with conn.cursor() as cur:
                    cur.execute("REVOKE ALL ON SCHEMA public FROM PUBLIC")
                    conn.commit()
                    logger.info("Revoked excessive permissions from public schema")
            else:
                logger.info("[DRY RUN] Would revoke excessive permissions from public schema")
        
        # Set strong passwords for users without passwords
        for issue in issues:
            if "has no password set" in issue:
                user = issue.split(" ")[1]
                logger.info(f"Setting strong password for user {user}")
                
                if not args.dry_run:
                    # Generate a strong random password
                    import secrets
                    import string
                    alphabet = string.ascii_letters + string.digits + string.punctuation
                    password = ''.join(secrets.choice(alphabet) for i in range(20))
                    
                    with conn.cursor() as cur:
                        cur.execute(f"ALTER USER {user} WITH PASSWORD %s", (password,))
                        conn.commit()
                        logger.info(f"Set strong password for user {user}")
                        logger.info(f"New password for {user}: {password}")
                        logger.info("IMPORTANT: Save this password securely!")
                else:
                    logger.info(f"[DRY RUN] Would set strong password for user {user}")
        
        # Update users to use SCRAM-SHA-256 password encryption
        for issue in issues:
            if "is not using SCRAM-SHA-256 password encryption" in issue:
                user = issue.split(" ")[1]
                logger.info(f"Updating password encryption for user {user}")
                
                if not args.dry_run:
                    # We need the current password to update it
                    logger.info(f"To update password encryption for {user}, run:")
                    logger.info(f"ALTER USER {user} WITH PASSWORD 'current_password';")
                    logger.info("(Replace 'current_password' with the actual current password)")
                else:
                    logger.info(f"[DRY RUN] Would update password encryption for user {user}")
        
        # Implement row-level security for sensitive tables
        if "Tables without row-level security" in " ".join(issues):
            for issue in issues:
                if "Tables without row-level security" in issue:
                    tables = issue.split(": ")[1].split(", ")
                    
                    for table in tables:
                        if table in ["users", "items", "rentals", "payments", "messages"]:
                            logger.info(f"Implementing row-level security for table {table}")
                            
                            if not args.dry_run:
                                with conn.cursor() as cur:
                                    # Enable row-level security
                                    cur.execute(f"ALTER TABLE {table} ENABLE ROW LEVEL SECURITY")
                                    
                                    # Create policies based on table
                                    if table == "users":
                                        cur.execute(f"""
                                            CREATE POLICY user_policy ON {table}
                                            USING (id = current_user_id())
                                            WITH CHECK (id = current_user_id())
                                        """)
                                    elif table in ["items", "rentals", "payments"]:
                                        cur.execute(f"""
                                            CREATE POLICY owner_policy ON {table}
                                            USING (owner_id = current_user_id())
                                            WITH CHECK (owner_id = current_user_id())
                                        """)
                                    elif table == "messages":
                                        cur.execute(f"""
                                            CREATE POLICY message_policy ON {table}
                                            USING (sender_id = current_user_id() OR receiver_id = current_user_id())
                                            WITH CHECK (sender_id = current_user_id())
                                        """)
                                    
                                    conn.commit()
                                    logger.info(f"Implemented row-level security for table {table}")
                            else:
                                logger.info(f"[DRY RUN] Would implement row-level security for table {table}")
        
        conn.close()
        
        return True
    
    except Exception as e:
        logger.error(f"Error implementing database security hardening: {e}")
        return False

def harden_api_security(audit_results, args):
    """Implement API security hardening."""
    logger.info("Implementing API security hardening...")
    
    if not audit_results or "results" not in audit_results or "api" not in audit_results["results"]:
        logger.error("No API audit results found")
        return False
    
    api_results = audit_results["results"]["api"]
    issues = api_results.get("issues", [])
    recommendations = api_results.get("recommendations", [])
    
    if not issues:
        logger.info("No API security issues found")
        return True
    
    try:
        # Add authentication to unauthenticated endpoints
        for issue in issues:
            if "may have unauthenticated endpoints" in issue:
                file_name = issue.split(" ")[2]
                file_path = Path(__file__).parent.parent / "app" / "api" / "v1" / file_name
                
                if not file_path.exists():
                    logger.warning(f"API file not found: {file_path}")
                    continue
                
                logger.info(f"Adding authentication to {file_name}")
                
                if args.backup:
                    backup_file(file_path)
                
                if not args.dry_run:
                    with open(file_path, "r") as f:
                        content = f.read()
                    
                    # Add authentication dependency
                    if "APIRouter" in content and "dependencies" not in content:
                        # Find the router definition
                        router_pattern = r'router\s*=\s*APIRouter\(\s*\)'
                        router_with_deps = 'router = APIRouter(dependencies=[Depends(get_current_user)])'
                        
                        # Add import for Depends and get_current_user if not present
                        if "from fastapi import" in content and "Depends" not in content:
                            content = re.sub(
                                r'from fastapi import ([^,\n]+)',
                                r'from fastapi import \1, Depends',
                                content
                            )
                        elif "from fastapi import" not in content:
                            content = "from fastapi import Depends\n" + content
                        
                        if "from app.api.deps import" in content and "get_current_user" not in content:
                            content = re.sub(
                                r'from app.api.deps import ([^,\n]+)',
                                r'from app.api.deps import \1, get_current_user',
                                content
                            )
                        elif "from app.api.deps import" not in content:
                            content = "from app.api.deps import get_current_user\n" + content
                        
                        # Replace router definition
                        content = re.sub(router_pattern, router_with_deps, content)
                        
                        with open(file_path, "w") as f:
                            f.write(content)
                        
                        logger.info(f"Added authentication to {file_name}")
                else:
                    logger.info(f"[DRY RUN] Would add authentication to {file_name}")
        
        # Add security headers to main.py
        main_file = Path(__file__).parent.parent / "app" / "main.py"
        
        if main_file.exists():
            security_headers_needed = False
            
            for issue in issues:
                if "Missing" in issue and "header" in issue:
                    security_headers_needed = True
                    break
            
            if security_headers_needed:
                logger.info("Adding security headers to main.py")
                
                if args.backup:
                    backup_file(main_file)
                
                if not args.dry_run:
                    with open(main_file, "r") as f:
                        content = f.read()
                    
                    # Check if middleware section exists
                    if "@app.middleware" in content:
                        # Add security headers to existing middleware
                        middleware_pattern = r'@app.middleware\("http"\)\nasync def ([^(]+)\(([^)]+)\):[^}]+}'
                        
                        def middleware_replacement(match):
                            func_name = match.group(1)
                            params = match.group(2)
                            
                            return f'''@app.middleware("http")
async def {func_name}({params}):
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["Content-Security-Policy"] = "default-src 'self'"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    return await call_next(request)'''
                        
                        content = re.sub(middleware_pattern, middleware_replacement, content)
                    else:
                        # Add new middleware section
                        app_pattern = r'app\s*=\s*FastAPI\([^)]*\)'
                        
                        def app_replacement(match):
                            return match.group(0) + '''

@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["Content-Security-Policy"] = "default-src 'self'"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    return response'''
                        
                        content = re.sub(app_pattern, app_replacement, content)
                        
                        # Add import for Request if not present
                        if "from fastapi import" in content and "Request" not in content:
                            content = re.sub(
                                r'from fastapi import ([^,\n]+)',
                                r'from fastapi import \1, Request',
                                content
                            )
                        elif "from fastapi import" not in content:
                            content = "from fastapi import FastAPI, Request\n" + content
                    
                    with open(main_file, "w") as f:
                        f.write(content)
                    
                    logger.info("Added security headers to main.py")
                else:
                    logger.info("[DRY RUN] Would add security headers to main.py")
        
        return True
    
    except Exception as e:
        logger.error(f"Error implementing API security hardening: {e}")
        return False

def fix_code_security(audit_results, args):
    """Implement code security fixes."""
    logger.info("Implementing code security fixes...")
    
    if not audit_results or "results" not in audit_results or "code" not in audit_results["results"]:
        logger.error("No code audit results found")
        return False
    
    code_results = audit_results["results"]["code"]
    issues = code_results.get("issues", [])
    recommendations = code_results.get("recommendations", [])
    
    if not issues:
        logger.info("No code security issues found")
        return True
    
    try:
        # Fix hardcoded secrets
        for issue in issues:
            if "Potential hardcoded secret in" in issue:
                file_path = issue.split("in ")[1]
                full_path = Path(__file__).parent.parent / file_path
                
                if not full_path.exists():
                    logger.warning(f"File not found: {full_path}")
                    continue
                
                logger.info(f"Fixing hardcoded secrets in {file_path}")
                
                if args.backup:
                    backup_file(full_path)
                
                if not args.dry_run:
                    with open(full_path, "r") as f:
                        content = f.read()
                    
                    # Replace hardcoded secrets with environment variables
                    secret_patterns = [
                        (r'password\s*=\s*["\']([^"\']+)["\']', r'password = os.environ.get("PASSWORD", "")'),
                        (r'secret\s*=\s*["\']([^"\']+)["\']', r'secret = os.environ.get("SECRET_KEY", "")'),
                        (r'token\s*=\s*["\']([^"\']+)["\']', r'token = os.environ.get("TOKEN", "")'),
                        (r'key\s*=\s*["\']([^"\']+)["\']', r'key = os.environ.get("API_KEY", "")'),
                        (r'api_key\s*=\s*["\']([^"\']+)["\']', r'api_key = os.environ.get("API_KEY", "")')
                    ]
                    
                    for pattern, replacement in secret_patterns:
                        content = re.sub(pattern, replacement, content)
                    
                    # Add import for os if not present
                    if "import os" not in content:
                        content = "import os\n" + content
                    
                    with open(full_path, "w") as f:
                        f.write(content)
                    
                    logger.info(f"Fixed hardcoded secrets in {file_path}")
                else:
                    logger.info(f"[DRY RUN] Would fix hardcoded secrets in {file_path}")
        
        # Fix SQL injection vulnerabilities
        for issue in issues:
            if "Potential SQL injection vulnerability in" in issue:
                file_path = issue.split("in ")[1]
                full_path = Path(__file__).parent.parent / file_path
                
                if not full_path.exists():
                    logger.warning(f"File not found: {full_path}")
                    continue
                
                logger.info(f"Fixing SQL injection vulnerability in {file_path}")
                
                if args.backup:
                    backup_file(full_path)
                
                if not args.dry_run:
                    with open(full_path, "r") as f:
                        content = f.read()
                    
                    # Replace string formatting with parameterized queries
                    sql_patterns = [
                        (r'execute\(\s*f"([^"]+)"\s*\)', r'execute(text("\1"), \2)'),
                        (r'execute\(\s*"([^"]+)" % ([^)]+)\)', r'execute(text("\1"), \2)'),
                        (r'execute\(\s*"([^"]+)".format\(([^)]+)\)', r'execute(text("\1"), {\2})')
                    ]
                    
                    for pattern, replacement in sql_patterns:
                        content = re.sub(pattern, replacement, content)
                    
                    # Add import for text if not present
                    if "from sqlalchemy import" in content and "text" not in content:
                        content = re.sub(
                            r'from sqlalchemy import ([^,\n]+)',
                            r'from sqlalchemy import \1, text',
                            content
                        )
                    elif "from sqlalchemy import" not in content:
                        content = "from sqlalchemy import text\n" + content
                    
                    with open(full_path, "w") as f:
                        f.write(content)
                    
                    logger.info(f"Fixed SQL injection vulnerability in {file_path}")
                else:
                    logger.info(f"[DRY RUN] Would fix SQL injection vulnerability in {file_path}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error implementing code security fixes: {e}")
        return False

def secure_environment_variables(audit_results, args):
    """Secure environment variables."""
    logger.info("Securing environment variables...")
    
    if not audit_results or "results" not in audit_results or "environment" not in audit_results["results"]:
        logger.error("No environment variable audit results found")
        return False
    
    env_results = audit_results["results"]["environment"]
    issues = env_results.get("issues", [])
    recommendations = env_results.get("recommendations", [])
    
    if not issues:
        logger.info("No environment variable security issues found")
        return True
    
    try:
        # Fix environment variable file permissions
        for issue in issues:
            if "has insecure permissions" in issue:
                file_path = issue.split(" ")[2]
                
                if not os.path.exists(file_path):
                    logger.warning(f"Environment file not found: {file_path}")
                    continue
                
                logger.info(f"Setting secure permissions for {file_path}")
                
                if not args.dry_run:
                    os.chmod(file_path, 0o600)
                    logger.info(f"Set permissions 600 for {file_path}")
                else:
                    logger.info(f"[DRY RUN] Would set permissions 600 for {file_path}")
        
        # Create .env.example file without sensitive values
        env_files = [
            ".env",
            ".env.local",
            ".env.development",
            ".env.production"
        ]
        
        for env_file in env_files:
            if os.path.exists(env_file):
                logger.info(f"Creating example environment file for {env_file}")
                
                if not args.dry_run:
                    with open(env_file, "r") as f:
                        content = f.read()
                    
                    # Replace sensitive values with placeholders
                    sensitive_patterns = [
                        (r'(PASSWORD\s*=\s*)[^\n]+', r'\1your_password_here'),
                        (r'(SECRET\s*=\s*)[^\n]+', r'\1your_secret_here'),
                        (r'(TOKEN\s*=\s*)[^\n]+', r'\1your_token_here'),
                        (r'(KEY\s*=\s*)[^\n]+', r'\1your_key_here'),
                        (r'(API_KEY\s*=\s*)[^\n]+', r'\1your_api_key_here')
                    ]
                    
                    for pattern, replacement in sensitive_patterns:
                        content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
                    
                    example_file = f"{env_file}.example"
                    
                    with open(example_file, "w") as f:
                        f.write(content)
                    
                    logger.info(f"Created example environment file: {example_file}")
                else:
                    logger.info(f"[DRY RUN] Would create example environment file for {env_file}")
        
        return True
    
    except Exception as e:
        logger.error(f"Error securing environment variables: {e}")
        return False

def update_dependencies(audit_results, args):
    """Update dependencies to secure versions."""
    logger.info("Updating dependencies to secure versions...")
    
    if not audit_results or "results" not in audit_results or "dependencies" not in audit_results["results"]:
        logger.error("No dependency audit results found")
        return False
    
    dependency_results = audit_results["results"]["dependencies"]
    issues = dependency_results.get("issues", [])
    recommendations = dependency_results.get("recommendations", [])
    
    if not issues:
        logger.info("No dependency security issues found")
        return True
    
    try:
        # Create a requirements.txt file with updated dependencies
        requirements_file = Path(__file__).parent.parent / "requirements.txt"
        
        if not requirements_file.exists():
            logger.warning("requirements.txt not found")
            return False
        
        logger.info("Updating dependencies in requirements.txt")
        
        if args.backup:
            backup_file(requirements_file)
        
        if not args.dry_run:
            with open(requirements_file, "r") as f:
                content = f.read()
            
            # Update vulnerable dependencies
            for issue in issues:
                if "Vulnerable dependency:" in issue or "Outdated dependency:" in issue:
                    parts = issue.split(" ")
                    package_name = parts[2]
                    current_version = parts[3]
                    
                    # Find the latest secure version
                    latest_version = None
                    
                    for recommendation in recommendations:
                        if f"Update {package_name} to" in recommendation:
                            latest_version = recommendation.split(" to ")[1]
                            break
                    
                    if latest_version:
                        # Update the dependency in requirements.txt
                        pattern = rf'{re.escape(package_name)}(==|>=|>|~=)[0-9.]*'
                        replacement = f'{package_name}=={latest_version}'
                        
                        content = re.sub(pattern, replacement, content)
                        
                        logger.info(f"Updated {package_name} from {current_version} to {latest_version}")
            
            with open(requirements_file, "w") as f:
                f.write(content)
            
            logger.info("Updated dependencies in requirements.txt")
            logger.info("Run 'pip install -r requirements.txt' to install updated dependencies")
        else:
            logger.info("[DRY RUN] Would update dependencies in requirements.txt")
        
        return True
    
    except Exception as e:
        logger.error(f"Error updating dependencies: {e}")
        return False

def main():
    """Main function."""
    args = parse_args()
    
    # Set log level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load audit results
    audit_results = load_audit_results(args.audit_file)
    
    if not audit_results:
        logger.error(f"Could not load audit results from {args.audit_file}")
        return 1
    
    # Determine which hardening to implement
    run_database = args.database or args.all
    run_api = args.api or args.all
    run_code = args.code or args.all
    run_env = args.env or args.all
    run_dependencies = args.dependencies or args.all
    
    # If no hardening specified, show help
    if not (run_database or run_api or run_code or run_env or run_dependencies):
        logger.info("No hardening specified. Use --all or specify individual hardening.")
        return 1
    
    # Implement security hardening
    results = {}
    
    if run_database:
        results["database"] = harden_database_security(audit_results, args)
    
    if run_api:
        results["api"] = harden_api_security(audit_results, args)
    
    if run_code:
        results["code"] = fix_code_security(audit_results, args)
    
    if run_env:
        results["environment"] = secure_environment_variables(audit_results, args)
    
    if run_dependencies:
        results["dependencies"] = update_dependencies(audit_results, args)
    
    # Print summary
    logger.info("Security hardening summary:")
    
    for category, success in results.items():
        status = "Success" if success else "Failed"
        logger.info(f"  {category.capitalize()}: {status}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
