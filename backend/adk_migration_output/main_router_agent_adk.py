"""
RentUp Main Router Agent - Google ADK Implementation
"""

from google.adk.agents import LlmAgent
from recommendation_agent import recommendation_agent
from fraud_detection_agent import fraud_detection_agent
# Import other agents as they are migrated

# Create the main router agent with hierarchical structure
main_router_agent = LlmAgent(
    model="gemini-2.0-flash-exp",
    name="rentup_main_agent",
    description="Main RentUp agent that coordinates specialized AI services",
    instruction="""You are the main RentUp AI coordinator. Your role is to understand 
    user requests and delegate them to the appropriate specialized agents.
    
    Delegation Rules:
    - For item recommendations, similar items, or personalized suggestions: delegate to recommendation_agent
    - For fraud detection, risk assessment, or security concerns: delegate to fraud_detection_agent
    - For pricing questions or market analysis: delegate to pricing_agent (when available)
    - For content moderation or inappropriate content: delegate to content_moderation_agent (when available)
    - For user behavior analysis or engagement: delegate to user_analysis_agent (when available)
    
    Always provide context to the specialized agents and synthesize their responses 
    into coherent, helpful answers for the user.""",
    sub_agents=[
        recommendation_agent,
        fraud_detection_agent,
        # Add other agents as they are migrated
    ]
)
