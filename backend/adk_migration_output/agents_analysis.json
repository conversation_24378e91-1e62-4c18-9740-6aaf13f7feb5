{"recommendation_agent": {"current_file": "app/ai/recommendation/agent.py", "functionality": ["Similar item recommendations", "Personalized recommendations", "Trending items", "Category-based recommendations"], "tools": ["similarity_search", "user_history", "trending_analysis"], "complexity": "high", "migration_priority": 1}, "fraud_detection_agent": {"current_file": "app/ai/fraud_detection/agent.py", "functionality": ["Transaction risk assessment", "User behavior analysis", "Pattern detection"], "tools": ["risk_calculator", "pattern_analyzer", "ml_model"], "complexity": "medium", "migration_priority": 2}, "pricing_agent": {"current_file": "app/ai/pricing/agent.py", "functionality": ["Dynamic pricing optimization", "Market analysis", "Demand prediction"], "tools": ["market_analyzer", "demand_predictor", "price_optimizer"], "complexity": "medium", "migration_priority": 3}, "content_moderation_agent": {"current_file": "app/ai/content_moderation/agent.py", "functionality": ["Content filtering", "Image analysis", "Text moderation"], "tools": ["text_analyzer", "image_classifier", "content_filter"], "complexity": "low", "migration_priority": 4}, "user_analysis_agent": {"current_file": "app/ai/user_analysis/agent.py", "functionality": ["User behavior tracking", "Churn prediction", "Engagement analysis"], "tools": ["behavior_tracker", "churn_predictor", "engagement_analyzer"], "complexity": "high", "migration_priority": 5}}