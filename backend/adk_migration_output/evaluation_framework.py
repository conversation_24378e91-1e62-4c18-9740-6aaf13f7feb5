"""
ADK Evaluation Framework for RentUp Agents
"""

import json
from google.adk.evaluation import AgentEvaluator
from typing import Dict, List, Any

class RentUpAgentEvaluator:
    """Custom evaluator for RentUp ADK agents."""
    
    def __init__(self):
        self.evaluator = AgentEvaluator()
        self.test_cases = self._load_test_cases()
    
    def _load_test_cases(self) -> List[Dict[str, Any]]:
        """Load test cases for agent evaluation."""
        return [
            {
                "test_id": "rec_001",
                "agent": "recommendation_agent",
                "input": {
                    "user_id": "test_user_123",
                    "context": "Looking for camping gear for weekend trip",
                    "location": "San Francisco, CA"
                },
                "expected_output": {
                    "recommendations": ["camping_tent", "sleeping_bag", "camping_stove"],
                    "explanation": "Based on your location and trip duration",
                    "confidence": "> 0.8"
                },
                "evaluation_criteria": [
                    "relevance_to_context",
                    "location_appropriateness", 
                    "explanation_quality",
                    "confidence_score"
                ]
            },
            {
                "test_id": "fraud_001",
                "agent": "fraud_detection_agent",
                "input": {
                    "transaction": {
                        "user_id": "suspicious_user_456",
                        "amount": 5000,
                        "location": "Nigeria",
                        "payment_method": "new_card"
                    }
                },
                "expected_output": {
                    "risk_score": "> 0.7",
                    "risk_factors": ["high_amount", "geographic_anomaly", "new_payment_method"],
                    "recommendation": "require_additional_verification"
                },
                "evaluation_criteria": [
                    "risk_score_accuracy",
                    "risk_factor_identification",
                    "recommendation_appropriateness"
                ]
            }
        ]
    
    async def evaluate_agent(self, agent_name: str) -> Dict[str, Any]:
        """Evaluate specific agent performance."""
        agent_tests = [tc for tc in self.test_cases if tc["agent"] == agent_name]
        
        results = {
            "agent": agent_name,
            "total_tests": len(agent_tests),
            "passed_tests": 0,
            "failed_tests": 0,
            "test_results": []
        }
        
        for test_case in agent_tests:
            try:
                # Run the test case through ADK evaluator
                result = await self.evaluator.evaluate(
                    agent_name=agent_name,
                    test_input=test_case["input"],
                    expected_output=test_case["expected_output"],
                    evaluation_criteria=test_case["evaluation_criteria"]
                )
                
                if result["passed"]:
                    results["passed_tests"] += 1
                else:
                    results["failed_tests"] += 1
                
                results["test_results"].append({
                    "test_id": test_case["test_id"],
                    "passed": result["passed"],
                    "score": result.get("score", 0.0),
                    "details": result.get("details", {})
                })
                
            except Exception as e:
                results["failed_tests"] += 1
                results["test_results"].append({
                    "test_id": test_case["test_id"],
                    "passed": False,
                    "error": str(e)
                })
        
        return results
    
    async def evaluate_all_agents(self) -> Dict[str, Any]:
        """Evaluate all migrated agents."""
        agents = list(set(tc["agent"] for tc in self.test_cases))
        
        evaluation_results = {
            "evaluation_timestamp": datetime.now().isoformat(),
            "total_agents": len(agents),
            "agent_results": {}
        }
        
        for agent in agents:
            evaluation_results["agent_results"][agent] = await self.evaluate_agent(agent)
        
        return evaluation_results

# Global evaluator instance
rentup_evaluator = RentUpAgentEvaluator()
