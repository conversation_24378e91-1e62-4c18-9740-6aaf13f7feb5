#!/usr/bin/env python3
"""
Simple test script to check imports.
"""

import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("Testing basic imports...")

try:
    print("1. Testing basic Python imports...")
    import json
    import re
    print("✓ Basic imports successful")
    
    print("2. Testing pydantic import...")
    from pydantic import BaseModel
    print("✓ Pydantic import successful")
    
    print("3. Testing app.ai.models import...")
    from app.ai.models import FlaggedContent
    print("✓ AI models import successful")
    
    print("4. Testing content checkers import...")
    from app.ai.content_moderation.modules.content_checkers import ContentCheckersService
    print("✓ Content checkers import successful")
    
    print("5. Testing service instantiation...")
    service = ContentCheckersService()
    print("✓ Service instantiation successful")
    
    print("6. Testing basic functionality...")
    score, flags = service.check_prohibited_content("This is a test message")
    print(f"✓ Basic functionality test: score={score}, flags={len(flags)}")
    
    print("\n🎉 All tests passed!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
