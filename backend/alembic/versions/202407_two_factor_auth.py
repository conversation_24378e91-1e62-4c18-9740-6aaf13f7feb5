"""Two-factor authentication

Revision ID: 202407_two_factor_auth
Revises: 202407_fraud_prevention
Create Date: 2024-07-20

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB


# revision identifiers, used by Alembic.
revision = '202407_two_factor_auth'
down_revision = '202407_fraud_prevention'
branch_labels = None
depends_on = None


def upgrade():
    # Add 2FA fields to users table
    op.add_column('users', sa.Column('totp_secret', sa.String(255), nullable=True))
    op.add_column('users', sa.Column('is_2fa_enabled', sa.Boolean(), nullable=False, server_default='false'))
    op.add_column('users', sa.Column('backup_codes', JSONB, nullable=True))
    op.add_column('users', sa.Column('last_password_change', sa.DateTime(timezone=True), nullable=True))
    
    # Create recovery_codes table for backup 2FA recovery
    op.create_table(
        'recovery_codes',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('code_hash', sa.String(255), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('used_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('is_used', sa.Boolean(), nullable=False, server_default='false'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add foreign key constraint
    op.create_foreign_key(
        'fk_recovery_codes_user',
        'recovery_codes', 'users',
        ['user_id'], ['id'],
        ondelete='CASCADE'
    )
    
    # Add index on user_id
    op.create_index(
        op.f('ix_recovery_codes_user_id'),
        'recovery_codes', ['user_id'],
        unique=False
    )
    
    # Create table for temporary 2FA verification tokens
    op.create_table(
        'totp_verification_tokens',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('token', sa.String(255), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('is_used', sa.Boolean(), nullable=False, server_default='false'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add foreign key constraint
    op.create_foreign_key(
        'fk_totp_verification_tokens_user',
        'totp_verification_tokens', 'users',
        ['user_id'], ['id'],
        ondelete='CASCADE'
    )
    
    # Add index on user_id
    op.create_index(
        op.f('ix_totp_verification_tokens_user_id'),
        'totp_verification_tokens', ['user_id'],
        unique=False
    )
    
    # Add index on token
    op.create_index(
        op.f('ix_totp_verification_tokens_token'),
        'totp_verification_tokens', ['token'],
        unique=True
    )


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_totp_verification_tokens_token'), table_name='totp_verification_tokens')
    op.drop_index(op.f('ix_totp_verification_tokens_user_id'), table_name='totp_verification_tokens')
    op.drop_index(op.f('ix_recovery_codes_user_id'), table_name='recovery_codes')
    
    # Drop foreign key constraints
    op.drop_constraint('fk_totp_verification_tokens_user', 'totp_verification_tokens', type_='foreignkey')
    op.drop_constraint('fk_recovery_codes_user', 'recovery_codes', type_='foreignkey')
    
    # Drop tables
    op.drop_table('totp_verification_tokens')
    op.drop_table('recovery_codes')
    
    # Drop columns from users table
    op.drop_column('users', 'last_password_change')
    op.drop_column('users', 'backup_codes')
    op.drop_column('users', 'is_2fa_enabled')
    op.drop_column('users', 'totp_secret')
