"""WebAuthn/FIDO2 support

Revision ID: 202407_webauthn_support
Revises: 202407_two_factor_auth
Create Date: 2024-07-21

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB


# revision identifiers, used by Alembic.
revision = '202407_webauthn_support'
down_revision = '202407_two_factor_auth'
branch_labels = None
depends_on = None


def upgrade():
    # Create webauthn_credentials table
    op.create_table(
        'webauthn_credentials',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('credential_id', sa.String(255), nullable=False),
        sa.Column('public_key', sa.Text(), nullable=False),
        sa.Column('sign_count', sa.Integer(), nullable=False, server_default='0'),
        sa.Column('aaguid', sa.String(255), nullable=True),
        sa.Column('credential_name', sa.String(255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('last_used_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False, server_default='true'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add foreign key constraint
    op.create_foreign_key(
        'fk_webauthn_credentials_user',
        'webauthn_credentials', 'users',
        ['user_id'], ['id'],
        ondelete='CASCADE'
    )
    
    # Add index on user_id
    op.create_index(
        op.f('ix_webauthn_credentials_user_id'),
        'webauthn_credentials', ['user_id'],
        unique=False
    )
    
    # Add index on credential_id
    op.create_index(
        op.f('ix_webauthn_credentials_credential_id'),
        'webauthn_credentials', ['credential_id'],
        unique=True
    )
    
    # Add is_passwordless column to users table
    op.add_column('users', sa.Column('is_passwordless', sa.Boolean(), nullable=False, server_default='false'))
    
    # Create webauthn_registration_challenges table
    op.create_table(
        'webauthn_registration_challenges',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('challenge', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('is_complete', sa.Boolean(), nullable=False, server_default='false'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add foreign key constraint
    op.create_foreign_key(
        'fk_webauthn_registration_challenges_user',
        'webauthn_registration_challenges', 'users',
        ['user_id'], ['id'],
        ondelete='CASCADE'
    )
    
    # Add index on user_id
    op.create_index(
        op.f('ix_webauthn_registration_challenges_user_id'),
        'webauthn_registration_challenges', ['user_id'],
        unique=False
    )
    
    # Create webauthn_authentication_challenges table
    op.create_table(
        'webauthn_authentication_challenges',
        sa.Column('id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('challenge', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=False),
        sa.Column('is_complete', sa.Boolean(), nullable=False, server_default='false'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Add foreign key constraint
    op.create_foreign_key(
        'fk_webauthn_authentication_challenges_user',
        'webauthn_authentication_challenges', 'users',
        ['user_id'], ['id'],
        ondelete='CASCADE'
    )
    
    # Add index on user_id
    op.create_index(
        op.f('ix_webauthn_authentication_challenges_user_id'),
        'webauthn_authentication_challenges', ['user_id'],
        unique=False
    )


def downgrade():
    # Drop indexes
    op.drop_index(op.f('ix_webauthn_authentication_challenges_user_id'), table_name='webauthn_authentication_challenges')
    op.drop_index(op.f('ix_webauthn_registration_challenges_user_id'), table_name='webauthn_registration_challenges')
    op.drop_index(op.f('ix_webauthn_credentials_credential_id'), table_name='webauthn_credentials')
    op.drop_index(op.f('ix_webauthn_credentials_user_id'), table_name='webauthn_credentials')
    
    # Drop foreign key constraints
    op.drop_constraint('fk_webauthn_authentication_challenges_user', 'webauthn_authentication_challenges', type_='foreignkey')
    op.drop_constraint('fk_webauthn_registration_challenges_user', 'webauthn_registration_challenges', type_='foreignkey')
    op.drop_constraint('fk_webauthn_credentials_user', 'webauthn_credentials', type_='foreignkey')
    
    # Drop tables
    op.drop_table('webauthn_authentication_challenges')
    op.drop_table('webauthn_registration_challenges')
    op.drop_table('webauthn_credentials')
    
    # Drop is_passwordless column from users table
    op.drop_column('users', 'is_passwordless')
