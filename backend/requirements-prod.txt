# Production Requirements for RentUp Backend (2025)
# Optimized for security, performance, and production deployment

# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
gunicorn==21.2.0

# Database
sqlalchemy==2.0.23
asyncpg==0.29.0
psycopg2-binary==2.9.9
alembic==1.13.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
cryptography==41.0.8

# Caching & Performance
redis==5.0.1
aioredis==2.0.1

# Validation & Serialization
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# HTTP Client
httpx==0.25.2
aiohttp==3.9.1

# Monitoring & Logging
prometheus-client==0.19.0
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Production Server
gunicorn==21.2.0
uvicorn[standard]==0.24.0

# Google ADK Dependencies (for AI agents migration)
google-cloud-aiplatform==1.40.0
litellm==1.0.3
google-adk==1.0.0

# Additional Production Dependencies
python-dotenv==1.0.0
Pillow==10.1.0
python-magic==0.4.27
celery==5.3.4
flower==2.0.1

# Development & Testing (for production debugging)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0

# Security & Rate Limiting
slowapi==0.1.9
python-limiter==3.5.0

# File handling
aiofiles==23.2.1

# Date/Time utilities
python-dateutil==2.8.2

# JSON handling
orjson==3.9.10

# Environment management
python-decouple==3.8
